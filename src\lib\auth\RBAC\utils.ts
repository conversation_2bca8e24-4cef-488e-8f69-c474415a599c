import {
  hasPermission,
  permissions,
  partnerResources,
  adminResources,
  createVenueResource,
  type Role,
  type Permission
} from './index';

import type { PermissionContext } from './types';

/**
 * RBAC Utility Functions for Common Operations
 */

/**
 * Check if user can view a resource
 */
export function canView(role: Role, resource: string, context?: PermissionContext): boolean {
  return hasPermission(role, resource, permissions.VIEW, context);
}

/**
 * Check if user can create in a resource
 */
export function canCreate(role: Role, resource: string, context?: PermissionContext): boolean {
  return hasPermission(role, resource, permissions.CREATE, context);
}

/**
 * Check if user can edit a resource
 */
export function canEdit(role: Role, resource: string, context?: PermissionContext): boolean {
  return hasPermission(role, resource, permissions.EDIT, context);
}

/**
 * Check if user can delete from a resource
 */
export function canDelete(role: Role, resource: string, context?: PermissionContext): boolean {
  return hasPermission(role, resource, permissions.DELETE, context);
}

/**
 * Partner Portal Specific Utilities
 */
export const partnerUtils = {
  // Dashboard access
  canViewDashboard: (role: Role) => canView(role, partnerResources.PARTNER_DASHBOARD),

  // Venue management
  canManageVenues: (role: Role) => canEdit(role, partnerResources.PARTNER_VENUES),
  canCreateVenues: (role: Role) => canCreate(role, partnerResources.PARTNER_VENUES),
  canDeleteVenues: (role: Role) => canDelete(role, partnerResources.PARTNER_VENUES),

  // Specific venue access
  canViewVenue: (role: Role, venueId: string) =>
    canView(role, createVenueResource(partnerResources.PARTNER_VENUE, venueId)),
  canEditVenue: (role: Role, venueId: string) =>
    canEdit(role, createVenueResource(partnerResources.PARTNER_VENUE, venueId)),

  // Venue events
  canManageVenueEvents: (role: Role, venueId: string) =>
    canEdit(role, createVenueResource(partnerResources.PARTNER_VENUE_EVENTS, venueId)),
  canCreateVenueEvents: (role: Role, venueId: string) =>
    canCreate(role, createVenueResource(partnerResources.PARTNER_VENUE_EVENTS, venueId)),

  // Team management
  canManageTeam: (role: Role) => canEdit(role, partnerResources.PARTNER_TEAM),
  canInviteTeamMembers: (role: Role) => canCreate(role, partnerResources.PARTNER_TEAM),
  canRemoveTeamMembers: (role: Role) => canDelete(role, partnerResources.PARTNER_TEAM),

  // Customer management
  canViewCustomers: (role: Role) => canView(role, partnerResources.PARTNER_CUSTOMERS),
  canEditCustomers: (role: Role) => canEdit(role, partnerResources.PARTNER_CUSTOMERS),

  // Billing access
  canViewBilling: (role: Role) => canView(role, partnerResources.PARTNER_BILLING),
  canEditBilling: (role: Role) => canEdit(role, partnerResources.PARTNER_BILLING),

  // Settings
  canViewSettings: (role: Role) => canView(role, partnerResources.PARTNER_SETTINGS),
  canEditSettings: (role: Role) => canEdit(role, partnerResources.PARTNER_SETTINGS),
};

/**
 * Admin Portal Specific Utilities
 */
export const adminUtils = {
  // User management
  canViewUsers: (role: Role) => canView(role, adminResources.ADMIN_USERS),
  canCreateUsers: (role: Role) => canCreate(role, adminResources.ADMIN_USERS),
  canEditUsers: (role: Role) => canEdit(role, adminResources.ADMIN_USERS),
  canDeleteUsers: (role: Role) => canDelete(role, adminResources.ADMIN_USERS),

  // User activity monitoring
  canViewUserActivity: (role: Role) => canView(role, adminResources.ADMIN_USERS_ACTIVITY),

  // Organization management
  canManageOrganizations: (role: Role) => canEdit(role, adminResources.ADMIN_ORGANIZATIONS),
  canCreateOrganizations: (role: Role) => canCreate(role, adminResources.ADMIN_ORGANIZATIONS),

  // Partner management
  canManagePartners: (role: Role) => canEdit(role, adminResources.ADMIN_PARTNERS),
  canCreatePartners: (role: Role) => canCreate(role, adminResources.ADMIN_PARTNERS),

  // Analytics access
  canViewAnalytics: (role: Role) => canView(role, adminResources.ADMIN_ANALYTICS),

  // Security management
  canManageSecurity: (role: Role) => canEdit(role, adminResources.ADMIN_SECURITY),
  canViewSecurityLogs: (role: Role) => canView(role, adminResources.ADMIN_SECURITY),

  // Feedback management
  canViewFeedback: (role: Role) => canView(role, adminResources.ADMIN_FEEDBACK),
  canRespondToFeedback: (role: Role) => canEdit(role, adminResources.ADMIN_FEEDBACK),

  // Link management
  canManageLinks: (role: Role) => canEdit(role, adminResources.ADMIN_LINKS),
  canCreateLinks: (role: Role) => canCreate(role, adminResources.ADMIN_LINKS),

  // Settings
  canViewAdminSettings: (role: Role) => canView(role, adminResources.ADMIN_SETTINGS),
  canEditAdminSettings: (role: Role) => canEdit(role, adminResources.ADMIN_SETTINGS),
};

/**
 * Permission level checker - returns the highest permission level for a resource
 */
export function getPermissionLevel(role: Role, resource: string, context?: PermissionContext): Permission | null {
  const permissionHierarchy: Permission[] = [
    permissions.VIEW,
    permissions.CREATE,
    permissions.EDIT,
    permissions.DELETE
  ];

  for (let i = permissionHierarchy.length - 1; i >= 0; i--) {
    if (hasPermission(role, resource, permissionHierarchy[i], context)) {
      return permissionHierarchy[i];
    }
  }

  return null;
}

/**
 * Check if a role has administrative privileges (can delete)
 */
export function isAdministrativeRole(role: Role, resource: string, context?: PermissionContext): boolean {
  return hasPermission(role, resource, permissions.DELETE, context);
}

/**
 * Check if a role has read-only access (only view permission)
 */
export function isReadOnlyRole(role: Role, resource: string, context?: PermissionContext): boolean {
  return canView(role, resource, context) &&
    !canCreate(role, resource, context) &&
    !canEdit(role, resource, context) &&
    !canDelete(role, resource, context);
}

/**
 * Generate a summary of permissions for a role
 */
export function getPermissionSummary(role: Role): {
  role: Role;
  totalResources: number;
  permissions: {
    view: number;
    create: number;
    edit: number;
    delete: number;
  };
  highestPrivilege: Permission | null;
} {
  const allResources = role.startsWith('partner:')
    ? Object.values(partnerResources)
    : Object.values(adminResources);

  let viewCount = 0;
  let createCount = 0;
  let editCount = 0;
  let deleteCount = 0;
  let highestPrivilege: Permission | null = null;

  allResources.forEach(resource => {
    const level = getPermissionLevel(role, resource);
    if (level) {
      if (level === permissions.DELETE) deleteCount++;
      if (level === permissions.EDIT || level === permissions.DELETE) editCount++;
      if (level === permissions.CREATE || level === permissions.EDIT || level === permissions.DELETE) createCount++;
      viewCount++;

      if (!highestPrivilege ||
        (level === permissions.DELETE) ||
        (level === permissions.EDIT && highestPrivilege !== permissions.DELETE) ||
        (level === permissions.CREATE && highestPrivilege === permissions.VIEW)) {
        highestPrivilege = level;
      }
    }
  });

  return {
    role,
    totalResources: allResources.length,
    permissions: {
      view: viewCount,
      create: createCount,
      edit: editCount,
      delete: deleteCount,
    },
    highestPrivilege,
  };
}

export default {
  canView,
  canCreate,
  canEdit,
  canDelete,
  partnerUtils,
  adminUtils,
  getPermissionLevel,
  isAdministrativeRole,
  isReadOnlyRole,
  getPermissionSummary,
};
