import { NextApiRequest, NextApiResponse } from 'next';

/**
 * Simple endpoint to check if we're running in development mode
 * Useful for debugging and understanding the current environment
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const isDevelopment = process.env.NODE_ENV === 'development' || 
                       process.env.NEXT_PUBLIC_APP_URL?.includes('localhost');

  return res.status(200).json({
    environment: process.env.NODE_ENV,
    isDevelopment,
    appUrl: process.env.NEXT_PUBLIC_APP_URL,
    testEventIds: ['E2i8pe45d'], // Current test events
    message: isDevelopment 
      ? '🧪 Development mode - Only test events will be processed'
      : '🚀 Production mode - All events will be processed'
  });
}
