import { Event } from '@/types';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import relativeTime from 'dayjs/plugin/relativeTime';
import isBetween from 'dayjs/plugin/isBetween';

// Load the plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);
dayjs.extend(isBetween);

// Set default timezone to Melbourne, Australia
const DEFAULT_TIMEZONE = 'Australia/Melbourne';

export function FormatDate(date: Date | string, tz?: string): string {
  return dayjs(date).tz(tz || DEFAULT_TIMEZONE).format('DD MMM YYYY');
}

export function FormatTime(date: Date | string, tz?: string): string {
  return dayjs(date).tz(tz || DEFAULT_TIMEZONE).format('h:mm A');
}

// Format date for display :: Autogen Code
const formatDate = (date: Date, tz?: string) => {
  return dayjs(date).tz(tz || DEFAULT_TIMEZONE).toDate().toLocaleDateString("en-US", {
    weekday: "short",
    month: "short",
    day: "numeric",
    year: "numeric",
  });
}

export function GetEventDateTimes(event: Event): { start: Date, end: Date } {
  const tz = event.timezone || DEFAULT_TIMEZONE;

  // Generate a string template to create an ISO date string with the event date and time
  const startTime = dayjs(`${dayjs(event.eventDate).format('YYYY-MM-DD')}T${event.start}:00`)
    .tz(tz).toDate();
  const endTime = dayjs(`${dayjs(event.eventDate).format('YYYY-MM-DD')}T${event.end}:00`)
    .tz(tz).toDate();

  return {
    start: startTime,
    end: endTime
  };
}

export function ToGoogleFormat(date: Date, tz?: string): string {
  return dayjs(date).tz(tz || DEFAULT_TIMEZONE).format('YYYYMMDDTHHmmss[Z]ZZ');
}

// Function to get user's local timezone
export function getUserTimezone(): string {
  try {
    return dayjs.tz.guess() || DEFAULT_TIMEZONE;
  } catch (error) {
    console.error('Error detecting timezone:', error);
    return DEFAULT_TIMEZONE;
  }
}

// Format time in "ago" format (e.g., "5 minutes ago", "2 hours ago", etc.)
export function formatTimeAgo(date: Date | string): string {
  return dayjs(date).fromNow();
}

/**
 * Parse a formatted date string with timezone information
 * Format example: "May 5, 2025 at 12:01:53 AM UTC+10"
 * 
 * @param dateString The date string to parse
 * @returns A dayjs object representing the parsed date
 */
export function parseFormattedDate(dateString: string): dayjs.Dayjs {
  // Extract components using regex
  const regex = /^(\w+)\s+(\d+),\s+(\d+)\s+at\s+(\d+):(\d+):(\d+)\s+(AM|PM)\s+UTC([+-])(\d+)$/;
  const match = dateString.match(regex);

  if (!match) {
    throw new Error(`Invalid date format. Expected format: "Month Day, Year at HH:MM:SS AM/PM UTC±X"`);
  }

  const [
    _, // Full match
    month,
    day,
    year,
    hour,
    minute,
    second,
    ampm,
    tzSign,
    tzOffset
  ] = match;

  // Convert 12-hour format to 24-hour
  let hour24 = parseInt(hour);
  if (ampm.toUpperCase() === 'PM' && hour24 < 12) {
    hour24 += 12;
  } else if (ampm.toUpperCase() === 'AM' && hour24 === 12) {
    hour24 = 0;
  }

  // Format the timezone offset
  const formattedTz = `${tzSign}${tzOffset.padStart(2, '0')}:00`;

  // Construct the ISO string (YYYY-MM-DDTHH:mm:ss±TZ:00)
  const monthMap: Record<string, string> = {
    'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
    'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12',
    'January': '01', 'February': '02', 'March': '03', 'April': '04', 'June': '06',
    'July': '07', 'August': '08', 'September': '09', 'October': '10', 'November': '11', 'December': '12'
  };

  const monthNum = monthMap[month];
  if (!monthNum) {
    throw new Error(`Invalid month: ${month}`);
  }

  const isoString = `${year}-${monthNum}-${day.padStart(2, '0')}T${hour24.toString().padStart(2, '0')}:${minute}:${second}${formattedTz}`;

  // Parse with dayjs
  return dayjs(isoString);
}

export default dayjs;