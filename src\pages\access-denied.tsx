import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { useRouter } from "next/router";
import { Shield } from "lucide-react";
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout";

export default function AccessDenied() {
  const router = useRouter();

  return (
    <ProtectedLayout>
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
        <div className="w-full max-w-md">
          <Card className="border-red-200 shadow-lg">
            <CardHeader className="bg-red-50 border-b border-red-200">
              <div className="flex items-center gap-2">
                <Shield className="h-6 w-6 text-red-600" />
                <h2 className="text-xl font-bold text-red-700">Access Denied</h2>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <p className="text-gray-700 mb-6">
                You don&apos;t have permission to access this page. This area is restricted to administrators only.
              </p>

              <Button
                variant="primary-button"
                className="w-full mt-2"
                onClick={() => router.push('/events')}
              >
                Go Back to Events
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedLayout>
  );
}
