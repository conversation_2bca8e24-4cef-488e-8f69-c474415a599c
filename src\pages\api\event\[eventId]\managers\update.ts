import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { Event } from '@/types';

/**
 * @api {POST} /api/event/:eventId/managers/update Update event managers
 * @apiName UpdateEventManagers
 * @apiGroup Event
 * @apiDescription Updates the event's managers array, replacing an email with a user ID
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId } = req.query;

  if (!eventId || typeof eventId !== 'string') {
    return res.status(400).json({ error: 'Event ID is required' });
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Get user info from session
    const session = await getServerSession(req, res, authConfig);

    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the email and userId from the request body
    const { email, userId } = req.body;
    if (!email || typeof email !== 'string' || !userId || typeof userId !== 'string') {
      return res.status(400).json({ error: 'Email and userId are required' });
    }

    // Get database instance
    const db = Database.getInstance();

    // Get event data
    const event = await db.readData('events', eventId) as Event;

    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Check if the event has a managers array
    if (!event.managers || !Array.isArray(event.managers)) {
      event.managers = [];
    }

    // Check if the email is in the managers array
    // Need to handle cases where the email might have extra quotes
    const emailIndex = event.managers.findIndex(manager => {
      if (typeof manager !== 'string') return false;

      // Clean the manager string by removing any extra quotes
      const cleanManager = manager.replace(/^"+|"+$/g, '');
      const cleanEmail = email.replace(/^"+|"+$/g, '');

      return cleanManager.toLowerCase() === cleanEmail.toLowerCase();
    });

    console.log(`Looking for email ${email} in managers array:`, event.managers);
    console.log(`Found at index: ${emailIndex}`);

    if (emailIndex === -1) {
      // If the email is not in the managers array, add the userId
      console.log(`Adding user ID ${userId} to managers array`);
      event.managers.push(userId);
    } else {
      // Replace the email with the userId
      console.log(`Replacing email at index ${emailIndex} with user ID ${userId}`);
      event.managers[emailIndex] = userId;
    }

    // Update the event in the database
    await db.updateData('events', eventId, { managers: event.managers });

    log(`Updated event managers for event ${eventId}: replaced ${email} with ${userId}`);

    return res.status(200).json({
      success: true,
      message: 'Event managers updated successfully'
    });
  } catch (error) {
    console.error('Error updating event managers:', error);
    return res.status(500).json({ error: 'Failed to update event managers' });
  }
}
