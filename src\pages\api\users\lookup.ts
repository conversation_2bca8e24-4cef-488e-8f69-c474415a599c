import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { UserProfile } from '@/types';
import { z } from 'zod';

// Input validation schema
const requestSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Authenticate the request
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Only allow POST method
    if (req.method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate request body
    const validatedData = requestSchema.safeParse(req.body);
    if (!validatedData.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: validatedData.error.issues
      });
    }

    const { userIds } = validatedData.data;

    // Get database instance
    const db = Database.getInstance();

    // Fetch all users in parallel using Promise.all
    const usersPromises = userIds.map(id => db.getUserProfile(id));
    const users = await Promise.all(usersPromises);

    // Filter out nulls and map to safe user data
    const validUsers = users
      .filter((user): user is NonNullable<typeof user> => user !== null)
      .map(user => ({
        id: user.id,
        name: user.name || null,
        email: user.email,
        image: user.image || null,
        isProfileComplete: user.isProfileComplete
      }));

    // Return the list of users
    return res.status(200).json({ users: validUsers });

  } catch (error) {
    console.error('Error in /api/users/lookup:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
