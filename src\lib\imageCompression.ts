import sharp from 'sharp';

export interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp' | 'auto';
  progressive?: boolean;
  preserveMetadata?: boolean;
  targetUse?: 'web' | 'print' | 'thumbnail';
  aggressiveCompression?: boolean; // Enable more aggressive compression
}

export interface CompressionResult {
  buffer: Buffer;
  format: string;
  size: number;
  originalSize: number;
  compressionRatio: number;
  width: number;
  height: number;
}

/**
 * Compresses an image buffer using <PERSON> with optimal settings for lossless or near-lossless compression
 */
export async function compressImage(
  inputBuffer: Buffer,
  options: CompressionOptions = {}
): Promise<CompressionResult> {
  const {
    maxWidth = 2048,
    maxHeight = 2048,
    quality = 88, // Optimized default for better compression
    format = 'auto',
    progressive = true,
    preserveMetadata = false,
    targetUse = 'web',
    aggressiveCompression = true // Enable by default for better file sizes
  } = options;

  try {
    // Get original image metadata
    const originalMetadata = await sharp(inputBuffer).metadata();
    const originalSize = inputBuffer.length;
    
    // Start Sharp pipeline
    let pipeline = sharp(inputBuffer);
    
    // Remove metadata unless explicitly preserving it (saves space)
    if (!preserveMetadata) {
      pipeline = pipeline.withMetadata();
    }
    
    // Resize if image is too large (maintain aspect ratio)
    const needsResize = 
      (originalMetadata.width && originalMetadata.width > maxWidth) ||
      (originalMetadata.height && originalMetadata.height > maxHeight);
    
    if (needsResize) {
      pipeline = pipeline.resize(maxWidth, maxHeight, {
        fit: 'inside',
        withoutEnlargement: true,
        kernel: sharp.kernel.lanczos3 // High-quality resampling
      });
    }    // Determine optimal format with enhanced logic
    let outputFormat: 'jpeg' | 'png' | 'webp' = 'jpeg';
    
    if (format === 'auto') {
      // Enhanced intelligent format selection with better compression priorities
      const hasTransparency = originalMetadata.channels === 4 || 
                             originalMetadata.hasAlpha === true;
      const fileSize = inputBuffer.length;
      const pixelCount = (originalMetadata.width || 0) * (originalMetadata.height || 0);
      
      if (hasTransparency) {
        // For transparent images, prioritize WebP for better compression
        if (targetUse === 'web') {
          outputFormat = 'webp'; // WebP handles transparency with excellent compression
        } else {
          outputFormat = fileSize > 500000 ? 'webp' : 'png';
        }
      } else {
        // For non-transparent images, prioritize WebP for web use
        const isLargeImage = fileSize > 500000; // 500KB threshold
        const isHighResolution = pixelCount > 1000000; // ~1000x1000
        
        if (targetUse === 'web') {
          // WebP provides superior compression for web content
          outputFormat = 'webp';
        } else if (isLargeImage || isHighResolution) {
          // JPEG for large photographic content when WebP isn't suitable
          outputFormat = 'jpeg';
        } else {
          // WebP even for smaller images if aggressive compression is enabled
          outputFormat = aggressiveCompression ? 'webp' : 'jpeg';
        }
      }
    } else {
      outputFormat = format as 'jpeg' | 'png' | 'webp';
    }    // Apply format-specific compression with enhanced settings
    switch (outputFormat) {
      case 'jpeg':
        pipeline = pipeline.jpeg({
          quality: aggressiveCompression ? Math.max(82, quality - 6) : quality,
          progressive,
          mozjpeg: true, // Use mozjpeg encoder for superior compression
          trellisQuantisation: true,
          overshootDeringing: true,
          optimiseScans: true,
          quantisationTable: aggressiveCompression ? 2 : 0 // More aggressive quantization
        });
        break;
          case 'png':
        pipeline = pipeline.png({
          quality: aggressiveCompression ? Math.max(85, quality - 5) : quality,
          progressive,
          compressionLevel: 9, // Maximum compression
          adaptiveFiltering: true, // Better compression for photos
          palette: aggressiveCompression || originalSize > 512 * 1024 // Use palette more aggressively
        });
        break;
          case 'webp':
        const webpQuality = aggressiveCompression ? Math.max(80, quality - 8) : quality;
        pipeline = pipeline.webp({
          quality: webpQuality,
          lossless: false, // Use lossy compression for better file sizes
          nearLossless: webpQuality >= 95, // Near-lossless only for highest quality
          effort: 6, // Maximum compression effort
          smartSubsample: true,
          preset: 'photo' // Optimize for photographic content
        });
        break;
    }

    // Execute compression
    const compressedBuffer = await pipeline.toBuffer();
    const compressedMetadata = await sharp(compressedBuffer).metadata();
    
    // Calculate compression statistics
    const compressionRatio = ((originalSize - compressedBuffer.length) / originalSize) * 100;
    
    return {
      buffer: compressedBuffer,
      format: outputFormat,
      size: compressedBuffer.length,
      originalSize,
      compressionRatio,
      width: compressedMetadata.width || 0,
      height: compressedMetadata.height || 0
    };
    
  } catch (error) {
    throw new Error(`Image compression failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validates if an image meets size and quality requirements
 */
export function validateImageForCompression(buffer: Buffer, maxSizeInMB: number = 10): boolean {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return buffer.length <= maxSizeInBytes;
}

/**
 * Gets optimal compression options based on image characteristics
 */
export async function getOptimalCompressionOptions(buffer: Buffer): Promise<CompressionOptions> {
  try {
    const metadata = await sharp(buffer).metadata();
    const fileSize = buffer.length;
    const isLargeFile = fileSize > 1 * 1024 * 1024; // 1MB threshold
    const isHighRes = (metadata.width || 0) > 1920 || (metadata.height || 0) > 1080;
    const isVeryLarge = fileSize > 3 * 1024 * 1024; // 3MB threshold
    
    return {
      maxWidth: isHighRes ? 2048 : 1920,
      maxHeight: isHighRes ? 2048 : 1080,
      quality: isVeryLarge ? 82 : isLargeFile ? 85 : 88, // More aggressive for larger files
      format: 'auto',
      progressive: true,
      preserveMetadata: false,
      targetUse: 'web',
      aggressiveCompression: true // Enable aggressive compression for better file sizes
    };
  } catch (error) {
    // Return safe defaults if metadata reading fails
    return {
      maxWidth: 2048,
      maxHeight: 2048,
      quality: 88,
      format: 'auto',
      progressive: true,
      preserveMetadata: false,
      targetUse: 'web',
      aggressiveCompression: true
    };
  }
}

/**
 * Smart compression that tries multiple formats and selects the best one
 * This provides maximum compression while maintaining visual quality
 */
export async function smartCompressImage(
  inputBuffer: Buffer,
  options: CompressionOptions = {}
): Promise<CompressionResult> {
  const baseOptions = { ...options, aggressiveCompression: true };
  
  try {
    // Get image metadata to determine if it has transparency
    const metadata = await sharp(inputBuffer).metadata();
    const hasTransparency = metadata.channels === 4 || metadata.hasAlpha === true;
    
    if (hasTransparency) {
      // For transparent images, try WebP and PNG, pick the smaller one
      const candidates = [
        compressImage(inputBuffer, { ...baseOptions, format: 'webp' }),
        compressImage(inputBuffer, { ...baseOptions, format: 'png' })
      ];
      
      const results = await Promise.all(candidates);
      return results.reduce((best, current) => 
        current.size < best.size ? current : best
      );
    } else {
      // For non-transparent images, try WebP, JPEG, and PNG
      const candidates = [
        compressImage(inputBuffer, { ...baseOptions, format: 'webp' }),
        compressImage(inputBuffer, { ...baseOptions, format: 'jpeg' }),
        compressImage(inputBuffer, { ...baseOptions, format: 'png' })
      ];
      
      const results = await Promise.all(candidates);
      
      // Select the format with the best compression while maintaining quality
      // Prefer WebP if it's significantly smaller, otherwise use the smallest
      const webpResult = results.find(r => r.format === 'webp');
      const smallestResult = results.reduce((best, current) => 
        current.size < best.size ? current : best
      );
      
      // Use WebP if it's within 10% of the smallest or if it's the smallest
      if (webpResult && (webpResult.size <= smallestResult.size * 1.1)) {
        return webpResult;
      }
      
      return smallestResult;
    }
  } catch (error) {
    // Fallback to regular compression if smart compression fails
    return compressImage(inputBuffer, baseOptions);
  }
}

/**
 * Ultra compression for very large files - maintains quality but prioritizes file size
 */
export async function ultraCompressImage(
  inputBuffer: Buffer,
  targetSizeKB?: number
): Promise<CompressionResult> {
  const metadata = await sharp(inputBuffer).metadata();
  const originalSizeKB = inputBuffer.length / 1024;
  
  // If no target size specified, aim for 70% reduction
  const targetSize = targetSizeKB || originalSizeKB * 0.3;
  
  // Start with aggressive WebP compression
  let currentOptions: CompressionOptions = {
    maxWidth: 1920,
    maxHeight: 1920,
    quality: 75,
    format: 'webp',
    progressive: true,
    preserveMetadata: false,
    targetUse: 'web',
    aggressiveCompression: true
  };
  
  // Try compression with decreasing quality until we hit target or minimum quality
  for (let quality = 75; quality >= 60; quality -= 5) {
    const options = { ...currentOptions, quality };
    const result = await compressImage(inputBuffer, options);
    
    if (result.size / 1024 <= targetSize || quality === 60) {
      return result;
    }
  }
  
  // If still too large, reduce dimensions further
  currentOptions.maxWidth = 1600;
  currentOptions.maxHeight = 1600;
  currentOptions.quality = 65;
  
  return compressImage(inputBuffer, currentOptions);
}
