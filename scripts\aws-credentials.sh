#!/bin/bash

# Ensure the script exits if any command fails
set -e

# Define variables
USER_NAME="iamcoming-app"
DOMAIN="iamcoming.io"
EMAIL_DOMAIN="app.iamcoming.io"
ENV_FILE="$(dirname $(dirname $(realpath $0)))/secrets.env"

# Check if user exists, if not then create user
if ! aws iam get-user --user-name ${USER_NAME} > /dev/null 2>&1; then
  aws iam create-user --user-name ${USER_NAME}
fi

# Create policy JSON file
POLICY_JSON=$(cat <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ses:SendEmail",
        "ses:SendRawEmail"
      ],
      "Resource": "arn:aws:ses:ap-southeast-2:045615149555:identity/${DOMAIN}",
      "Condition": {
        "StringLike": {
          "ses:FromAddress": "*@${EMAIL_DOMAIN}"
        }
      }
    }
  ]
}
EOF
)

# Create policy
aws iam put-user-policy --user-name ${USER_NAME} --policy-name SESPolicy --policy-document "${POLICY_JSON}"

# List access keys for the user
ACCESS_KEYS_LIST=$(aws iam list-access-keys --user-name ${USER_NAME} | jq -r '.AccessKeyMetadata[] | .AccessKeyId')

# Count the number of access keys
ACCESS_KEYS_COUNT=$(echo "${ACCESS_KEYS_LIST}" | wc -l)

# If there is more than one access key, delete the oldest one
if [ ${ACCESS_KEYS_COUNT} -gt 1 ]; then
  OLDEST_ACCESS_KEY=$(echo "${ACCESS_KEYS_LIST}" | head -n 1)
  aws iam delete-access-key --user-name ${USER_NAME} --access-key-id ${OLDEST_ACCESS_KEY}
fi

# Generate access keys
ACCESS_KEYS=$(aws iam create-access-key --user-name ${USER_NAME})

# Extract Access Key ID and Secret Access Key
ACCESS_KEY_ID=$(echo ${ACCESS_KEYS} | jq -r '.AccessKey.AccessKeyId')
SECRET_ACCESS_KEY=$(echo ${ACCESS_KEYS} | jq -r '.AccessKey.SecretAccessKey')

# Write to .env file
echo "AWS_ACCESS_KEY_ID=${ACCESS_KEY_ID}" >> ${ENV_FILE}
echo "AWS_SECRET_ACCESS_KEY=${SECRET_ACCESS_KEY}" >> ${ENV_FILE}

# Print success message
echo "IAM user ${USER_NAME} created with SES permissions. API keys have been set in ${ENV_FILE}."