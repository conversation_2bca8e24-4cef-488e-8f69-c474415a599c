import { APIResponse, ActivityHistoryItem } from '@/types';
import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { sendGuestResponseConfirmationEmail } from '@/lib/mailer';
import { log } from '@/lib/logger';
import analytics from '@/lib/analytics';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';

const RSVPRequestSchema = z.object({
  status: z.enum(['accepted', 'declined']),
  adults: z.number(),
  children: z.number(),
  message: z.string(),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string()
    .refine(
      (val) => {
        if (!val) return true; // Allow empty string
        
        // Remove spaces, dashes, parentheses, dots, and plus sign
        const cleaned = val.trim().replace(/[\s\-\(\)\.\+]/g, '');
        
        // Check if the cleaned number is a valid numeric string 
        // and has a reasonable length (6-15 digits is common for most phone numbers)
        return /^\d{6,15}$/.test(cleaned);
      }, 
      {
        message: "Please enter a valid phone number"
      }
    )
    .optional(),
});

/**
 * @api {POST} /event/:eventId/invites/:inviteId/rsvp Respond RSVP to invite
 * @apiName Respond RSVP
 * @apiGroup RSVP
 * @apiDescription Respond to an invite with RSVP status
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId, inviteId } = req.query;

  log('RSVP Response' + eventId + ' ' + inviteId , {
    eventId,
    inviteId,
    method: req.method,
    body: req.body
  });

  if (!eventId || typeof eventId !== 'string' || !inviteId || typeof inviteId !== 'string') {
    return res.status(400).json({ error: 'Event ID and Invite ID are required' });
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Get user info from session
    const session = await getServerSession(req, res, authConfig);
    const userId = session?.user?.id || null;

    // Pass the userId to the POST function
    const response = await POST(eventId, inviteId, req.body, userId);

    // Track the RSVP form submission in analytics
    await analytics.trackRSVPSubmitted(
      eventId,
      inviteId,
      userId,
      req.body.status,
      req.body.adults,
      req.body.children
    );
    
    return res.status(response.code).json(response.data);
  } catch (error) {
    console.error('Error processing RSVP:', error);
    return res.status(500).json({ error: 'Failed to process RSVP' });
  }
}

/**
 * Processes an RSVP response for an invite
 * 
 * @param eventId The ID of the event
 * @param inviteId The ID of the invite
 * @param data The RSVP response data
 * @param userId The ID of the user making the request (if authenticated)
 * @returns APIResponse containing the updated invite
 */
async function POST(eventId: string, inviteId: string, data: any, userId: string | null = null): Promise<APIResponse> {
  // Validate request data
  const validatedData = RSVPRequestSchema.parse(data);

  // Get current invite data
  const currentInvite = await Database.getInstance().readData('invites', inviteId);
  if (!currentInvite) {
    throw new Error('Invite not found');
  }

  // Get event data to check RSVP due date
  const event = await Database.getInstance().readData('events', eventId);
  if (!event) {
    throw new Error('Event not found');
  }

  // Check if RSVP due date has passed
  if (event.rsvpDueDate) {
    const now = new Date();
    const dueDate = new Date(event.rsvpDueDate);

    if (now > dueDate) {
      return {
        data: {
          error: 'RSVP deadline has passed',
          dueDate: dueDate.toISOString()
        },
        code: 403,
        headers: {
          'Content-Type': 'application/json',
        }
      };
    }
  }

  // Create timestamp for response and activity history
  const timestamp = new Date().toISOString();

  // Create activity history item
  const activityItem: ActivityHistoryItem = {
    type: 'rsvp_response',
    timestamp: timestamp,
    userId: userId, // Use the userId from the session if available
    properties: {
      status: validatedData.status,
      // Include additional data for analytics purposes
      eventId,
      adults: validatedData.adults,
      children: validatedData.children,
      email: validatedData.email
    }
  };

  // Prepare update data
  const updateData: any = {
    status: validatedData.status,
    response: {
      adults: validatedData.adults,
      children: validatedData.children,
      message: validatedData.message,
      timestamp: timestamp
    },
    updatedAt: timestamp
  };

  // Add to activityHistory array if it exists, otherwise create it
  if (currentInvite.activityHistory && Array.isArray(currentInvite.activityHistory)) {
    updateData.activityHistory = [...currentInvite.activityHistory, activityItem];
  } else {
    updateData.activityHistory = [activityItem];
  }

  // No longer updating the legacy rsvpHistory array

  // Update email if provided and different from current
  if (validatedData.email && validatedData.email !== currentInvite.email) {
    updateData.email = validatedData.email;
  }

  // Update phone if provided and different from current
  if (validatedData.phone !== undefined && validatedData.phone !== currentInvite.phone) {
    updateData.phone = validatedData.phone;
  }

  const response = await Database.getInstance().updateData('invites', inviteId, updateData);

  // Get the updated invite data
  const invite = await Database.getInstance().readData('invites', inviteId);

  await sendGuestResponseConfirmationEmail(event, invite, {
    email: invite.email,
    name: invite.name
  });

  log('RSVP processed for invite [' + inviteId + ']', response);

  return {
    data: {
      ...response
    },
    code: 200,
    headers: {
      'Content-Type': 'application/json',
    }
  }
}