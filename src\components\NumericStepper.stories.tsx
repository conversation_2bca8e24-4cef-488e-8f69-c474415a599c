import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { NumericStepper } from './NumericStepper';
import { FormProvider, useForm } from 'react-hook-form';

const meta = {
  title: 'Components/NumericStepper',
  component: NumericStepper,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof NumericStepper>;

export default meta;
type Story = StoryObj<typeof NumericStepper>;

// Wrapper component to provide form context
const FormWrapper = ({ children, defaultValues }: { children: React.ReactNode, defaultValues: any }) => {
  const methods = useForm({ defaultValues });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

export const Default: Story = {
  render: () => (
    <FormWrapper defaultValues={{ count: 1 }}>
      <NumericStepper name="count" defaultValue={1} />
    </FormWrapper>
  ),
};

export const WithHigherDefaultValue: Story = {
  render: () => (
    <FormWrapper defaultValues={{ guests: 5 }}>
      <NumericStepper name="guests" defaultValue={5} />
    </FormWrapper>
  ),
};

export const WithCustomStyling: Story = {
  render: () => (
    <FormWrapper defaultValues={{ quantity: 3 }}>
      <div className="p-4 bg-background rounded-md shadow-sm">
        <h3 className="mb-2 text-sm font-medium">Quantity</h3>
        <NumericStepper name="quantity" defaultValue={3} />
      </div>
    </FormWrapper>
  ),
};