/**
 * Utility functions for date formatting used across the application
 */

/**
 * Format a date for display in AI prompts and user interfaces
 * @param date - The date to format
 * @returns Formatted date string (e.g., "Monday, December 25, 2023")
 */
export function formatEventDate(date: Date): string {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Format a date for display with short format
 * @param date - The date to format
 * @returns Formatted date string (e.g., "Dec 25, 2023")
 */
export function formatEventDateShort(date: Date): string {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

/**
 * Format a date with time for display
 * @param date - The date to format
 * @returns Formatted date and time string
 */
export function formatEventDateWithTime(date: Date): string {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
}
