import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { EventListItem } from '@/types';

export function useEvents() {
  const { data: session, status } = useSession();
  const [events, setEvents] = useState<EventListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      // Don't fetch if session is still loading
      if (status === 'loading') {
        return;
      }

      if (!session?.user?.email) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const response = await fetch('/api/events');
        if (!response.ok) {
          throw new Error('Failed to fetch events');
        }
        const data = await response.json();
        setEvents(data);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch events'));
        setEvents([]); // Clear events on error
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, [session?.user?.email, status]);

  return { events, loading, error };
} 