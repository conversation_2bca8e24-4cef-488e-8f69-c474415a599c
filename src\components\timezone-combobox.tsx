"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface TimezoneOption {
  name: string
  offsetString: string
  offsetMinutes: number
}

interface TimezoneComboboxProps {
  timezones: TimezoneOption[]
  value: string
  onValueChange: (value: string) => void
  placeholder?: string
}

export function TimezoneCombobox({
  timezones,
  value,
  onValueChange,
  placeholder = "Select timezone..."
}: TimezoneComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchQuery, setSearchQuery] = React.useState("")

  // Filter timezones based on search query
  const filteredTimezones = React.useMemo(() => {
    if (!searchQuery) return timezones

    const query = searchQuery.toLowerCase()
    return timezones.filter(tz =>
      tz.name.toLowerCase().includes(query) ||
      tz.offsetString.toLowerCase().includes(query)
    )
  }, [timezones, searchQuery])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1"
        >
          <span className="truncate">
            {value
              ? timezones.find((tz) => tz.name === value)?.name.replace('_', ' ') || placeholder
              : placeholder}
          </span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="start">
        <Command>
          <CommandInput
            placeholder="Search timezone..."
            onValueChange={setSearchQuery}
          />
          <CommandList className="max-h-[240px] overflow-y-auto">
            <CommandEmpty>No timezone found.</CommandEmpty>
            <CommandGroup>
              {filteredTimezones.map((tz) => (
                <CommandItem
                  key={tz.name}
                  value={tz.name}
                  onSelect={(currentValue) => {
                    onValueChange(currentValue)
                    setOpen(false)
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === tz.name ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span className="flex justify-between w-full items-center">
                    <span className="truncate">{tz.name.replace('_', ' ')}</span>
                    <span className="text-gray-400 ml-2 text-xs shrink-0">{tz.offsetString}</span>
                  </span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
