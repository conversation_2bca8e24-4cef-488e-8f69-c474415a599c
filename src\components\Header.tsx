'use client'

import Image from "next/image"
import { ComponentProps, ReactNode, useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/router"
import { UserProfile } from "./UserProfile"
import { ChevronRight, Menu, Home, DollarSign, Info, Mail, User } from "lucide-react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Sheet, SheetContent, SheetTrigger } from "../components/ui/sheet"
import RemoteConfig from "@/lib/remoteConfig"

export type ButtonVariant = ComponentProps<typeof Button>["variant"];

type NavButton = {
  label: string;
  onClick: () => void;
  variant?: ButtonVariant;
  icon?: ReactNode;
};

type Breadcrumb = {
  label: string;
  href: string;
};

interface HeaderProps {
  buttons?: NavButton[];
  showUserProfile?: boolean;
  title?: string;
  breadcrumbs?: Breadcrumb[];
  className?: string;
}

export function Header({
  buttons = [],
  showUserProfile = true,
  title,
  breadcrumbs = [],
  className
}: HeaderProps) {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [isBetaMode, setIsBetaMode] = useState(false)
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  // Check if beta mode is enabled
  useEffect(() => {
    const checkBetaMode = async () => {
      const remoteConfig = RemoteConfig.getInstance();
      setIsBetaMode(remoteConfig.isBetaMode());
    };

    checkBetaMode();
  }, []);

  // No longer need handleLogoClick as we're using Link components

  // Build breadcrumbs array with home/events as first item if not provided
  const fullBreadcrumbs = breadcrumbs.length > 0
    ? breadcrumbs
    : title
      ? [{ label: 'Events', href: '/events' }]
      : []

  return (
    <>
      <header className={cn("w-full border-b print:hidden", className)}>
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Logo - redirect to home page if on home/pricing/about/contact, otherwise to events if logged in or home if not */}
            <Link href={
              router.pathname === "/" ||
              router.pathname === "/pricing" ||
              router.pathname === "/about" ||
              router.pathname === "/contact"
                ? "/"
                : status === "authenticated" ? "/events" : "/"
            }>
              {/* Desktop logo */}
              <Image
                src="/iac-logo-standard.svg"
                alt="I am Coming - Event RSVP Management Platform"
                width={120}
                height={32}
                className="cursor-pointer h-8 w-auto hidden md:block"
              />
              {/* Mobile logo - using smaller icon.svg */}
              <Image
                src="/icon.svg"
                alt="I am Coming"
                width={32}
                height={32}
                className="cursor-pointer h-8 w-8 md:hidden"
              />
            </Link>

            {/* Beta tag */}
            {isBetaMode && (
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                BETA
              </Badge>
            )}
          </div>

          {/* Empty middle section for spacing */}
          <div className="flex-1"></div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-2 mr-4">
            {/* Always show Home link for all users */}
            <Link
              href="/"
              className={cn(
                "px-3 py-2 rounded-md text-sm font-medium transition-colors",
                router.pathname === "/"
                  ? "bg-accent text-accent-foreground"
                  : "hover:bg-accent/50"
              )}
            >
              Home
            </Link>
            <Link
              href="/pricing"
              className={cn(
                "px-3 py-2 rounded-md text-sm font-medium transition-colors",
                router.pathname === "/pricing"
                  ? "bg-accent text-accent-foreground"
                  : "hover:bg-accent/50"
              )}
            >
              Pricing
            </Link>
            <Link
              href="/about"
              className={cn(
                "px-3 py-2 rounded-md text-sm font-medium transition-colors",
                router.pathname === "/about"
                  ? "bg-accent text-accent-foreground"
                  : "hover:bg-accent/50"
              )}
            >
              About
            </Link>
            <Link
              href="/contact"
              className={cn(
                "px-3 py-2 rounded-md text-sm font-medium transition-colors",
                router.pathname === "/contact"
                  ? "bg-accent text-accent-foreground"
                  : "hover:bg-accent/50"
              )}
            >
              Contact
            </Link>

            {/* Partner Panel link - only shown for partner organizations */}
            {session?.user?.organization?.type === 'partner' && (
              <Link
                href="/partner"
                className={cn(
                  "px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  router.pathname.startsWith("/partner")
                    ? "bg-accent text-accent-foreground"
                    : "hover:bg-accent/50"
                )}
              >
                Partner Panel
              </Link>
            )}
          </nav>

          {/* Buttons and User Profile */}
          <div className="flex items-center gap-4">
            {/* Mobile Menu */}


            {buttons.map((button, index) => (
              <Button
                key={index}
                variant={button.variant || "outline"}
                onClick={button.onClick}
              >
                {button.icon && <span className="mr-2">{button.icon}</span>}
                {button.label}
              </Button>
            ))}

            {showUserProfile ? (
              <UserProfile />
            ) : (
              <Button
                variant="ghost"
                className="!border !border-[#F43F5E] !text-[#F43F5E] !bg-transparent hover:!bg-transparent hover:!text-[#F43F5E] hover:!border-[#F43F5E]"
                onClick={() => {
                  // If user is already authenticated, redirect to events page
                  if (status === 'authenticated') {
                    router.push('/events');
                  } else {
                    router.push('/auth/signin');
                  }
                }}
              >
                Sign In
              </Button>
            )}
          </div>
           <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild className="md:hidden">
                <Button variant="ghost" size="icon" className="">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[280px] sm:w-[320px] p-0 border-l shadow-lg">
                <div className="flex flex-col h-full bg-background">
                  <div className="h-16 px-6 border-b flex items-center justify-between">
                    <Link href={
                      router.pathname === "/" ||
                      router.pathname === "/pricing" ||
                      router.pathname === "/about" ||
                      router.pathname === "/contact"
                        ? "/"
                        : status === "authenticated" ? "/events" : "/"
                    } onClick={() => setIsMenuOpen(false)}>
                      <div className="flex items-center">
                        <Image
                          src="/iac-logo-large.svg"
                          alt="I am Coming - Event RSVP Management Platform"
                          width={120}
                          height={32}
                          className="h-8 w-auto cursor-pointer"
                        />
                      </div>
                    </Link>
                  </div>
                  <nav className="flex-1 py-6 px-4">
                    <div className="text-xs uppercase tracking-wider font-semibold text-muted-foreground mb-4 px-2">Navigation</div>
                    <div className="space-y-1">
                      <Link
                        href="/"
                        className={cn(
                          "flex items-center py-2.5 px-3 rounded-md transition-colors text-sm font-medium w-full",
                          router.pathname === "/"
                            ? "bg-primary/10 text-primary"
                            : "hover:bg-accent/80"
                        )}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Home className="h-4 w-4 mr-3" />
                        Home
                      </Link>
                      <Link
                        href="/pricing"
                        className={cn(
                          "flex items-center py-2.5 px-3 rounded-md transition-colors text-sm font-medium w-full",
                          router.pathname === "/pricing"
                            ? "bg-primary/10 text-primary"
                            : "hover:bg-accent/80"
                        )}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <DollarSign className="h-4 w-4 mr-3" />
                        Pricing
                      </Link>
                      <Link
                        href="/about"
                        className={cn(
                          "flex items-center py-2.5 px-3 rounded-md transition-colors text-sm font-medium w-full",
                          router.pathname === "/about"
                            ? "bg-primary/10 text-primary"
                            : "hover:bg-accent/80"
                        )}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Info className="h-4 w-4 mr-3" />
                        About
                      </Link>
                      <Link
                        href="/contact"
                        className={cn(
                          "flex items-center py-2.5 px-3 rounded-md transition-colors text-sm font-medium w-full",
                          router.pathname === "/contact"
                            ? "bg-primary/10 text-primary"
                            : "hover:bg-accent/80"
                        )}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Mail className="h-4 w-4 mr-3" />
                        Contact
                      </Link>

                      {/* Partner Panel link in mobile menu - only shown for partner organizations */}
                      {session?.user?.organization?.type === 'partner' && (
                        <Link
                          href="/partner"
                          className={cn(
                            "flex items-center py-2.5 px-3 rounded-md transition-colors text-sm font-medium w-full",
                            router.pathname.startsWith("/partner")
                              ? "bg-primary/10 text-primary"
                              : "hover:bg-accent/80"
                          )}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <User className="h-4 w-4 mr-3" />
                          Partner Panel
                        </Link>
                      )}
                    </div>
                  </nav>
                  <div className="p-4 mt-auto border-t bg-muted/30">
                    {showUserProfile ? (
                      <div className="flex items-center justify-center p-2 rounded-lg bg-background shadow-sm">
                        <UserProfile />
                      </div>
                    ) : (
                      <Button
                        className="w-full"
                        variant="default"
                        onClick={() => {
                          // If user is already authenticated, redirect to events page
                          if (status === 'authenticated') {
                            router.push('/events');
                          } else {
                            router.push('/auth/signin');
                          }
                          setIsMenuOpen(false);
                        }}
                      >
                        <User className="h-4 w-4 mr-2" />
                        Sign In
                      </Button>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
        </div>
      </header>

      {/* Breadcrumb navigation below header */}
      {fullBreadcrumbs.length > 0 && (
        <div className="w-full bg-gray-50 border-b print:hidden">
          <div className="container mx-auto px-4 py-2">
            <div className="flex items-center">
              {fullBreadcrumbs.map((crumb, index) => (
                <div key={index} className="flex items-center">
                  {index > 0 && (
                    <ChevronRight className="h-4 w-4 mx-1 text-gray-400" />
                  )}
                  <Link
                    href={crumb.href}
                    className="text-sm text-muted-foreground hover:text-gray-900"
                  >
                    {crumb.label}
                  </Link>
                </div>
              ))}
              {title && (
                <>
                  <ChevronRight className="h-4 w-4 mx-1 text-gray-400" />
                  <h1 className="text-sm font-medium text-gray-900">{title}</h1>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  )
}