import { debugLog } from "@/lib/logger"
import { Event, CSVInvite, RSVPReport } from "@/types"
import { useEffect, useState } from "react"

export function useEvent(eventId?: string) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [event, setEvent] = useState<Event | null>(null)
  const [rsvpReport, setRsvpReport] = useState<RSVPReport | null>(null)

  useEffect(() => {
    if (!eventId) return

    setLoading(true)
    fetch(`/api/event/${eventId}`)
      .then((res) => {
        if (!res.ok) {
          if (res.status === 403) {
            // Handle forbidden error
            return res.json().then(data => {
              throw new Error(data.message || "Forbidden - You do not have access to this event", { 
                cause: { code: data.code || "FORBIDDEN_ACCESS" } 
              });
            });
          }
          throw new Error(`Error ${res.status}: ${res.statusText}`);
        }
        return res.json();
      })
      .then((data) => {
        setEvent(data)
        setLoading(false)
      })
      .catch((err) => {
        setError(err)
        setLoading(false)
      })
    
    fetch(`/api/event/${eventId}/rsvp/report`)
      .then((res) => {
        if (!res.ok) {
          throw new Error(`Error fetching RSVP report: ${res.statusText}`);
        }
        return res.json();
      })
      .then((data) => {
        setRsvpReport(data)
      })
      .catch((err) => {
        setRsvpReport(null)
        console.error("Error fetching RSVP report:", err);
        // Don't set error state for RSVP report failures - this is secondary data
      })
  }, [eventId])
  function createBulkInvites(invites: CSVInvite[]) {
    setLoading(true)
    setError(null)

    return fetch(`/api/event/${eventId}/invites`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invites),
    })
      .then((res) => res.json())
      .then((data) => {
        setLoading(false)
        return data
      })
      .catch((err) => {
        setError(err)
        setLoading(false)
        throw err
      })
  }

  return {
    loading,
    error,
    event,
    rsvpReport,
    createBulkInvites,
    saveEvent: async (event: Event) => {
      setLoading(true)
      setError(null)

      debugLog('Saving event', event);

      const endpoint = event.ID === 'new' ? `/api/events` : `/api/event/${event.ID}`
      const method = event.ID === 'new' ? 'POST' : 'PUT'
      
      try {
        const res = await fetch(endpoint, {
          method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(event),
        })
        
        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(errorData.message || `Error ${res.status}: ${res.statusText}`);
        }
        
        const data = await res.json()
        
        setEvent(data)
        setLoading(false)

        return data
        
      } catch (error) {
        setError(error as Error)
        setLoading(false)
        return null;
      }
    },
  }  
}