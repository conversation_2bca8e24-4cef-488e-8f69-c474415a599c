'use client'

import { useState } from "react"
import { PartnerLayout } from "@/components/layouts/PartnerLayout"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/router"
import { Building2, Plus, MapPin, Pencil, Trash2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

// Mock data for venues - in a real app, this would come from an API/database
const mockVenues = [
  {
    id: "venue-1",
    name: "Grand Ballroom",
    address: "123 Main St, Melbourne",
    description: "Elegant ballroom for weddings and corporate events",
    capacity: 200,
    locations: [
      { id: "loc-1", name: "Main Hall" },
      { id: "loc-2", name: "Terrace" }
    ]
  },
  {
    id: "venue-2",
    name: "Garden Pavilion",
    address: "456 Park Ave, Sydney",
    description: "Beautiful outdoor venue with garden views",
    capacity: 150,
    locations: [
      { id: "loc-3", name: "Garden Area" },
      { id: "loc-4", name: "Covered Pavilion" }
    ]
  }
];

export default function VenuesManagement() {
  const router = useRouter()
  const [venues, setVenues] = useState(mockVenues)
  const [isAddVenueOpen, setIsAddVenueOpen] = useState(false)
  const [isAddLocationOpen, setIsAddLocationOpen] = useState(false)
  const [selectedVenue, setSelectedVenue] = useState<any>(null)
  const [newVenue, setNewVenue] = useState({
    name: "",
    address: "",
    description: "",
    capacity: ""
  })
  const [newLocation, setNewLocation] = useState({
    name: "",
    description: ""
  })

  const handleAddVenue = () => {
    const venue = {
      id: `venue-${Date.now()}`,
      name: newVenue.name,
      address: newVenue.address,
      description: newVenue.description,
      capacity: parseInt(newVenue.capacity) || 0,
      locations: []
    };
    
    setVenues([...venues, venue]);
    setNewVenue({ name: "", address: "", description: "", capacity: "" });
    setIsAddVenueOpen(false);
  }

  const handleAddLocation = () => {
    if (!selectedVenue) return;
    
    const location = {
      id: `loc-${Date.now()}`,
      name: newLocation.name,
      description: newLocation.description
    };
    
    const updatedVenues = venues.map(venue => {
      if (venue.id === selectedVenue.id) {
        return {
          ...venue,
          locations: [...venue.locations, location]
        };
      }
      return venue;
    });
    
    setVenues(updatedVenues);
    setNewLocation({ name: "", description: "" });
    setIsAddLocationOpen(false);
  }

  const handleDeleteVenue = (venueId: string) => {
    setVenues(venues.filter(venue => venue.id !== venueId));
  }

  return (
    <PartnerLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Venues Management</h1>
            <p className="text-muted-foreground">Manage your venues and locations</p>
          </div>
          <Dialog open={isAddVenueOpen} onOpenChange={setIsAddVenueOpen}>
            <DialogTrigger asChild>
              <Button variant="primary-button">
                <Plus className="mr-2 h-4 w-4" />
                Add Venue
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Venue</DialogTitle>
                <DialogDescription>
                  Enter the details for your new venue.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="venue-name">Venue Name</Label>
                  <Input 
                    id="venue-name" 
                    placeholder="Enter venue name" 
                    value={newVenue.name}
                    onChange={(e) => setNewVenue({...newVenue, name: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="venue-address">Address</Label>
                  <Input 
                    id="venue-address" 
                    placeholder="Enter venue address" 
                    value={newVenue.address}
                    onChange={(e) => setNewVenue({...newVenue, address: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="venue-description">Description</Label>
                  <Textarea 
                    id="venue-description" 
                    placeholder="Enter venue description" 
                    value={newVenue.description}
                    onChange={(e) => setNewVenue({...newVenue, description: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="venue-capacity">Capacity</Label>
                  <Input 
                    id="venue-capacity" 
                    type="number" 
                    placeholder="Enter venue capacity" 
                    value={newVenue.capacity}
                    onChange={(e) => setNewVenue({...newVenue, capacity: e.target.value})}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddVenueOpen(false)}>Cancel</Button>
                <Button variant="default" onClick={handleAddVenue}>Add Venue</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        {/* Venues List */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {venues.map((venue) => (
            <Card key={venue.id} className="overflow-hidden">
              <CardHeader className="bg-gray-50 pb-4">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{venue.name}</CardTitle>
                    <div className="flex items-center text-muted-foreground text-sm mt-1">
                      <MapPin className="h-3.5 w-3.5 mr-1" />
                      {venue.address}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="icon">
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will permanently delete the venue and all its locations. This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleDeleteVenue(venue.id)} className="bg-red-500 hover:bg-red-600">
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-4">
                <p className="text-sm mb-4">{venue.description}</p>
                <div className="flex items-center mb-4">
                  <span className="text-sm font-medium">Capacity:</span>
                  <span className="text-sm ml-2">{venue.capacity} people</span>
                </div>
                
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="font-medium">Locations/Areas</h3>
                    <Dialog open={isAddLocationOpen && selectedVenue?.id === venue.id} onOpenChange={(open) => {
                      setIsAddLocationOpen(open);
                      if (open) setSelectedVenue(venue);
                    }}>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Plus className="h-3.5 w-3.5 mr-1" />
                          Add Location
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add New Location</DialogTitle>
                          <DialogDescription>
                            Add a new location or area to {venue.name}.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                          <div className="space-y-2">
                            <Label htmlFor="location-name">Location Name</Label>
                            <Input 
                              id="location-name" 
                              placeholder="Enter location name" 
                              value={newLocation.name}
                              onChange={(e) => setNewLocation({...newLocation, name: e.target.value})}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="location-description">Description (Optional)</Label>
                            <Textarea 
                              id="location-description" 
                              placeholder="Enter location description" 
                              value={newLocation.description}
                              onChange={(e) => setNewLocation({...newLocation, description: e.target.value})}
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setIsAddLocationOpen(false)}>Cancel</Button>
                          <Button variant="default" onClick={handleAddLocation}>Add Location</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                  
                  {venue.locations.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No locations added yet.</p>
                  ) : (
                    <ul className="space-y-2">
                      {venue.locations.map((location) => (
                        <li key={location.id} className="text-sm flex justify-between items-center p-2 bg-gray-50 rounded">
                          {location.name}
                          <div className="flex space-x-1">
                            <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                              <Pencil className="h-3.5 w-3.5" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-red-500">
                              <Trash2 className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {venues.length === 0 && (
          <Card className="text-center p-8">
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="bg-gray-100 p-4 rounded-full">
                <Building2 className="h-12 w-12 text-gray-400" />
              </div>
              <CardTitle>No Venues Yet</CardTitle>
              <CardDescription>
                You haven&apos;t added any venues yet. Add your first venue to get started.
              </CardDescription>
              <Button variant="primary-button" onClick={() => setIsAddVenueOpen(true)} className="mt-2">
                <Plus className="mr-2 h-4 w-4" />
                Add Venue
              </Button>
            </div>
          </Card>
        )}
      </div>
    </PartnerLayout>
  )
}
