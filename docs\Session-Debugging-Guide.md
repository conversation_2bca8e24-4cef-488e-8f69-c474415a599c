# Session Endpoint Debugging Guide

This guide explains how to debug 401 errors and authentication issues with the `/api/id/session` endpoint.

## Quick Start

1. **Enable debugging:**

   ```bash
   ./scripts/enable-session-debug.sh
   ```

2. **Restart your development server:**

   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

3. **Make a request to the session endpoint** and check the server console for detailed logs.

## Debug Output Explained

When debugging is enabled (`IAC_DEBUG=true` or `NODE_ENV=development`), the session endpoint will log detailed information:

### 1. Request Details

```log
🔍 Session endpoint DEBUG - Request details:
- Method: GET
- URL: /api/id/session
- Origin: https://partner.iamcoming.io
- User-Agent: Mozilla/5.0...
- Referer: https://partner.iamcoming.io/dashboard
- X-Forwarded-For: *************
- Cookie header present: true
- Raw cookies: PRESENT
```

### 2. Environment Configuration

```log
🔍 Environment details:
- NODE_ENV: development
- IAC_DEBUG: true
- AUTH_SECRET available: true
- AUTH_SECRET length: 64
- NEXTAUTH_URL: https://app.iamcoming.io
- NEXTAUTH_SECRET available: true
```

### 3. Cookie Analysis

```log
🔍 Cookie parsing results:
- Total cookies parsed: 5
- All cookie names: ['cross-domain-session', 'next-auth.session-token', '_ga', '_gid', 'theme']
- cross-domain-session present: true
- cross-domain-session length: 247
- next-auth.session-token present: YES (length: 186)
- __Secure-next-auth.session-token present: NO
- cross-domain-session starts with: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1...
```

### 4. Token Verification (Cross-Domain)

```log
🔐 Token verification details:
- Token length: 247
- Secret available: true
- Secret length: 64
- Header length: 36
- Payload length: 156
- Signature length: 43
- Expected signature: K7gNU3sdo+OL0wNhqoVWhr...
- Actual signature: K7gNU3sdo+OL0wNhqoVWhr...
- Signatures match: true
- Payload decoded successfully
- Payload keys: ['userId', 'email', 'iss', 'exp']
- User ID: user_12345
- Email: <EMAIL>
- Issuer: iamcoming.io
- Expires at: 2024-01-15T10:30:00.000Z
- Current time: 2024-01-15T09:15:00.000Z
✅ Token verification successful
```

### 5. NextAuth Session Analysis (Fallback)

```log
🔍 NextAuth session analysis:
- Session object exists: true
- Session type: object
- Session keys: ['user', 'expires']
- User object exists: true
- User keys: ['id', 'email', 'name', 'image']
- User ID: user_12345
- User email: <EMAIL>
- User name: John Doe
- Session expires: 2024-02-15T09:15:00.000Z
```

### 6. Database Operations

```log
🔍 Database operations:
- Looking up user profile for ID: user_12345
- Database instance available: true
- Profile lookup completed
- Profile exists: true
- Profile ID: user_12345
- Profile email: <EMAIL>
- Profile name: John Doe
- Profile isAdmin: false
- Profile organizationId: org_67890
```

### 7. Organization and Role Assignment

```log
🔍 Organization lookup for user: user_12345
- Organization lookup completed
- Organization exists: true
- Organization ID: org_67890
- Organization type: partner
- Organization name: Partner Corp
- Final roles assigned: ['partner']
```

## Common Issues and Solutions

### Issue 1: No Cookies Present

**Debug output:**

```log
- Cookie header present: false
- Raw cookies: MISSING
🔍 No cookie header present in request
```

**Solution:**

- Ensure the client is sending `credentials: 'include'` in fetch requests
- Check CORS configuration allows credentials
- Verify the domain/subdomain cookie settings

### Issue 2: Cross-Domain Token Invalid

**Debug output:**

```log
❌ Signature verification failed
```

**Possible causes:**

- `AUTH_SECRET` mismatch between domains
- Token corruption during transmission
- Token expired

**Solution:**

- Verify `AUTH_SECRET` is identical across all domains
- Check token expiration time
- Ensure secure cookie transmission (HTTPS)

### Issue 3: NextAuth Session Invalid

**Debug output:**

```log
❌ NextAuth session validation failed - detailed analysis:
- Session is null/undefined: true
- Session.user is null/undefined: true
- Session.user.id is null/undefined: true
```

**Possible causes:**

- User not logged in
- Session expired
- NextAuth configuration issues
- Cookie domain mismatch

**Solution:**

- Check user login status
- Verify NextAuth configuration
- Check cookie domain settings in NextAuth config

### Issue 4: User Profile Not Found

**Debug output:**

```log
❌ User profile not found in database
- Profile exists: false
- Profile is null/undefined
```

**Possible causes:**

- User not created in database
- Database connection issues
- User ID mismatch

**Solution:**

- Check database connectivity
- Verify user exists in the users table
- Check user ID format consistency

## Manual Testing

### Test Cross-Domain Authentication

```bash
# Test from external domain with cross-domain cookie
curl -X GET 'https://app.iamcoming.io/api/id/session' \
  -H 'Origin: https://partner.iamcoming.io' \
  -H 'Cookie: cross-domain-session=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1...' \
  --verbose
```

### Test Same-Domain Authentication

```bash
# Test from same domain with NextAuth session
curl -X GET 'https://app.iamcoming.io/api/id/session' \
  -H 'Cookie: next-auth.session-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1...' \
  --verbose
```

## Environment Variables

Make sure these environment variables are properly set:

```bash
# Required for authentication
AUTH_SECRET=your-secret-here-64-chars-min
NEXTAUTH_URL=https://app.iamcoming.io
NEXTAUTH_SECRET=your-nextauth-secret

# Enable debugging
IAC_DEBUG=true

# Database (if using custom database)
DATABASE_URL=postgresql://...
```

## Disabling Debug Mode

To disable debugging in production:

```bash
# Set in .env.local or environment
IAC_DEBUG=false
```

Or remove the environment variable entirely.

## Security Notes

- Debug logs may contain sensitive information (user IDs, partial tokens)
- Always disable debugging in production environments
- Never log complete authentication tokens
- Ensure logs are not accessible to unauthorized users
