import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import Stripe from 'stripe';
import { debugLog, log } from '@/lib/logger';
import { PaymentHistoryItem } from '@/types';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia'
});

// Mock payments data to use when no real Stripe payments exist
const mockPayments:PaymentHistoryItem[] = [
  {
    id: "pi_mock001",
    date: (new Date("2025-03-28")).toISOString(),
    amount: 10.00,
    description: "Host+ Plan",
    status: "succeeded",
    paymentMethod: "Visa •••• 4242",
    eventId: "evt-abc123",
    plan: "host_plus",
    receiptUrl: "https://dashboard.stripe.com/test/payments",
    invoiceUrl: "https://dashboard.stripe.com/test/invoices"
  },
  {
    id: "pi_mock002",
    date: (new Date("2025-03-15")).toISOString(),
    amount: 25.00,
    description: "Host Pro Plan (Upgrade)",
    status: "succeeded",
    paymentMethod: "Mastercard •••• 5555",
    eventId: "evt-def456",
    plan: "host_pro",
    receiptUrl: "https://dashboard.stripe.com/test/payments",
    invoiceUrl: "https://dashboard.stripe.com/test/invoices"
  }
];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authConfig);
    
    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const userId = session.user.id;
    debugLog('Fetching payment history', { userId });

    let enhancedPayments:PaymentHistoryItem[] = [];

    try {
      // First, get checkout sessions that belong to this user
      const checkoutSessions = await stripe.checkout.sessions.list({
        limit: 100,
        expand: ['data.payment_intent', 'data.line_items']
      });
      
      // Filter sessions by user ID in metadata
      const userSessions = checkoutSessions.data.filter(
        session => session.metadata?.userId === userId
      );

      log('Found user checkout sessions', {
        count: checkoutSessions.data.length,
        userSessionCount: userSessions.length
      });
      
      debugLog('Found user checkout sessions', { 
        count: userSessions.length,
        sessionIds: userSessions.map(s => s.id)
      });

      // For each session, get the associated payment intent
      const userPaymentIntents = [];
      
      for (const session of userSessions) {
        // If payment intent is not expanded, fetch it directly
        let paymentIntent = session.payment_intent;
        
        if (paymentIntent && typeof paymentIntent === 'string') {
          try {
            paymentIntent = await stripe.paymentIntents.retrieve(paymentIntent, {
              expand: ['latest_charge']
            });
          } catch (error) {
            debugLog('Error fetching payment intent', { paymentIntentId: paymentIntent, error });
            continue;
          }
        }
        
        if (paymentIntent) {
          // Add session metadata to the payment intent for reference
          if (typeof paymentIntent !== 'string') {
            paymentIntent.metadata = {
              ...paymentIntent.metadata,
              ...session.metadata
            };
            userPaymentIntents.push(paymentIntent);
          }
        }
      }
      
      debugLog('Found user payment intents', { 
        count: userPaymentIntents.length,
        paymentIntentIds: userPaymentIntents.map(pi => pi.id)
      });

      // For each payment, get additional details like receipt
      enhancedPayments = await Promise.all(
        userPaymentIntents.map(async (payment) => {
          // Get associated charges
          const charges = payment.latest_charge ? 
            typeof payment.latest_charge === 'string' ?
              [await stripe.charges.retrieve(payment.latest_charge)] :
              [payment.latest_charge] : 
            [];
          
          // Get receipt URL if available
          const receiptUrl = charges[0]?.receipt_url || null;
          
          // Get payment method details if available
          let paymentMethodDisplay = payment.payment_method_types[0] || 'card';
          
          if (payment.payment_method && typeof payment.payment_method === 'string') {
            try {
              const paymentMethod = await stripe.paymentMethods.retrieve(payment.payment_method);
              if (paymentMethod.card) {
                paymentMethodDisplay = `${paymentMethod.card.brand.charAt(0).toUpperCase() + paymentMethod.card.brand.slice(1)} •••• ${paymentMethod.card.last4}`;
              }
            } catch (error) {
              debugLog('Error fetching payment method', { paymentMethodId: payment.payment_method, error });
            }
          }
          
          // Create enhanced payment object
          return {
            id: payment.id,
            date: new Date(payment.created * 1000),
            amount: payment.amount / 100, // Convert from cents
            description: `${payment.description || ''}${payment.metadata?.isUpgrade === 'true' ? ' (Upgrade)' : ''}`,
            status: payment.status,
            paymentMethod: paymentMethodDisplay,
            eventId: payment.metadata?.eventId,
            plan: payment.metadata?.plan,
            receiptUrl,
            invoiceUrl: payment.invoice ?
              (await stripe.invoices.retrieve(payment.invoice as string)).hosted_invoice_url :
              null
          } as unknown as PaymentHistoryItem;
        })
      );
    } catch (stripeError) {
      console.error('Error fetching from Stripe:', stripeError);
      debugLog('Stripe API error, falling back to mock data', { error: stripeError });
      // Stripe error - proceed with mock data
    }

    // If no payments were found in Stripe, use mock data for development/testing
    if (enhancedPayments.length === 0) {
      debugLog('No payments found, using mock data');
      enhancedPayments = mockPayments;
    }

    // Sort by date (newest first)
    enhancedPayments.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    log('Payment history fetched successfully', { userId, paymentCount: enhancedPayments.length });
    debugLog('Payment history details', { payments: enhancedPayments });

    return res.status(200).json({ payments: enhancedPayments });
  } catch (error) {
    console.error('Error fetching payment history:', error);
    debugLog('Payment history error', { error });
    return res.status(500).json({ error: 'Failed to fetch payment history' });
  }
}