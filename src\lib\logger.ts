/**
 * Logs a debug message if IAC_DEBUG environment variable is set.
 * @param message The message to log.
 */
export function debugLog(message: string, data?: any) {
  if (process.env.IAC_DEBUG) {
    console.debug(`[DEBUG] ${message}`, data);
  }
}

/**
 * Logs a general message.
 * @param message The message to log.
 */
export function log(message: string, data?: any) {
  console.log(`[LOG] ${message}`, data);
}