# Instructions for Developers

## Automatic Organization Creation for New Users

### Overview
When a new user logs in, an individual organization is automatically created for them. This ensures that every user has their own organization by default.

### Implementation Details
1. **`createUserProfile` Method**:
   - Located in `src/lib/database.ts`.
   - Automatically creates an individual organization for each new user.
   - The organization ID is the same as the user's ID.
   - The organization is added to the `organizations` collection in Firestore.

2. **Standardized `ID` Handling**:
   - The `addData` method in `database.ts` supports both `ID` and `id` fields for document identification.
   - Validation ensures that the document ID is a non-empty string.

### Testing and Verification
1. **Verify Integration**:
   - Ensure the `createUserProfile` method is invoked correctly when a new user logs in.

2. **End-to-End Testing**:
   - Log in as a new user.
   - Confirm that an individual organization is created in the `organizations` collection in Firestore.

3. **Migration Script**:
   - Use the `migrate-users-to-organizations.ts` script to migrate existing users to organizations.
   - Enhanced dry run mode simulates errors for debugging.

### Pending Tasks
- Standardize `ID` usage across all models (`accounts`, `events`, `invites`, `users`, and `organization`).
- Test the entire flow to ensure seamless integration.

### Notes
- Refer to the `README.md` for project setup and development guidelines.
