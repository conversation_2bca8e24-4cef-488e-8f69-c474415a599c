import { EventFormData } from '../../../types';
import { formatEventDate } from '../../../utils/dateFormatter';

/**
 * Generate prompt for reminder message based on event message
 */
export function generateReminderMessagePrompt(eventMessage: string, eventData: EventFormData): string {
  const formattedDate = formatEventDate(new Date(eventData.eventDate));
  
  return `You are an event assistant helping to create a friendly reminder message for event guests.

Original Event Message:
"${eventMessage}"

Event Details:
- Event: ${eventData.eventName}
- Date: ${formattedDate}
- Time: ${eventData.start} - ${eventData.end}
- Location: ${eventData.location}
- Host: ${eventData.host}

Create a warm, friendly reminder message that:
1. References the original event message naturally
2. Reminds guests about the upcoming event
3. Includes key event details (date, time, location)
4. Maintains the same tone as the original message
5. Encourages attendance
6. Is personal and welcoming
7. Includes a subtle call to action to RSVP if they haven't already

Keep the message concise but engaging (2-4 sentences). Make it feel like a personal reminder from the host, not a formal notification.

Return only the reminder message without any additional formatting or explanations.`;
}
