import { useEffect, useState, useCallback } from 'react';
import { UserProfile } from '@/types';

interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface SortingState {
  sortBy: 'name' | 'email' | 'createdOn';
  sortOrder: 'asc' | 'desc';
}

export function useUsers() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [sorting, setSorting] = useState<SortingState>({
    sortBy: 'createdOn',
    sortOrder: 'desc'
  });

  const fetchUsers = useCallback(async (
    query: string = searchQuery,
    page: number = pagination.page,
    sortBy: string = sorting.sortBy,
    sortOrder: string = sorting.sortOrder
  ) => {
    setLoading(true);
    setError(null);
    try {
      const url = new URL('/api/admin/users', window.location.origin);
      url.searchParams.append('page', page.toString());
      url.searchParams.append('limit', pagination.limit.toString());
      url.searchParams.append('sortBy', sortBy);
      url.searchParams.append('sortOrder', sortOrder);

      if (query) {
        url.searchParams.append('search', query);
      }

      const response = await fetch(url.toString());

      if (!response.ok) {
        throw new Error(`Error fetching users: ${response.statusText}`);
      }

      const data = await response.json();
      setUsers(data.users);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch users'));
    } finally {
      setLoading(false);
    }
  }, [searchQuery, pagination.page, pagination.limit, sorting.sortBy, sorting.sortOrder]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    fetchUsers(query, 1); // Reset to first page on new search
  };

  const handlePageChange = (newPage: number) => {
    fetchUsers(searchQuery, newPage);
  };

  const handleSort = (sortBy: 'name' | 'email' | 'createdOn', sortOrder?: 'asc' | 'desc') => {
    const newSortOrder = sortOrder || (sorting.sortBy === sortBy && sorting.sortOrder === 'asc' ? 'desc' : 'asc');
    setSorting({ sortBy, sortOrder: newSortOrder });
    fetchUsers(searchQuery, 1, sortBy, newSortOrder); // Reset to first page on new sort
  };

  const refreshUsers = () => {
    fetchUsers(searchQuery, pagination.page);
  };

  const getUserById = (userId: string) => {
    return users.find(user => user.id === userId) || null;
  };

  return {
    users,
    loading,
    error,
    searchQuery,
    pagination,
    sorting,
    fetchUsers,
    handleSearch,
    handlePageChange,
    handleSort,
    refreshUsers,
    getUserById
  };
}