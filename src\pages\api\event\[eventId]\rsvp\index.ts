import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId } = req.query;
  if (!eventId) {
    return res.status(400).json({ error: 'Event ID is required' });
  }
  if (req.method === 'POST') {
    // Logic for handling RSVP
    res.status(200).json({ eventId, message: 'RSVP recorded successfully' });
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}