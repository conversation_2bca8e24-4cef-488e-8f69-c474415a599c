/**
 * ORGANIZATION-SCOPED RBAC UTILITIES
 * 
 * Utility functions for managing and checking permissions in a multi-organization context
 * using the Database class.
 */

import { Database } from '@/lib/database';
import { Organization } from '@/types';
import {
  OrganizationScopedRole,
  OrganizationWithRBAC,
  OrganizationPermissionContext,
  UserOrganizationRoleDocument,
  UserActiveOrganizationDocument,
  DATABASE_COLLECTIONS
} from './organization-types';
import { Role } from './index';
import { Permission, permissions } from './permissions';
import { rbacConfig } from './index';

/**
 * Get all organization roles for a user
 */
export async function getUserOrganizationRoles(userId: string): Promise<OrganizationScopedRole[]> {
  try {
    const db = Database.getInstance();

    // Use ListData to get user roles with filter
    const roleDocuments = await db.ListData<UserOrganizationRoleDocument>(
      DATABASE_COLLECTIONS.USER_ORGANIZATION_ROLES,
      { field: 'userId', operator: '==', value: userId }
    );

    // Convert documents to OrganizationScopedRole objects
    return roleDocuments
      .filter(doc => doc.isActive) // Only return active roles
      .map((doc, index) => ({
        id: `${doc.userId}_${doc.organizationId}_${doc.role}`, // Generate consistent ID
        userId: doc.userId,
        organizationId: doc.organizationId,
        role: doc.role as Role,
        assignedBy: doc.assignedBy,
        assignedAt: doc.assignedAt,
        expiresAt: doc.expiresAt,
        isActive: doc.isActive,
        context: {
          venueIds: doc.venueRestrictions,
          metadata: doc.permissionsMetadata
        }
      }));
  } catch (error) {
    console.error('Error fetching user organization roles:', error);
    return [];
  }
}

/**
 * Get organization details by ID
 */
export async function getOrganization(organizationId: string): Promise<Organization | null> {
  try {
    const db = Database.getInstance();
    return await db.getOrganizationById(organizationId);
  } catch (error) {
    console.error('Error fetching organization:', error);
    return null;
  }
}

/**
 * Get all organizations with RBAC info for a user
 */
export async function getUserOrganizationsWithRBAC(userId: string): Promise<OrganizationWithRBAC[]> {
  try {
    // Get explicit RBAC roles
    const userRoles = await getUserOrganizationRoles(userId);
    const organizationIds = [...new Set(userRoles.map(role => role.organizationId))];

    // Also get organizations where user is an owner
    const db = Database.getInstance();
    const orgsWhereOwner = await db.query('organizations')
      .where('members', 'array-contains', { userId, role: 'owner' })
      .get();
    
    // Add owner organization IDs to the list
    orgsWhereOwner.forEach(doc => {
      const orgId = doc.id;
      if (!organizationIds.includes(orgId)) {
        organizationIds.push(orgId);
      }
    });

    const organizations: OrganizationWithRBAC[] = [];

    for (const orgId of organizationIds) {
      const org = await getOrganization(orgId);
      if (!org) continue;

      const orgRoles = userRoles.filter(role => role.organizationId === orgId);
      const userRolesList = orgRoles.map(role => role.role);

      // Check if user is an owner in the organization
      const isOwner = org.members?.some(member => 
        member.userId === userId && member.role === 'owner'
      );

      // If user is owner of a partner organization, add partner:admin role
      if (isOwner && org.type === 'partner' && !userRolesList.includes('partner:admin')) {
        userRolesList.push('partner:admin');
      }

      // Get base RBAC config for organization type
      const baseRbacConfig = getRBACConfigForOrganization(org.type as 'partner' | 'admin');

      // Filter rolePermissions to only include roles the user has
      const userRolePermissions: Record<string, any> = {};
      userRolesList.forEach(role => {
        if (baseRbacConfig.rolePermissions[role]) {
          userRolePermissions[role] = baseRbacConfig.rolePermissions[role];
        }
      });

      organizations.push({
        id: org.id,
        name: org.name,
        type: org.type as 'partner' | 'admin',
        rbacConfig: {
          ...baseRbacConfig,
          rolePermissions: userRolePermissions
        },
        canAccess: isOwner || (orgRoles.length > 0 && orgRoles.some(role => role.isActive)),
        isActive: true // Assume organization is active if it exists
      });
    }

    return organizations;
  } catch (error) {
    console.error('Error fetching user organizations with RBAC:', error);
    return [];
  }
}

/**
 * Get user's currently active organization
 */
export async function getUserActiveOrganization(userId: string): Promise<string | null> {
  try {
    const db = Database.getInstance();
    const activeOrgDoc = await db.readData(DATABASE_COLLECTIONS.USER_ACTIVE_ORGANIZATION, userId);

    if (!activeOrgDoc) {
      return null;
    }

    return activeOrgDoc.organizationId;
  } catch (error) {
    console.error('Error fetching user active organization:', error);
    return null;
  }
}

/**
 * Set user's active organization
 */
export async function setUserActiveOrganization(userId: string, organizationId: string): Promise<void> {
  try {
    const db = Database.getInstance();
    const now = new Date().toISOString();

    const activeOrgDoc: UserActiveOrganizationDocument & { id: string } = {
      id: userId,
      userId,
      organizationId,
      updatedAt: now
    };

    await db.addData(DATABASE_COLLECTIONS.USER_ACTIVE_ORGANIZATION, activeOrgDoc);
  } catch (error) {
    console.error('Error setting user active organization:', error);
    throw error;
  }
}

/**
 * Check if user has specific permission in an organization
 */
export function hasOrganizationPermission(
  userRoles: OrganizationScopedRole[],
  resource: string,
  permission: Permission,
  context: OrganizationPermissionContext
): boolean {
  const { organizationId, venueId } = context;

  // Get roles for the specific organization
  const orgRoles = userRoles.filter(role =>
    role.organizationId === organizationId &&
    role.isActive &&
    (!role.expiresAt || new Date(role.expiresAt) > new Date())
  );

  if (orgRoles.length === 0) {
    return false;
  }

  // Check each role for the permission
  for (const roleAssignment of orgRoles) {
    const role = roleAssignment.role;

    // Check venue restrictions if specified
    if (venueId && roleAssignment.context?.venueIds) {
      if (!roleAssignment.context.venueIds.includes(venueId)) {
        continue; // Skip this role if venue access is restricted
      }
    }

    // Get permissions for this role
    const rolePermissions = getRolePermissions(role, resource);

    if (rolePermissions.includes(permission)) {
      return true;
    }
  }

  return false;
}

/**
 * Get roles for a user in a specific organization
 */
export function getUserRolesInOrganization(
  userRoles: OrganizationScopedRole[],
  organizationId: string
): OrganizationScopedRole[] {
  return userRoles.filter(role =>
    role.organizationId === organizationId &&
    role.isActive &&
    (!role.expiresAt || new Date(role.expiresAt) > new Date())
  );
}

/**
 * Get all permissions for a user in an organization grouped by resource
 */
export function getPermissionsInOrganization(
  userRoles: OrganizationScopedRole[],
  organizationId: string
): Record<string, Permission[]> {
  const orgRoles = getUserRolesInOrganization(userRoles, organizationId);
  const permissionMap: Record<string, Set<Permission>> = {};

  for (const roleAssignment of orgRoles) {
    const role = roleAssignment.role;

    // Get all resources this role has access to
    const allResources = Object.keys(permissions);

    for (const resource of allResources) {
      const rolePermissions = getRolePermissions(role, resource);

      if (rolePermissions.length > 0) {
        if (!permissionMap[resource]) {
          permissionMap[resource] = new Set();
        }

        rolePermissions.forEach(permission => {
          permissionMap[resource].add(permission);
        });
      }
    }
  }

  // Convert Sets to arrays
  const result: Record<string, Permission[]> = {};
  for (const [resource, permissionSet] of Object.entries(permissionMap)) {
    result[resource] = Array.from(permissionSet);
  }

  return result;
}

/**
 * Get permissions for a specific role and resource
 */
function getRolePermissions(role: Role, resource: string): Permission[] {
  // Use the universal permission checker from the main RBAC system
  const allPermissions = Object.values(permissions);
  const rolePermissions: Permission[] = [];

  for (const permission of allPermissions) {
    // Use the main RBAC hasPermission function
    if (role.startsWith('partner:')) {
      // For partner roles, we need to import the partner permission checker
      const hasPartnerPerm = require('./partner').hasPartnerPermission;
      if (hasPartnerPerm(role, resource, permission)) {
        rolePermissions.push(permission);
      }
    } else if (role.startsWith('admin:')) {
      // For admin roles, we need to import the admin permission checker
      const hasAdminPerm = require('./admin').hasAdminPermission;
      if (hasAdminPerm(role, resource, permission)) {
        rolePermissions.push(permission);
      }
    }
  }

  return rolePermissions;
}

/**
 * Get RBAC configuration for organization type
 */
function getRBACConfigForOrganization(orgType: 'partner' | 'admin'): any {
  // Return relevant RBAC config based on organization type
  if (orgType === 'partner') {
    return rbacConfig.partner;
  } else if (orgType === 'admin') {
    return rbacConfig.admin;
  }

  return null;
}

/**
 * Assign a role to a user in an organization
 */
export async function assignOrganizationRole(
  userId: string,
  organizationId: string,
  role: Role,
  assignedBy: string,
  options: {
    venueRestrictions?: string[];
    expiresAt?: Date;
    metadata?: Record<string, any>;
  } = {}
): Promise<void> {
  try {
    const db = Database.getInstance();
    const now = new Date().toISOString();
    const docId = `${userId}_${organizationId}_${role}`;

    const roleDoc: UserOrganizationRoleDocument & { id: string } = {
      id: docId,
      userId,
      organizationId,
      role,
      assignedBy,
      assignedAt: now,
      expiresAt: options.expiresAt ? options.expiresAt.toISOString() : undefined,
      isActive: true,
      venueRestrictions: options.venueRestrictions,
      permissionsMetadata: options.metadata
    };

    await db.addData(DATABASE_COLLECTIONS.USER_ORGANIZATION_ROLES, roleDoc);
  } catch (error) {
    console.error('Error assigning organization role:', error);
    throw error;
  }
}

/**
 * Revoke a role from a user in an organization
 */
export async function revokeOrganizationRole(
  userId: string,
  organizationId: string,
  role: Role
): Promise<void> {
  try {
    const db = Database.getInstance();
    const docId = `${userId}_${organizationId}_${role}`;
    const now = new Date().toISOString();

    await db.updateData(DATABASE_COLLECTIONS.USER_ORGANIZATION_ROLES, docId, {
      isActive: false,
      revokedAt: now
    });
  } catch (error) {
    console.error('Error revoking organization role:', error);
    throw error;
  }
}

/**
 * Check if a user can manage another user in an organization
 */
export function canManageUserInOrganization(
  adminRole: OrganizationScopedRole,
  targetUserId: string,
  organizationId: string
): boolean {
  // Only users with admin or manager roles can manage other users
  const managementRoles = ['admin:super', 'admin:organization', 'partner:manager'];

  return adminRole.organizationId === organizationId &&
    adminRole.isActive &&
    managementRoles.includes(adminRole.role) &&
    (!adminRole.expiresAt || new Date(adminRole.expiresAt) > new Date());
}
