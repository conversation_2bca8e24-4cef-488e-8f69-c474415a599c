const path = require('path');

module.exports = ({ config }) => {
  // Make sure webpack can resolve the css-loader from node_modules
  config.resolveLoader = {
    ...config.resolveLoader,
    modules: ['node_modules'],
  };
  
  // Replace the CSS rule with a properly configured one
  config.module.rules = config.module.rules.filter(
    rule => !(rule.test && rule.test.toString().includes('.css'))
  );
  
  // Add a new rule for CSS files
  config.module.rules.push({
    test: /\.css$/,
    use: [
      'style-loader',
      {
        loader: 'css-loader',
        options: {
          importLoaders: 1,
        },
      },
      {
        loader: 'postcss-loader',
        options: {
          postcssOptions: {
            config: path.resolve(__dirname, './postcss.config.js'),
          },
        },
      },
    ],
  });

  // Add path aliases
  config.resolve.alias = {
    ...config.resolve.alias,
    '@': path.resolve(__dirname, '../src'),
  };

  return config;
};