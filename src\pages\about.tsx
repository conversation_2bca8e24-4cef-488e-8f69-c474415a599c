'use client'

import { <PERSON><PERSON> } from "@/components/Header"
import { <PERSON><PERSON> } from "@/components/Footer"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { useSession } from "next-auth/react"
import { useRouter } from "next/router"

export default function AboutUsPage() {
  const router = useRouter()
  const { data: session } = useSession()

  return (
    <div className="flex flex-col min-h-screen">
      <Header
        buttons={session ? [
          {
            label: "Manage Events",
            onClick: () => router.push('/events'),
            variant: "outline"
          }
        ] : []}
      />      
      
      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="bg-white overflow-hidden">
          <div className="flex flex-col items-center justify-center text-center px-4 sm:px-8 md:px-16 lg:px-32 xl:px-[304px] py-16 sm:py-24 md:py-32 lg:py-[140px] gap-6 md:gap-8">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-forground leading-normal max-w-6xl">
              We&apos;re on a mission to make event planning joyful — not stressful.
            </h1>
            <p className="text-base sm:text-lg md:text-xl font-medium text-muted-foreground leading-normal max-w-[802px]">
              I am coming helps hosts manage invitation and RSVPs with ease, so they can focus 
              on what matters <span className="font-bold">—</span> celebrating with their people.
            </p>
          </div>
        </div>          
        {/* Story Section */}
        <div className="bg-white py-16 md:py-20">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
            <div className="flex flex-col lg:flex-row items-center gap-8 md:gap-12 lg:gap-16">
              <div className="w-full lg:w-1/2 flex justify-center">
                <div className="bg-gray-200 rounded-md h-[280px] sm:h-[320px] md:h-[380px] w-full max-w-[500px] flex items-center justify-center flex-shrink-0">
                  <Image 
                    src="/aboutUsImg1.png" 
                    alt="Event planning illustration" 
                    width={500} 
                    height={380}
                    className="object-cover rounded-md w-full h-full"
                  />
                </div>
              </div>
              <div className="w-full lg:w-1/2 flex flex-col gap-4 md:gap-6 items-center lg:items-start text-center lg:text-left">
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-forground leading-normal">
                  Why We built I Am Coming
                </h2>
                <div className="text-base sm:text-lg font-normal text-muted-foreground leading-normal">
                  <p className="mb-4">
                    I am Coming began as a personal solution to a common problem—managing invites and RSVPs for my child&apos;s events 
                    was chaotic and exhausting. Frustrated by endless messages and tracking manually, I built a quick fix using Google Forms 
                    and Sheets. It worked—but only to a point. That experience sparked the idea for something better.
                  </p>
                  <p>
                    In March 2025, I am Coming launched its first beta version: a simple, focused platform to make event planning smoother. 
                    Today, we&apos;re continuously improving based on real feedback, driven by one goal—making invitations effortless and joyful.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Mission Section */}
        <div className="bg-white flex flex-col items-center py-16 md:py-20 lg:py-[100px] px-4">
          <div className="flex flex-col gap-12 md:gap-16 lg:gap-20 items-center justify-start w-full max-w-7xl">
            <div className="flex flex-col gap-6 md:gap-8 items-center justify-start text-center">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-semibold text-forground tracking-tight">Our Mission</h2>
              <div className="text-base sm:text-lg font-normal text-muted-foreground leading-normal max-w-[968px]">
                <p className="mb-4">
                  At I am Coming (IaC), our mission is to simplify event management by providing intuitive RSVP solutions that 
                  connect event hosts with their guests seamlessly. We believe that planning events should be joyful, not stressful.
                </p>
                <p>
                  We&apos;re dedicated to creating technology that enhances human connections, making it easier for people to come 
                  together and celebrate life&apos;s important moments.
                </p>
              </div>
            </div>

            {/* Values Section */}
            <div className="flex flex-col gap-6 md:gap-8 items-center justify-start w-full">
              <div className="flex flex-col gap-1 items-center justify-start w-full">
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-semibold text-forground tracking-tight text-center">Our Values</h2>
              </div>              
              <div className="flex flex-col gap-4 items-start justify-start w-full">
                <div className="flex flex-col md:flex-row items-center px-4 py-0 w-full">
                  <div className="flex-1 border-b md:border-b border-slate-200 w-full md:w-auto">
                    <div className="flex flex-col items-center py-6 px-4 md:px-6 gap-2">
                      <div className="w-10 h-10 flex items-center justify-center">
                        <img src="privacy.svg" alt="" />
                      </div>
                      <h3 className="text-base font-semibold text-forground text-center leading-7">Privacy</h3>
                      <p className="text-sm sm:text-base font-normal text-muted-foreground text-center leading-6 max-w-[212px]">
                        We respect your data and the data of your guests, ensuring it&apos;s always secure.
                      </p>
                    </div>
                  </div>
                  <div className="flex-1 border-b md:border-b border-slate-200 w-full md:w-auto">
                    <div className="flex flex-col items-center py-6 px-4 md:px-6 gap-2">
                      <div className="w-10 h-10 flex items-center justify-center">
                        <img src="connection.svg" alt="" />
                      </div>
                      <h3 className="text-base font-semibold text-forground text-center leading-7">Connection</h3>
                      <p className="text-sm sm:text-base font-normal text-muted-foreground text-center leading-6 max-w-[230px]">
                        We build tools that strengthen relationships and create meaningful experiences.
                      </p>
                    </div>
                  </div>
                  <div className="flex-1 border-b md:border-b border-slate-200 w-full md:w-auto">
                    <div className="flex flex-col items-center py-6 px-4 md:px-6 gap-2">
                      <div className="w-10 h-10 flex items-center justify-center">
                        <img src="simplicity.svg" alt="" />
                      </div>
                      <h3 className="text-base font-semibold text-forground text-center leading-7">Simplicity</h3>
                      <p className="text-sm sm:text-base font-normal text-muted-foreground text-center leading-6 max-w-[258px]">
                        We believe in straightforward solutions that don&apos;t require technical expertise to use.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>        
        {/* Founder Section */}        
        <div className="bg-white flex flex-col items-center px-4">
          <div className="flex flex-col gap-12 md:gap-16 lg:gap-20 items-center justify-center pb-16 md:pb-20 lg:pb-[100px] pt-0 px-4 w-full max-w-7xl">
            <div className="flex flex-col gap-8 md:gap-11 items-center justify-center">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-semibold text-forground tracking-tight text-center">Our Founder</h2>
              <div className="bg-white rounded-lg shadow-sm w-full">
                <div className="flex flex-col md:flex-row gap-6 md:gap-8 items-start md:items-start justify-start p-4 md:p-6">
                  <div className="w-24 h-24 sm:w-32 sm:h-32 rounded-full overflow-hidden flex-shrink-0">
                    <Image 
                      src="/balwant.jpeg" 
                      alt="Balwant Singh" 
                      width={128} 
                      height={128}
                      className="object-cover w-full h-full"
                    />
                  </div>
                  <div className="flex flex-col items-start md:items-start justify-start text-left md:text-left">
                    <h3 className="text-lg sm:text-xl font-medium text-stone-950 leading-7">Balwant Singh</h3>
                    <p className="text-sm sm:text-base font-normal text-muted-foreground leading-6">Founder</p>
                    <div className="pt-3">
                      <p className="text-sm sm:text-base font-normal text-foreground leading-6 mb-4">
                        Balwant founded I am Coming after experiencing firsthand the challenges of managing RSVPs for children&apos;s events. With a background in technology and a passion for solving real-world problems, he developed IaC to make event organization accessible and stress-free for everyone.
                      </p>
                    </div>
                    <div className="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4 pt-4 w-full justify-center md:justify-start">
                      <a 
                        href="https://www.linkedin.com/in/balwantmatharu/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center justify-center text-forground hover:text-foreground transition-colors"
                      >
                        <Image 
                          src="/linkedin.png" 
                          alt="LinkedIn" 
                          width={20} 
                          height={20} 
                          className="h-5 w-5 mr-2" 
                        />
                        <span className="text-sm sm:text-base font-normal leading-6">Connect with Founder</span>
                      </a>
                      <a 
                        href="https://www.linkedin.com/company/iamcoming/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center justify-center text-forground hover:text-foreground transition-colors"
                      >
                        <Image 
                          src="/linkedin.png" 
                          alt="LinkedIn" 
                          width={20} 
                          height={20} 
                          className="h-5 w-5 mr-2" 
                        />
                        <span className="text-sm sm:text-base font-normal leading-6">Follow IaC on LinkedIn</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>        </div>        
      </main>
      {/* CTA Section */}
      <section className="py-20 bg-[#F43F5E] text-white">
        <div className="container mx-auto px-4 text-center max-w-4xl">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Join us in simplifying event planning.</h2>
          <Button
            size="lg"
            className="py-6 px-10 text-lg bg-white text-[#F43F5E] cursor-pointer hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all duration-300"
            onClick={() => router.push('/event/new/edit')}
          >
            Get Started now
          </Button>
        </div>
      </section>
      
      <Footer type="marketing" />
    </div>
  )
}