import { NextApiRequest, NextApiResponse } from 'next'
import { getServerSession } from 'next-auth'
import { authConfig } from '@/auth'
import Stripe from 'stripe'
import { debugLog } from '@/lib/logger'
import { getBaseUrl } from "@/lib/utils"
import { calculateUpgradeCost } from '@/lib/plans'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia'
})

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  debugLog('Create Checkout Session Request', {
    method: req.method,
    body: req.body
  });

  if (req.method !== 'POST') {
    debugLog('Invalid method', { method: req.method });
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const session = await getServerSession(req, res, authConfig)
    debugLog('Auth Session', { 
      hasSession: !!session,
      userId: session?.user?.id 
    });

    if (!session?.user) {
      debugLog('Unauthorized request - no session');
      return res.status(401).json({ error: 'Unauthorized' })
    }

    const { eventId, plan, price, from_plan } = req.body
    debugLog('Request Parameters', { eventId, plan, price, from_plan });

    if (!eventId || !plan) {
      debugLog('Missing required fields', { eventId, plan });
      return res.status(400).json({ error: 'Missing required fields' })
    }

    // If upgrading from an existing plan, calculate the price difference
    const finalPrice = from_plan ? calculateUpgradeCost(from_plan, plan) : price;
    
    debugLog('Creating Stripe Checkout Session', {
      eventId,
      plan,
      originalPrice: price,
      finalPrice,
      from_plan,
      userId: session.user.id
    });
    
    // Skip payment if price is 0 (free plan or same tier)
    if (finalPrice === 0) {
      // Update event status directly instead of going through Stripe
      // This would normally involve your database update logic
      // For now, we'll just return success with a redirect
      return res.status(200).json({ 
        url: `${getBaseUrl(req)}/event/${eventId}?success=true&skipPayment=true`,
        skipPayment: true
      });
    }

    const checkoutSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'aud',
            product_data: {
              name: `I am Coming - ${eventId.toUpperCase()} ${plan.toUpperCase()}`,
              description: from_plan ? `Upgrade from ${from_plan.toUpperCase()}` : undefined,
            },
            unit_amount: Math.round(finalPrice * 100), // Convert to cents
            tax_behavior: 'inclusive'
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${getBaseUrl(req)}/event/${eventId}?success=true`,
      cancel_url: `${getBaseUrl(req)}/event/${eventId}?canceled=true`,
      metadata: {
        eventId,
        plan,
        from_plan: from_plan || '',
        userId: session.user.id,
        isUpgrade: from_plan ? 'true' : 'false',
      },
      invoice_creation: {
        enabled: true,
      },
    })

    debugLog('Checkout Session Created', {
      sessionId: checkoutSession.id,
      url: checkoutSession.url,
      status: checkoutSession.status
    });

    return res.status(200).json({ url: checkoutSession.url })
  } catch (error) {
    debugLog('Stripe Checkout Error', { error });
    console.error('Stripe checkout error:', error)
    return res.status(500).json({ error: 'Failed to create checkout session' })
  }
}