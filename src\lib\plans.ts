export interface Plan {
  name: string;
  price: string;
  description: string;
  features: Array<string>;
  comingSoonFeatures?: Array<string>;
  priceId: 'free' | 'host_plus' | 'host_pro' | 'the_gatsby';
  ribbon?: {
    text: string;
    backgroundColor: string;
    textColor: string;
  };
  includesText?: string;
}

export const plans: Plan[] = [
  {
    name: "Host",
    price: "A$0",
    description: "Perfect for small events",
    features: [
      "Up to 10 invitations",
      "Basic QR code label printing only",
      "RSVP tracking",
      "No credit card needed"
    ],
    priceId: "free",
    includesText: "Host Includes:"
  },
  {
    name: "Host+",
    price: "A$10",
    description: "For growing events",
    features: [
      "Up to 25 invitations",
      "Advanced invite printing with guest name and custom QR code",
      "Event Reminders",
      "Push notifications for hosts"
    ],
    comingSoonFeatures: [
      "Bulk invite upload",
      "Guest list export",
      "Create invites from phone contacts",
      "Email invitations with guest name",
      "SMS/WhatsApp invitations with guest name (3 per invite)"
    ],
    priceId: "host_plus",
    includesText: "Everything in Host, plus:"
  },
  {
    name: "Host Pro",
    price: "A$35",
    description: "For professional events",
    features: [
      "Up to 50 invitations",
      "Push notifications for guests and hosts",
      "Custom invite and QR Label printing",
      "Bulk invite upload"
    ],
    comingSoonFeatures: [
      "Guest list export",
      "Digital Wallet Passes",
      "Create invites from phone contacts",
      "Invite Verification (Scan QR code/Pass to verify guest)",
      "Email invitations with guest name",
      "SMS/WhatsApp invitations with guest name (3 per invite)",
      "Import from phone contacts"
    ],
    priceId: "host_pro",
    includesText: "Everything in Host+, plus:"
  },
  {
    name: "The Gatsby",
    price: "Coming Soon",
    description: "For luxury events with no limits",
    features: [
      "Unlimited invitations",
      "Priority Support",
      "SMS/WhatsApp invitations with guest name (unlimited)",
      "Advanced RSVP tracking and analytics",
      "Guest list export with custom fields",
      "VIP guest identification",
      "Advanced invite verification",
      "Event management dashboard"
    ],
    comingSoonFeatures: [],
    priceId: "the_gatsby",
    ribbon: {
      text: "Premium",
      backgroundColor: "#FF9900",
      textColor: "white"
    },
    includesText: "Everything in Host Pro, plus:"
  }
];

// Define plan pricing amounts for calculations
export const planPriceAmounts = {
  free: 0,
  host_plus: 10,
  host_pro: 35,
  the_gatsby: 100
};

// Calculate plan upgrade cost (returns the difference amount)
export function calculateUpgradeCost(fromPlan: string, toPlan: string): number {
  const fromPrice = planPriceAmounts[fromPlan as keyof typeof planPriceAmounts] || 0;
  const toPrice = planPriceAmounts[toPlan as keyof typeof planPriceAmounts] || 0;
  
  // If downgrading, return 0 (no charge for downgrades)
  if (toPrice <= fromPrice) {
    return 0;
  }
  
  // If upgrading, return the difference
  return toPrice - fromPrice;
}