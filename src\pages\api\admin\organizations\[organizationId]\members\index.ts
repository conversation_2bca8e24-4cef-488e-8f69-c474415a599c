import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';
import { UserProfile, Organization } from '@/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const db = Database.getInstance();

    // Get organization ID from the query parameters
    const { organizationId } = req.query;

    if (!organizationId || Array.isArray(organizationId)) {
      return res.status(400).json({ error: 'Invalid organization ID' });
    }

    // Get the organization
    const organization = await db.getOrganizationById(organizationId);

    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    // Handle GET request - fetch organization members
    if (req.method === 'GET') {
      // Get the member user IDs from the organization
      const memberIds = organization.members?.map(member => member.userId) || [];

      // If no members in organization, return empty array
      if (memberIds.length === 0) {
        return res.status(200).json({
          success: true,
          members: []
        });
      }

      // Fetch the user profiles for each member
      const members: UserProfile[] = [];

      for (const memberId of memberIds) {
        const user = await db.getUserById(memberId);

        if (user) {
          members.push(user);
        } else {
          // Create a placeholder user for members that don't exist in the users collection
          // This ensures we show something rather than nothing
          members.push({
            id: memberId,
            email: 'Unknown user',
            name: 'Unknown user',
            isProfileComplete: false,
            isWelcomeEmailSent: false
          } as UserProfile);
        }
      }

      // If we couldn't find any members but the organization has members,
      // ensure we return at least placeholder members
      if (members.length === 0 && organization.members && organization.members.length > 0) {
        for (const member of organization.members) {
          members.push({
            id: member.userId,
            email: 'Unknown user',
            name: 'Unknown user',
            isProfileComplete: false,
            isWelcomeEmailSent: false
          } as UserProfile);
        }
      }

      // Return members list
      return res.status(200).json({
        success: true,
        members
      });
    }

    // Handle POST request - add a new member to the organization
    if (req.method === 'POST') {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ error: 'Email is required' });
      }

      // Find the user by email
      const user = await db.getUserByEmail(email);

      if (!user) {
        return res.status(404).json({ error: 'User not found with this email' });
      }

      // Check if the user is already a member of the organization
      const isMember = organization.members?.some(member => member.userId === user.id);

      if (isMember) {
        return res.status(400).json({ error: 'User is already a member of this organization' });
      }

      // Add the user to the organization
      const updatedMembers = [
        ...(organization.members || []),
        {
          userId: user.id,
          role: 'member' as 'owner' | 'member'
        }
      ];

      // Update the organization in the database
      await db.updateOrganization(organizationId, {
        members: updatedMembers,
        lastUpdatedOn: new Date().toISOString()
      });

      // Get the updated organization
      const updatedOrganization = await db.getOrganizationById(organizationId);

      // Return success response
      return res.status(200).json({
        success: true,
        message: `${user.name || user.email} has been added to the organization`,
        organization: updatedOrganization
      });
    }

    // If not GET or POST, return method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error handling organization members request:', error);
    return res.status(500).json({ error: 'Failed to process organization members request' });
  }
}
