'use client'

import { useState } from "react"
import { PartnerLayout } from "@/components/layouts/PartnerLayout"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/router"
import { Calendar, Clock, MapPin, User, Mail, Plus, Info, Users } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useSession } from "next-auth/react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Pop<PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"

// Mock data for venues - in a real app, this would come from an API/database
const mockVenues = [
  {
    id: "venue-1",
    name: "Grand Ballroom",
    address: "123 Main St, Melbourne",
    locations: [
      { id: "loc-1", name: "Main Hall" },
      { id: "loc-2", name: "Terrace" }
    ]
  },
  {
    id: "venue-2",
    name: "Garden Pavilion",
    address: "456 Park Ave, Sydney",
    locations: [
      { id: "loc-3", name: "Garden Area" },
      { id: "loc-4", name: "Covered Pavilion" }
    ]
  }
];

// Form schema
const formSchema = z.object({
  eventName: z.string().min(2, { message: "Event name must be at least 2 characters." }),
  eventDate: z.date({ required_error: "Event date is required." }),
  start: z.string().min(1, { message: "Start time is required." }),
  end: z.string().min(1, { message: "End time is required." }),
  venueId: z.string().min(1, { message: "Venue is required." }),
  locationId: z.string().min(1, { message: "Location is required." }),
  hostName: z.string().min(2, { message: "Host name is required." }),
  hostEmail: z.string().email({ message: "Please enter a valid email address." }),
  message: z.string().optional(),
  maxInvites: z.string().min(1, { message: "Maximum invites is required." }),
  pricePerInvite: z.string().optional(),
});

export default function CreatePartnerEvent() {
  const router = useRouter()
  const { data: session } = useSession()
  const [selectedVenue, setSelectedVenue] = useState<any>(null)
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      eventName: "",
      start: "",
      end: "",
      venueId: "",
      locationId: "",
      hostName: "",
      hostEmail: "",
      message: "",
      maxInvites: "50",
      pricePerInvite: "0",
    },
  });
  
  const onSubmit = (values: z.infer<typeof formSchema>) => {
    console.log(values);
    // In a real app, this would send the data to the server
    // and create the event for the host
    
    // For now, just redirect back to the partner dashboard
    router.push('/partner');
  };
  
  // Update available locations when venue changes
  const handleVenueChange = (venueId: string) => {
    form.setValue("locationId", "");
    const venue = mockVenues.find(v => v.id === venueId);
    setSelectedVenue(venue);
  };

  return (
    <PartnerLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Create Event for Customer</h1>
          <p className="text-muted-foreground">Set up an event for your customer to manage</p>
        </div>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Event Details</CardTitle>
                <CardDescription>
                  Enter the basic information about the event
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="eventName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Event Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter event name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="eventDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Event Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date(new Date().setHours(0, 0, 0, 0))
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="start"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Start Time</FormLabel>
                          <FormControl>
                            <Input type="time" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="end"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>End Time</FormLabel>
                          <FormControl>
                            <Input type="time" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="venueId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Venue</FormLabel>
                        <Select 
                          onValueChange={(value) => {
                            field.onChange(value);
                            handleVenueChange(value);
                          }} 
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a venue" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {mockVenues.map((venue) => (
                              <SelectItem key={venue.id} value={venue.id}>
                                {venue.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="locationId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location/Area</FormLabel>
                        <Select 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                          disabled={!selectedVenue}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={selectedVenue ? "Select a location" : "Select a venue first"} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {selectedVenue?.locations.map((location: any) => (
                              <SelectItem key={location.id} value={location.id}>
                                {location.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Event Description (Optional)</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter a description or message for the event" 
                          className="min-h-[100px]" 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Host Information</CardTitle>
                <CardDescription>
                  Enter the details of the customer who will manage this event
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="hostName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Host Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter host name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="hostEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Host Email</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Enter host email" {...field} />
                        </FormControl>
                        <FormDescription>
                          An invitation will be sent to this email address.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Event Settings</CardTitle>
                <CardDescription>
                  Configure additional settings for this event
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="maxInvites"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Invites</FormLabel>
                        <FormControl>
                          <Input type="number" min="1" {...field} />
                        </FormControl>
                        <FormDescription>
                          The maximum number of invites the host can create.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="pricePerInvite"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price Per Additional Invite ($)</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" step="0.01" {...field} />
                        </FormControl>
                        <FormDescription>
                          Cost for each invite beyond the maximum (0 for no additional cost).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => router.push('/partner')}>
                  Cancel
                </Button>
                <Button type="submit" variant="primary-button">
                  Create Event
                </Button>
              </CardFooter>
            </Card>
          </form>
        </Form>
      </div>
    </PartnerLayout>
  )
}
