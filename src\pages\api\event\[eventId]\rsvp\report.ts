import { NextApiRequest, NextApiResponse } from 'next';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { EventInvite, RSVPReport } from '@/types';
import { log } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { eventId } = req.query;

    if (!eventId || typeof eventId !== 'string') {
      return res.status(400).json({ message: 'Invalid event ID' });
    }

    log('Fetching RSVP report for event: ' + eventId);
    const db = Database.getInstance();

    const rsvpReport: RSVPReport = {
      invites: {
        total: 0,
        accepted: 0,
        declined: 0,
      },
      adults: {
        invited: 0,
        accepted: 0,
        declined: 0,
      },
      children: {
        invited: 0,
        accepted: 0,
        declined: 0,
      },
      total: {
        invited: 0,
        accepted: 0,
        declined: 0,
      }
    };

    const invites = await db.ListData<EventInvite>('invites', {
      field: 'eventId',
      operator: '==',
      value: eventId
    });

    invites.forEach((invite) => {
      rsvpReport.invites.total += 1;
      rsvpReport.invites.accepted += invite.status === 'accepted' ? 1 : 0;
      rsvpReport.invites.declined += invite.status === 'declined' ? 1 : 0;

      rsvpReport.adults.invited += invite.adults;
      rsvpReport.children.invited += invite.children;
      rsvpReport.total.invited += invite.adults + invite.children;

      if (invite.status === 'accepted') {
        rsvpReport.adults.accepted += invite.response?.adults || 0;
        rsvpReport.children.accepted += invite.response?.children || 0;
        rsvpReport.total.accepted += (invite.response?.adults || 0) + (invite.response?.children || 0);
      } else if (invite.status === 'declined') {
        rsvpReport.adults.declined += invite.adults;
        rsvpReport.children.declined += invite.children;
        rsvpReport.total.declined += invite.adults + invite.children;
      }
    });

    log('RSVP report generated successfully');
    return res.status(200).json(rsvpReport);
  } catch (error) {
    console.error('Error generating RSVP report:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}