import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const db = Database.getInstance();

    // Get organization ID and member ID from the query parameters
    const { organizationId, memberId } = req.query;

    if (!organizationId || Array.isArray(organizationId) || !memberId || Array.isArray(memberId)) {
      return res.status(400).json({ error: 'Invalid organization ID or member ID' });
    }

    // Get the organization
    const organization = await db.getOrganizationById(organizationId);

    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    // Handle DELETE request - remove a member from the organization
    if (req.method === 'DELETE') {
      // Check if the member exists in the organization
      const memberIndex = organization.members?.findIndex(member => member.userId === memberId);

      if (memberIndex === undefined || memberIndex === -1) {
        return res.status(404).json({ error: 'Member not found in this organization' });
      }

      // Remove the member from the organization
      const updatedMembers = [...(organization.members || [])];
      updatedMembers.splice(memberIndex, 1);

      // Update the organization in the database
      await db.updateOrganization(organizationId, {
        members: updatedMembers,
        lastUpdatedOn: new Date().toISOString()
      });

      // Get the updated organization
      const updatedOrganization = await db.getOrganizationById(organizationId);

      // Return success response
      return res.status(200).json({
        success: true,
        message: 'Member has been removed from the organization',
        organization: updatedOrganization
      });
    }

    // If not DELETE, return method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error handling organization member request:', error);
    return res.status(500).json({ error: 'Failed to process organization member request' });
  }
}
