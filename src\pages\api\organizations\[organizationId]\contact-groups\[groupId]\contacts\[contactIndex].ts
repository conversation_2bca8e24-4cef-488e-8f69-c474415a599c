import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { organizationId, groupId, contactIndex } = req.query;

  if (!organizationId || !groupId || contactIndex === undefined) {
    return res.status(400).json({ error: 'Missing required parameters' });
  }

  // Check user session
  const session = await getServerSession(req, res, authConfig);
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  const db = Database.getInstance();

  try {
    // Check organization membership
    const organization = await db.getOrganizationById(organizationId as string);
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const isMember = organization.members?.some(member => member.userId === session.user.id);
    if (!isMember) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get the contact group
    const { getSavedContactGroups, updateSavedContactGroup } = await import('@/lib/saved-contact-groups');
    const contactGroups = await getSavedContactGroups(organizationId as string);
    const group = contactGroups.find(g => g.id === groupId);

    if (!group) {
      return res.status(404).json({ error: 'Contact group not found' });
    }

    const contactIndexNum = parseInt(contactIndex as string);
    if (isNaN(contactIndexNum) || contactIndexNum < 0 || contactIndexNum >= group.contacts.length) {
      return res.status(400).json({ error: 'Invalid contact index' });
    }

    if (req.method === 'DELETE') {
      // Remove contact from group
      const updatedContacts = [...group.contacts];
      const removedContact = updatedContacts.splice(contactIndexNum, 1)[0];

      // Update the group with the modified contacts array (hard delete of contact)
      await updateSavedContactGroup(groupId as string, {
        contacts: updatedContacts
      });

      return res.status(200).json({
        success: true,
        message: 'Contact removed from group successfully',
        removedContact,
        remainingContacts: updatedContacts.length
      });

    } else if (req.method === 'PUT') {
      // Update contact in group
      const { name, email, phone } = req.body;

      if (!name && !email && !phone) {
        return res.status(400).json({ error: 'At least one contact field (name, email, or phone) is required' });
      }

      // Update the contact
      const updatedContacts = [...group.contacts];
      updatedContacts[contactIndexNum] = {
        name: name || '',
        email: email || '',
        phone: phone || ''
      };

      // Update the group
      await updateSavedContactGroup(groupId as string, {
        contacts: updatedContacts,
        updatedAt: new Date().toISOString()
      });

      return res.status(200).json({
        success: true,
        message: 'Contact updated successfully',
        updatedContact: updatedContacts[contactIndexNum]
      });

    } else {
      res.setHeader('Allow', ['DELETE', 'PUT']);
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Error managing contact:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
