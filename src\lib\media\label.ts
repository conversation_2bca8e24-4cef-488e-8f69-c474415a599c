import { DEFAULT_LABEL_DIMENSIONS, DEFAULT_LABEL_PRINT_OPTIONS, DEFAULT_LOGO_PATH, GetLabelDimensions, GetLabelPrintOptions } from "@/constants/defaults";
import { Event, ImageBuffer, LabelPrintingOptions } from "@/types";
import sharp from "sharp";
import QRCode from "qrcode";
import path from "path";
import fs from "fs";
import { mmToPixels } from "@/lib/utils";

/**
 * `wrapText` function wraps and truncates text to fit within a specified width and number of lines.
 * It uses a rough estimate of character width based on the font size to determine how many characters can fit in a line.
 * If the text exceeds the maximum width, it will be split into multiple lines, and if it exceeds the maximum number of lines,
 * it will be truncated with an ellipsis.
 * 
 * @param text `string` - The text to be wrapped and truncated.
 * @param maxWidth `number` - The maximum width in pixels that the text can occupy.
 * @param fontSize `number` - The font size in pixels.
 * @param maxLines `number` - The maximum number of lines allowed. Default is 2.
 * @returns `string[]` - An array of strings representing the wrapped and truncated text.
 */
function wrapText(text: string, maxWidth: number, fontSize: number, maxLines: number = 2): string[] {
  // Approximate character width based on font size (rough estimate)
  // For a typical font, average character width is about 0.6 times the font size
  const avgCharWidth = fontSize * 0.6;

  // Calculate approximately how many characters can fit in the max width
  const maxCharsPerLine = Math.floor(maxWidth / avgCharWidth);

  // If the text is short enough, return it as a single line
  if (text.length <= maxCharsPerLine) {
    return [text];
  }

  // Split text into words
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';

  // Build lines word by word
  for (const word of words) {
    // If adding this word would exceed the max width
    if ((currentLine.length + word.length + 1) > maxCharsPerLine) {
      // If we already have a current line, add it to lines
      if (currentLine.length > 0) {
        lines.push(currentLine);
        currentLine = '';
      }

      // If we've reached max lines, truncate and stop
      if (lines.length >= maxLines) {
        // Add ellipsis to show truncation
        const lastLine = lines[maxLines - 1];
        lines[maxLines - 1] = lastLine.substring(0, maxCharsPerLine - 3) + '...';
        break;
      }

      // If the word itself is too long for a line, truncate it
      if (word.length > maxCharsPerLine) {
        lines.push(word.substring(0, maxCharsPerLine - 3) + '...');
        if (lines.length >= maxLines) break;
        currentLine = '';
      } else {
        currentLine = word;
      }
    } else {
      // Add word to the current line
      currentLine = currentLine.length === 0 ? word : currentLine + ' ' + word;
    }
  }

  // Add the last line if there is one and we haven't reached max lines
  if (currentLine.length > 0 && lines.length < maxLines) {
    lines.push(currentLine);
  }

  // If we still have only one line but it's too long, truncate it
  if (lines.length === 1 && lines[0].length > maxCharsPerLine) {
    lines[0] = lines[0].substring(0, maxCharsPerLine - 3) + '...';
  }

  return lines;
}

/**
 * `RenderLabel` function generates a label image with a QR code and text.
 * It uses the sharp library to create the image and QRCode library to generate the QR code.
 * 
 * @param name `string` - The name to be displayed on the label.
 * @param qrContent `string` - The content to be encoded in the QR code.
 * @param options `LabelPrintingOptions` - Options for label printing including orientation, theme, and branding.
 * @returns `Promise<{ fileContents: ImageBuffer; mimeType: string }>` - A promise that resolves to an object containing the image buffer and MIME type.
 */
export async function RenderLabel(
  name: string,
  qrContent: string,
  options?: LabelPrintingOptions
): Promise<ImageBuffer> {
  try {
    // Ensure name is a valid string and sanitize it
    const sanitizedName = (name || 'Guest').trim();

    // Ensure QR content is valid
    if (!qrContent) {
      throw new Error('QR content is required');
    }

    const finalOptions = GetLabelPrintOptions(options || {});
    const finalDimensions = GetLabelDimensions();
    const { orientation, theme, showBranding } = finalOptions;

    // Constants for label dimensions (in mm)
    const PADDING = 3; // 3mm padding

    const { dpi, QR, LANDSCAPE, PORTRAIT } = finalDimensions;

    const isLandscape = orientation === "landscape";

    const width = isLandscape
      ? mmToPixels(LANDSCAPE.width, dpi)
      : mmToPixels(PORTRAIT.width, dpi);
    const height = isLandscape
      ? mmToPixels(LANDSCAPE.height, dpi)
      : mmToPixels(PORTRAIT.height, dpi);

    const qrWidth = mmToPixels(QR.width, dpi);
    const qrX = mmToPixels(QR.x, dpi);
    const qrY = mmToPixels(QR.y, dpi);

    const logoWidthPx = mmToPixels(QR.width - PADDING, dpi);

    const qrBuffer = await QRCode.toBuffer(qrContent, {
      margin: 1,
      width: qrWidth,
      color: {
        dark: theme.qr,
        light: theme.background,
      },
    });

    // Calculate font size - slightly smaller for better readability
    const fontSize = mmToPixels(PADDING, dpi) * 1.2;

    // Wrap and truncate text to fit the max width
    const lines = wrapText(name, qrWidth, fontSize);

    // Create text SVG for the name with support for multiple lines
    const lineHeight = fontSize * 1.4; // Line height based on font size
    const textSvgHeight = lineHeight * lines.length;

    // Escape special characters in the text to prevent XML parsing errors
    const escapeXml = (str: string) => {
      return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&apos;');
    };

    let textContent = '';
    lines.forEach((line, index) => {
      const escapedLine = escapeXml(line);
      textContent += `<text x="${isLandscape ? 0 : qrWidth / 2}" y="${lineHeight * (index + 0.8)}" 
      text-anchor="${isLandscape ? 'start' : 'middle'}" class="name">${escapedLine}</text>`;
    });

    const textSvg = `
    <svg width="${qrWidth}" height="${textSvgHeight}" xmlns="http://www.w3.org/2000/svg">
      <style>
        .name { fill: ${theme.qr}; font-size: ${fontSize}px; font-weight: bold; font-family: sans-serif; }
      </style>
      ${textContent}
    </svg>
  `;
    // Convert SVG to PNG using sharp
    const textX = isLandscape ? qrX * 2 + qrWidth + PADDING : PADDING;
    const textY = isLandscape ? qrY + qrWidth + PADDING : qrY + qrWidth + PADDING;
    const textBufferWithPosition = Buffer.from(textSvg);
    const textBuffer = await sharp(textBufferWithPosition)
      // .resize(qrWidth, textSvgHeight, { fit: 'contain' })
      .toBuffer();

    let logoBuffer: Buffer | null = null;
    if (showBranding) {
      try {
        const logoPath = DEFAULT_LOGO_PATH;
        logoBuffer = fs.readFileSync(logoPath);

        logoBuffer = await sharp(logoBuffer)
          .resize(logoWidthPx, null, { fit: 'contain' })
          .toBuffer();

      } catch (error) {
        console.warn("Failed to load logo, skipping branding.");
      }
    }

    const compositeLayers = [
      { input: qrBuffer, top: qrY, left: qrX },
      { input: textBuffer, top: textY, left: textX },
    ];

    if (logoBuffer && showBranding) {
      // Adjust the logo position based on the orientation
      const logoX = mmToPixels(isLandscape ? (LANDSCAPE.width - QR.width - PADDING) : PADDING, dpi);
      const logoY = mmToPixels(isLandscape ? QR.height - 6 : (PORTRAIT.height - PADDING - 6), dpi);

      compositeLayers.push({ input: logoBuffer, top: logoY, left: logoX });
    }

    try {
      const finalImage = await sharp({
        create: {
          width,
          height,
          channels: 4,
          background: theme.background,
        },
      })
        .composite(compositeLayers)
        .png()
        .toBuffer();

      return { fileContents: finalImage, mimeType: "image/png", dimensions: { width, height } };
    } catch (error) {
      console.error('Error generating label:', error);

      // Create a fallback simple image without SVG
      const fallbackImage = await sharp({
        create: {
          width,
          height,
          channels: 4,
          background: theme.background,
        },
      })
        .png()
        .toBuffer();

      return { fileContents: fallbackImage, mimeType: "image/png", dimensions: { width, height } };
    }
  } catch (outerError) {
    console.error('Critical error in RenderLabel:', outerError);

    // If all else fails, create an extremely simple fallback
    const fallbackWidth = 400;
    const fallbackHeight = 200;
    const fallbackImage = await sharp({
      create: {
        width: fallbackWidth,
        height: fallbackHeight,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 1 },
      },
    })
      .png()
      .toBuffer();

    return {
      fileContents: fallbackImage,
      mimeType: "image/png",
      dimensions: { width: fallbackWidth, height: fallbackHeight }
    };
  }
}