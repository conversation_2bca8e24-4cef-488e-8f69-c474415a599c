import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if user is authenticated
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { eventId, forceExpire } = req.body;

    if (!eventId) {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    const db = Database.getInstance();

    // Get the event
    const event = await db.readData('events', eventId);
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Check if user has access to this event (owner or manager)
    const organization = await db.getOrganizationById(event.ownerAccountId);
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const isMember = organization.members?.some(member => member.userId === session.user.id);
    const isOwner = event.ownerAccountId === session.user.id;
    const isManager = event.managers?.includes(session.user.email);

    if (!isMember && !isOwner && !isManager) {
      return res.status(403).json({ error: 'Access denied' });
    }

    let processedEvent = event;

    // If forceExpire is true, update the event date to make it "expired"
    if (forceExpire) {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 2); // Set to 2 days ago to ensure it's past the 24-hour buffer
      
      const expiredEventData = {
        ...event,
        eventDate: yesterday.toISOString(),
        lastUpdatedOn: new Date().toISOString()
      };

      await db.addData('events', expiredEventData);
      processedEvent = expiredEventData;

      console.log(`Force expired event ${eventId} by setting date to ${yesterday.toISOString()}`);
    }

    // Check if the event has contact groups to save
    const { hasContactGroupsToSave, extractContactGroupsFromEvent } = await import('@/lib/saved-contact-groups');
    const hasGroups = await hasContactGroupsToSave(eventId);
    
    if (!hasGroups) {
      return res.status(400).json({ 
        error: 'Event has no contact groups to save',
        message: 'Add some invites with groups to test the feature'
      });
    }

    // Get the contact groups for display
    const contactGroups = await extractContactGroupsFromEvent(eventId);

    // Trigger the post-event actions
    await db.checkAndTriggerPostEventActions(eventId);

    return res.status(200).json({
      success: true,
      message: 'Event completion triggered successfully',
      eventId,
      eventName: processedEvent.eventName,
      organizationId: organization.id,
      organizationName: organization.name,
      contactGroups: contactGroups.map(group => ({
        name: group.name,
        contactCount: group.contacts.length
      })),
      emailSentTo: event.ownerEmail,
      forceExpired: forceExpire || false
    });

  } catch (error) {
    console.error('Error triggering event completion:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
