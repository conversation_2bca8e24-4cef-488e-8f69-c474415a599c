import { APIResponse } from '@/types';
import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth";

/**
 * @api {DELETE} /event/:eventId/invites/bulk-delete Bulk delete invites
 * @apiName Bulk Delete Invites
 * @apiGroup Invites
 * @apiDescription Deletes multiple invites at once
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId } = req.query;

  if (!eventId) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  // Get the session to check authentication
  const session = await getServerSession(req, res, authConfig);
  if (!session?.user) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  if (req.method !== 'DELETE') {
    res.setHeader('Allow', ['DELETE']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Check if the user is the owner of the event
    const event = await Database.getInstance().readData('events', eventId as string);
    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }

    // Check if the user is the owner of the event
    const isOwner = (event.ownerEmail === session.user.email) ||
                    (event.ownerAccountId === session.user.id);

    if (!isOwner) {
      return res.status(403).json({
        error: "Permission denied",
        message: "Only the event owner can delete invites"
      });
    }

    const deleteResponse = await DELETE(req.body.inviteIds);
    res.status(deleteResponse.code).json(deleteResponse.data);
  } catch (error) {
    console.error('Error in bulk delete handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Deletes multiple invites by their IDs
 * 
 * @param inviteIds Array of invite IDs to delete
 * @returns APIResponse containing the deletion status and details
 */
async function DELETE(inviteIds: string[]): Promise<APIResponse> {
  if (!inviteIds || !Array.isArray(inviteIds) || inviteIds.length === 0) {
    return {
      data: { error: "No invite IDs provided" },
      code: 400,
      headers: {
        'Content-Type': 'application/json',
      }
    };
  }

  const deletedInvites: Array<{ id: string; name: string }> = [];
  const failedDeletes: Array<{ id: string; error: string }> = [];

  // Process each invite deletion
  for (const inviteId of inviteIds) {
    try {
      // First get the invite details before deleting
      const invite = await Database.getInstance().readData('invites', Database.normalizeId(inviteId));

      if (!invite) {
        failedDeletes.push({ id: inviteId, error: 'Invite not found' });
        continue;
      }

      const name = invite?.name || 'Unknown';
      
      // Delete the invite
      await Database.getInstance().deleteData('invites', Database.normalizeId(inviteId));
      
      deletedInvites.push({ id: inviteId, name });
      log('Invite deleted [' + inviteId + ']');
    } catch (error) {
      console.error(`Error deleting invite ${inviteId}:`, error);
      failedDeletes.push({ 
        id: inviteId, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }

  const totalRequested = inviteIds.length;
  const totalDeleted = deletedInvites.length;
  const totalFailed = failedDeletes.length;

  return {
    data: {
      success: totalFailed === 0,
      message: totalFailed === 0 
        ? `Successfully deleted ${totalDeleted} invite${totalDeleted !== 1 ? 's' : ''}`
        : `Deleted ${totalDeleted} of ${totalRequested} invites. ${totalFailed} failed.`,
      deletedInvites,
      failedDeletes,
      summary: {
        totalRequested,
        totalDeleted,
        totalFailed
      }
    },
    code: totalFailed === 0 ? 200 : 207, // 207 = Multi-Status for partial success
    headers: {
      'Content-Type': 'application/json',
    }
  };
}
