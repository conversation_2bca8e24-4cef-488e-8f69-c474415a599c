import { NextApiRequest, NextApiResponse } from 'next';
import { getEventInviteImageUrl, getEventImageWithFallback } from '@/lib/storage';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  try {
    // Get the eventId and imageType from the query parameters
    const { eventId, imageType } = req.query;
    
    if (!eventId || Array.isArray(eventId)) {
      return res.status(400).json({ error: 'Event ID is required as a single string' });
    }

    // Validate imageType if provided
    const validImageTypes = ['invitation-card', 'digital-invite'];
    if (imageType && (Array.isArray(imageType) || !validImageTypes.includes(imageType))) {
      return res.status(400).json({ error: 'Invalid image type. Must be invitation-card or digital-invite' });
    }

    // Log for debugging
    console.log(`Attempting to get invite image for event: ${eventId}, type: ${imageType || 'default'}`);
    
    let result;
    
    if (imageType) {
      // If imageType is specified, use the fallback logic for digital-invite
      const preferredType = imageType as 'invitation-card' | 'digital-invite';
      result = await getEventImageWithFallback(eventId, preferredType);
    } else {
      // Backward compatibility: use invitation-card as default
      result = await getEventInviteImageUrl(eventId, 'invitation-card');
    }
    
    // Return the result
    return res.status(200).json(result);
    
  } catch (error) {
    console.error('Error getting image URL:', error);
    return res.status(500).json({ 
      error: 'Failed to get image URL',
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    });
  }
}