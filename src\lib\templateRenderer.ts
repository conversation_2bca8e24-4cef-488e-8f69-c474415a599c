import { readFileSync } from "fs";
import { join } from "path";
import Handlebars from "handlebars";

export async function renderTemplate(templateName: string, data: object): Promise<string> {
  const templatePath = join("src", "lib", "templates", templateName);

  const templateContent = readFileSync(templatePath, "utf8");
  const template = Handlebars.compile(templateContent);
  return template(data);
}
