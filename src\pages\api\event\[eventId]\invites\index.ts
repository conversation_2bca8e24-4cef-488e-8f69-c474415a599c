import { APIResponse } from '@/types';
import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { EventInvite, CSVInvite } from '@/types';
import { GenerateID } from '@/lib/ID';
import { debugLog, log } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId } = req.query;

  if (!eventId || typeof eventId !== 'string') {
    return res.status(400).json({ error: 'Event ID is required' });
  }

  switch (req.method) {
    case 'GET':
      try {
        const response = await GET(eventId);
        return res.status(response.code).json(response.data);
      } catch (error) {
        console.error('Error fetching invites:', error);
        return res.status(500).json({ error: 'Failed to fetch invites' });
      }

    case 'POST':
      try {
        const invite: Partial<EventInvite> = req.body;
        const response = await POST(eventId, invite);
        return res.status(response.code).json(response.data);
      } catch (error) {
        console.error('Error creating invite:', error);
        return res.status(500).json({ error: 'Failed to create invite' });
      }

    case 'PUT':
      try {
        const invites: CSVInvite[] = req.body;
        const response = await PUT(eventId, invites);
        return res.status(response.code).json(response.data);
      } catch (error) {
        console.error('Error creating bulk invites:', error);
        return res.status(500).json({ error: 'Failed to create bulk invites' });
      }

    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT']);
      return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }
}

/**
 * Creates an invite for an event
 * 
 * @param eventId The ID of the event to create an invite for
 * @param invite Partial invite data
 * @returns APIResponse containing the created invite
 */
export async function POST(eventId: string, invite: Partial<EventInvite>): Promise<APIResponse> {
  debugLog("POST", {eventId, invite})
  const inviteData: EventInvite = {
    eventId: eventId,
    name: invite.name || '',
    status: "invited",
    adults: invite.adults || 1,
    children: invite.children || 0,
    email: invite.email || '',
    phone: invite.phone || '',
    group: invite.group || '',
    message: [],
    createdAt: new Date().toISOString() 
  };

  inviteData.ID = GenerateID('I');

  const response = await Database.getInstance().addData('invites', inviteData);
  log('Invite created [' + inviteData.ID + ']', response);

  return {
    data: {
      ...inviteData
    },
    code: 200,
    headers: {
      'Content-Type': 'application/json',
    }
  }
}

/**
 * Bulk creates invites for an event. Receives an array of invites in the request body
 * @param eventId The ID of the event to create invites for
 * @param invites Array of CSVInvite objects to create
 * @returns APIResponse containing the created invites
 */
export async function PUT(eventId: string, invites: CSVInvite[]): Promise<APIResponse> {
  const now = new Date().toISOString(); 
  const response = await Database.getInstance().addBulkData('invites', invites.map((invite, i) => ({
    ...invite,
    ID: GenerateID('I', i),
    status: 'invited',
    eventId,
    email: invite.email || '',
    phone: invite.phone || '',
    message: [],
    createdAt: now
  })));

  return {
    data: {
      invites: response
    },
    code: 200,
    headers: {
      'Content-Type': 'application/json',
    }
  }
}

/**
 * Fetches all invites for an event
 * 
 * @param eventId The ID of the event to fetch invites for
 * @returns APIResponse containing the list of invites
 */
export async function GET(eventId: string): Promise<APIResponse> {
  const invites = await Database.getInstance().ListData('invites', {
    field: 'eventId',
    operator: '==',
    value: Database.normalizeId(eventId)
  });

  return {
    data: {
      invites
    },
    code: 200,
    headers: {
      'Content-Type': 'application/json',
    }
  }
}