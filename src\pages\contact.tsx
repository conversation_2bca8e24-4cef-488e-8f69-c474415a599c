'use client'

import { <PERSON><PERSON> } from "@/components/Header"
import { <PERSON><PERSON> } from "@/components/Footer"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useState, useEffect } from "react"
import Image from "next/image"
import { useSession } from "next-auth/react"
import { useRouter } from "next/router"
import Head from "next/head"
import { useToast } from "@/components/ui/use-toast"

export default function ContactPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const { toast } = useToast()
  const [formState, setFormState] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    website: '' // Honeypot field
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [recaptchaToken, setRecaptchaToken] = useState("")

  // Load reCAPTCHA script
  useEffect(() => {
    // Add reCAPTCHA script
    const script = document.createElement("script")
    script.src = `https://www.google.com/recaptcha/api.js?render=${process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}`
    script.async = true
    document.body.appendChild(script)

    return () => {
      document.body.removeChild(script)
    }
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormState(prev => ({ ...prev, [name]: value }))
  }

  const executeRecaptcha = () => {
    return new Promise<string>((resolve) => {
      if (!(window as any).grecaptcha) {
        console.error("reCAPTCHA not loaded")
        toast({
          title: "Security Check Not Available",
          description: "Our security system didn't load properly. Please refresh the page or try using a different browser.",
          variant: "destructive"
        })
        resolve("")
        return
      }

      ;(window as any).grecaptcha.ready(() => {
        ;(window as any).grecaptcha
          .execute(process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY, { action: "contact_submit" })
          .then((token: string) => {
            setRecaptchaToken(token)
            resolve(token)
          })
          .catch((error: any) => {
            console.error("reCAPTCHA execution error:", error)
            toast({
              title: "Security Verification Failed",
              description: "We couldn't complete the security check. This might be due to network issues or browser settings. Please try again or contact support.",
              variant: "destructive"
            })
            resolve("")
          })
      })
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Get reCAPTCHA token
      const token = await executeRecaptcha()

      if (!token) {
        toast({
          title: "Security Verification Failed",
          description: "We couldn't verify your request. Please refresh the page and try again.",
          variant: "destructive"
        })
        setIsSubmitting(false)
        return
      }

      // Send form data to the API endpoint
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formState,
          recaptchaToken: token
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 429) {
          toast({
            title: "Too Many Attempts",
            description: "You've made too many requests. Please wait a few minutes before trying again.",
            variant: "destructive"
          })
        } else if (data.error) {
          // Format the error message to be more user-friendly
          let errorTitle = "Message Not Sent";
          let errorDescription = data.error;

          // Customize error messages based on common errors
          if (data.error.includes('inappropriate content')) {
            errorTitle = "Content Not Allowed";
            errorDescription = "Your message contains content that appears to be spam. Please revise and try again.";
          } else if (data.error.includes('valid email')) {
            errorTitle = "Invalid Email";
            errorDescription = "Please provide a valid email address so we can respond to your inquiry.";
          } else if (data.error.includes('verification failed')) {
            errorTitle = "Security Check Failed";
            errorDescription = "Our security system couldn't verify your request. Please try again later.";
          }

          toast({
            title: errorTitle,
            description: errorDescription,
            variant: "destructive"
          })
        } else {
          toast({
            title: "Message Not Sent",
            description: "We encountered an issue sending your message. Please try again later or contact us <NAME_EMAIL>",
            variant: "destructive"
          })
        }
        setIsSubmitting(false)
        return
      }

      // Reset form and show success message
      setFormState({ name: '', email: '', subject: '', message: '', website: '' })

      // Show success toast
      toast({
        title: "Message Sent Successfully!",
        description: "Thank you for contacting us. Our team will review your message and respond to your inquiry as soon as possible.",
      })
    } catch (error) {
      console.error('Error sending contact form:', error)
      toast({
        title: "Connection Error",
        description: "We couldn't connect to our servers. Please check your internet connection and try again, or email us <NAME_EMAIL>",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Head>
        <title>Contact Us | I am Coming</title>
      </Head>
      <Header
        buttons={session ? [
          {
            label: "Manage Events",
            onClick: () => router.push('/events'),
            variant: "outline"
          }
        ] : []}
      />

      <main className="flex-1 container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Contact Us</h1>
        <Separator className="my-4" />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mt-8">
          <div>
            <h2 className="text-2xl font-semibold mb-6">Get in Touch</h2>
            <p className="text-gray-700 mb-8">
              We&apos;d love to hear from you! Whether you have a question about our platform, 
              need help with your account, or want to discuss event management solutions, 
              our team is ready to assist you.
            </p>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                  <Image src="/Mail.svg" alt="Email" width={20} height={20} className="h-7 w-7" />
                <div>
                  <h3 className="font-medium">Email Us</h3>
                  <div className="space-y-4 mt-2">
                    <div>
                      <p className="text-gray-900 font-medium"><EMAIL></p>
                      <p className="text-muted-foreground text-sm">For account help, technical issues, or questions about using our platform</p>
                    </div>
                    <div>
                      <p className="text-gray-900 font-medium"><EMAIL></p>
                      <p className="text-muted-foreground text-sm">For privacy concerns, data access requests, or questions about our Privacy Policy</p>
                    </div>
                    {/* <div>
                      <p className="text-gray-900 font-medium"><EMAIL></p>
                      <p className="text-muted-foreground text-sm">For data protection inquiries, GDPR/CCPA compliance questions, or to contact our Data Protection Officer</p>
                    </div> */}
                  </div>
                </div>
              </div>
            </div>
            
            <h3 className="text-xl font-semibold mt-10 mb-4">Connect With Us</h3>
            <div className="flex space-x-4">
              <a 
                href="https://www.facebook.com/app.iamcoming.io/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-gray-100 p-3 rounded-full hover:bg-primary/10 transition-colors"
              >
                <Image src="/Facebook.svg" alt="Facebook" width={20} height={20} className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </a>
              <a
                href="https://twitter.com/iamcoming_io"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gray-100 p-3 rounded-full hover:bg-primary/10 transition-colors"
              >
                <Image src="/X.svg" alt="X" width={20} height={20} className="h-5 w-5" />
                <span className="sr-only">X</span>
              </a>
              <a 
                href="https://www.instagram.com/iamcoming.io/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-gray-100 p-3 rounded-full hover:bg-primary/10 transition-colors"
              >
                <Image src="/Instagram.svg" alt="Instagram" width={20} height={20} className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </a>
              <a 
                href="https://www.linkedin.com/company/iamcoming/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-gray-100 p-3 rounded-full hover:bg-primary/10 transition-colors"
              >
                <Image src="/LinkedIn.svg" alt="LinkedIn" width={20} height={20} className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </a>
            </div>
          </div>
          
          <div>
            <h2 className="text-2xl font-semibold mb-6">Send Us a Message</h2>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  Name
                </label>
                <Input
                  id="name"
                  name="name"
                  value={formState.name}
                  onChange={handleChange}
                  placeholder="Your name"
                  required
                  className="w-full"
                />
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-1">
                  Email
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formState.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  required
                  className="w-full"
                />
              </div>
              
              <div>
                <label htmlFor="subject" className="block text-sm font-medium mb-1">
                  Subject
                </label>
                <Input
                  id="subject"
                  name="subject"
                  value={formState.subject}
                  onChange={handleChange}
                  placeholder="What is your message about?"
                  required
                  className="w-full"
                />
              </div>
              
              <div>
                <label htmlFor="message" className="block text-sm font-medium mb-1">
                  Message
                </label>
                <Textarea
                  id="message"
                  name="message"
                  value={formState.message}
                  onChange={handleChange}
                  placeholder="Your message here..."
                  required
                  className="w-full min-h-[150px]"
                />
              </div>

              {/* Honeypot field - hidden from real users but bots will fill it out */}
              <div className="hidden" aria-hidden="true">
                <label htmlFor="website" className="sr-only">Website</label>
                <Input
                  id="website"
                  name="website"
                  type="text"
                  autoComplete="off"
                  value={formState.website}
                  onChange={handleChange}
                  tabIndex={-1}
                />
              </div>

              <Button 
                type="submit" 
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </Button>
              <p className="text-xs text-muted-foreground text-center mt-2">
                This site is protected by reCAPTCHA and the Google{' '}
                <a href="https://policies.google.com/privacy" className="underline" target="_blank" rel="noreferrer">Privacy Policy</a> and{' '}
                <a href="https://policies.google.com/terms" className="underline" target="_blank" rel="noreferrer">Terms of Service</a> apply.
              </p>
            </form>
          </div>
        </div>
      </main>
      
      <Footer type="marketing" />
    </div>
  )
}