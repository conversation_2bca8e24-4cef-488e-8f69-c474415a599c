# IAC Universe v1 Type System Documentation

This directory contains comprehensive documentation of all TypeScript types used in the IAC Universe v1 application. Types are categorized based on their purpose and data lifecycle.

## Directory Structure

```
types/
├── README.md (this file)
├── database-types.md     # Core entities stored in database
├── utility-types.md      # Helper types for data passing and enums
├── helper-types.md       # Application interaction types (API, forms, etc.)
├── auth-rbac-types.md    # Authentication and authorization types
├── component-types.md    # React component prop types
└── migration-analysis.md # Analysis for universe/packages/types migration
```

## Type Categories

### Database Types
Core entities that represent actual data structure stored in the database:
- User profiles and accounts
- Events and event management
- Invites and RSVP responses
- Organizations and venues
- Contact groups and saved data
- Billing and payment records

### Utility Types
Supporting types used across the codebase for data passing and configuration:
- Enums for status values
- Configuration objects
- Print and media settings
- Display preferences
- Constants and lookup values

### Helper Types
Essential types that facilitate application interaction but are not stored in DB:
- API request/response structures
- Form submission schemas
- Validation results
- Pagination metadata
- Error handling structures

### Authentication & RBAC Types
Comprehensive role-based access control system types:
- Permission definitions
- Role hierarchies
- Organization-scoped access
- Session management
- Audit logging

## Key Design Principles

1. **Type Safety**: All database operations use strongly typed interfaces
2. **Separation of Concerns**: Clear distinction between database, API, and UI types
3. **Backward Compatibility**: Legacy fields maintained for migration support
4. **Organization Context**: Multi-tenant architecture with organization-scoped permissions
5. **Audit Trail**: Comprehensive tracking of permission and role changes

## Migration Context

This v1 type system serves as the source of truth for data stored in production databases. The new universe/packages/types system should:
- Support v1 database structures for backward compatibility
- Provide improved type definitions for new features
- Maintain migration paths from v1 to new structures
- Support both legacy and modern API patterns

## Usage Guidelines

When working with v1 types:
1. **Database Operations**: Use database types for all data persistence
2. **API Endpoints**: Use helper types for request/response handling
3. **Component Props**: Use component types for React component interfaces
4. **Permissions**: Use RBAC types for all authorization logic
5. **Forms**: Use utility types for form validation and submission

## Related Documentation

- [Database Schema Documentation](../docs/database-schema.md)
- [API Documentation](../docs/API.md)
- [RBAC System Guide](../docs/RBAC_Roles_and_Resources.md)
- [Authentication Guide](../docs/Cross-Domain-Authentication.md)