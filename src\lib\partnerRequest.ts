import { renderTemplate } from "./templateRenderer";
import { transporter } from "./mailer";
import { log } from "./logger";

const FROM_EMAIL = process.env.FROM_EMAIL || "<EMAIL>";
const SUPPORT_EMAIL = process.env.SUPPORT_EMAIL || "<EMAIL>";

export interface PartnerRequestData {
  businessName: string;
  taxId: string;
  phoneNumber: string;
  address?: string;
  hasMultipleVenues: boolean;
  userEmail: string;
  userName: string;
  userImage?: string;
}

/**
 * Send partner request emails to both support team and the user
 */
export async function sendPartnerRequestEmails(requestData: PartnerRequestData): Promise<boolean> {
  try {
    // Send notification to support team
    await sendPartnerRequestToSupport(requestData);
    
    // Send thank you email to user
    await sendPartnerRequestThankYou(requestData);
    
    log(`Partner request emails sent successfully for ${requestData.businessName}`);
    return true;
  } catch (error) {
    console.error('Error sending partner request emails:', error);
    throw new Error('Failed to send partner request emails');
  }
}

/**
 * Send partner request notification to support team
 */
async function sendPartnerRequestToSupport(requestData: PartnerRequestData): Promise<void> {
  const supportPersonalization = {
    businessName: requestData.businessName,
    taxId: requestData.taxId,
    phoneNumber: requestData.phoneNumber,
    address: requestData.address || 'Not provided',
    hasMultipleVenues: requestData.hasMultipleVenues ? 'Yes' : 'No',
    userEmail: requestData.userEmail,
    userName: requestData.userName,
    submissionDate: new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    })
  };

  const supportTextBody = await renderTemplate("partnerRequestSupportText.hbs", supportPersonalization);
  const supportHtmlBody = await renderTemplate("partnerRequestSupportHtml.hbs", supportPersonalization);

  await transporter.sendMail({
    from: `I am Coming Partner Requests <${FROM_EMAIL}>`,
    to: SUPPORT_EMAIL,
    subject: `New Partner Request: ${requestData.businessName}`,
    text: supportTextBody,
    html: supportHtmlBody
  });

  log(`Partner request notification sent to support for ${requestData.businessName}`);
}

/**
 * Send thank you email to user who submitted partner request
 */
async function sendPartnerRequestThankYou(requestData: PartnerRequestData): Promise<void> {
  const userPersonalization = {
    userName: requestData.userName,
    businessName: requestData.businessName,
    supportEmail: SUPPORT_EMAIL
  };

  const userTextBody = await renderTemplate("partnerRequestThankYouText.hbs", userPersonalization);
  const userHtmlBody = await renderTemplate("partnerRequestThankYouHtml.hbs", userPersonalization);

  await transporter.sendMail({
    from: `I am Coming <${FROM_EMAIL}>`,
    to: requestData.userEmail,
    subject: `Thank you for your interest in partnering with I am Coming!`,
    text: userTextBody,
    html: userHtmlBody
  });

  log(`Partner request thank you email sent to ${requestData.userEmail}`);
}
