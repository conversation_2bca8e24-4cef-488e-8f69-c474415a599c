import { Database } from "../src/lib/database";
import { UserProfile, Organization, Event } from "../src/types";

async function migrateUsersToOrganizations() {
  const db = Database.getInstance();

  try {
    // Fetch all users
    const users: UserProfile[] = await db.ListAllData<UserProfile>('users');

    for (const user of users) {
      // Create an organization for each user
      const organization: Organization = {
        id: user.id, // Use user ID as organization ID for simplicity
        name: user.name || "Unnamed Organization",
        type: "individual",
        members: [
          {
            userId: user.id,
            role: "owner",
          },
        ],
        createdOn: new Date().toISOString(),
        lastUpdatedOn: new Date().toISOString(),
      };

      // Add the organization to the database
      await db.addData('organizations', organization);

      // Update the user to reference the new organization
      await db.updateData('users', user.id, {
        organizationId: organization.id,
      });

      // Fetch all events owned by the user
      const events: Event[] = await db.ListData<Event>('events', {
        field: 'ownerAccountId',
        operator: '==',
        value: user.id,
      });

      for (const event of events) {
        // Update each event to reference the new organization
        await db.updateData('events', event.ID!, {
          organizationId: organization.id,
        });
      }
    }

    console.log("Migration completed successfully.");
  } catch (error) {
    console.error("Error during migration:", error);
  }
}

migrateUsersToOrganizations();
