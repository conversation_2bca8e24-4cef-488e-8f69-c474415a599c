import { Database } from '@/lib/database';
import { StringArrayToString } from '@/lib/utils';
import { APIResponse, Event } from '@/types';
import { NextApiRequest, NextApiResponse } from 'next';
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { log } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId } = req.query;
  if (!eventId) {
    return res.status(400).json({ error: 'Event ID is required' });
  }

  switch (req.method) {
    case 'GET':
      // Get user session to check authorization
      const getSession = await getServerSession(req, res, authConfig);
      const response = await getEvent(eventId as string, getSession?.user?.email, getSession?.user?.id);
      res.status(response.code).json(response.data);
      break;
    case 'PUT':
      // Keep auth check for PUT operations
      const session = await getServerSession(req, res, authConfig);
      if (!session?.user?.id) {
        return res.status(401).json({ message: 'Unauthorized' });
      }
      const updateResponse = await updateEvent(eventId as string, req.body, session.user.id);
      res.status(updateResponse.code).json(updateResponse.data);
      break;
    case 'DELETE':
      // Auth check for DELETE operations
      const deleteSession = await getServerSession(req, res, authConfig);
      if (!deleteSession?.user?.id) {
        return res.status(401).json({ message: 'Unauthorized' });
      }
      // Pass the user ID for permission check in delete operation
      const deleteResponse = await deleteEvent(eventId as string, deleteSession.user.id);
      res.status(deleteResponse.code).json(deleteResponse.data);
      break;
    default:
      res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

async function getEvent(eventId: string, userEmail?: string, userId?: string) : Promise<APIResponse> {
  if (!eventId) {
    return {
      data: {
        message: 'Event ID is required',
        code: 'EVENT_ID_REQUIRED'
      },
      code: 400,
      headers: {
        'Content-Type': 'application/json',
      }
    }
  }

  if(eventId === 'new') {
    return {
      data: {
        ID: 'new',
        title: '',
        description: '',
        date: new Date().toISOString(),
        location: '',
        media: '/static/events/default.jpg',
        invites: []
      },
      code: 200,
      headers: {
        'Content-Type': 'application/json',
      }
    }
  }

  // fetch event from firebase database based on id passed
  try {
    const event = await Database.getInstance().readData('events', Database.normalizeId(StringArrayToString(eventId))) as Event;
    
    if (!event) {
      return {
        data: {
          message: 'Event not found'
        },
        code: 404,
        headers: {
          'Content-Type': 'application/json',
        }
      };
    }

    // If userEmail or userId is provided, check authorization
    if (userEmail || userId) {
      // Check if user is owner by email (backward compatibility)
      const isOwnerByEmail = userEmail && event.ownerEmail === userEmail;
      
      // Check if user is owner by account ID
      const isOwnerById = userId && event.ownerAccountId === userId;
      
      // Combined owner check
      const isOwner = isOwnerByEmail || isOwnerById;
      
      // Check if user is a manager by email (backward compatibility)
      // Some events might still have email addresses in the managers array
      const isManagerByEmail = userEmail && Array.isArray(event.managers) && event.managers.includes(userEmail);
      
      // Check if user is a manager by account ID
      const isManagerById = userId && Array.isArray(event.managers) && event.managers.includes(userId);
      
      // Combined manager check
      const isManager = isManagerByEmail || isManagerById;
      
      // Log for debugging
      log('Event access check', {
        eventId,
        userEmail,
        userId,
        isOwnerByEmail,
        isOwnerById,
        isOwner,
        isManagerByEmail,
        isManagerById,
        isManager,
        eventOwnerEmail: event.ownerEmail,
        eventOwnerAccountId: event.ownerAccountId,
        eventManagers: event.managers
      });
      
      // Check if admin (we need to get the user profile)
      let isAdmin = false;
      try {
        // Get user profile by email (we'll only use email for admin check for now)
        if (userEmail) {
          const userProfile = await Database.getInstance().getUserByEmail(userEmail);
          isAdmin = !!userProfile?.isAdmin;
        }
      } catch (error) {
        // If error checking admin status, default to not admin
        isAdmin = false;
      }

      // If not owner, manager or admin, return forbidden
      if (!isOwner && !isManager && !isAdmin) {
        return {
          data: {
            message: 'Forbidden - You do not have access to this event',
            code: 'FORBIDDEN_ACCESS'
          },
          code: 403,
          headers: {
            'Content-Type': 'application/json',
          }
        };
      }
    }
    
    return {
      data: {
        ...event
      },
      code: 200,
      headers: {
        'Content-Type': 'application/json',
      }
    }
  } catch (error: Error | any) {
    return {
      data: {
        message: 'Error fetching event'
      },
      headers: {
        'Content-Type': 'application/json',
      },
      code: 500
    }
  }
}

async function updateEvent(eventId: string, data: any, userId: string): Promise<APIResponse> {
  try {
    // Fetch the current event to check permissions
    const currentEvent = await Database.getInstance().readData('events', Database.normalizeId(eventId)) as Event;
    
    if (!currentEvent) {
      return {
        data: {
          message: 'Event not found'
        },
        code: 404,
        headers: {
          'Content-Type': 'application/json',
        }
      };
    }

    // Check if this update includes managers changes
    if (data.managers !== undefined) {
      const currentManagers = Array.isArray(currentEvent.managers) ? currentEvent.managers : [];
      const newManagers = Array.isArray(data.managers) ? data.managers : [];
      
      // Check if the user is the owner - owners can modify the managers list freely
      const isOwner = currentEvent.ownerAccountId === userId || currentEvent.ownerEmail === userId;
      
      // Check if the user is a manager - managers can only remove themselves
      const isManager = !isOwner && currentManagers.includes(userId);
      
      // Determine which managers were removed
      const removedManagers = currentManagers.filter(id => !newManagers.includes(id));
      
      // Log for debugging
      log('Event manager update', {
        eventId,
        userId,
        isOwner,
        isManager,
        currentManagers,
        newManagers,
        removedManagers
      });

      // Validation rules:
      // 1. Owners can modify the managers list freely
      // 2. Managers can only remove themselves
      if (!isOwner) {
        // If not the owner and trying to add new managers
        if (newManagers.length > currentManagers.length) {
          return {
            data: {
              message: 'Only the event owner can add managers'
            },
            code: 403,
            headers: {
              'Content-Type': 'application/json',
            }
          };
        }
        
        // If not the owner, check if they're only removing themselves
        if (removedManagers.length > 0) {
          // Check if more than one manager is being removed OR if someone other than the current user is being removed
          if (removedManagers.length > 1 || !removedManagers.includes(userId)) {
            return {
              data: {
                message: 'Managers can only remove themselves, not others'
              },
              code: 403,
              headers: {
                'Content-Type': 'application/json',
              }
            };
          }
          
          // Check if the user is actually a manager
          if (!isManager) {
            return {
              data: {
                message: 'You are not a manager of this event'
              },
              code: 403,
              headers: {
                'Content-Type': 'application/json',
              }
            };
          }
        }
      }
    }
    
    // If all validation passes or no managers were modified, proceed with the update
    const response = await Database.getInstance().updateData('events', Database.normalizeId(eventId), data);
    return {
      data: {
        ID: eventId,
        ...data
      },
      code: 200,
      headers: {
        'Content-Type': 'application/json',
      }
    };
  } catch (error) {
    console.error('Error updating event:', error);
    return {
      data: {
        message: 'Error updating event'
      },
      headers: {
        'Content-Type': 'application/json',
      },
      code: 500
    };
  }
}

async function deleteEvent(eventId: string, userId: string): Promise<APIResponse> {
  try {
    // Fetch the current event to check permissions
    const currentEvent = await Database.getInstance().readData('events', Database.normalizeId(eventId)) as Event;
    
    if (!currentEvent) {
      return {
        data: {
          message: 'Event not found'
        },
        code: 404,
        headers: {
          'Content-Type': 'application/json',
        }
      };
    }

    // Check if the user is the owner - only owners can delete the event
    const isOwner = currentEvent.ownerAccountId === userId;
    
    if (!isOwner) {
      return {
        data: {
          message: 'Only the event owner can delete the event'
        },
        code: 403,
        headers: {
          'Content-Type': 'application/json',
        }
      };
    }

    const response = await Database.getInstance().deleteData('events', Database.normalizeId(eventId));
    return {
      data: {
        message: 'Event deleted successfully'
      },
      code: 200,
      headers: {
        'Content-Type': 'application/json',
      }
    }
  } catch (error) {
    return {
      data: {
        message: 'Error deleting event'
      },
      headers: {
        'Content-Type': 'application/json',
      },
      code: 500
    }
  }
}