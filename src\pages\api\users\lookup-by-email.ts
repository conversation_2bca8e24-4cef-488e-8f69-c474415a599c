import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { z } from 'zod';

// Input validation schema
const requestSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Only allow POST method
    if (req.method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate request body
    const validatedData = requestSchema.safeParse(req.body);
    if (!validatedData.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: validatedData.error.issues
      });
    }

    const { email } = validatedData.data;

    // Look up user in database
    const user = await Database.getInstance().getUserByEmail(email);

    if (!user) {
      // If user not found, return 200 with user: null
      // This allows the UI to handle invited vs existing users
      return res.status(200).json({ user: null });
    }

    // Return user info but exclude sensitive data
    return res.status(200).json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        image: user.image || null
      }
    });

  } catch (error) {
    console.error('Error in /api/users/lookup-by-email:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
