import { Event, ImageBuffer, Orientation } from "@/types";
import sharp from "sharp";

interface Dimensions {
  width: number;
  height: number;
}

interface PaperSizes {
  [key: string]: {
    portrait: Dimensions;
    landscape: Dimensions;
  };
}

// Paper sizes in pixels (assuming 300 DPI)
const PAPER_SIZES: PaperSizes = {
  'A4': { 
    portrait: { width: 2480, height: 3508 },
    landscape: { width: 3508, height: 2480 }
  },
  'A5': { 
    portrait: { width: 1748, height: 2480 },
    landscape: { width: 2480, height: 1748 }
  },
  'A6': { 
    portrait: { width: 1240, height: 1748 },
    landscape: { width: 1748, height: 1240 }
  },
  'photo4x6': { 
    portrait: { width: 1200, height: 1800 },
    landscape: { width: 1800, height: 1200 }
  },
  'photo5x7': { 
    portrait: { width: 1500, height: 2100 },
    landscape: { width: 2100, height: 1500 }
  },
  'photo6x8': { 
    portrait: { width: 1800, height: 2400 },
    landscape: { width: 2400, height: 1800 }
  },
  'photo8x10': { 
    portrait: { width: 2400, height: 3000 },
    landscape: { width: 3000, height: 2400 }
  }
};

/**
 * Determines image dimensions based on size and orientation parameters
 * @param size - Size parameter (A4, A5, A6 or custom dimensions like 600x800)
 * @param orientation - Orientation parameter ('portrait' or 'landscape')
 * @returns Width and height in pixels
 */
export function getImageDimensions(size?: string, orientation?: string): Dimensions {
  // Default to A5 portrait if not specified
  if (!size) {
    return PAPER_SIZES['A5'].portrait;
  }
  
  // Check if size is a custom dimension (e.g., "600x800")
  if (typeof size === 'string' && size.includes('x')) {
    const [width, height] = size.split('x').map(dim => parseInt(dim, 10));
    return { width, height };
  }
  
  // Check if it's a standard paper size
  if (typeof size === 'string' && PAPER_SIZES[size]) {
    const orient = typeof orientation === 'string' ? orientation as Orientation : 'portrait';
    console.log(`Using standard paper size: ${size}`, PAPER_SIZES[size][orient || 'portrait']);
    
    return PAPER_SIZES[size][orient || 'portrait'];
  }
  
  // Default to A5 portrait if invalid size
  return PAPER_SIZES['A5'].portrait;
}

/**
 * Composites a background and label image into a single invitation image using the specified print settings.
 *
 * @param backgroundImage - The base ImageBuffer for the invitation.
 * @param labelImage - The overlay ImageBuffer to be applied on top of the background.
 * @param printingPreferences - Configuration options for rendering, including output format, dimensions, DPI, and other print-specific parameters.
 *
 * @returns A Promise that resolves to an object with:
 *   • fileContents: ImageBuffer — the rendered invitation image  
 *   • mimeType: string — the MIME type corresponding to the output format (e.g., "image/png", "image/jpeg")
 *   • dimensions: { width: number, height: number } — the dimensions of the rendered image
 */
export async function RenderInvite(backgroundImage: ImageBuffer, labelImage: ImageBuffer, printingPreferences: Event["printSettings"]): Promise<ImageBuffer> {
  const { inviteSettings } = printingPreferences || {};
  const { pageSize, orientation, labelPosition = {
    x: 50, // Default to center
    y: 80 // Default to center
  }, labelScale = 1 } = inviteSettings || {};
  
  // Determine dimensions based on size and orientation
  const { width: imageWidth, height: imageHeight } = getImageDimensions( pageSize, orientation );
    
  const LabelDimensions = labelImage.dimensions;
  
  // Scale label dimensions
  LabelDimensions.width = Math.floor(LabelDimensions.width * labelScale);
  LabelDimensions.height = Math.floor(LabelDimensions.height * labelScale);
  
  // Calculate position based on percentage values
  const posX = Math.floor((labelPosition.x / 100) * imageWidth); // Center QR at position
  const posY = Math.floor((labelPosition.y / 100) * imageHeight); // Center QR at position
  
  const label = await sharp(labelImage.fileContents)
  .resize(LabelDimensions.width, LabelDimensions.height, { fit: 'contain' })
  .toBuffer();
  
  // Create a base image with specified dimensions
  let sharpInstance = sharp({
    create: {
      width: imageWidth,
      height: imageHeight,
      channels: 4,
      background: { r: 255, g: 255, b: 255, alpha: 1 }
    }
  })
  .composite([
    // First overlay the event image (resized to cover dimensions)
    {
      input: await sharp(backgroundImage.fileContents)
      .resize(imageWidth, imageHeight, { fit: 'cover' })
      .toBuffer(),
      gravity: 'center'
    },
    // Then overlay the rectangle, text and QR code
    { input: label, top: posY - Math.round(LabelDimensions.height/2), left: posX - Math.round(LabelDimensions.width/2) },
  ]);
  
  // Convert to requested format
  let outputFormat = 'jpeg';
  
  const finalImage = await sharpInstance.jpeg({
    quality: 100, // Set quality for JPEG/PNG
  }).toBuffer();

  return {

    fileContents: finalImage,
    mimeType: `image/${outputFormat}`,
    dimensions: {
      width: imageWidth,
      height: imageHeight 
    }
  };
}