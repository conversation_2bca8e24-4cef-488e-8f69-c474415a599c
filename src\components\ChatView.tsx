import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft, Send } from "lucide-react"

interface ChatViewProps {
  messages: { id: string; text: string; sender: string; timestamp: Date }[];
  newMessage: string;
  setNewMessage: (message: string) => void;
  handleSendMessage: () => void;
  setView: React.Dispatch<React.SetStateAction<"invite" | "confirmation" | "chat">>;
}

export default function ChatView({ messages, newMessage, setNewMessage, handleSendMessage, setView }: ChatViewProps) {
  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <div className="sticky top-0 z-10 bg-white border-b p-4 flex items-center">
        <Button variant="ghost" size="icon" className="mr-2" onClick={() => setView("confirmation")}>
          <ArrowLeft className="h-5 w-5" />
          <span className="sr-only">Back</span>
        </Button>
        <h1 className="text-lg font-semibold">Message Host</h1>
      </div>
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`max-w-[80%] p-3 rounded-lg ${
                message.sender === "guest"
                  ? "bg-blue-500 text-white ml-auto rounded-br-none"
                  : "bg-gray-200 text-gray-800 rounded-bl-none"
              }`}
            >
              <p>{message.text}</p>
              <p className="text-xs opacity-70 mt-1">
                {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
              </p>
            </div>
          ))}
        </div>
      </div>
      <div className="p-4 border-t bg-white">
        <div className="flex space-x-2">
          <Textarea
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            className="flex-1"
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault()
                handleSendMessage()
              }
            }}
          />
          <Button onClick={handleSendMessage}>
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
