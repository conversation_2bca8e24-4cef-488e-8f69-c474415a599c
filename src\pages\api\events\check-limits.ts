import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { Event } from '@/types';
import { log } from '@/lib/logger';

/**
 * Event limit configuration
 */
const EVENT_LIMITS = {
  MAX_UPCOMING_EVENTS: 3,
  MAX_TOTAL_EVENTS: 6
};

/**
 * Checks if the user has reached their event creation limits
 * - Maximum of 3 upcoming events (excluding managed events)
 * - Maximum of 6 total events (excluding managed events)
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authConfig);
    
    if (!session?.user?.id) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    log('Checking event limits for user: ' + session.user.id);
    const db = Database.getInstance();
    
    // Fetch events owned by the user by account ID
    const ownedEventsByAccountId = await db.ListData('events', {
      field: 'ownerAccountId',
      operator: '==',
      value: session.user.id
    }) as Event[];
    
    // For backward compatibility, also fetch events owned by email
    const ownedEventsByEmail = await db.ListData('events', {
      field: 'ownerEmail',
      operator: '==',
      value: session.user.email
    }) as Event[];
    
    // Combine both sets, ensuring no duplicates
    const ownedEvents = [...ownedEventsByAccountId];
    ownedEventsByEmail.forEach(event => {
      if (!ownedEvents.some(e => e.ID === event.ID)) {
        ownedEvents.push(event);
      }
    });

    // Count upcoming events (event date is in the future)
    const now = new Date();
    const upcomingEventsCount = ownedEvents.filter(event => {
      const eventDate = new Date(event.eventDate);
      return eventDate > now;
    }).length;

    // Total events count (both past and upcoming, owned by the user)
    const totalEventsCount = ownedEvents.length;

    // Check if user has reached event limits
    const hasReachedUpcomingLimit = upcomingEventsCount >= EVENT_LIMITS.MAX_UPCOMING_EVENTS;
    const hasReachedTotalLimit = totalEventsCount >= EVENT_LIMITS.MAX_TOTAL_EVENTS;
    const canCreateEvent = !hasReachedUpcomingLimit && !hasReachedTotalLimit;

    return res.status(200).json({
      canCreateEvent,
      hasReachedUpcomingLimit,
      hasReachedTotalLimit,
      limits: {
        upcomingEvents: EVENT_LIMITS.MAX_UPCOMING_EVENTS,
        totalEvents: EVENT_LIMITS.MAX_TOTAL_EVENTS
      },
      counts: {
        upcomingEvents: upcomingEventsCount,
        totalEvents: totalEventsCount
      }
    });
  } catch (error) {
    console.error('Error checking event limits:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}