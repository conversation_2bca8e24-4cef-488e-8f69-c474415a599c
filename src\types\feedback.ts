/**
 * Types related to feedback functionality
 */

// Status options for feedback
export type FeedbackStatus = 'new' | 'in-triage' | 'in-progress' | 'resolved' | 'rejected';

// Comment on a feedback item
export interface FeedbackComment {
  id: string;
  feedbackId: string;
  userId: string;
  userName: string;
  userEmail: string;
  createdAt: string;
  comment: string;
  statusChange?: {
    from: FeedbackStatus;
    to: FeedbackStatus;
  };
}

// Session data collected with feedback
export interface FeedbackSessionData {
  sessionStartTime: number;
  submittedAt: number;
  currentPage: string;
  referrer: string;
  pagesVisited: string[];
  timeOnPage: number;
  isLoggedIn: boolean;
  userId: string | null;
  userEmail: string | null;
  userRole: 'admin' | 'user' | 'guest';
  deviceType: string;
  browser: string;
  os: string;
  userAgent: string;
  formData: {
    related: string;
    experience: string;
    feedbackType: string;
    details: string;
    email: string;
  };
}

// Form data submitted with feedback
export interface FeedbackFormData {
  related: string;
  experience: string;
  feedbackType: string;
  details: string;
  email: string;
}

// Feedback item structure
export interface Feedback {
  id: string;
  message: string;
  rating: number;
  category: string;
  source: string;
  page: string;
  userId?: string;
  userEmail?: string;
  sessionData?: FeedbackSessionData;
  createdAt: string;
  lastUpdated?: string;
  status: FeedbackStatus;
  recaptchaScore: number;
  comments?: FeedbackComment[];
}

// Response from the feedback list API
export interface FeedbackListResponse {
  success: boolean;
  feedback: Feedback[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

// Response from the feedback detail API
export interface FeedbackDetailResponse {
  success: boolean;
  feedback: Feedback & {
    comments: FeedbackComment[];
  };
}

// Status badge colors map
export const feedbackStatusColors: Record<FeedbackStatus, string> = {
  'new': 'bg-blue-100 text-blue-800',
  'in-triage': 'bg-yellow-100 text-yellow-800',
  'in-progress': 'bg-purple-100 text-purple-800',
  'resolved': 'bg-green-100 text-green-800',
  'rejected': 'bg-red-100 text-red-800',
};

// Format status for display (capitalize, replace hyphens with spaces)
export function formatFeedbackStatus(status: FeedbackStatus): string {
  return status
    .replace(/-/g, ' ')
    .replace(/\b\w/g, char => char.toUpperCase());
}
