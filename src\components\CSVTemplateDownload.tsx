"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download } from "lucide-react"

export default function CSVTemplateDownload() {
  const handleDownload = () => {
    const csvContent =
      "Name,Group,Adults,Children,Email,Phone\nSmith Family,Family,2,1,<EMAIL>,************\n<PERSON><PERSON><PERSON>,Friends,1,0,<EMAIL>,************"
    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = "invites-template.csv"
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <Button variant="outline" size="sm" onClick={handleDownload}>
      <Download className="h-4 w-4 mr-2" />
      Download CSV Template
    </Button>
  )
}

