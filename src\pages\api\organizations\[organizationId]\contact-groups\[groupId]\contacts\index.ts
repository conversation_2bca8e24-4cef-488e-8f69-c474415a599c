import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { organizationId, groupId } = req.query;

  if (!organizationId || !groupId) {
    return res.status(400).json({ error: 'Missing required parameters' });
  }

  // Check user session
  const session = await getServerSession(req, res, authConfig);
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  const db = Database.getInstance();

  try {
    // Check organization membership
    const organization = await db.getOrganizationById(organizationId as string);
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const isMember = organization.members?.some(member => member.userId === session.user.id);
    if (!isMember) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get the contact group
    const { getSavedContactGroups, updateSavedContactGroup } = await import('@/lib/saved-contact-groups');
    const contactGroups = await getSavedContactGroups(organizationId as string);
    const group = contactGroups.find(g => g.id === groupId);

    if (!group) {
      return res.status(404).json({ error: 'Contact group not found' });
    }

    if (req.method === 'POST') {
      // Add new contact to group
      const { name, email, phone } = req.body;

      // Validate input - at least one field is required
      if (!name && !email && !phone) {
        return res.status(400).json({ error: 'At least one contact field (name, email, or phone) is required' });
      }

      // Create new contact object
      const newContact = {
        name: name?.trim() || '',
        email: email?.trim() || '',
        phone: phone?.trim() || ''
      };

      // Check for duplicates (same email or phone)
      const isDuplicate = group.contacts.some(contact => {
        if (newContact.email && contact.email === newContact.email) return true;
        if (newContact.phone && contact.phone === newContact.phone) return true;
        return false;
      });

      if (isDuplicate) {
        return res.status(400).json({ 
          error: 'A contact with this email or phone number already exists in the group' 
        });
      }

      // Add contact to group
      const updatedContacts = [...group.contacts, newContact];

      // Update the group
      await updateSavedContactGroup(groupId as string, {
        contacts: updatedContacts,
        updatedAt: new Date().toISOString()
      });

      return res.status(201).json({
        success: true,
        message: 'Contact added to group successfully',
        newContact,
        totalContacts: updatedContacts.length
      });

    } else if (req.method === 'GET') {
      // Get all contacts in the group
      return res.status(200).json({
        success: true,
        contacts: group.contacts,
        totalContacts: group.contacts.length
      });

    } else {
      res.setHeader('Allow', ['POST', 'GET']);
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Error managing group contacts:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
