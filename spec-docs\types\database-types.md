# Database Types - IAC Universe v1

This document catalogues all types that represent actual data structures stored in the database.

## Core User & Account Types

### UserProfile
**File**: `src/types/index.ts`
**Storage**: Firestore `users` collection

```typescript
interface UserProfile {
  id: string;
  name?: string | null;
  email: string;
  image?: string | null;
  originalImage?: string | null;
  isProfileComplete: boolean;
  googleId?: string;
  googleEmail?: string;
  hasGoogleLinked?: boolean;
  isWelcomeEmailSent: boolean;
  isAdmin?: boolean;
  hasAiAccess?: boolean;
  createdOn?: string | Date;
  lastUpdatedOn?: string | Date;
  organizationId?: string;
  passwordHash?: string;
  resetPasswordToken?: string;
  resetPasswordExpires?: number;
}
```

**Key Features**:
- Primary user entity with Google OAuth integration
- Support for email/password authentication
- Organization membership tracking
- Admin and AI access controls
- Profile completion status

### Organization
**File**: `src/types/index.ts`
**Storage**: Firestore `organizations` collection

```typescript
interface Organization {
  id: string;
  name: string;
  type: 'individual' | 'partner';
  members: Array<{
    userId: string;
    role: 'owner' | 'member';
  }>;
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
  contactGroupSettings?: {
    hasBeenAsked: boolean;
  };
  overrides?: {
    AI_GEN_LIMIT?: number;
    [key: string]: any;
  };
}
```

**Key Features**:
- Multi-tenant organization structure
- Member role management
- Partner vs individual organization types
- AI usage override capabilities

## Event Management Types

### Event
**File**: `src/types/index.ts`
**Storage**: Firestore `events` collection

```typescript
interface Event {
  ID?: string;
  eventName: string;
  eventDate: Date;
  start: string;
  end: string;
  location: string;
  timezone?: string;
  rsvpDueDate?: Date;
  message: string;
  host: string;
  ownerAccountId: string;
  ownerEmail?: string; // deprecated
  managers: string[];
  plan?: 'free' | 'host_plus' | 'host_pro';
  status?: 'pending_payment' | 'active';
  paymentStatus?: 'pending' | 'paid' | 'failed';
  paymentId?: string;
  printSettings?: PrintSettings;
  organizationId: string;
  aiUsageCount?: number;
  overrides?: {
    AI_GEN_LIMIT?: number;
    [key: string]: any;
  };
  // Partner-specific fields
  createdByPartnerId?: string;
  venueId?: string;
  locationId?: string;
  maxInvites?: number;
  pricePerAdditionalInvite?: number;
}
```

**Key Features**:
- Complete event lifecycle management
- Partner venue integration
- Payment tracking
- AI usage monitoring
- Manager delegation system
- Print settings embedded

### EventInvite
**File**: `src/types/index.ts`
**Storage**: Firestore `events/{eventId}/invites` subcollection

```typescript
interface EventInvite {
  ID?: string;
  name: string;
  eventId: string;
  email?: string;
  phone?: string;
  group?: string;
  status: InviteStatus; // "invited" | "accepted" | "declined"
  createdAt?: string;
  updatedAt?: string;
  adults: number;
  children: number;
  message: Message[];
  response?: {
    adults: number;
    children: number;
    timestamp: string;
    message?: string;
  } | null;
  activityHistory?: ActivityHistoryItem[];
}
```

**Key Features**:
- Comprehensive invite management
- Adult/children count tracking
- Real-time messaging between guests and hosts
- Activity history for analytics
- RSVP response tracking

### Message
**File**: `src/types/index.ts**
**Storage**: Embedded in `EventInvite.message` array

```typescript
interface Message {
  id: string;
  content: string;
  sender: 'guest' | 'host';
  timestamp: Date;
}
```

## Partner & Venue Types

### Venue
**File**: `src/types/index.ts`
**Storage**: Firestore `venues` collection

```typescript
interface Venue {
  id: string;
  name: string;
  address: string;
  description?: string;
  capacity: number;
  organizationId: string;
  locations: VenueLocation[];
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
}
```

### VenueLocation
**File**: `src/types/index.ts`
**Storage**: Embedded in `Venue.locations` array

```typescript
interface VenueLocation {
  id: string;
  name: string;
  description?: string;
  venueId: string;
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
}
```

### TimeSlot
**File**: `src/types/index.ts`
**Storage**: Firestore `timeSlots` collection

```typescript
interface TimeSlot {
  id: string;
  venueId: string;
  locationId: string;
  date: Date;
  startTime: string;
  endTime: string;
  isBooked: boolean;
  eventId?: string;
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
}
```

### PartnerInvoice
**File**: `src/types/index.ts`
**Storage**: Firestore `partnerInvoices` collection

```typescript
interface PartnerInvoice {
  id: string;
  organizationId: string;
  period: {
    start: Date;
    end: Date;
  };
  events: string[];
  amount: number;
  status: 'pending' | 'paid' | 'overdue';
  paymentMethod?: 'card' | 'bank_transfer';
  paymentDate?: Date;
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
}
```

## Contact & Communication Types

### SavedContactGroup
**File**: `src/types/index.ts`
**Storage**: Firestore `savedContactGroups` collection

```typescript
interface SavedContactGroup {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  contacts: Array<{
    email: string;
    name?: string;
    phone?: string;
  }>;
  createdAt: string | Date;
  updatedAt: string | Date;
  createdFromEventId: string;
  isActive?: boolean;
}
```

**Key Features**:
- Reusable contact groups across events
- Organization-scoped storage
- Contact information with optional fields
- Event origin tracking

## Authentication & Authorization Database Types

### OrganizationScopedRole
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Storage**: Firestore `userOrganizationRoles` collection

```typescript
interface OrganizationScopedRole {
  id: string;
  userId: string;
  organizationId: string;
  role: Role;
  assignedBy: string;
  assignedAt: string;
  expiresAt?: string;
  isActive: boolean;
  context?: {
    venueIds?: string[];
    limitations?: string[];
    metadata?: Record<string, any>;
  };
}
```

### UserOrganizationRoleDocument
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Storage**: Firestore `userOrganizationRoles` collection

```typescript
interface UserOrganizationRoleDocument {
  userId: string;
  organizationId: string;
  role: string;
  assignedBy: string;
  assignedAt: string;
  expiresAt?: string;
  isActive: boolean;
  venueRestrictions?: string[];
  permissionsMetadata?: Record<string, any>;
}
```

### UserActiveOrganizationDocument
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Storage**: Firestore `userActiveOrganization` collection

```typescript
interface UserActiveOrganizationDocument {
  userId: string;
  organizationId: string;
  updatedAt: string;
}
```

### OrganizationDocument
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Storage**: Firestore `organizations` collection

```typescript
interface OrganizationDocument {
  id: string;
  name: string;
  type: 'partner' | 'admin';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  settings?: Record<string, any>;
}
```

## Audit & Tracking Types

### PermissionAuditLog
**File**: `src/lib/auth/RBAC/types.ts`
**Storage**: Firestore `permissionAuditLogs` collection

```typescript
interface PermissionAuditLog {
  id: string;
  action: 'grant' | 'revoke' | 'check';
  userId: string;
  targetUserId?: string;
  role: string;
  resource: string;
  permission: string;
  granted: boolean;
  context?: PermissionContext;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
}
```

### OrganizationRoleAuditLog
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Storage**: Firestore `organizationRoleAudit` collection

```typescript
interface OrganizationRoleAuditLog {
  id: string;
  action: 'assign' | 'revoke' | 'modify' | 'expire';
  userId: string;
  organizationId: string;
  role: Role;
  performedBy: string;
  performedAt: string;
  previousRole?: Role;
  newRole?: Role;
  reason?: string;
  metadata?: Record<string, any>;
}
```

### ActivityHistoryItem
**File**: `src/types/index.ts`
**Storage**: Embedded in `EventInvite.activityHistory` array

```typescript
interface ActivityHistoryItem {
  type: string; // 'invite_link_opened', 'rsvp_response', etc.
  timestamp: string;
  userId?: string | null;
  properties?: Record<string, any>;
}
```

## Payment & Billing Types

### PaymentHistoryItem
**File**: `src/types/index.ts`
**Storage**: Firestore `paymentHistory` collection

```typescript
interface PaymentHistoryItem {
  id: string;
  date: string;
  amount: number;
  description: string;
  status: string;
  paymentMethod: string;
  eventId: string;
  userId?: string;
  plan: string;
  receiptUrl?: string | null;
  invoiceUrl?: string | null;
}
```

## Feedback System Types

### Feedback
**File**: `src/types/feedback.ts`
**Storage**: Firestore `feedback` collection

```typescript
interface Feedback {
  id: string;
  message: string;
  rating: number;
  category: string;
  source: string;
  page: string;
  userId?: string;
  userEmail?: string;
  sessionData?: FeedbackSessionData;
  createdAt: string;
  lastUpdated?: string;
  status: FeedbackStatus;
  recaptchaScore: number;
  comments?: FeedbackComment[];
}
```

### FeedbackComment
**File**: `src/types/feedback.ts`
**Storage**: Embedded in `Feedback.comments` array

```typescript
interface FeedbackComment {
  id: string;
  feedbackId: string;
  userId: string;
  userName: string;
  userEmail: string;
  createdAt: string;
  comment: string;
  statusChange?: {
    from: FeedbackStatus;
    to: FeedbackStatus;
  };
}
```

## Database Collections Reference

| Collection | Type | Purpose |
|------------|------|---------|
| `users` | UserProfile | User accounts and profiles |
| `organizations` | Organization | Multi-tenant organizations |
| `events` | Event | Event entities |
| `events/{eventId}/invites` | EventInvite | Event invitations |
| `venues` | Venue | Partner venue information |
| `timeSlots` | TimeSlot | Venue booking slots |
| `partnerInvoices` | PartnerInvoice | Partner billing |
| `savedContactGroups` | SavedContactGroup | Reusable contact lists |
| `userOrganizationRoles` | OrganizationScopedRole | RBAC role assignments |
| `userActiveOrganization` | UserActiveOrganizationDocument | Active org tracking |
| `permissionAuditLogs` | PermissionAuditLog | Permission audit trail |
| `organizationRoleAudit` | OrganizationRoleAuditLog | Role change audit |
| `paymentHistory` | PaymentHistoryItem | Payment transactions |
| `feedback` | Feedback | User feedback system |

## Migration Considerations

When migrating to the new universe/packages/types system:

1. **Preserve Database Structure**: All v1 database types must be supported for data compatibility
2. **Field Mapping**: Optional fields should remain optional for backward compatibility  
3. **Embedded Objects**: Arrays and nested objects should maintain structure
4. **Date Handling**: Support both string and Date types during transition
5. **Organization Context**: Maintain organization-scoped data access patterns
6. **RBAC System**: Preserve complex permission and role structures