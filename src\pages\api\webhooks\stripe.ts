import { NextApiRequest, NextApiResponse } from 'next'
import Stripe from 'stripe'
import { Database } from '@/lib/database'
import { buffer } from 'micro'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia'
})

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

// This disables the automatic body parsing for this specific route
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const sig = req.headers['stripe-signature']

  if (!sig) {
    return res.status(400).json({ error: 'No signature' })
  }

  // Get the raw request body as a buffer
  const rawBody = await buffer(req);
  
  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      rawBody,
      sig,
      webhookSecret
    )
  } catch (err) {
    console.error('Webhook signature verification failed:', err)
    return res.status(400).json({ error: 'Invalid signature' })
  }

  try {
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session
      const { eventId, plan } = session.metadata!

      // Update event with the paid plan
      const db = Database.getInstance()
      await db.updateData('events', eventId, {
        plan,
        status: 'active',
        paymentStatus: 'paid',
        paymentId: session.payment_intent as string
      })
      await db.addData('payments', session)
    }

    return res.status(200).json({ received: true })
  } catch (error) {
    console.error('Webhook processing error:', error)
    return res.status(500).json({ error: 'Webhook processing failed' })
  }
}