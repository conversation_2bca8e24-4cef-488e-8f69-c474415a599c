import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { SavedContactGroup } from '@/types';
import { Users, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { GroupSelector } from '@/components/GroupSelector';
import { SendInviteEmailsDialog } from '@/components/SendInviteEmailsDialog';



interface SavedContactGroupsSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  eventId: string;
  existingInvites?: any[]; // Pass existing invites from parent
  onImportComplete: () => void;
}

export function SavedContactGroupsSheet({
  open,
  onOpenChange,
  eventId,
  existingInvites = [],
  onImportComplete
}: SavedContactGroupsSheetProps) {
  const [contactGroups, setContactGroups] = useState<SavedContactGroup[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [importing, setImporting] = useState(false);
  const [freshExistingInvites, setFreshExistingInvites] = useState<any[]>([]);
  const [importedInvites, setImportedInvites] = useState<any[]>([]);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const { toast } = useToast();

  // Fetch fresh existing invites when sheet opens to ensure accurate duplicate checking
  const fetchFreshInvites = async () => {
    try {
      const response = await fetch(`/api/event/${eventId}/invites`);
      if (response.ok) {
        const data = await response.json();
        console.log('Fresh invites fetched:', data.invites?.length || 0, 'invites');
        console.log('Fresh invites data:', data.invites);
        setFreshExistingInvites(data.invites || []);
      } else {
        console.log('Failed to fetch fresh invites, using passed data:', existingInvites?.length || 0, 'invites');
        // Fallback to passed existingInvites if API call fails
        setFreshExistingInvites(existingInvites);
      }
    } catch (error) {
      console.error('Error fetching fresh invites:', error);
      console.log('Using passed existingInvites as fallback:', existingInvites?.length || 0, 'invites');
      // Fallback to passed existingInvites if API call fails
      setFreshExistingInvites(existingInvites);
    }
  };

  // Fetch saved contact groups when sheet opens
  useEffect(() => {
    if (open) {
      fetchContactGroups();
      fetchFreshInvites();
    }
  }, [open]);

  // Initialize fresh invites with passed data as fallback
  useEffect(() => {
    if (existingInvites.length > 0 && freshExistingInvites.length === 0) {
      setFreshExistingInvites(existingInvites);
    }
  }, [existingInvites, freshExistingInvites.length]);

  const fetchContactGroups = async () => {
    setLoading(true);
    try {
      // Get user's organization first
      const orgResponse = await fetch('/api/user/organization');
      if (!orgResponse.ok) {
        const orgError = await orgResponse.json();
        throw new Error(orgError.error || 'Failed to get organization');
      }
      const orgData = await orgResponse.json();

      // Fetch contact groups for the organization
      const response = await fetch(`/api/organizations/${orgData.organization.id}/contact-groups`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch contact groups');
      }

      const data = await response.json();
      setContactGroups(data.contactGroups || []);
    } catch (error) {
      console.error('Error fetching contact groups:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load saved contact groups. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };



  const handleImport = async () => {
    if (selectedContacts.length === 0) {
      toast({
        title: "No contacts selected",
        description: "Please select at least one contact to import.",
        variant: "destructive",
      });
      return;
    }

    // Convert selected contact IDs back to actual contacts
    const contactsToImport = selectedContacts.map(contactId => {
      const [groupId, contactIndex] = contactId.split('-');
      const group = contactGroups.find(g => g.id === groupId);
      if (group && group.contacts[parseInt(contactIndex)]) {
        return {
          ...group.contacts[parseInt(contactIndex)],
          groupId: groupId,
          groupName: group.name
        };
      }
      return null;
    }).filter(Boolean);

    setImporting(true);
    try {
      const response = await fetch(`/api/event/${eventId}/invites/import-contacts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contacts: contactsToImport
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to import contact groups');
      }

      const data = await response.json();

      // Get unique group names from the imported contacts
      const uniqueGroups = new Set();
      contactsToImport.forEach(contact => {
        if (contact && contact.groupName) {
          uniqueGroups.add(contact.groupName);
        }
      });
      
      let description = `Added ${data.importedCount} contact${data.importedCount !== 1 ? 's' : ''}.`;
      if (data.skippedCount > 0) {
        description += ` ${data.skippedCount} duplicate contact${data.skippedCount !== 1 ? 's' : ''} were skipped.`;
      }

      // Count how many imported invites have email addresses
      const invitesWithEmail = data.invites?.filter((invite: any) => invite.email) || [];      toast({
        title: "Contact groups imported successfully!",
        description,
      });

      // Store the imported invites for the email dialog
      if (data.invites && data.invites.length > 0) {
        setImportedInvites(data.invites);
        setShowEmailDialog(true);
      } else {
        // Reset state and close sheet if no invites to send emails to
        setSelectedContacts([]);
        onOpenChange(false);
        onImportComplete();
      }

    } catch (error) {
      console.error('Error importing contact groups:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to import contact groups. Please try again.",
        variant: "destructive",
      });
    } finally {
      setImporting(false);
    }
  };



  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="grid grid-rows-[auto_1fr] w-full sm:max-w-lg">
        <SheetHeader className="">
          <SheetTitle className="text-2xl font-semibold">Add Saved Contact Groups</SheetTitle>
          <SheetDescription>
            Select contact groups to add to your event. All contacts from selected groups will be added as invites.
          </SheetDescription>
        </SheetHeader>

        <div className="grid grid-rows-[auto_1fr_auto] px-4 pb-4 bg-white shadow-md rounded-lg gap-1 -mt-3">
          <div></div> {/* Empty div to match the tabs section in ExportInvites */}

          <div className="min-h-0">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading contact groups...</span>
              </div>
            ) : contactGroups.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No saved contact groups</h3>
                <p className="text-muted-foreground text-sm">
                  You don&apos;t have any saved contact groups yet. Complete an event to save contact groups for future use.
                </p>
              </div>
            ) : (
              <GroupSelector
                contactGroups={contactGroups}
                selectedContacts={selectedContacts}
                onChange={setSelectedContacts}
                existingInvites={freshExistingInvites}
              />
            )}
          </div>

          <Button
            onClick={handleImport}
            disabled={selectedContacts.length === 0 || importing}
            variant="primary-button"
            className="w-full"
          >            {importing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Importing...
              </>
            ) : (
              `Import ${selectedContacts.length} Contact${selectedContacts.length !== 1 ? 's' : ''}`
            )}          </Button>
        </div>
      </SheetContent>

      {/* Email Dialog */}
      <SendInviteEmailsDialog
        open={showEmailDialog}
        onOpenChange={(open) => {
          setShowEmailDialog(open);
          if (!open) {
            // Reset state and close sheet when email dialog is closed
            setSelectedContacts([]);
            onOpenChange(false);
            onImportComplete();
          }
        }}
        importedInvites={importedInvites}
        eventId={eventId}
        importType="groups"
      />
    </Sheet>
  );
}
