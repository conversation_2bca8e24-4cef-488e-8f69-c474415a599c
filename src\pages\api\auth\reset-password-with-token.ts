import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { validatePassword } from '@/lib/auth/password';
import { z } from 'zod';
import { debugLog } from '@/lib/logger';

// Define validation schema for password reset request
const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Validate request body
    const validationResult = resetPasswordSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: validationResult.error.errors 
      });
    }

    const { token, password } = validationResult.data;

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.success) {
      return res.status(400).json({ error: passwordValidation.error });
    }

    // Get database instance
    const db = Database.getInstance();

    // Verify the token and reset the password
    const success = await db.resetPasswordWithToken(token, password);

    if (!success) {
      return res.status(400).json({ error: 'Invalid or expired token' });
    }

    debugLog('Password reset successfully with token');

    // Return success response
    return res.status(200).json({ 
      success: true, 
      message: 'Password reset successfully' 
    });
  } catch (error) {
    console.error('Error in reset password with token handler:', error);
    return res.status(500).json({ error: 'Failed to reset password' });
  }
}
