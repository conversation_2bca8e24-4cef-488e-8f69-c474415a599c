import { log } from './logger';
import { Database } from './database';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { NextApiRequest, NextApiResponse } from 'next';
import { ActivityHistoryItem } from '@/types';

// Add Google Analytics integration
import { logEvent } from '@/components/GoogleAnalytics';
import { UTMParams, extractUtmParams } from './utm';

/**
 * Analytics event data structure
 */
export interface AnalyticsEvent {
  eventName: string;
  userId: string | null;
  userType: 'host' | 'event_manager' | 'guest' | 'anonymous';
  timestamp: Date;
  properties: {
    eventId?: string;
    inviteId?: string;
    planType?: 'free' | 'paid';
    source?: string;
    // UTM parameters
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_term?: string;
    utm_content?: string;
    // Additional event-specific properties
    [key: string]: any;
  };
}

/**
 * Analytics service for tracking user interactions
 */
export class Analytics {
  private static instance: Analytics;

  private constructor() {}

  /**
   * Get the singleton instance of Analytics
   */
  public static getInstance(): Analytics {
    if (!Analytics.instance) {
      Analytics.instance = new Analytics();
    }
    return Analytics.instance;
  }

  /**
   * Track an analytics event
   * @param event The event to track
   */
  public async trackEvent(event: AnalyticsEvent): Promise<void> {
    try {
      // Store event in database
      await Database.getInstance().addData('analytics', {
        ...event,
        timestamp: event.timestamp.toISOString(),
      });

      // Also send to Google Analytics if available
      if (typeof window !== 'undefined') {
        logEvent(
          event.eventName,
          event.userType,
          event.userId || 'anonymous',
          // Pass first numeric property as value if available
          Object.values(event.properties).find(val => typeof val === 'number') as number | undefined
        );
      }

      log(`Analytics event tracked: ${event.eventName}`, event);
    } catch (error) {
      console.error('Error tracking analytics event:', error);
    }
  }

  /**
   * Get user information from session for analytics
   * @param req The Next.js API request object
   * @param res The Next.js API response object
   * @returns User info including id and type
   */
  public async getUserInfoFromRequest(req: NextApiRequest, res: NextApiResponse): Promise<{
    userId: string | null,
    userType: 'host' | 'event_manager' | 'guest' | 'anonymous'
  }> {
    try {
      const session = await getServerSession(req, res, authConfig);

      if (session?.user?.id) {
        return {
          userId: session.user.id,
          userType: 'host', // Assume logged in users are hosts by default
        };
      }

      return {
        userId: null,
        userType: 'anonymous',
      };
    } catch (error) {
      console.error('Error getting user info for analytics:', error);
      return {
        userId: null,
        userType: 'anonymous',
      };
    }
  }

  /**
   * Extract UTM parameters from a request
   * @param req The Next.js API request object
   * @returns UTM parameters object
   */
  public extractUtmParamsFromRequest(req: NextApiRequest): UTMParams {
    try {
      // Check for UTM parameters in query string
      const utmParams: UTMParams = {};

      // Extract UTM parameters from query
      if (req.query.utm_source) utmParams.utm_source = req.query.utm_source as string;
      if (req.query.utm_medium) utmParams.utm_medium = req.query.utm_medium as string;
      if (req.query.utm_campaign) utmParams.utm_campaign = req.query.utm_campaign as string;
      if (req.query.utm_term) utmParams.utm_term = req.query.utm_term as string;
      if (req.query.utm_content) utmParams.utm_content = req.query.utm_content as string;

      // Check for referer header to potentially extract UTM parameters
      const referer = req.headers.referer;
      if (referer && Object.keys(utmParams).length === 0) {
        const refererUtmParams = extractUtmParams(referer);
        Object.assign(utmParams, refererUtmParams);
      }

      return utmParams;
    } catch (error) {
      console.error('Error extracting UTM parameters:', error);
      return {};
    }
  }

  /**
   * Track an invite link open event
   * Always track in activity history, but only track in analytics if not owner/manager
   */
  public async trackInviteLinkOpen(
    eventId: string,
    inviteId: string,
    userId: string | null,
    isEventOwner: boolean,
    isEventManager: boolean,
    utmParams?: UTMParams
  ): Promise<void> {
    const timestamp = new Date();
    const timestampStr = timestamp.toISOString();

    // Only track in analytics if not owner/manager
    if (!(userId && (isEventOwner || isEventManager))) {
      await this.trackEvent({
        eventName: 'invite_link_opened',
        userId,
        userType: userId ? 'host' : 'guest',
        timestamp,
        properties: {
          eventId,
          inviteId,
          source: utmParams?.utm_source || 'rsvp_link',
          // Include all UTM parameters if available
          ...(utmParams || {})
        }
      });
    } else {
      log('Not tracking invite link open in analytics for event owner/manager', {
        eventId,
        inviteId,
        userId,
        utmParams
      });
    }

    // Always update the invite with a record of this open in the activity history
    try {
      const invite = await Database.getInstance().readData('invites', inviteId);
      if (invite) {
        // Check if we've already recorded an open in the last 30 seconds to prevent duplicates from component re-renders
        const thirtySecondsAgo = new Date(timestamp.getTime() - 30 * 1000).toISOString();
        const recentOpens = (invite.activityHistory || []).filter((item: ActivityHistoryItem) =>
          item.type === 'invite_link_opened' &&
          item.timestamp > thirtySecondsAgo &&
          item.userId === (userId || 'anonymous')
        );

        // If we've already recorded an open in the last 30 seconds, don't record another one
        if (recentOpens.length > 0) {
          log('Skipping duplicate invite link open record (within 30 seconds)', {
            eventId,
            inviteId,
            userId,
            recentOpen: recentOpens[0].timestamp
          });
          return;
        }

        // Create activity history item
        const activityItem: ActivityHistoryItem = {
          type: 'invite_link_opened',
          timestamp: timestampStr,
          userId: userId || 'anonymous',
          properties: {
            source: utmParams?.utm_source || 'rsvp_link',
            // Include all UTM parameters if available
            ...(utmParams || {})
          }
        };

        // Add to activity history array
        const activityHistory = invite.activityHistory || [];
        activityHistory.push(activityItem);

        // Only update the activityHistory array, not the legacy openHistory array
        await Database.getInstance().updateData('invites', inviteId, {
          activityHistory
        });

        log('Successfully recorded invite link open', {
          eventId,
          inviteId,
          userId
        });
      }
    } catch (error) {
      console.error('Error updating invite activity history:', error);
    }
  }

  /**
   * Track an RSVP initiation event
   */
  public async trackRSVPInitiated(
    eventId: string,
    inviteId: string,
    userId: string | null
  ): Promise<void> {
    await this.trackEvent({
      eventName: 'rsvp_initiated',
      userId,
      userType: userId ? 'host' : 'guest',
      timestamp: new Date(),
      properties: {
        eventId,
        inviteId
      }
    });
  }

  /**
   * Track an RSVP form submission event
   * Note: This only tracks the analytics event and does not update the activityHistory array
   * The activityHistory array is updated in the RSVP endpoint itself
   */
  public async trackRSVPSubmitted(
    eventId: string,
    inviteId: string,
    userId: string | null,
    status: 'accepted' | 'declined',
    adults: number,
    children: number
  ): Promise<void> {
    // Only track the analytics event, don't update the activityHistory array
    // to avoid duplicate entries (the RSVP endpoint already updates activityHistory)
    await this.trackEvent({
      eventName: 'rsvp_form_submitted',
      userId,
      userType: userId ? 'host' : 'guest',
      timestamp: new Date(),
      properties: {
        eventId,
        inviteId,
        status,
        adults,
        children
      }
    });
  }

  /**
   * Track event creation events
   */
  public async trackEventCreationStarted(userId: string): Promise<void> {
    await this.trackEvent({
      eventName: 'event_creation_started',
      userId,
      userType: 'host',
      timestamp: new Date(),
      properties: {}
    });
  }

  public async trackEventCreationCompleted(userId: string, eventId: string, planType: 'free' | 'paid'): Promise<void> {
    await this.trackEvent({
      eventName: 'event_creation_completed',
      userId,
      userType: 'host',
      timestamp: new Date(),
      properties: {
        eventId,
        planType
      }
    });
  }

  /**
   * Track adding to calendar event
   */
  public async trackAddToCalendar(eventId: string, inviteId: string, userId: string | null): Promise<void> {
    await this.trackEvent({
      eventName: 'add_to_calendar_clicked',
      userId,
      userType: userId ? 'host' : 'guest',
      timestamp: new Date(),
      properties: {
        eventId,
        inviteId
      }
    });
  }
}

export default Analytics.getInstance();