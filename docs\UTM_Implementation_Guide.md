# UTM Parameter Implementation Guide

This document outlines the implementation of UTM parameter tracking for Google Analytics in the iamcoming.io application.

## Overview

UTM parameters are tracking parameters added to URLs to help identify traffic sources, marketing campaigns, and user journeys. The standard UTM parameters are:

- **utm_source**: Where traffic comes from (e.g., facebook, whatsapp, qr)
- **utm_medium**: How it was shared (e.g., post, dm, ad, print)
- **utm_campaign**: Campaign/event name (e.g., beta_launch, johns_wedding)
- **utm_term**: Optional, for ad keywords
- **utm_content**: Optional, for A/B variants

## Implementation Details

### 1. UTM Parameter Utility (src/lib/utm.ts)

A new utility file has been created to handle UTM parameter generation and parsing:

- `addUtmParams(url, params)`: Adds UTM parameters to a URL
- `extractUtmParams(urlOrSearch)`: Extracts UTM parameters from a URL or search string
- `generateUtmParams(source, medium, campaign, additionalParams)`: Generates UTM parameters for different sharing methods
- `generateQrUtmParams(eventId, content)`: Generates UTM parameters for QR codes
- `generateEmailUtmParams(eventId, content)`: Generates UTM parameters for email sharing
- `generateSocialUtmParams(platform, eventId, content)`: Generates UTM parameters for social sharing

### 2. URL Generation Functions (src/lib/utils.ts)

The link generation functions have been updated to include UTM parameters:

- `generateRsvpLink(invite, utmParams)`: Generates an RSVP link with optional UTM parameters
- `generateQrRsvpLink(invite, content)`: Generates a QR code RSVP link with QR-specific UTM parameters
- `generateDownloadInviteLink(invite, printingPreferences, utmParams)`: Generates a download link with optional UTM parameters

### 3. Analytics Interface (src/lib/analytics.ts)

The analytics interface has been updated to include UTM parameters:

- Added UTM parameter fields to the `AnalyticsEvent` interface
- Added `extractUtmParamsFromRequest(req)` method to extract UTM parameters from requests
- Updated `trackInviteLinkOpen` to include UTM parameters in tracking events
- Updated activity history items to include UTM parameters

### 4. Google Analytics Component (src/components/GoogleAnalytics.tsx)

The Google Analytics component has been enhanced to better handle UTM parameters:

- Updated page view tracking to include UTM parameters
- Enhanced event tracking to include UTM parameters from the current URL
- Added support for passing additional parameters to events

### 5. QR Code Component (src/components/ui/qrcode.tsx)

The QR code component has been updated to support UTM parameters:

- Added `utmParams` prop to the `QRCode` component
- Updated the component to add UTM parameters to the QR code value

### 6. Email Templates (src/lib/mailer.ts)

The email templates have been updated to include UTM parameters in links:

- Updated `createPersonalization` to generate trackable links with UTM parameters
- Added email type parameter to differentiate between invitation, reminder, confirmation, and welcome emails
- Updated all email sending functions to use the correct email type

### 7. Label Printer (src/components/LabelPrinter.tsx)

The label printer component has been updated to include UTM parameters in QR codes:

- Updated QR code generation to include UTM parameters for tracking
- Added different content identifiers for different types of printed materials

## Usage Examples

### Adding UTM Parameters to Links

```typescript
import { generateUtmParams, addUtmParams } from '@/lib/utm';

// Generate UTM parameters
const utmParams = generateUtmParams('facebook', 'post', 'summer_event');

// Add UTM parameters to a URL
const url = 'https://iamcoming.io/event/123';
const trackableUrl = addUtmParams(url, utmParams);
// Result: https://iamcoming.io/event/123?utm_source=facebook&utm_medium=post&utm_campaign=summer_event
```

### Generating QR Codes with UTM Parameters

```typescript
import { generateQrUtmParams } from '@/lib/utm';
import { QRCode } from '@/components/ui/qrcode';

// Generate UTM parameters for QR code
const utmParams = generateQrUtmParams('event_123', 'printed_invite');

// Use in QR code component
<QRCode 
  value="https://iamcoming.io/event/123/rsvp/456" 
  utmParams={utmParams} 
/>
```

### Tracking Events with UTM Parameters

```typescript
import { logEvent } from '@/components/GoogleAnalytics';

// Track an event with UTM parameters
logEvent(
  'button_click',
  'ui_interaction',
  'share_button',
  1,
  { utm_source: 'email', utm_medium: 'newsletter' }
);
```

## Testing UTM Parameters

To test UTM parameter tracking:

1. Add UTM parameters to a URL: `https://iamcoming.io/event/123?utm_source=test&utm_medium=documentation&utm_campaign=testing`
2. Visit the URL and check Google Analytics to verify the parameters are being tracked
3. Test QR code scanning to ensure UTM parameters are included in the generated links
4. Test email links to ensure UTM parameters are included in the links

## Maintenance

When adding new sharing methods or tracking points:

1. Use the appropriate UTM parameter generation function
2. Ensure UTM parameters are passed to analytics events
3. Update Google Analytics configuration if needed to track new parameters
