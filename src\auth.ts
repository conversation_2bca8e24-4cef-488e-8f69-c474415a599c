import NextAuth from "next-auth"
import { FirestoreAdapter } from "@auth/firebase-adapter"
import { Database } from "./lib/database"
import { AuthVerificationRequest } from "./types"
import EmailProvider from "next-auth/providers/email"
import GoogleProvider, { GoogleProfile } from "next-auth/providers/google"
import CredentialsProvider from "next-auth/providers/credentials"
import { sendLoginEmail, sendUserWelcomeEmail } from "./lib/mailer"
import { Session } from "next-auth"
import { JWT } from "next-auth/jwt"
import { AuthOptions } from "next-auth"
import { SendVerificationRequestParams } from "next-auth/providers/email"
import { getBaseUrl } from "@/lib/utils"
import { debugLog } from "./lib/logger"
import { verifyPassword } from "./lib/auth/password"
import { ALLOWED_SITES, isAllowedSite, type AllowedSite } from "./lib/auth/config"

// Initialize Firebase Admin only on the server side
const getFirestoreAdapter = async () => {
  // Only initialize on the server side
  if (typeof window !== 'undefined') {
    return undefined;
  }

  try {
    const firestore = await Database.getFirestore();
    return FirestoreAdapter(firestore) as any; // Type assertion needed due to adapter version mismatch
  } catch (error) {
    console.error('Failed to initialize Firestore adapter:', error);
    return undefined;
  }
};

// Create auth config
export const authConfig: AuthOptions = {
  providers: [
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: Number(process.env.EMAIL_SERVER_PORT),
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM,
      sendVerificationRequest,
      maxAge: 24 * 60 * 60, // 24 hours
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      allowDangerousEmailAccountLinking: true, // Allow linking accounts with the same email
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const db = Database.getInstance();
          const user = await db.getUserByEmail(credentials.email);

          if (!user || !user.passwordHash) {
            return null;
          }

          const isValid = await verifyPassword(credentials.password, user.passwordHash);

          if (!isValid) {
            return null;
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
          };
        } catch (error) {
          console.error("Error in credentials authorization:", error);
          return null;
        }
      }
    }),
  ],
  adapter: await getFirestoreAdapter(),
  pages: {
    signIn: "/auth/signin",
    signOut: "/auth/sign-out",
    error: "/auth/error",
    verifyRequest: "/auth/verify-request",
  },
  debug: !!process.env.IAC_DEBUG,
  session: {
    strategy: "jwt" as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  cookies: {
    sessionToken: {
      name: 'next-auth.session-token',
      options: {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        domain: process.env.NODE_ENV === 'production' ? '.iamcoming.io' : 'localhost',
      },
    },
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        // Pass data from the user to the token
        token.sub = user.id;

        // Check if user is admin and has AI access, add them to the token
        try {
          const db = Database.getInstance();
          const userProfile = await db.getUserProfile(user.id);

          if (userProfile) {
            token.isAdmin = userProfile.isAdmin || false;
            token.hasAiAccess = userProfile.hasAiAccess || false;
          }
        } catch (error) {
          console.error('Error fetching user status for token:', error);
        }
      }
      return token;
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      if (session.user) {
        // Get user profile from Firestore
        const db = Database.getInstance();
        const userProfile = await db.getUserProfile(token.sub as string);

        if (userProfile) {
          // Update session with profile data
          session.user.id = userProfile.id;
          session.user.name = userProfile.name || null;
          session.user.email = userProfile.email;
          session.user.image = userProfile.image || null;
          session.user.isProfileComplete = userProfile.isProfileComplete;          // Add custom properties to the session object
          session.user.isAdmin = userProfile?.isAdmin || false;
          session.user.hasAiAccess = userProfile?.hasAiAccess || false;

          // Get organization details
          const organization = await db.getOrganizationByUserId(userProfile.id);
          if (organization) {
            session.user.organization = {
              id: organization.id,
              name: organization.name,
              type: organization.type
            };
            console.log("Organization details added to session:", session.user.organization);
          }

          // Check if isWelcomeEmailSent field exists, if not initialize it
          const isWelcomeEmailSent = userProfile.isWelcomeEmailSent !== undefined ?
            userProfile.isWelcomeEmailSent : false;

          // Check if welcome email has been sent or if field is missing
          if (isWelcomeEmailSent === false && session.user.email) {
            try {
              // Send welcome email to the user
              await sendUserWelcomeEmail({
                name: session.user.name || "there",
                email: session.user.email
              });

              // Update the flag in the database
              await db.updateUserProfile(token.sub as string, {
                isWelcomeEmailSent: true
              });

              debugLog(`Welcome email sent to ${session.user.email}`);
            } catch (error) {
              console.error("Error sending welcome email:", error);
            }
          }
        } else {
          // If no profile exists, create one with basic info
          session.user.id = token.sub as string;
          session.user.name = null;
          session.user.email = session.user.email || "";
          session.user.image = null;
          session.user.isProfileComplete = false;

          // Create initial profile in Firestore
          await db.updateUserProfile(token.sub as string, {
            id: token.sub as string,
            email: session.user.email,
            isProfileComplete: false,
            isWelcomeEmailSent: false // Set initial welcome email flag to false
          });
        }
      }
      return session;
    },
    async signIn({ user, account, profile, credentials, ...rest }) {
      debugLog("SignIn callback triggered");
      debugLog("User:", user);
      debugLog("Account:", account);
      debugLog("Profile:", profile);
      debugLog("Credentials:", credentials);
      debugLog("Rest:", rest);

      // Allow email provider
      if (account?.provider === "email") {
        return true;
      }

      // Allow credentials provider (email/password)
      if (account?.provider === "credentials") {
        return true;
      }

      // Handle Google provider
      if (account?.provider === "google" && profile) {
        const googleProfile = profile as GoogleProfile;
        try {
          const db = Database.getInstance();

          // Get the Google ID
          const googleId = googleProfile.sub || googleProfile.id;
          debugLog("Google ID:", googleId);

          // First check if this Google account is already linked to a user
          const existingGoogleUser = await db.isGoogleAccountLinked(googleId);
          debugLog("Google account already linked:", existingGoogleUser);

          if (existingGoogleUser) {
            // User already exists with this Google ID, just sign in
            debugLog("User already exists with this Google ID, signing in normally");
            return true;
          }

          // Check if there's an existing user with this email
          const existingUser = await db.getUserByEmail(googleProfile.email as string);
          debugLog("Existing user with email:", existingUser);

          // If there's an existing user with the same email but created with a different method,
          // we need to handle this case differently to avoid the OAuthAccountNotLinked error
          if (existingUser) {
            // Check if the user was created with email provider
            if (existingUser.hasGoogleLinked === false) {
              // Link the Google account to the existing user
              debugLog(`User exists with email ${googleProfile.email}, linking Google account`);
              const userId = existingUser.id;

              await db.linkGoogleAccount(userId, {
                id: googleId,
                email: googleProfile.email || "",
                image: googleProfile.picture || googleProfile.image as string
              });

              // Return true to allow sign in
              return true;
            }
          }

          // New user signing up with Google
          // We need to ensure we're using the correct document ID
          // The user.id provided by NextAuth should be the correct document ID
          debugLog("Creating new user with Google sign-in");
          debugLog("NextAuth user ID:", user.id);

          // Create a new user profile for this Google user
          try {
            // First check if the document exists
            const userExists = await db.userExists(user.id);

            if (userExists) {
              // If it exists, update it
              await db.updateUserProfile(user.id, {
                name: googleProfile.name,
                email: googleProfile.email || "",
                image: googleProfile.picture || googleProfile.image as string,
                isProfileComplete: true,
                googleId: googleId,
                googleEmail: googleProfile.email,
                hasGoogleLinked: true,
                isWelcomeEmailSent: false // Set the welcome email flag for new Google sign-ups
              });
            } else {
              // If it doesn't exist, create it
              await db.createUserProfile({
                id: user.id,
                name: googleProfile.name,
                email: googleProfile.email || "",
                image: googleProfile.picture || googleProfile.image as string,
                isProfileComplete: true,
                googleId: googleId,
                googleEmail: googleProfile.email,
                hasGoogleLinked: true,
                isWelcomeEmailSent: false // Set the welcome email flag for new Google sign-ups
              });
            }

            return true;
          } catch (err) {
            console.error("Error creating/updating user profile:", err);
            return false;
          }
        } catch (error) {
          console.error("Error in Google sign in:", error);
          return false;
        }
      }

      // Default fallback - deny sign in
      return false;
    },
    async redirect({ url, baseUrl }) {
      // Parse the URL to get query parameters
      try {
        console.log("NextAuth redirect callback - url:", url, "baseUrl:", baseUrl);
        const urlObj = new URL(url);
        const site = urlObj.searchParams.get('site');
        const callbackUrl = urlObj.searchParams.get('callbackUrl');
        const redirectUrl = urlObj.searchParams.get('redirectUrl');
        
        // Check if this is a successful sign-in to /events without specific redirects
        // and we have a stored redirectUrl in the flow
        if (url.includes('/events') && !site && !redirectUrl && !callbackUrl) {
          // This might be a Google sign-in completion, check if there's a stored redirect
          // Note: We can't access sessionStorage directly in the server-side callback,
          // but the Google sign-in components should handle this via the callbackUrl parameter
          console.log("Sign-in completed to /events, checking for stored redirects");
        }

        if (site) {
          // Handle different site redirects using unified callback with site parameter
          // Preserve redirectUrl parameter if present
          const queryParams = new URLSearchParams();
          queryParams.set('site', site);
          if (redirectUrl && redirectUrl !== '/auth/signin') {
            queryParams.set('redirectUrl', redirectUrl);
          }

          if (isAllowedSite(site)) {
            // Use unified callback that handles cross-domain auth
            return `${baseUrl}/api/auth/callback?${queryParams.toString()}`;
          } else {
            // Unknown site, fall back to sign-in with site parameter preserved
            return `${baseUrl}/auth/signin?site=${site}`;
          }
        }

        // Check if this is a callback URL that already contains site parameter
        if (url.includes('site=')) {
          // If URL starts with baseUrl, it's already absolute
          if (url.startsWith(baseUrl)) return url;
          // If URL starts with "/", make it absolute
          if (url.startsWith("/")) return `${baseUrl}${url}`;
        }

        // If there's a specific redirectUrl parameter, use it
        if (redirectUrl && redirectUrl !== '/auth/signin' && !redirectUrl.includes('/auth/callback')) {
          if (redirectUrl.startsWith("/")) return `${baseUrl}${redirectUrl}`;
          if (redirectUrl.startsWith(baseUrl)) return redirectUrl;
          // Handle external URLs (cross-domain redirects)
          if (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://')) {
            return redirectUrl;
          }
        }

        // If there's a callbackUrl that's not generic auth pages, use it
        if (callbackUrl && !callbackUrl.includes('/auth/') && callbackUrl !== url) {
          console.log("Using callbackUrl:", callbackUrl);
          if (callbackUrl.startsWith("/")) return `${baseUrl}${callbackUrl}`;
          if (callbackUrl.startsWith(baseUrl)) return callbackUrl;
          // Handle decoded callback URLs
          try {
            const decodedCallbackUrl = decodeURIComponent(callbackUrl);
            if (decodedCallbackUrl.startsWith("/")) return `${baseUrl}${decodedCallbackUrl}`;
            if (decodedCallbackUrl.startsWith(baseUrl)) return decodedCallbackUrl;
          } catch (e) {
            console.error("Error decoding callbackUrl:", e);
          }
        }

        // Check if this might be a cross-domain flow by examining the URL for cross-domain indicators
        const urlString = url.toString();
        const hasCrossDomainIndicators =
          urlString.includes('partner.') ||
          urlString.includes('localhost:3001') ||
          (redirectUrl && (redirectUrl.includes('partner.') || redirectUrl.includes('localhost:3001'))) ||
          (callbackUrl && (callbackUrl.includes('partner.') || callbackUrl.includes('localhost:3001')));

        // If the URL is the sign-in page and there's no specific redirect,
        // only redirect to events dashboard if this is NOT a cross-domain flow
        if (url.includes('/auth/signin') && !site && !redirectUrl && !callbackUrl && !hasCrossDomainIndicators) {
          return `${baseUrl}/events`;
        }

        // If URL starts with baseUrl, it's a relative redirect
        if (url.startsWith("/")) return `${baseUrl}${url}`;
        // If URL starts with baseUrl, it's already absolute
        if (url.startsWith(baseUrl)) return url;
        // Default to events dashboard for successful authentication
        return `${baseUrl}/events`;
      } catch (error) {
        console.error('Error in redirect callback:', error);
        // If URL parsing fails, return sign-in page
        return `${baseUrl}/auth/signin`;
      }
    },
  },
  secret: process.env.AUTH_SECRET,
}

// Export the auth handler
const handler = NextAuth(authConfig)

// Export the default handler for the API route
export default handler

function sendVerificationRequest({ identifier, url, provider, expires, token, theme }: SendVerificationRequestParams) {
  // Only run on the server side
  if (typeof window !== 'undefined') {
    return
  }

  console.log("Sending verification request")
  console.log("Params:", { identifier, url, provider, expires, token, theme })

  sendLoginEmail({ identifier, url, provider, expires, token, theme }).then(() => {
    console.log("Email sent")
  }).catch((error) => {
    console.error("Error sending email:", error)
  })
}