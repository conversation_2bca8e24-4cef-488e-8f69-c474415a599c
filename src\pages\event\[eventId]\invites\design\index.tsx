import Container from "@/components/Container";
import { HeaderTabs } from "@/components/HeaderTabs";
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout";
import { Button } from "@/components/ui/button";
import { useEvent } from "@/hooks/useEvent";
import { DownloadIcon } from "lucide-react";
import { useRouter } from "next/router";
import { InvitationCardForm } from "@/components/invitationCardForm";
import { DigitalInviteForm } from "@/components/DigitalInviteForm";
import { StringArrayToString } from "@/lib/utils";
import { useRef, useState, useEffect } from "react";
import { ExportInvites } from "@/components/ExportInvites";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet";

export default function PrintInvitesScreen() {
  const router = useRouter();
  const { eventId } = router.query;
  const { event } = useEvent(eventId as string);
  const formRef = useRef<{ savePreferences: () => Promise<void> }>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Get Hash from URL
  const hash = router.asPath.split("#")[1];
  const validTabs = ["invitation-card", "digital-card"];
  const initialTab = validTabs.includes(hash) ? hash : validTabs[0];
  const [selectedTab, setSelectedTab] = useState<string>(initialTab);

  useEffect(() => {
    setSelectedTab(initialTab);
  }, [initialTab]);

  return (
    <ProtectedLayout
      hideFooter={true}
      headerProps={
        {
          title: "Print Invite Labels",
          breadcrumbs: [
            { label: "Events", href: "/events" },
            { label: event?.eventName || "Event Details", href: `/event/${eventId}` },
            { label: "Invites", href: `/event/${eventId}/invites` },
          ],
          buttons: [],
          showUserProfile: true,
        }
      }
    >
      <div className="flex flex-col bg-gray-50 flex-1">
        <Container className="w-full h-full flex-1">          <header className="mb-4 lg:p-8 p-4 sm:p-8">
            <div className="flex items-center justify-between sm:flex-row flex-col">
              <div className="flex flex-col sm:mb-0 mb-4">
                <h1 className="text-xl font-bold">Customize and Print</h1>
                <p className="text-sm text-muted-foreground">Review and update invite and QR Labels. Once you&apos;re done, you can download or print.</p>
              </div>
              <div className="hidden sm:flex items-end">
                <Button
                  className="btn btn-secondary"
                  variant={"primary-button"}
                  size={"sm"}
                  onClick={() => setIsDrawerOpen(true)}
                >
                  <DownloadIcon />  Export
                </Button>
              </div>
            </div>            <div className="flex sm:hidden">
              <Button
                className="btn btn-secondary -mb-3"
                variant={"primary-button"}
                size={"sm"}
                onClick={() => setIsDrawerOpen(true)}
              >
                <DownloadIcon />  Export
              </Button>
            </div>
          </header>
          {/* Tabs */}
          <HeaderTabs tabs={[
            { label: "Invitation Card & Label", id: "invitation-card" },
            { label: "Digital Invite", id: "digital-card" },
          ]}
            selected={selectedTab}
            onChange={(tabId) => {
              setSelectedTab(tabId);
            }}
          />
          <section className="flex-1 flex min-h-[745px]">
            {selectedTab === 'invitation-card' && <InvitationCardForm
              eventId={StringArrayToString(eventId || '')}
              ref={formRef}
            />}
            {selectedTab === 'digital-card' && <DigitalInviteForm
              eventId={StringArrayToString(eventId || '')}
            />}
          </section>

          <Sheet open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
            <SheetContent className="grid grid-rows-[auto_1fr] w-full sm:max-w-lg">
              <SheetHeader className="">
                <SheetTitle className="text-2xl font-semibold">Export Options</SheetTitle>
                <SheetDescription>
                  Select and download QR labels or invitations in your preferred format. Choose from ready-to-print PDF sheets or individual image files.
                </SheetDescription>
              </SheetHeader>
              <ExportInvites className="-mt-3" eventId={StringArrayToString(eventId || '')} />
            </SheetContent>
          </Sheet>
        </Container>
      </div>
    </ProtectedLayout>
  )
}