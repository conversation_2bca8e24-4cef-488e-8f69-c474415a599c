const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.resolve(process.cwd(), '.env.local') });

/**
 * Generate a JWT token for the shortlink service
 */
function generateJWT() {
  // Check if the secret exists in environment variables
  const secret = process.env.SHORTLINK_JWT_SECRET;

  if (!secret) {
    console.error('Error: SHORTLINK_JWT_SECRET not found in .env.local');
    process.exit(1);
  }

  // Create payload with minimal claims
  const payload = {
    iss: 'iamcoming.io', // Issuer
    sub: 'shortlink-api',  // Subject
    iat: Math.floor(Date.now() / 1000), // Issued at
    exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 30), // Expires in 30 days
  };

  // Sign the JWT
  const token = jwt.sign(payload, secret);
  console.log('Generated JWT Token:');
  console.log(token);

  // Print environment variable setting command
  console.log('\nAdd this to your .env.local and .env.production:');
  console.log(`SHORTLINK_JWT_TOKEN=${token}`);
}

generateJWT();
