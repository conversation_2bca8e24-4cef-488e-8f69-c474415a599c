# RBAC Roles and Resources

## Overview

This document outlines the roles and resources used in the IAC RBAC (Role-Based Access Control) system for the **Admin Portal** and **Partner Portal**. It provides a comprehensive list of roles, their associated permissions, and the resources they can access.

This document serves as the **source of truth** for the RBAC system, ensuring that all roles and permissions are clearly defined and easily accessible for developers and administrators.

## Key Concepts

- **Resource**: A specific feature, page, or functionality within the system
- **Permission**: An action that can be performed on a resource (view, create, edit, delete)
- **Role**: A collection of permissions that define what a user can do
- **Hierarchical Resources**: Resources can be nested (e.g., `partner:venue:{venueId}:events`)
- **Wildcard Resources**: Use `*` to grant access to all instances (e.g., `partner:venue:*`)

### Standard Permissions

| Permission | Description                                                                  | Example Use Case                          |
| ---------- | ---------------------------------------------------------------------------- | ----------------------------------------- |
| `view`     | Read-only access to view resources and their details                         | View event details, read user information |
| `create`   | Permission to create new resources within the associated resource scope      | Add new venue, create new user account    |
| `edit`     | Permission to modify existing resources within the associated resource scope | Update venue details, edit user profile   |
| `delete`   | Permission to remove resources within the associated resource scope          | Delete events, remove team members        |

### Permission Inheritance

- **Explicit Permissions**: Directly assigned to a role for specific resources
- **Inherited Permissions**: Automatically granted based on resource hierarchy
- **Wildcard Permissions**: Granted when using `*` notation (e.g., `partner:venue:*`)

## System Implementation

### Resource Naming Convention

Resources follow a hierarchical naming pattern:

- Format: `{portal}:{feature}:{identifier}:{subfeature}`
- Examples:
  - `partner:venue:123:events`
  - `admin:users:activity`
  - `partner:billing`

## IAC Portals and Features

### Partner Portal

#### Partner Portal Resources

| Resource                           | Description                                                                                                    | Typical Permissions                |
| ---------------------------------- | -------------------------------------------------------------------------------------------------------------- | ---------------------------------- |
| `partner`                          | The root resource for the Partner Portal, which includes all features and functionalities.                     | `view`, `create`, `edit`, `delete` |
| `partner:dashboard`                | Access to the Partner Portal dashboard, which provides an overview of partner activities and statistics.       | `view`                             |
| `partner:venues`                   | Management of venues associated with the partner, including adding, editing, and deleting venues.              | `view`, `create`, `edit`, `delete` |
| `partner:team`                     | Management of team members associated with the partner, including adding, editing, and deleting team members.  | `view`, `create`, `edit`, `delete` |
| `partner:billing`                  | Access to billing information and management, including viewing invoices and payment history.                  | `view`, `edit`                     |
| `partner:customers`                | Management of customers associated with the partner, including adding, editing, and deleting customer records. | `view`, `create`, `edit`, `delete` |
| `partner:settings`                 | Access to partner settings, allowing configuration of various options related to the partner's account.        | `view`, `edit`                     |
| `partner:venue:{venueId}`          | Access to a specific venue's details, allowing management of that venue.                                       | `view`, `create`, `edit`, `delete` |
| `partner:venue:{venueId}:events`   | Management of events associated with a specific venue, including adding, editing, and deleting events.         | `view`, `create`, `edit`, `delete` |
| `partner:venue:{venueId}:settings` | Access to settings for a specific venue, allowing configuration of options related to that venue.              | `view`, `edit`                     |

**Special Resource Patterns:**

- `partner:venue:*` - Access to all venues associated with the partner, allowing management of all venues
- `partner:venue:{venueId}:*` - Access to all resources within a specific venue

#### Partner Portal Roles

| Role              | Description                                                                                    | Resources & Permissions                                                                                                     | Use Case                           |
| ----------------- | ---------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------- | ---------------------------------- |
| `partner:admin`   | Full administrative access to the Partner Portal, including all resources and permissions.     | **All partner resources**: `view`, `create`, `edit`, `delete`                                                               | Partner owner, senior management   |
| `partner:manager` | Management access to the Partner Portal, with permissions to manage venues, team, and billing. | **Limited resources**: `view`, `create`, `edit` on `partner:dashboard`, `partner:venues`, `partner:team`, `partner:billing` | Venue managers, operational leads  |
| `partner:staff`   | Limited access to the Partner Portal, primarily for viewing and managing specific resources.   | **Customer-focused**: `view`, `create`, `edit` on `partner:dashboard`, `partner:venues`, `partner:customers`                | Front desk staff, customer service |
| `partner:viewer`  | Read-only access to Partner Portal for monitoring and reporting purposes.                      | **Read-only**: `view` on `partner:dashboard`, `partner:venues`, `partner:customers`                                         | Accountants, analysts, auditors    |

### Admin Portal

#### Admin Portal Resources

| Resource               | Description                                                                                                              | Typical Permissions                |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------------ | ---------------------------------- |
| `admin`                | The root resource for the Admin Portal, which includes all features and functionalities. Also grants access to Dashboard | `view`, `create`, `edit`, `delete` |
| `admin:users`          | Management of users within the Admin Portal, including adding, editing, and deleting user accounts.                      | `view`, `create`, `edit`, `delete` |
| `admin:users:activity` | Access to user activity logs, allowing administrators to view user actions and changes within the system.                | `view`                             |
| `admin:users:events`   | Management of user events, including viewing and managing events associated with users.                                  | `view`, `edit`, `delete`           |
| `admin:users:features` | Management of user features, allowing administrators to enable or disable specific features for users.                   | `view`, `edit`                     |
| `admin:organizations`  | Management of organizations within the Admin Portal, including adding, editing, and deleting organizations.              | `view`, `create`, `edit`, `delete` |
| `admin:partners`       | Management of partner accounts, including onboarding, configuration, and account status management.                      | `view`, `create`, `edit`, `delete` |
| `admin:settings`       | Access to admin settings, allowing configuration of various options related to the admin account.                        | `view`, `edit`                     |
| `admin:feedback`       | Management of feedback submitted by users, including viewing and responding to feedback.                                 | `view`, `edit`, `delete`           |
| `admin:links`          | Management of shortlinks (`l.iamcoming.io`) within the Admin Portal, including adding, editing, and deleting links.      | `view`, `create`, `edit`, `delete` |
| `admin:analytics`      | Access to system analytics, reporting, and business intelligence dashboards.                                             | `view`                             |
| `admin:security`       | Management of security settings, audit logs, and security incidents.                                                     | `view`, `edit`                     |

#### Admin Portal Roles

| Role               | Description                                                                                           | Resources & Permissions                                                                                                                       | Use Case                             |
| ------------------ | ----------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------ |
| `admin:superadmin` | Full administrative access to the Admin Portal, including all resources and permissions.              | **All admin resources**: `view`, `create`, `edit`, `delete`                                                                                   | System administrators, CTO, DevOps   |
| `admin:manager`    | Management access to the Admin Portal, with permissions to manage users, organizations, and feedback. | **Management focus**: `view`, `create`, `edit` on `admin:users`, `admin:organizations`, `admin:partners`, `admin:feedback`, `admin:analytics` | Product managers, team leads         |
| `admin:staff`      | Limited access to the Admin Portal, primarily for viewing and managing specific resources.            | **Support focus**: `view` on `admin:users`, `admin:organizations`, `admin:feedback`; `edit` on `admin:feedback`                               | Customer support, content moderators |
| `admin:analyst`    | Access to analytics and reporting features with limited operational permissions.                      | **Analytics focus**: `view` on `admin:analytics`, `admin:users:activity`, `admin:organizations`                                               | Data analysts, business intelligence |
| `admin:security`   | Security-focused role with access to security features and audit logs.                                | **Security focus**: `view`, `edit` on `admin:security`, `admin:users:activity`; `view` on all admin resources                                 | Security officers, compliance team   |

## Implementation Guidelines

### Role Assignment Strategy

1. **Principle of Least Privilege**: Assign the minimum permissions necessary for a user to perform their job function
2. **Role Hierarchy**: Users can be assigned multiple roles, with permissions being additive
3. **Temporal Roles**: Consider time-based role assignments for temporary access
4. **Context-Aware Permissions**: Some permissions may be context-dependent (e.g., venue-specific access)

### Security Considerations

- **Permission Validation**: Always validate permissions server-side before executing operations
- **Audit Trail**: Log all permission changes and role assignments
- **Regular Review**: Periodically review and audit role assignments
- **Emergency Access**: Maintain emergency access procedures for critical situations

### Development Guidelines

- **Resource Registration**: All new features must register their resources in this document
- **Permission Checking**: Implement permission checks at the API endpoint level
- **UI/UX Integration**: Hide/disable UI elements based on user permissions
- **Testing**: Include RBAC scenarios in integration tests

## Appendix

### Permission Matrix Reference

Quick reference for developers implementing permission checks:

#### Partner Portal Permission Matrix

| Resource            | Admin | Manager | Staff | Viewer |
| ------------------- | ----- | ------- | ----- | ------ |
| `partner:dashboard` | CRUD  | R       | R     | R      |
| `partner:venues`    | CRUD  | CRU     | CR    | R      |
| `partner:team`      | CRUD  | CRU     | -     | -      |
| `partner:billing`   | CRUD  | CRU     | -     | R      |
| `partner:customers` | CRUD  | -       | CRU   | R      |
| `partner:settings`  | CRUD  | -       | -     | R      |

#### Admin Portal Permission Matrix

| Resource              | SuperAdmin | Manager | Staff | Analyst | Security |
| --------------------- | ---------- | ------- | ----- | ------- | -------- |
| `admin:users`         | CRUD       | CRU     | R     | R       | R        |
| `admin:organizations` | CRUD       | CRU     | R     | R       | R        |
| `admin:partners`      | CRUD       | CRU     | -     | -       | R        |
| `admin:analytics`     | CRUD       | R       | -     | R       | R        |
| `admin:security`      | CRUD       | -       | -     | -       | RU       |
| `admin:feedback`      | CRUD       | CRU     | RU    | R       | R        |

**Legend**: C = Create, R = Read/View, U = Update/Edit, D = Delete, - = No Access
