import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { GenerateID } from '@/lib/ID';
import { EventInvite } from '@/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { eventId } = req.query;
    const { contacts } = req.body;

    if (!eventId || !Array.isArray(contacts) || contacts.length === 0) {
      return res.status(400).json({ error: 'Event ID and contacts array are required' });
    }

    const db = Database.getInstance();

    // Verify event exists and user has permission
    const event = await db.readData('events', eventId as string);
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Check if user owns the event or is a manager
    const isOwner = (event.ownerEmail === session.user.email) ||
                   (event.ownerAccountId === session.user.id);
    const isManager = event.managers && event.managers.some((manager: any) =>
      manager.email === session.user.email || manager.accountId === session.user.id
    );

    if (!isOwner && !isManager) {
      return res.status(403).json({ error: 'Permission denied' });
    }

    // Get existing invites to check for duplicates
    const existingInvitesSnapshot = await db.query('invites')
      .where('eventId', '==', eventId as string)
      .get();

    const existingInvites = existingInvitesSnapshot.docs.map(doc => doc.data());

    const importedInvites: EventInvite[] = [];
    const skippedContacts: string[] = [];
    const now = new Date().toISOString();

    // Process each selected contact
    for (const contact of contacts) {
      if (!contact.email && !contact.phone) {
        continue; // Skip contacts without email or phone
      }

      // Check for duplicates
      const isDuplicate = existingInvites.some(invite => {
        // Check for email match
        if (contact.email && invite.email &&
            contact.email.toLowerCase() === invite.email.toLowerCase()) {
          return true;
        }
        // Check for phone match
        if (contact.phone && invite.phone &&
            contact.phone.replace(/\D/g, '') === invite.phone.replace(/\D/g, '')) {
          return true;
        }
        return false;
      });

      if (isDuplicate) {
        skippedContacts.push(contact.name || contact.email || contact.phone || 'Unknown');
        continue; // Skip duplicate contacts
      }

      const inviteData: EventInvite = {
        ID: GenerateID('I'),
        eventId: eventId as string,
        name: contact.name || contact.email || contact.phone || 'Unknown',
        status: 'invited',
        adults: 1,
        children: 0,
        email: contact.email || '',
        phone: contact.phone || '',
        group: contact.groupName || '',
        message: [],
        createdAt: now
      };

      await db.addData('invites', inviteData);
      importedInvites.push(inviteData);

      // Add to existing invites list to prevent duplicates within this import
      existingInvites.push(inviteData);
    }

    return res.status(200).json({
      success: true,
      importedCount: importedInvites.length,
      skippedCount: skippedContacts.length,
      contactsProcessed: contacts.length,
      invites: importedInvites,
      skippedContacts
    });

  } catch (error) {
    console.error('Error importing contacts:', error);
    return res.status(500).json({
      error: 'Failed to import contacts',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}