'use client'

import Script from 'next/script'
import { useEffect } from 'react'
import { useRouter } from 'next/router'
import { extractUtmParams } from '@/lib/utm'
import { groupUrl, createPageTitle, extractUrlMetadata } from '@/lib/url-grouping'
import '@/types/analytics'

// Google Analytics measurement ID
const GA_MEASUREMENT_ID = 'G-0N9CPV0Z0R'

export const GoogleAnalytics = () => {
  const router = useRouter()

  useEffect(() => {
    // Function to handle route changes and track page views with UTM parameters
    const handleRouteChange = (url: string) => {
      // Extract UTM parameters from URL if present
      const utmParams = extractUtmParams(url);

      // Group the URL to consolidate similar dynamic routes
      const groupedUrl = groupUrl(url);
      const pageTitle = createPageTitle(groupedUrl, url);
      const urlMetadata = extractUrlMetadata(url, groupedUrl);

      // Configure Google Analytics with grouped page path and UTM parameters
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_path: groupedUrl, // Use grouped URL instead of original
        page_title: pageTitle,
        // Add URL metadata as custom dimensions
        custom_map: {
          'custom_dimension_1': 'page_group',
          'custom_dimension_2': 'original_path',
          'custom_dimension_3': 'event_id',
          'custom_dimension_4': 'invite_id',
          'custom_dimension_5': 'event_context',
          'custom_dimension_6': 'page_type',
          'custom_dimension_7': 'user_id',
          'custom_dimension_8': 'org_id'
        },
        ...urlMetadata,
        // Add UTM parameters as custom dimensions if available
        ...(utmParams.utm_source && { utm_source: utmParams.utm_source }),
        ...(utmParams.utm_medium && { utm_medium: utmParams.utm_medium }),
        ...(utmParams.utm_campaign && { utm_campaign: utmParams.utm_campaign }),
        ...(utmParams.utm_term && { utm_term: utmParams.utm_term }),
        ...(utmParams.utm_content && { utm_content: utmParams.utm_content })
      });

      // Also send a page_view event with additional context and custom parameters for filtering
      window.gtag('event', 'page_view', {
        page_title: pageTitle,
        page_location: window.location.href,
        page_path: groupedUrl,
        page_group: groupedUrl,
        original_path: url,
        // Include all metadata for filtering capabilities
        ...urlMetadata,
        // UTM parameters
        ...(utmParams.utm_source && { utm_source: utmParams.utm_source }),
        ...(utmParams.utm_medium && { utm_medium: utmParams.utm_medium }),
        ...(utmParams.utm_campaign && { utm_campaign: utmParams.utm_campaign }),
        ...(utmParams.utm_term && { utm_term: utmParams.utm_term }),
        ...(utmParams.utm_content && { utm_content: utmParams.utm_content })
      });
    }

    // Track initial page load
    if (typeof window !== 'undefined') {
      handleRouteChange(window.location.pathname + window.location.search);
    }

    // Set up route change tracking
    router.events.on('routeChangeComplete', handleRouteChange);

    // Load testing utilities in development mode
    if (process.env.NODE_ENV === 'development') {
      import('@/utils/ga-testing').catch(console.error);
    }

    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    }
  }, [router.events])

  return (
    <>
      {/* Google Analytics Script */}
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            // Import URL grouping functions (inline implementation)
            function groupUrl(url) {
              if (!url || typeof url !== 'string') return url;
              
              const urlPath = url.split('?')[0].split('#')[0];
              const patterns = [
                { pattern: /^\\/event\\/[^\\/]+$/, replacement: '/event/*' },
                { pattern: /^\\/event\\/[^\\/]+\\/invites$/, replacement: '/event/*/invites' },
                { pattern: /^\\/event\\/[^\\/]+\\/invites\\/[^\\/]+$/, replacement: '/event/*/invites/*' },
                { pattern: /^\\/event\\/[^\\/]+\\/invites\\/[^\\/]+\\/edit$/, replacement: '/event/*/invites/*/edit' },
                { pattern: /^\\/event\\/[^\\/]+\\/rsvp\\/[^\\/]+$/, replacement: '/event/*/rsvp/*' },
                { pattern: /^\\/event\\/[^\\/]+\\/settings$/, replacement: '/event/*/settings' },
                { pattern: /^\\/event\\/[^\\/]+\\/analytics$/, replacement: '/event/*/analytics' },
                { pattern: /^\\/event\\/[^\\/]+\\/guests$/, replacement: '/event/*/guests' },
                { pattern: /^\\/event\\/[^\\/]+\\/messages$/, replacement: '/event/*/messages' },
                { pattern: /^\\/user\\/[^\\/]+$/, replacement: '/user/*' },
                { pattern: /^\\/user\\/[^\\/]+\\/profile$/, replacement: '/user/*/profile' },
                { pattern: /^\\/user\\/[^\\/]+\\/events$/, replacement: '/user/*/events' }
              ];
              
              for (const {pattern, replacement} of patterns) {
                if (pattern.test(urlPath)) {
                  return urlPath.replace(pattern, replacement);
                }
              }
              return urlPath;
            }

            function createPageTitle(groupedUrl) {
              const titleMap = {
                '/': 'Home',
                '/about': 'About',
                '/contact': 'Contact',
                '/pricing': 'Pricing',
                '/login': 'Login',
                '/signup': 'Sign Up',
                '/dashboard': 'Dashboard',
                '/event/*': 'Event Details',
                '/event/*/invites': 'Event Invites',
                '/event/*/invites/*': 'Invite Details',
                '/event/*/invites/*/edit': 'Edit Invite',
                '/event/*/rsvp/*': 'Event RSVP',
                '/event/*/settings': 'Event Settings',
                '/event/*/analytics': 'Event Analytics',
                '/event/*/guests': 'Event Guests',
                '/event/*/messages': 'Event Messages',
                '/user/*': 'User Profile',
                '/user/*/profile': 'User Profile Settings',
                '/user/*/events': 'User Events'
              };
              return titleMap[groupedUrl] || groupedUrl || 'Unknown Page';
            }

            // Extract UTM parameters from URL
            const urlParams = new URLSearchParams(window.location.search);
            const utmSource = urlParams.get('utm_source');
            const utmMedium = urlParams.get('utm_medium');
            const utmCampaign = urlParams.get('utm_campaign');
            const utmTerm = urlParams.get('utm_term');
            const utmContent = urlParams.get('utm_content');

            // Group the initial URL
            const originalPath = window.location.pathname + window.location.search;
            const groupedPath = groupUrl(originalPath);
            const pageTitle = createPageTitle(groupedPath);

            // Configure Google Analytics with grouped URL and UTM parameters
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_path: groupedPath,
              page_title: pageTitle,
              custom_map: {
                'custom_dimension_1': 'page_group',
                'custom_dimension_2': 'original_path'
              },
              page_group: groupedPath,
              original_path: originalPath,
              ...(utmSource && { utm_source: utmSource }),
              ...(utmMedium && { utm_medium: utmMedium }),
              ...(utmCampaign && { utm_campaign: utmCampaign }),
              ...(utmTerm && { utm_term: utmTerm }),
              ...(utmContent && { utm_content: utmContent })
            });
          `,
        }}
      />
    </>
  )
}

// Custom events for Google Analytics with UTM parameter support and URL grouping
export const logEvent = (
  action: string,
  category: string,
  label: string,
  value?: number,
  additionalParams?: Record<string, any>
) => {
  // Get current UTM parameters from URL if available
  const currentUtmParams = typeof window !== 'undefined'
    ? extractUtmParams(window.location.search)
    : {};

  // Get current page grouping information
  const currentUrl = typeof window !== 'undefined'
    ? window.location.pathname + window.location.search
    : '';
  const groupedUrl = groupUrl(currentUrl);
  const urlMetadata = extractUrlMetadata(currentUrl, groupedUrl);

  window.gtag?.('event', action, {
    event_category: category,
    event_label: label,
    value: value,
    // Include page grouping information
    page_group: groupedUrl,
    original_path: currentUrl,
    ...urlMetadata,
    // Include UTM parameters from URL
    ...(currentUtmParams.utm_source && { utm_source: currentUtmParams.utm_source }),
    ...(currentUtmParams.utm_medium && { utm_medium: currentUtmParams.utm_medium }),
    ...(currentUtmParams.utm_campaign && { utm_campaign: currentUtmParams.utm_campaign }),
    ...(currentUtmParams.utm_term && { utm_term: currentUtmParams.utm_term }),
    ...(currentUtmParams.utm_content && { utm_content: currentUtmParams.utm_content }),
    // Include any additional parameters
    ...(additionalParams || {})
  })
}

// Goal conversion tracking for Google Analytics
export const trackGoal = (goalName: string, value?: number) => {
  window.gtag?.('event', 'conversion', {
    send_to: GA_MEASUREMENT_ID,
    event_category: 'Goals',
    event_action: goalName,
    value: value,
    non_interaction: false,
  })
}

// Specific goal tracking functions
export const trackEventCreation = (eventId: string, isPaid: boolean = false) => {
  // Track general event creation
  trackGoal('event_created', 1)
  logEvent('create_event', 'Events', `Event ID: ${eventId}`)

  // If it's a paid event, track the paid event creation goal
  if (isPaid) {
    trackGoal('paid_event_created', 1)
    logEvent('create_paid_event', 'Events', `Paid Event ID: ${eventId}`)
  }
}

// Enhanced event tracking functions with event/invite ID parameters for filtering

// Track event-specific actions with event ID for filtering
export const trackEventAction = (
  action: string,
  eventId: string,
  additionalParams?: Record<string, any>
) => {
  const currentUrl = typeof window !== 'undefined'
    ? window.location.pathname + window.location.search
    : '';
  const groupedUrl = groupUrl(currentUrl);
  const urlMetadata = extractUrlMetadata(currentUrl, groupedUrl);

  logEvent(action, 'Event Actions', `Event: ${eventId}`, 1, {
    event_id: eventId,
    custom_parameter_1: eventId, // For GA4 filtering
    page_group: groupedUrl,
    event_context: urlMetadata.event_context || 'general',
    ...additionalParams
  });
}

// Track invite-specific actions with both event ID and invite ID
export const trackInviteAction = (
  action: string,
  eventId: string,
  inviteId: string,
  additionalParams?: Record<string, any>
) => {
  const currentUrl = typeof window !== 'undefined'
    ? window.location.pathname + window.location.search
    : '';
  const groupedUrl = groupUrl(currentUrl);
  const urlMetadata = extractUrlMetadata(currentUrl, groupedUrl);

  logEvent(action, 'Invite Actions', `Event: ${eventId}, Invite: ${inviteId}`, 1, {
    event_id: eventId,
    invite_id: inviteId,
    custom_parameter_1: eventId, // For GA4 filtering by event
    custom_parameter_2: inviteId, // For GA4 filtering by invite
    page_group: groupedUrl,
    event_context: 'invite_management',
    ...additionalParams
  });
}

// Track RSVP-specific actions for event-based RSVPs
/**
 * Track RSVP-related actions for event-based RSVPs (/event/[eventId]/rsvp/[inviteId])
 */
export const trackRSVPAction = (
  action: string,
  eventId: string,
  inviteId: string,
  additionalParams?: Record<string, any>
) => {
  const currentUrl = typeof window !== 'undefined'
    ? window.location.pathname + window.location.search
    : '';
  const groupedUrl = groupUrl(currentUrl);
  const urlMetadata = extractUrlMetadata(currentUrl, groupedUrl);

  logEvent(action, 'RSVP Actions', `Event: ${eventId}, Invite: ${inviteId}`, 1, {
    event_id: eventId,
    invite_id: inviteId,
    custom_parameter_1: eventId, // For GA4 filtering by event
    custom_parameter_2: inviteId, // For GA4 filtering by invite
    page_group: groupedUrl,
    event_context: 'rsvp_flow',
    ...additionalParams
  });
}

// Track user journey through event pages with filtering parameters
export const trackEventPageJourney = (
  fromPage: string,
  toPage: string,
  eventId: string,
  additionalParams?: Record<string, any>
) => {
  logEvent('page_navigation', 'User Journey', `${fromPage} → ${toPage}`, 1, {
    event_id: eventId,
    custom_parameter_1: eventId,
    from_page: fromPage,
    to_page: toPage,
    navigation_type: 'event_journey',
    ...additionalParams
  });
}