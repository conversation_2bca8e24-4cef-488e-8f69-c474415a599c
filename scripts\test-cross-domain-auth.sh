#!/bin/bash

# Test script for secure cross-domain authentication with HTTP-only cookies

echo "🔒 Testing Secure Cross-Domain Authentication (HTTP-Only Cookies)"
echo "================================================================="

# Base URL (adjust as needed)
BASE_URL="http://localhost:3000"

echo ""
echo "📋 Test URLs:"
echo ""

echo "1. SECURE Login with partner site redirect:"
echo "   ${BASE_URL}/auth/signin?site=partner"
echo "   → Sets HTTP-only cookie → Redirects to: https://partner.iamcoming.io/auth/callback"
echo ""

echo "2. SECURE Login with local site redirect:"
echo "   ${BASE_URL}/auth/signin?site=local"  
echo "   → Sets HTTP-only cookie → Redirects to: http://localhost:3000/auth/callback"
echo ""

echo "3. Legacy login (URL-based tokens):"
echo "   Use /api/auth/callback instead of /api/auth/callback-secure"
echo ""

echo "4. Direct secure callback test (requires authentication):"
echo "   ${BASE_URL}/api/auth/callback-secure?site=partner"
echo ""

echo "5. Session verification with cookie (automatic):"
echo "   ${BASE_URL}/api/id/session"
echo "   (Automatically reads cross-domain-session cookie)"
echo ""

echo "6. Session verification with manual token (fallback):"
echo "   ${BASE_URL}/api/id/session?sessionToken=YOUR_TOKEN_HERE"
echo ""

echo "📝 Test Steps:"
echo ""
echo "1. Open a browser and navigate to one of the SECURE login URLs above"
echo "2. Complete the authentication process"
echo "3. Verify you are redirected to the external site WITHOUT a token in URL"
echo "4. Check browser developer tools → Application → Cookies"
echo "5. Verify 'cross-domain-session' cookie is set with HttpOnly flag"
echo "6. Use the /api/id/session endpoint to verify it works"
echo ""

echo "✅ Expected Behavior (SECURE):"
echo ""
echo "- Login with site=partner should redirect to: https://partner.iamcoming.io/auth/callback"
echo "- Login with site=local should redirect to: http://localhost:3000/auth/callback"
echo "- NO sessionToken should appear in any URLs"
echo "- HTTP-only cookie 'cross-domain-session' should be set on target domain"
echo "- External sites can authenticate by calling /api/id/session (cookie auto-sent)"
echo ""

echo "🔒 Security Improvements:"
echo ""
echo "- ✅ No tokens in URLs (not logged, not in history, not in referrers)"
echo "- ✅ HTTP-only cookies prevent XSS access"
echo "- ✅ Secure flag ensures HTTPS-only transmission in production"
echo "- ✅ SameSite=None allows cross-site functionality"
echo "- ✅ Domain scoping to .iamcoming.io for subdomain access"
echo "- ✅ 1-hour expiration for security"
echo ""

echo "🧪 Cookie Testing Commands:"
echo ""
echo "# Check if cookie is set (after authentication)"
echo "curl -H 'Cookie: cross-domain-session=TOKEN_HERE' ${BASE_URL}/api/id/session"
echo ""
echo "# Verify cookie properties in browser DevTools:"
echo "Application → Storage → Cookies → Check 'cross-domain-session'"
echo "Should show: HttpOnly=true, Secure=true (in prod), SameSite=None"
echo ""
