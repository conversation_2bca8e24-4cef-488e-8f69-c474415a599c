import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Avatar, AvatarImage, AvatarFallback } from './avatar';

const meta = {
  title: 'UI/Avatar',
  component: Avatar,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Avatar>;

export default meta;
type Story = StoryObj<typeof Avatar>;

export const WithImage: Story = {
  render: () => (
    <Avatar>
      <AvatarImage src="/profile.png" alt="Profile picture" />
      <AvatarFallback>JD</AvatarFallback>
    </Avatar>
  ),
};

export const WithFallback: Story = {
  render: () => (
    <Avatar>
      <AvatarImage src="/nonexistent-image.png" alt="Profile picture" />
      <AvatarFallback>JD</AvatarFallback>
    </Avatar>
  ),
};

export const Small: Story = {
  render: () => (
    <Avatar className="size-6">
      <AvatarImage src="/profile.png" alt="Profile picture" />
      <AvatarFallback>JD</AvatarFallback>
    </Avatar>
  ),
};

export const Medium: Story = {
  render: () => (
    <Avatar className="size-10">
      <AvatarImage src="/profile.png" alt="Profile picture" />
      <AvatarFallback>JD</AvatarFallback>
    </Avatar>
  ),
};

export const Large: Story = {
  render: () => (
    <Avatar className="size-14">
      <AvatarImage src="/profile.png" alt="Profile picture" />
      <AvatarFallback>JD</AvatarFallback>
    </Avatar>
  ),
};

export const WithCustomFallback: Story = {
  render: () => (
    <Avatar>
      <AvatarImage src="/nonexistent-image.png" alt="Profile picture" />
      <AvatarFallback className="bg-primary text-primary-foreground font-semibold">
        BP
      </AvatarFallback>
    </Avatar>
  ),
};