"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import dynamic from "next/dynamic"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerProps {
  value?: Date
  onChange: (date: Date | undefined) => void
  disabled?: boolean
  disabledDates?: Date | Date[] | undefined
  fromDate?: Date
  toDate?: Date
  className?: string
}

const DatePickerContent = dynamic(() => Promise.resolve(function DatePickerContent({ value, onChange, disabled, disabledDates, fromDate, toDate, className }: DatePickerProps) {
  // Create a function to determine if a date should be disabled
  const isDateDisabled = (date: Date) => {
    // Check if date is before fromDate (if fromDate is provided)
    if (fromDate && date < fromDate) {
      return true;
    }

    // Check if date is after toDate (if toDate is provided)
    if (toDate && date > toDate) {
      return true;
    }

    // Check against any other disabled dates
    if (disabledDates) {
      if (Array.isArray(disabledDates)) {
        return disabledDates.some(disabledDate =>
          disabledDate.getDate() === date.getDate() &&
          disabledDate.getMonth() === date.getMonth() &&
          disabledDate.getFullYear() === date.getFullYear()
        );
      } else if (disabledDates instanceof Date) {
        return disabledDates.getDate() === date.getDate() &&
               disabledDates.getMonth() === date.getMonth() &&
               disabledDates.getFullYear() === date.getFullYear();
      }
    }

    return false;
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !value && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {value ? format(value, "PPP") : <span>Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={value}
          onSelect={onChange}
          disabled={isDateDisabled}
          initialFocus
          fromDate={fromDate}
          toDate={toDate}
        />
      </PopoverContent>
    </Popover>
  )
}), { ssr: false })

export function DatePicker({ value, onChange, disabled, disabledDates, fromDate, toDate, className }: DatePickerProps) {
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <Button
        variant="outline"
        className={cn(
          "w-full justify-start text-left font-normal",
          !value && "text-muted-foreground",
          className
        )}
        disabled={disabled}
      >
        <CalendarIcon className="mr-2 h-4 w-4" />
        {value ? format(value, "PPP") : <span>Pick a date</span>}
      </Button>
    )
  }

  return <DatePickerContent
    value={value}
    onChange={onChange}
    disabled={disabled}
    disabledDates={disabledDates}
    fromDate={fromDate}
    toDate={toDate}
    className={className}
  />
}