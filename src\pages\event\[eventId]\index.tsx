'use client';
import { useEffect, useState } from "react";
import { useEvent } from "@/hooks/useEvent"
import { FormatDate } from "@/lib/dayjs"
import { Format24to12 } from "@/lib/time"
import { useRouter } from "next/router"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Calendar, Clock, MapPin, Edit, Bell, Plus, Trash, LockIcon, AlertTriangle, Users, ChevronDown, Upload, PenTool } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout";
import EventManagers from "@/components/EventManagers"
import { debugLog } from "@/lib/logger";
import { Header } from "@/components/Header";
import { trackGoal } from "@/components/GoogleAnalytics";
import { isEventLocked, getEventLockMessage, isInviteManagementLocked, isReminderLocked, getInviteManagementLockMessage, isEventDatePassed } from "@/lib/event/eventLock";
import SendReminderDialog from "@/components/SendReminderDialog";
import { ResponsiveContainer, PieChart as RechartPieChart, Pie as RechartPie, Cell as RechartCell, Tooltip as RechartTooltip } from "recharts";
import { ChartTooltipContent } from "@/components/ui/chart-tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useSession } from "next-auth/react";
import { useInvites } from "@/hooks/useInvites";
import { useDropdownToModal } from "@/hooks/useDropdownToModal";

function EventManagementScreen() {
  const router = useRouter();
  const { eventId } = router.query;  const { loading, event, rsvpReport, saveEvent, error: eventError } = useEvent(eventId as string);
  const { invites, loading: invitesLoading } = useInvites(eventId as string);
  const { data: session } = useSession();
  const { transitionToModal } = useDropdownToModal();

  // State for message expansion
  const [isMessageExpanded, setIsMessageExpanded] = useState(false);

  const [eventLoading, setEventLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [reminderDialogOpen, setReminderDialogOpen] = useState(false);
  const [forbidden, setForbidden] = useState(false);
  // Determine if the current user is a manager (not the owner)
  // Updated to use account IDs instead of emails
  const isManager = event && session?.user?.id &&
    (event.ownerAccountId !== session.user.id &&
      // For backward compatibility, also check ownerEmail
      event.ownerEmail !== session.user.email);

  useEffect(() => {
    if (typeof window === "undefined" || !event) return;
    const urlParams = new URLSearchParams(window.location.search);
    const invitedEmail = urlParams.get("invitedEmail");
    if (invitedEmail) {
      // Check if the provided email is in the list of event managers
      const isManagerEmail = event.managers?.some((manager) => manager === invitedEmail);
      // replace the invitedEmail in the managers list with current user's userId
      const updatedManagers = event.managers?.map((manager) => {
        if (manager === invitedEmail) {
          return session?.user?.id || "";
        }
        return manager;
      });
      // Update the event with the new managers list
      if (updatedManagers) {
        saveEvent({ ...event, managers: updatedManagers }).then(savedEvent => {
          if (savedEvent) {
            router.replace(`/event/${eventId}`);
          }
        });
      }
    }
  }, [event, session?.user?.id, eventId, saveEvent, router]);

  useEffect(() => {
    if (event && rsvpReport) {
      setEventLoading(false);
    }
  }, [event, rsvpReport]);

  useEffect(() => {
    if (!loading && eventId === 'new') {
      router.replace('/event/new/edit');
    }
  }, [eventId, router, loading]);

  // Check for forbidden access
  useEffect(() => {
    if (eventError && (eventError.message === "Forbidden - You do not have access to this event" ||
      (eventError as any).code === "FORBIDDEN_ACCESS")) {
      setForbidden(true);
    }
  }, [eventError]);

  // Calculate total confirmed guests
  const totalConfirmed = rsvpReport ? rsvpReport.adults.accepted + rsvpReport.children.accepted : 0;

  // Track payment success events
  useEffect(() => {
    const { success } = router.query;

    // If redirected after successful payment, track it as a payment completion goal
    if (success === 'true' && event) {
      debugLog('Payment successful, tracking goal', { eventId, plan: event.plan });

      // Track paid event goal
      trackGoal('payment_completed', 1);

      // Log detailed payment event
      const isPaid = event.plan !== 'free';
      if (isPaid) {
        trackGoal('paid_event_payment_completed', 1);
      }

      // Remove success parameter from URL without refreshing the page
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [router.query, event, eventId]);

  const handleUpdateManagers = async (managers: string[]) => {
    if (!event) return;

    const updatedEvent = {
      ...event,
      managers
    };

    const savedEvent = await saveEvent(updatedEvent);
    if (savedEvent) {
      // The event data will be updated in the useEvent hook
      // No need to force reload
    }
  };

  const handleDeleteEvent = async () => {
    try {
      const response = await fetch(`/api/event/${eventId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Redirect to events list after successful deletion
        router.push('/events');
      } else {
        console.error("Failed to delete event:", await response.text());
        alert("Failed to delete the event. Please try again later.");
      }
    } catch (error) {
      console.error("Error deleting event:", error);
      alert("An error occurred while deleting the event.");
    }
  };

  // Show forbidden error screen
  if (forbidden) {
    return (
      <ProtectedLayout>
        <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
          <div className="w-full max-w-md">
            <Card className="border-red-200 shadow-lg">
              <CardHeader className="bg-red-50 border-b border-red-200">
                <div className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M12 3a9 9 0 110 18 9 9 0 010-18z" />
                  </svg>
                  <h2 className="text-xl font-bold text-red-700">Access Denied</h2>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <p className="text-gray-700 mb-6">
                  You don&apos;t have permission to access this event. You need to be the event owner, a designated event manager.
                </p>
                <Button
                  variant="primary-button"
                  className="w-full mt-2"
                  onClick={() => router.push('/events')}
                >
                  Go Back to Events
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </ProtectedLayout>
    );
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col  bg-gray-50">
        {/* Use the shared Header component with breadcrumbs */}
        <Header
          title={event?.eventName || "Event Details"}
          breadcrumbs={[
            { label: 'Events', href: '/events' }
          ]}
        />

        {/* Delete confirmation dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure you want to delete this event?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the event
                &quot;{event?.eventName}&quot; and all associated invitations.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteEvent}
                className="bg-red-600 hover:bg-red-700"
              >
                Delete Event
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Content */}
        <div className="flex-1 p-4 pb-20">
          <div className="container mx-auto space-y-4">
            {!event || !rsvpReport || eventLoading ? (
              <>
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-5 w-24" />
                      </div>
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-5/6" />

                    <div className="space-y-3 pt-2">
                      <div className="flex items-start gap-3">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <div className="space-y-2 flex-1">
                          <Skeleton className="h-4 w-16" />
                          <Skeleton className="h-4 w-32" />
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <div className="space-y-2 flex-1">
                          <Skeleton className="h-4 w-16" />
                          <Skeleton className="h-4 w-40" />
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <div className="space-y-2 flex-1">
                          <Skeleton className="h-4 w-16" />
                          <Skeleton className="h-4 w-48" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <div>
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-64 mt-1" />
                      </div>
                      <div className="flex flex-row w-full gap-2">
                        <Skeleton className="h-9 flex-1" />
                        <Skeleton className="h-9 flex-1" />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Stats boxes skeleton */}
                    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <Skeleton className="h-24 w-full rounded-md" />
                      <Skeleton className="h-24 w-full rounded-md" />
                      <Skeleton className="h-24 w-full rounded-md" />
                      <Skeleton className="h-24 w-full rounded-md" />
                    </div>

                    {/* RSVP Status and Guest Count skeletons */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-center">
                            <Skeleton className="h-5 w-24" />
                            <Skeleton className="h-3 w-32" />
                          </div>
                        </CardHeader>
                        <CardContent className="flex justify-center items-center py-6">
                          <Skeleton className="h-[200px] w-full rounded-full" />
                        </CardContent>
                        <CardFooter className="pt-0 pb-4 flex justify-center">
                          <Skeleton className="h-4 w-48" />
                        </CardFooter>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-center">
                            <Skeleton className="h-5 w-24" />
                            <Skeleton className="h-3 w-32" />
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4 py-4">
                          <div className="space-y-1">
                            <div className="flex justify-between items-center">
                              <Skeleton className="h-4 w-16" />
                              <Skeleton className="h-4 w-16" />
                            </div>
                            <Skeleton className="h-2 w-full rounded-full" />
                          </div>

                          <div className="space-y-1">
                            <div className="flex justify-between items-center">
                              <Skeleton className="h-4 w-16" />
                              <Skeleton className="h-4 w-16" />
                            </div>
                            <Skeleton className="h-2 w-full rounded-full" />
                          </div>

                          <div className="space-y-1">
                            <div className="flex justify-between items-center">
                              <Skeleton className="h-4 w-16" />
                              <Skeleton className="h-4 w-16" />
                            </div>
                            <Skeleton className="h-2 w-full rounded-full" />
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Action buttons skeleton */}
                    <div className="flex gap-2 w-full">
                      <Skeleton className="h-9 w-full" />
                      <Skeleton className="h-9 w-full" />
                      <Skeleton className="h-9 w-full" />
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <>
                {/* RSVP date passed message - only show if event is not completed */}
                {event && isInviteManagementLocked(event) && !isEventDatePassed(event) && (
                  <div className="bg-yellow-50 border border-yellow-300 rounded-lg p-4 mb-4">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-yellow-800">Event Management Limited</h3>
                        <p className="text-sm text-yellow-700">{getInviteManagementLockMessage(event)}</p>
                        <p className="text-sm text-yellow-700 mt-1">
                          Invite management and event editing are now disabled.
                          You can still send reminders until the event date passes.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Event date passed message */}
                {event && isEventDatePassed(event) && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div className="flex items-start">
                      <div className="h-5 w-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                          <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold text-blue-700">Event Completed</h3>
                        <p className="text-sm text-blue-600">{getInviteManagementLockMessage(event)}</p>
                        <p className="text-sm text-blue-600 mt-1">All event management features are now locked.</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* No invites message - Placed at the very top */}
                {!invitesLoading && invites.length === 0 && (
                  <div className="bg-yellow-50 border border-amber-300 rounded-lg p-4 mb-4">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                      <div>
                        <h3 className="font-semibold">You haven&apos;t created any invites yet.</h3>
                        <p className="text-sm text-muted-foreground">Start by creating invites and sending RSVP links to track attendance.</p>
                      </div>
                      <div className="sm:text-right mt-3 sm:mt-0">
                        {isInviteManagementLocked(event) ? (
                          <Button
                            variant="outline"
                            className="cursor-not-allowed opacity-60 sm:ml-auto"
                            disabled={true}
                            title={getEventLockMessage(event) || "Invite management locked"}
                          >
                            <LockIcon className="h-4 w-4" />
                            Create Invite
                          </Button>
                        ) : (
                          <Button
                            variant="primary-button"
                            className="sm:ml-auto cursor-pointer"
                            onClick={() => router.push(`/event/${eventId}/invites/new/edit`)}
                          >
                            <Plus className="h-4 w-4" />
                            Create Invite
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <h2 className="text-xl font-bold">{event.eventName}</h2>
                        <div className="flex flex-wrap gap-2 mt-1">
                          <Badge variant="outline">
                            ID: {event.ID}
                          </Badge>
                          {isManager && (
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                              Managed
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        {event.paymentStatus === 'paid' && event.plan && (
                          <Badge className="bg-purple-500 hover:bg-purple-600">
                            {event.plan === 'host_plus' ? 'Host+' : event.plan === 'host_pro' ? 'Host Pro' : 'Free'}
                          </Badge>
                        )}
                        <Badge className={`${event.status === 'active' ? 'bg-green-500 hover:bg-green-600' : 'bg-yellow-500 hover:bg-yellow-600'}`}>
                          {event.status === 'active' ? 'Active' : 'Pending Payment'}
                        </Badge>
                        {!isManager && (
                          <>
                            {isEventLocked(event) ? (
                              <div className="flex items-center">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="ml-2 cursor-not-allowed opacity-60"
                                  disabled={true}
                                  title={getEventLockMessage(event) || "Event locked"}
                                >
                                  <LockIcon className="h-4 w-4" />
                                  Edit Event
                                </Button>
                              </div>
                            ) : (
                              <Link href={`/event/${eventId}/edit`}>
                                <Button variant="outline" size="sm" className="ml-2">
                                  <Edit className="h-4 w-4" />
                                  Edit Event
                                </Button>
                              </Link>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {event.message ? (
                      <div>
                        <p className={`text-sm text-muted-foreground whitespace-pre-wrap ${isMessageExpanded ? '' : 'line-clamp-4'}`}>
                          {event.message}
                        </p>
                        {event.message.length > 200 && (
                          <button
                            onClick={() => setIsMessageExpanded(!isMessageExpanded)}
                            className="text-blue-500 text-sm mt-2 focus:outline-none cursor-pointer hover:text-blue-600 transition-colors"
                          >
                            {isMessageExpanded ? 'See less' : 'See more'}
                          </button>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No message provided for this event.</p>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2">
                      <div className="flex items-start gap-3">
                        <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium text-sm">Date</p>
                          <p className="text-sm text-muted-foreground">{FormatDate(event.eventDate)}</p>
                          {event.rsvpDueDate && (
                            <div className="mt-1">
                              <p className="text-xs font-medium text-muted-foreground">RSVP Deadline</p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(event.rsvpDueDate).toLocaleDateString()} at {new Date(event.rsvpDueDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                {new Date() > new Date(event.rsvpDueDate) && (
                                  <Badge variant="destructive" className="ml-2 text-xs text-white ">Closed</Badge>
                                )}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Clock className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium text-sm">Time</p>
                          <p className="text-sm text-muted-foreground">
                            {Format24to12(event.start)} - {event?.start ? Format24to12(event.end) : "TBD"}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Timezone: {event.timezone || "Australia/Melbourne"}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium text-sm">Location</p>
                          <p className="text-sm text-muted-foreground">{event.location}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                      <div>
                        <h2 className="text-lg font-semibold">Guest Management</h2>
                        <p className="text-sm text-muted-foreground">Track and manage your event invitations</p>
                      </div>

                      {/* Large screen buttons - hidden on mobile and medium */}
                      <div className="hidden lg:flex flex-row gap-2 mt-3 lg:gap-2 lg:mt-0 w-full lg:w-auto lg:ml-auto">
                        <Button
                          variant="outline"
                          className="cursor-pointer flex-1 text-xs sm:text-sm sm:flex-none"
                          onClick={() => router.push(`/event/${eventId}/invites/design`)}
                          disabled={isEventDatePassed(event)}
                          title={isEventDatePassed(event) ? "Design is locked because the event date has passed" : ""}
                        >
                          <PenTool className="h-4 w-4" />
                          Design Invite Cards
                        </Button>
                        <Button
                          className="cursor-pointer flex-1 text-xs sm:text-sm sm:flex-none"
                          variant="outline"
                          onClick={() => router.push(`/event/${eventId}/invites`)}
                        >
                          <Users className="h-4 w-4" />
                          Manage Invites
                        </Button>
                        {isInviteManagementLocked(event) ? (
                          <Button
                            variant="outline"
                            className="cursor-not-allowed opacity-60 flex-1 text-xs sm:text-sm sm:flex-none"
                            disabled={true}
                            title={getEventLockMessage(event) || "Invite management locked"}
                          >
                            <LockIcon className="h-4 w-4 mr-1" />
                            Create Invite
                          </Button>
                        ) : (
                          <div className="flex items-center flex-1">
                            <Button
                              variant="primary-button"
                              className="rounded-r-none flex-1 text-xs sm:text-sm font-medium"
                              onClick={() => router.push(`/event/${eventId}/invites/new/edit`)}
                            >
                              <Plus className="h-4 w-4" />
                              Create Invite
                            </Button>

                            {/* Separator */}
                            <div className="w-px h-10 bg-white" />

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant={"primary-button"} className="w-8 rounded-l-none border-none shadow-none p-0 focus:outline-none focus:ring-0 focus-visible:ring-0">
                                  <ChevronDown className="w-5 h-5" />
                                </Button>
                              </DropdownMenuTrigger>                              <DropdownMenuContent className="w-56">
                                <DropdownMenuItem
                                  onClick={() => router.push(`/event/${eventId}/invites/csv-upload`)}
                                  className="cursor-pointer"
                                >
                                  <div className="flex items-center gap-2">
                                    <Upload className="w-4 h-4" />
                                    Create Bulk Invite
                                  </div>
                                </DropdownMenuItem>
                                {!isReminderLocked(event) && (
                                  <DropdownMenuItem
                                    onClick={() => transitionToModal(() => setReminderDialogOpen(true))}
                                    className="cursor-pointer"
                                  >
                                    <div className="flex items-center gap-2">
                                      <Bell className="w-4 h-4" />
                                      Send Reminder
                                    </div>
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        )}
                      </div>

                      {/* Medium screen buttons - only visible on medium screens */}
                      <div className="hidden sm:flex lg:hidden flex-row gap-2 mt-3 sm:gap-2 sm:mt-0 w-full sm:w-auto sm:ml-auto">
                        <Button
                          variant="outline"
                          className="cursor-pointer flex-1 text-xs sm:text-sm sm:flex-none"
                          onClick={() => router.push(`/event/${eventId}/invites/design`)}
                          disabled={isEventDatePassed(event)}
                          title={isEventDatePassed(event) ? "Design is locked because the event date has passed" : ""}
                        >
                          <PenTool className="h-4 w-4" />
                          Design Invite Cards
                        </Button>
                        {isInviteManagementLocked(event) ? (
                          <Button
                            variant="outline"
                            className="cursor-not-allowed opacity-60 flex-1 text-xs sm:text-sm sm:flex-none"
                            disabled={true}
                            title={getEventLockMessage(event) || "Invite management locked"}
                          >
                            <LockIcon className="h-4 w-4" />
                            Manage Invites
                          </Button>
                        ) : (
                          <div className="flex items-center flex-1">
                            <Button
                              variant="primary-button"
                              className="rounded-r-none flex-1 text-xs sm:text-sm font-medium"
                              onClick={() => router.push(`/event/${eventId}/invites`)}
                            >
                              <Users className="h-4 w-4" />
                              Manage Invites
                            </Button>

                            {/* Separator */}
                            <div className="w-px h-10 bg-white" />

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant={"primary-button"} className="w-8 rounded-l-none border-none shadow-none p-0 focus:outline-none focus:ring-0 focus-visible:ring-0">
                                  <ChevronDown className="w-5 h-5" />
                                </Button>
                              </DropdownMenuTrigger>                              <DropdownMenuContent className="w-56">
                                <DropdownMenuItem
                                  onClick={() => router.push(`/event/${eventId}/invites/new/edit`)}
                                  className="cursor-pointer"
                                >
                                  <div className="flex items-center gap-2">
                                    <Plus className="w-4 h-4" />
                                    Create New Invite
                                  </div>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => router.push(`/event/${eventId}/invites/csv-upload`)}
                                  className="cursor-pointer"
                                >
                                  <div className="flex items-center gap-2">
                                    <Upload className="w-4 h-4" />
                                    Create Bulk Invite
                                  </div>
                                </DropdownMenuItem>
                                {!isReminderLocked(event) && (
                                  <DropdownMenuItem
                                    onClick={() => transitionToModal(() => setReminderDialogOpen(true))}
                                    className="cursor-pointer"
                                  >
                                    <div className="flex items-center gap-2">
                                      <Bell className="w-4 h-4" />
                                      Send Reminder
                                    </div>
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        )}
                      </div>

                      {/* Mobile buttons - only visible on mobile */}
                      <div className="sm:hidden w-full mt-3 space-y-2">
                        {/* First row - Manage Invites with dropdown */}
                        <div className="flex gap-2">
                          {isInviteManagementLocked(event) ? (
                            <Button
                              variant="outline"
                              className="cursor-not-allowed opacity-60 flex-1"
                              disabled={true}
                              title={getEventLockMessage(event) || "Invite management locked"}
                            >
                              <LockIcon className="h-4 w-4" />
                              Manage Invites
                            </Button>
                          ) : (
                            <div className="flex items-center flex-1">
                              <Button
                                variant="primary-button"
                                className=" rounded-r-none flex-1 text-xs font-medium"
                                onClick={() => router.push(`/event/${eventId}/invites`)}
                              >
                                <Users className="h-4 w-4 mr-0" />
                                Manage Invites
                              </Button>

                              {/* Separator */}
                              <div className="w-px h-10 bg-white" />

                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant={"primary-button"} className="w-8 rounded-l-none border-none shadow-none p-0 focus:outline-none focus:ring-0 focus-visible:ring-0">
                                    <ChevronDown className="w-5 h-5" />
                                  </Button>
                                </DropdownMenuTrigger>                                <DropdownMenuContent className="w-56">
                                  <DropdownMenuItem
                                    onClick={() => router.push(`/event/${eventId}/invites/new/edit`)}
                                    className="cursor-pointer"
                                  >
                                    <div className="flex items-center gap-2">
                                      <Plus className="w-4 h-4" />
                                      Create New Invite
                                    </div>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => router.push(`/event/${eventId}/invites/csv-upload`)}
                                    className="cursor-pointer"
                                  >
                                    <div className="flex items-center gap-2">
                                      <Upload className="w-4 h-4" />
                                      Create Bulk Invite
                                    </div>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => transitionToModal(() => setReminderDialogOpen(true))}
                                    className="cursor-pointer"
                                  >
                                    <div className="flex items-center gap-2">
                                      <Bell className="w-4 h-4" />
                                      Send Reminder
                                    </div>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          )}
                        </div>

                        {/* Second row - Design Invitation button */}
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            className="cursor-pointer w-full border-rose-200 text-rose-600 hover:bg-rose-50 text-xs font-medium"
                            onClick={() => router.push(`/event/${eventId}/invites/design`)}
                            disabled={isEventDatePassed(event)}
                            title={isEventDatePassed(event) ? "Design is locked because the event date has passed" : ""}
                          >
                            <PenTool className="h-4 w-4" />
                            Design Invitation
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">


                    {/* Stats boxes */}
                    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="border-l-4 border-l-black border-t border-r border-b rounded-md p-4">
                        <p className="text-xl font-semibold">{rsvpReport.invites.total}</p>
                        <p className="text-sm text-muted-foreground">Total</p>
                      </div>
                      <div className="border-l-4 border-l-green-500 border-t border-r border-b rounded-md p-4">
                        <p className="text-xl font-semibold">{rsvpReport.invites.accepted}</p>
                        <div className="flex justify-between items-center">
                          <p className="text-sm text-muted-foreground">Accepted</p>
                          <p className="text-[16px] font-medium text-foreground">{rsvpReport.invites.total > 0 ? Math.round((rsvpReport.invites.accepted / rsvpReport.invites.total) * 100) : 0}%</p>
                        </div>
                      </div>
                      <div className="border-l-4 border-l-amber-500 border-t border-r border-b rounded-md p-4">
                        <p className="text-xl font-semibold">{rsvpReport.invites.total - (rsvpReport.invites.accepted + rsvpReport.invites.declined)}</p>
                        <div className="flex justify-between items-center">
                          <p className="text-sm text-muted-foreground">Pending</p>
                          <p className="text-[16px] font-medium text-foreground">{rsvpReport.invites.total > 0 ? Math.round(((rsvpReport.invites.total - (rsvpReport.invites.accepted + rsvpReport.invites.declined)) / rsvpReport.invites.total) * 100) : 0}%</p>
                        </div>
                      </div>
                      <div className="border-l-4 border-l-red-500 border-t border-r border-b rounded-md p-4">
                        <p className="text-xl font-semibold">{rsvpReport.invites.declined}</p>
                        <div className="flex justify-between items-center">
                          <p className="text-sm text-muted-foreground">Declined</p>
                          <p className="text-[16px] font-medium text-foreground">{rsvpReport.invites.total > 0 ? Math.round((rsvpReport.invites.declined / rsvpReport.invites.total) * 100) : 0}%</p>
                        </div>
                      </div>
                    </div>

                    {/* RSVP Status and Guest Count */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* RSVP Status */}
                      <Card>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-center">
                            <h3 className="text-base font-medium">RSVP Status</h3>
                            <p className="text-sm text-muted-foreground">{rsvpReport.invites.accepted + rsvpReport.invites.declined} of {rsvpReport.invites.total} responded</p>
                          </div>
                        </CardHeader>
                        <CardContent className="flex justify-center items-center py-2">
                          <div style={{ width: '100%', height: 200 }}>
                            {/* Use direct Recharts components for more control */}
                            <ResponsiveContainer width="100%" height="100%">
                              <RechartPieChart>
                                {rsvpReport.invites.total > 0 ? (
                                  <RechartPie
                                    data={[
                                      {
                                        name: "Accepted",
                                        value: rsvpReport.invites.accepted || 0,
                                        fill: "#22c55e" // green-500
                                      },
                                      {
                                        name: "Declined",
                                        value: rsvpReport.invites.declined || 0,
                                        fill: "#ef4444" // red-500
                                      },
                                      {
                                        name: "Pending",
                                        value: (rsvpReport.invites.total - (rsvpReport.invites.accepted + rsvpReport.invites.declined)) || 0,
                                        fill: "#f59e0b" // amber-500
                                      }
                                    ].filter(item => item.value > 0)}
                                    cx="50%"
                                    cy="50%"
                                    innerRadius={60}
                                    outerRadius={75}
                                    paddingAngle={2}
                                    dataKey="value"
                                  >
                                    {/* Add cells explicitly */}
                                    {rsvpReport.invites.accepted > 0 && <RechartCell key="cell-0" fill="#22c55e" />}
                                    {rsvpReport.invites.declined > 0 && <RechartCell key="cell-1" fill="#ef4444" />}
                                    {(rsvpReport.invites.total - (rsvpReport.invites.accepted + rsvpReport.invites.declined)) > 0 &&
                                      <RechartCell key="cell-2" fill="#f59e0b" />
                                    }
                                  </RechartPie>
                                ) : (
                                  <RechartPie
                                    data={[{ name: "No Data", value: 1 }]}
                                    cx="50%"
                                    cy="50%"
                                    innerRadius={60}
                                    outerRadius={80}
                                    fill="#e5e7eb" // gray-200
                                    dataKey="value"
                                  />
                                )}

                                {/* Add tooltip */}
                                <RechartTooltip
                                  content={({ active, payload }) => (
                                    <ChartTooltipContent
                                      active={active}
                                      payload={payload}
                                    />
                                  )}
                                  cursor={false}
                                  wrapperStyle={{
                                    outline: "none",
                                    zIndex: 100,
                                    pointerEvents: "none"
                                  }}
                                  animationDuration={300}
                                  animationEasing="ease-out"
                                />

                                {/* Add center text */}
                                <text
                                  x="50%"
                                  y="50%"
                                  textAnchor="middle"
                                  dominantBaseline="middle"
                                >
                                  <tspan
                                    x="50%"
                                    dy="-0.4em"
                                    style={{
                                      fontSize: '28px',
                                      fontWeight: 'bold',
                                      fill: 'currentColor'
                                    }}
                                  >
                                    {rsvpReport.invites.total}
                                  </tspan>
                                  <tspan
                                    x="50%"
                                    dy="1.6em"
                                    style={{
                                      fontSize: '13px',
                                      fill: '#6b7280' // text-muted-foreground
                                    }}
                                  >
                                    Total Invites
                                  </tspan>
                                </text>
                              </RechartPieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                        <CardFooter className="pt-0 flex justify-center">
                          <div className="flex gap-6">
                            <div className="flex items-center gap-1">
                              <div className="w-3 h-3 rounded-full bg-green-500"></div>
                              <span className="text-xs">Accepted</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <div className="w-3 h-3 rounded-full bg-red-500"></div>
                              <span className="text-xs">Declined</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                              <span className="text-xs">Pending</span>
                            </div>
                          </div>
                        </CardFooter>
                      </Card>

                      {/* Guest Count */}
                      <Card>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-center">
                            <h3 className="text-base font-medium">Guest Count</h3>
                            <p className="text-sm text-muted-foreground">{totalConfirmed} confirmed</p>
                          </div>
                        </CardHeader>
                        <CardContent className="flex flex-col justify-center h-[220px]">
                          <div className="space-y-5">
                            <div className="space-y-1">
                              <div className="flex justify-between items-center">
                                <p className="text-sm">Adults</p>
                                <p className="text-sm">{rsvpReport.adults.accepted} of {rsvpReport.adults.invited}</p>
                              </div>
                              <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-indigo-500 rounded-full"
                                  style={{ width: `${(rsvpReport.adults.accepted / rsvpReport.adults.invited) * 100}%` }}
                                ></div>
                              </div>
                            </div>

                            <div className="space-y-1">
                              <div className="flex justify-between items-center">
                                <p className="text-sm">Children</p>
                                <p className="text-sm">{rsvpReport.children.accepted} of {rsvpReport.children.invited}</p>
                              </div>
                              <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-pink-400 rounded-full"
                                  style={{ width: `${(rsvpReport.children.accepted / rsvpReport.children.invited) * 100}%` }}
                                ></div>
                              </div>
                            </div>

                            <div className="space-y-1">
                              <div className="flex justify-between items-center">
                                <p className="text-sm font-semibold">Total Guests</p>
                                <p className="text-sm font-semibold">{totalConfirmed} of {rsvpReport.total.invited}</p>
                              </div>
                              <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-black rounded-full"
                                  style={{ width: `${(totalConfirmed / rsvpReport.total.invited) * 100}%` }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>


                  </CardContent>
                </Card>                {/* Show EventManagers component with appropriate permissions */}
                <EventManagers
                  eventId={eventId as string}
                  managers={event?.managers || []}
                  onUpdate={handleUpdateManagers}
                  isManager={!!isManager}
                />

                {/* Danger Zone Section - Only show for event owners */}
                {!isManager && (
                  <Card className="border-red-200">
                    <CardHeader>
                      <h2 className="text-lg font-semibold text-red-600">Danger Zone</h2>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-4">
                        Actions in this section can lead to permanent data loss. Please proceed with caution.
                      </p>
                      <Button
                        variant="destructive"
                        className="w-full"
                        onClick={() => setDeleteDialogOpen(true)}
                      >
                        <Trash className="h-4 w-4" />
                        Delete Event
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Send Reminder Dialog */}
      {event && (
        <SendReminderDialog
          open={reminderDialogOpen}
          onOpenChange={setReminderDialogOpen}
          event={event}
          totalInvites={invites.length}
          invites={invites}
        />
      )}
    </ProtectedLayout>
  );
}

export default EventManagementScreen;