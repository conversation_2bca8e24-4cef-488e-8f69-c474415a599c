import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Event, EventInvite } from '@/types';
import { sendReminderEmail } from '@/lib/mailer';

/**
 * @api {POST} /event/:eventId/send-reminders Send reminder emails
 * @apiName SendReminders
 * @apiGroup Events
 * @apiDescription Sends reminder emails to all invites with email addresses
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId } = req.query;

  if (!eventId || typeof eventId !== 'string') {
    return res.status(400).json({ error: 'Event ID is required' });
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Get user info from session
    const session = await getServerSession(req, res, authConfig);

    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get event data
    const event = await Database.getInstance().readData('events', eventId) as Event;

    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Verify user has access to this event
    if (event.ownerEmail !== session.user.email &&
        (!event.managers || !event.managers.includes(session.user.email))) {
      return res.status(403).json({ error: 'You do not have permission to send reminders for this event' });
    }

    // Get all invites for this event
    const invites = await Database.getInstance().ListData('invites', {
      field: 'eventId',
      operator: '==',
      value: Database.normalizeId(eventId)
    }) as EventInvite[];

    // Filter invites that have emails and are not declined
    const invitesWithEmail = invites.filter(invite =>
      invite.email &&
      invite.email.trim() !== '' &&
      invite.status !== 'declined'
    );

    if (invitesWithEmail.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No invites with email addresses found',
        sentCount: 0
      });
    }

    // Get the message from the request body
    const { message } = req.body;

    // Send reminder emails
    let sentCount = 0;

    for (const invite of invitesWithEmail) {
      try {
        // Send reminder email using the mailer function
        const emailSent = await sendReminderEmail(event, invite, message);

        if (emailSent) {
          // Add to activity history but don't store in message array
          const activityItem = {
            timestamp: new Date().toISOString(),
            type: 'reminder_sent',
            details: {
              email: invite.email
            }
          };

          // We're not storing reminder activities in the message array anymore
          // Just log the activity without updating the invite
          log(`Reminder activity recorded for invite ${invite.ID}: ${JSON.stringify(activityItem)}`);

          sentCount++;
        }
      } catch (error) {
        console.error(`Error processing reminder for ${invite.email}:`, error);
        // Continue with the next invite even if one fails
      }
    }

    return res.status(200).json({
      success: true,
      message: `Sent ${sentCount} reminder emails`,
      sentCount
    });
  } catch (error) {
    console.error('Error sending reminders:', error);
    return res.status(500).json({ error: 'Failed to send reminders' });
  }
}
