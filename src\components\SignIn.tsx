"use client"

import { signIn, useSession } from "next-auth/react"
import { Loader2, CheckCircle2 } from "lucide-react"
import { Alert, AlertDescription } from "./ui/alert"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { useState, useEffect } from "react"
// We're using signIn directly now
import { useGoogleAuth } from "@/hooks/useGoogleAuth"
import { useRouter } from "next/router"
import { Separator } from "./ui/separator"
import Image from "next/image"
import { PasswordSignIn } from "./PasswordSignIn";
import { EmailMagicLinkSignIn } from "./EmailMagicLinkSignIn";
import { GoogleSignInButton } from "./GoogleSignInButton";

export function SignInForm() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'email' | 'google' | 'password'>('password');
  const [showSignUp, setShowSignUp] = useState(false);

  interface FeatureCardProps {
    icon: string;
    alt: string;
    title: string;
    description: string;
  }

  const featureCards: FeatureCardProps[] = [
    {
      icon: "/easy-event-creation.svg",
      alt: "Easy Event Creation",
      title: "Easy Event Creation",
      description: "Create beautiful event pages in minutes with our intuitive interface.",
    },
    {
      icon: "/smart-RSVP-management.svg",
      alt: "Smart RSVP Management",
      title: "Smart RSVP Management",
      description: "Track responses, manage guests, and even collect dietary preferences with ease.",
    },
    {
      icon: "/multiple-ways-to-invite.svg",
      alt: "Multiple Ways to Invite",
      title: "Multiple Ways to Invite",
      description: "Send invites via email, share QR codes, or drop a link in your group chat.",
    },
    {
      icon: "/realtime-updates.svg",
      alt: "Real-time Updates",
      title: "Real-time Updates",
      description: "Get notified the moment someone RSVPs, cancels, or leaves a note.",
    },
  ];
  function FeatureCard({ icon, alt, title, description }: FeatureCardProps) {
    return (
      <div className="bg-white rounded-md border border-slate-200 flex flex-col items-center px-4 pt-5 pb-6 w-full min-w-0">
        <div className="mb-2 h-9 w-9 flex items-center justify-center">
          <img src={icon} alt={alt} />
        </div>
        <div className="font-semibold text-base text-foreground text-center">{title}</div>
        <div className="text-sm text-muted-foreground text-center mt-1">{description}</div>
      </div>
    );
  }

  // Check if there's a provider parameter in the URL
  useEffect(() => {
    const provider = router.query.provider;
    if (provider === 'email') {
      setActiveTab('email');
    }
  }, [router.query]);

  // Don't redirect here - let the parent SignInPage handle it
  // This prevents duplicate redirects
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      // Log organization details
      console.log("User authenticated:", session.user.id);
      console.log("User name:", session.user.name);
      console.log("User email:", session.user.email);

      // Log organization details if available
      if (session.user.organization) {
        console.log("Organization details:");
        console.log("  ID:", session.user.organization.id);
        console.log("  Name:", session.user.organization.name);
        console.log("  Type:", session.user.organization.type);
      } else {
        console.log("No organization details available");
      }

      // Redirection is handled by the parent SignInPage component
    }
  }, [status, session]);

  // Show loading state when checking authentication status
  if (status === 'loading') {
    return (
      <div className="flex min-h-screen w-full items-center justify-center bg-white">
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="mt-2 text-sm text-muted-foreground">Checking your authentication status...</p>
        </div>
      </div>
    );
  } 

  return (
    <div className="flex min-h-screen w-full flex-row bg-white">
      {/* Left Side: Marketing/Features */}
      <div className="hidden lg:flex flex-col justify-center items-center w-1/2 bg-orange-50 relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/HostMomentsBackground.svg"
            alt="Background pattern"
            fill
            className="object-cover"
          />
        </div>        <div className="relative z-10 flex flex-col items-center w-full px-6 lg:px-8 xl:px-12">
          <h2 className="font-semibold text-2xl lg:text-3xl text-foreground mb-2 text-center tracking-tight">Get Started with I am Coming</h2>
          <p className="text-base lg:text-lg text-muted-foreground mb-8 lg:mb-12 text-center">Host smarter, faster, and better — all in one place.</p>
          <div className="grid grid-cols-2 gap-4 lg:gap-x-6 lg:gap-y-6 xl:gap-x-8 xl:gap-y-6 w-full max-w-sm lg:max-w-md xl:max-w-lg 2xl:max-w-xl">
            {featureCards.map((card) => (
              <FeatureCard key={card.title} {...card} />
            ))}
          </div>

        </div>
      </div>
      {/* Right Side: Sign In Form */}
      <div className="flex flex-col justify-center items-center w-full lg:w-1/2 bg-white min-h-screen">
        <div className="mb-8">
          <Image
            src="/iac-logo-large.svg"
            alt="I am Coming - Event RSVP Management Platform"
            className="h-10 w-auto cursor-pointer"
            width={200}
            height={40}
            onClick={() => window.location.href = '/'}
          />
        </div>
        <Card className="w-full max-w-md mx-auto border border-[#E2E8F0]">
          <CardHeader className="pb-4">
            <CardTitle className="text-2xl font-semibold text-center">Welcome</CardTitle>
            <CardDescription className="text-center">
              Sign in to your account to continue
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-5">
            {/* Password Sign In */}
            {activeTab === 'password' && (
              <PasswordSignIn
                error={error}
                isLoading={isLoading}
                showSignUp={showSignUp}
                setShowSignUp={setShowSignUp}
              />
            )}

            {/* Email Magic Link Sign In */}
            {activeTab === 'email' && (
              <EmailMagicLinkSignIn
                error={error}
                isLoading={isLoading}
                setActiveTab={setActiveTab}
                setShowSignUp={setShowSignUp}
              />
            )}

            {/* Google Sign In (button only, layout unchanged) */}
            {activeTab === 'password' && !showSignUp && (
              <>
                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-card px-2 text-muted-foreground">Or continue with</span>
                  </div>
                </div>
                <GoogleSignInButton />
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}