/**
 * Shared type definitions for Google Analytics
 */

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    // Testing utilities (set by ga-testing.ts when loaded)
    testUrlGrouping: () => void;
    testGAEvents: () => void;
    testEventTracking: () => void;
    checkCurrentPageGrouping: () => void;
    monitorDataLayer: () => void;
    runAllTests: () => void;
  }
}

export { };
