/**
 * Centralized configuration for authentication and allowed sites
 * 
 * Note: RBAC (Role-Based Access Control) functionality has been moved to:
 * @see /lib/auth/RBAC/ for the new modular RBAC system
 */

export type AllowedSite = 'partner' | 'local';

/**
 * Configuration for allowed external sites
 * This is the single source of truth for allowed sites across the application
 */
export const ALLOWED_SITES: Record<AllowedSite, string> = {
  partner: 'https://partner.iamcoming.io',
  local: 'http://localhost:3001'
};

/**
 * JWT audience configuration (domains that can use the JWT tokens)
 */
export const JWT_AUDIENCES = [
  'partner.iamcoming.io',
  'localhost:3000',
  'localhost:3001'
];



/**
 * Check if a site is allowed
 */
export function isAllowedSite(site: string): site is AllowedSite {
  return site in ALLOWED_SITES;
}

/**
 * Get the URL for an allowed site
 */
export function getSiteUrl(site: AllowedSite): string {
  return ALLOWED_SITES[site];
}

/**
 * Get all allowed site keys
 */
export function getAllowedSiteKeys(): AllowedSite[] {
  return Object.keys(ALLOWED_SITES) as AllowedSite[];
}


