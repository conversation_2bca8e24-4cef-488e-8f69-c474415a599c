import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ider } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormField, FormMessage } from "./ui/form";
import { Label } from "./ui/label";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { Alert, AlertDescription } from "./ui/alert";
import { Loader2, Mail } from "lucide-react";
import React, { useState } from "react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/router";

type EmailMagicLinkSignInProps = {
  error: string;
  isLoading: boolean;
  setActiveTab: (tab: 'password' | 'email' | 'google') => void;
  setShowSignUp: (show: boolean) => void;
};

const emailSchema = z.object({
  email: z.string().email("Invalid email address"),
});

type EmailFormValues = z.infer<typeof emailSchema>;

export function EmailMagicLinkSignIn({ error, isLoading, setActiveTab, setShowSignUp }: EmailMagicLinkSignInProps) {
  const [localError, setLocalError] = useState("");
  const [loading, setLoading] = useState(false);
  const [magicLinkSent, setMagicLinkSent] = useState(false);
  const [email, setEmail] = useState("");
  const router = useRouter();
  const emailForm = useForm<EmailFormValues>({
    resolver: zodResolver(emailSchema),
    defaultValues: { email: "" },
  });

  const handleEmailSignIn: SubmitHandler<EmailFormValues> = async (formData) => {
    setLoading(true);
    setLocalError("");
    try {
      const redirectUrl = sessionStorage.getItem("redirectUrl") || (router.query.callbackUrl as string) || "/events";
      const result = await signIn("email", {
        email: formData.email,
        redirect: false,
        callbackUrl: redirectUrl,
      });
      if (result?.error) {
        throw new Error(result.error);
      }
      setEmail(formData.email);
      setMagicLinkSent(true);
    } catch (error) {
      setLocalError(error instanceof Error ? error.message : "Failed to send magic link");
    } finally {
      setLoading(false);
    }
  };

  return (
    <FormProvider {...emailForm}>
      <form onSubmit={emailForm.handleSubmit(handleEmailSignIn)} className="space-y-4">
        <FormField
          control={emailForm.control}
          name="email"
          render={({ field }) => (
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" className="h-11 shadow-none border-[#E2E8F0]" {...field} />
              <FormMessage />
            </div>
          )}
        />
        {(error || localError) && (
          <Alert variant="destructive">
            <AlertDescription>{error || localError}</AlertDescription>
          </Alert>
        )}
        <Button type="submit" className="w-full h-11 mt-2" disabled={isLoading || loading}>
          {isLoading || loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending magic link...
            </>
          ) : (
            <>
              <Mail className="mr-2 h-4 w-4" />
              Send magic link
            </>
          )}
        </Button>
      </form>
      <div className="mt-6 text-center">
        <Button
          variant="link"
          onClick={() => {
            setActiveTab('password');
            setShowSignUp(false);
          }}
          className="text-sm text-muted-foreground"
        >
          Back to sign in with password
        </Button>
      </div>
      {magicLinkSent && (
        <Alert>
          <AlertDescription>
            Magic link sent to {email}. Please check your email.
          </AlertDescription>
        </Alert>
      )}
    </FormProvider>
  );
}
