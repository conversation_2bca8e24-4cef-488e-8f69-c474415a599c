import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { ActivityHistoryItem, AnalyticsEvent, Event } from '@/types';

// Define a more flexible activity type for our combined activities
type ActivityType = {
  type: string;
  timestamp: string;
  userId?: string | null;
  properties?: Record<string, any>;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { eventId, inviteId } = req.query;

    if (!eventId || typeof eventId !== 'string' || !inviteId || typeof inviteId !== 'string') {
      return res.status(400).json({ error: 'Invalid event or invite ID' });
    }

    // Check auth
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the invite to access its activity history
    const invite = await Database.getInstance().readData('invites', inviteId);
    if (!invite) {
      return res.status(404).json({ error: 'Invite not found' });
    }

    // Get the event to verify permissions
    const event = await Database.getInstance().readData('events', eventId) as Event;
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Check if the user has permission to view this event
    // Check both ownerEmail and ownerAccountId for backward compatibility
    const isOwner = (event.ownerEmail === session.user.email) ||
                    (event.ownerAccountId === session.user.id);

    // Check if user is a manager - ensure managers is an array before using includes
    // Check both email and user ID in the managers array
    const isManager = Array.isArray(event.managers) &&
                     (event.managers.includes(session.user.email) ||
                      event.managers.includes(session.user.id));

    if (!isOwner && !isManager) {
      return res.status(403).json({ error: 'Permission denied' });
    }

    // Get activity history from the invite
    const activityHistory = invite.activityHistory || [];

    // Get analytics events for this invite (in case they're not in activityHistory)
    const analyticsEvents = await Database.getInstance().ListData('analytics', {
      field: 'properties.inviteId',
      operator: '==',
      value: inviteId
    }) as AnalyticsEvent[];

    // Combine all activities and sort by timestamp
    const activities: ActivityType[] = [
      // Include the activity history
      ...activityHistory,

      // Map analytics events (that might not be in activityHistory)
      ...analyticsEvents.map(event => ({
        type: event.name,
        timestamp: typeof event.timestamp === 'string' ? event.timestamp : event.timestamp.toISOString(),
        userId: event.userId,
        properties: event.properties
      }))
    ].sort((a, b) => {
      const dateA = new Date(a.timestamp);
      const dateB = new Date(b.timestamp);
      return dateB.getTime() - dateA.getTime(); // Sort descending (newest first)
    });

    // Filter activities to only show the ones we want in Activity History
    // RSVP link was opened, Invitation Accepted, Invitation Declined
    const filteredActivities = activities.filter((activity) => {
      // Only show 'invite_link_opened' entries that have the source 'rsvp_link'
      if (activity.type === 'invite_link_opened') {
        return activity.properties?.source === 'rsvp_link';
      }

      if (activity.type === 'rsvp_response' && activity.properties) {
        const status = activity.properties.status as string;
        return status === 'accepted' || status === 'declined';
      }

      return false;
    });

    return res.status(200).json({ activities: filteredActivities });
  } catch (error) {
    console.error('Error fetching invite activity:', error);
    return res.status(500).json({ error: 'Failed to fetch invite activity' });
  }
}