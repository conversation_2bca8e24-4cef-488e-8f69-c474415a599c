#!/bin/bash

# Script to enable session debugging for IAC application
# This will set the IAC_DEBUG environment variable to enable detailed logging
# in the /api/id/session endpoint

echo "🔧 Enabling session debugging for IAC application..."

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "📝 Creating .env.local file..."
    touch .env.local
fi

# Check if IAC_DEBUG is already set
if grep -q "IAC_DEBUG" .env.local; then
    echo "⚠️  IAC_DEBUG already exists in .env.local"
    echo "📝 Updating IAC_DEBUG to true..."
    sed -i '' 's/IAC_DEBUG=.*/IAC_DEBUG=true/' .env.local
else
    echo "📝 Adding IAC_DEBUG=true to .env.local..."
    echo "" >> .env.local
    echo "# Session debugging" >> .env.local
    echo "IAC_DEBUG=true" >> .env.local
fi

echo "✅ Session debugging enabled!"
echo ""
echo "📋 To view debug logs:"
echo "1. Restart your development server"
echo "2. Make requests to /api/id/session"
echo "3. Check the server console for detailed debug output"
echo ""
echo "🔍 Debug logs will show:"
echo "   - Request headers and cookies"
echo "   - Environment variables"
echo "   - Cookie parsing details"
echo "   - Token verification steps"
echo "   - NextAuth session details"
echo "   - Database lookup operations"
echo "   - Role assignment logic"
echo ""
echo "💡 To disable debugging, run: echo 'IAC_DEBUG=false' >> .env.local"
