"use client"

import { useEvent } from "@/hooks/useEvent";
import { useInvites } from "@/hooks/useInvites";
import { useState } from "react";
import { InviteSelector } from "@/components/InviteSelector";
import {
  Ta<PERSON>,
  <PERSON><PERSON>List,
  Ta<PERSON>Trigger,
  TabsContent
} from "@/components/ui/tabs";
import {
  RadioGroup,
  RadioGroupItem
} from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Info } from "lucide-react";
import { cn } from "@/lib/utils";

interface ExportInvitesProps {
  eventId: string;
  className?: string;
}

export function ExportInvites({ eventId, className }: ExportInvitesProps) {
  const { event } = useEvent(eventId);
  const { invites } = useInvites(eventId);
  const [selectedTab, setSelectedTab] = useState<string>("qr-labels");
  const [exportFormat, setExportFormat] = useState<string>("pdf");
  const [selectedInvites, setSelectedInvites] = useState<string[]>([]);

  // Generate export URL
  const getExportUrl = () => {
    const type = `${selectedTab}-${exportFormat}`;
    return `/api/media/export?type=${type}&invites=${selectedInvites.join(',')}`;
  };

  return (
    <div className={cn("grid grid-rows-[auto_1fr_auto] px-4 pb-4 bg-white shadow-md rounded-lg gap-1", className)}>
      <Tabs defaultValue="qr-labels" onValueChange={setSelectedTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="qr-labels">QR Labels</TabsTrigger>
          <TabsTrigger value="invitation-cards">Invitation Cards</TabsTrigger>
        </TabsList>

        <TabsContent value="qr-labels" className="space-y-4">
          <RadioGroup
            value={exportFormat}
            onValueChange={setExportFormat}
            className="gap-2"
          >
            <div className="flex items-start space-x-3 mt-2">
              <RadioGroupItem value="pdf" id="qr-pdf" />
              <div className="space-y-1 flex-1">
                <div className="flex items-center justify-between">
                  <Label htmlFor="qr-pdf" className="font-medium">
                    PDF Label Sheet (Recommended)
                  </Label>
                  {/* Info button - only visible on small screens */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <button className="sm:hidden p-1 rounded-full hover:bg-gray-100 transition-colors">
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogDescription>
                          Download a ready-to-print PDF where each invitation is on its own separate page.
                        </DialogDescription>
                      </DialogHeader>
                    </DialogContent>
                  </Dialog>
                </div>
                {/* Description text - hidden on small screens */}
                <p className="text-sm text-muted-foreground hidden sm:block">
                  Download a ready-to-print PDF where each invitation is on its own separate page.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <RadioGroupItem value="jpeg" id="qr-jpeg" />
              <div className="space-y-1 flex-1">
                <div className="flex items-center justify-between">
                  <Label htmlFor="qr-jpeg" className="font-medium">
                    Download as Invite Label image (JPEG)
                  </Label>
                  {/* Info button - only visible on small screens */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <button className="sm:hidden p-1 rounded-full hover:bg-gray-100 transition-colors">
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogDescription>
                          Each Label for each invite selected. More than one labels selected will download a zip archive with all selected label images.
                        </DialogDescription>
                      </DialogHeader>
                    </DialogContent>
                  </Dialog>
                </div>
                {/* Description text - hidden on small screens */}
                <p className="text-sm text-muted-foreground hidden sm:block">
                  Each Label for each invite selected. More than one labels selected will download a zip archive with all selected label images.
                </p>
              </div>
            </div>
          </RadioGroup>

          <Separator className="my-4" />
        </TabsContent>

        <TabsContent value="invitation-cards" className="space-y-4">
          <RadioGroup
            value={exportFormat}
            onValueChange={setExportFormat}
            className="gap-2"
          >
            <div className="flex items-start space-x-3 mt-2">
              <RadioGroupItem value="pdf" id="invite-pdf" />
              <div className="space-y-1 flex-1">
                <div className="flex items-center justify-between">
                  <Label htmlFor="invite-pdf" className="font-medium">
                    PDF Invites (Recommended)
                  </Label>
                  {/* Info button - only visible on small screens */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <button className="sm:hidden p-1 rounded-full hover:bg-gray-100 transition-colors">
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogDescription>
                          Download ready to print PDF file with each printable invite as an individual page.
                        </DialogDescription>
                      </DialogHeader>
                    </DialogContent>
                  </Dialog>
                </div>
                {/* Description text - hidden on small screens */}
                <p className="text-sm text-muted-foreground hidden sm:block">
                  Download ready to print PDF file with each printable invite as an individual page.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <RadioGroupItem value="jpeg" id="invite-jpeg" />
              <div className="space-y-1 flex-1">
                <div className="flex items-center justify-between">
                  <Label htmlFor="invite-jpeg" className="font-medium">
                    Download as Invitation image (JPEG)
                  </Label>
                  {/* Info button - only visible on small screens */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <button className="sm:hidden p-1 rounded-full hover:bg-gray-100 transition-colors">
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogDescription>
                          Each selected invite downloads its individual invitation. Multiple selections will download a ZIP with all invitation images.
                        </DialogDescription>
                      </DialogHeader>
                    </DialogContent>
                  </Dialog>
                </div>
                {/* Description text - hidden on small screens */}
                <p className="text-sm text-muted-foreground hidden sm:block">
                  Each selected invite downloads its individual invitation. Multiple selections will download a ZIP with all invitation images.
                </p>
              </div>
            </div>
          </RadioGroup>

          <Separator className="my-4" />
        </TabsContent>
      </Tabs>

      <div className="min-h-0">
        <InviteSelector
          invites={invites || []}
          selectedInvites={selectedInvites}
          onChange={(ids) => setSelectedInvites(ids)}
        />
      </div>

      <Button
        variant={"primary-button"}
        asChild
        disabled={selectedInvites.length === 0}
        className="w-full"
      >
        <a href={getExportUrl()} target="_blank" rel="noopener noreferrer">
          Download {selectedInvites.length} {selectedInvites.length === 1 ? 'invite' : 'invites'}
        </a>
      </Button>
    </div>
  );
}
