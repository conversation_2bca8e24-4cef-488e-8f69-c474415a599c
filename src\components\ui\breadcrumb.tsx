import * as React from "react";
import Link from "next/link";
import {
  ChevronRight,
  Home,
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface BreadcrumbItem {
  href: string;
  label: string;
  isCurrent?: boolean;
}

interface BreadcrumbProps extends React.HTMLAttributes<HTMLDivElement> {
  items: BreadcrumbItem[];
  separator?: React.ReactNode;
  showHomeIcon?: boolean;
}

const Breadcrumb = React.forwardRef<HTMLDivElement, BreadcrumbProps>(
  ({ className, items, separator = <ChevronRight className="h-4 w-4" />, showHomeIcon = true, ...props }, ref) => {
    return (
      <nav
        aria-label="Breadcrumb"
        className={cn("flex items-center text-sm text-muted-foreground", className)}
        ref={ref}
        {...props}
      >
        <ol className="flex items-center gap-1.5">
          {showHomeIcon && (
            <li className="flex items-center">
              <Link href="/" className="flex items-center hover:text-foreground transition-colors">
                <Home className="h-4 w-4" />
                <span className="sr-only">Home</span>
              </Link>
            </li>
          )}
          
          {items.map((item, index) => (
            <li key={item.href} className="flex items-center">
              {index > 0 || showHomeIcon ? (
                <span className="mx-1 text-muted-foreground">{separator}</span>
              ) : null}
              
              {item.isCurrent ? (
                <span className="font-medium text-foreground" aria-current="page">
                  {item.label}
                </span>
              ) : (
                <Link 
                  href={item.href} 
                  className="hover:text-foreground transition-colors"
                  aria-current={item.isCurrent ? "page" : undefined}
                >
                  {item.label}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </nav>
    );
  }
);

Breadcrumb.displayName = "Breadcrumb";

export { Breadcrumb };