import { APIResponse } from '@/types';
import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { EventInvite } from '@/types';
import { log } from '@/lib/logger';
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth";

/**
 * @api {GET} /event/:eventId/invites/:inviteId Get invite details
 * @apiName Get Invite
 * @apiGroup Invites
 * @apiDescription Fetches details of a specific invite
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId, inviteId } = req.query;

  if (!eventId || !inviteId) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  try {
    // For GET requests, no authentication is required
    if (req.method === 'GET') {
      const getResponse = await GET(inviteId as string);
      return res.status(getResponse.code).json(getResponse.data);
    }
    
    // For all other methods, require authentication
    const session = await getServerSession(req, res, authConfig);
    if (!session) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    switch (req.method) {
      case 'DELETE':
        // For DELETE operations, check if the user is the owner of the event
        const event = await Database.getInstance().readData('events', eventId as string);
        if (!event) {
          return res.status(404).json({ error: "Event not found" });
        }

        // Check if the user is the owner of the event
        const isOwner = (event.ownerEmail === session.user.email) ||
                        (event.ownerAccountId === session.user.id);

        if (!isOwner) {
          return res.status(403).json({
            error: "Permission denied",
            message: "Only the event owner can delete invites"
          });
        }

        const deleteResponse = await DELETE(inviteId as string);
        res.status(deleteResponse.code).json(deleteResponse.data);
        break;

      case 'POST':
        const response = await POST(inviteId as string, req.body);
        res.status(response.code).json(response.data);
        break;

      default:
        res.setHeader('Allow', ['GET', 'DELETE', 'POST']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Fetches a specific invite by ID
 * 
 * @param inviteId The ID of the invite to fetch
 * @returns APIResponse containing the invite details
 */
async function GET(inviteId: string): Promise<APIResponse> {
  const invite = await Database.getInstance().readData('invites', Database.normalizeId(inviteId));

  log('Invite fetched [' + inviteId + ']', invite);

  return {
    data: {
      ...invite
    },
    code: 200,
    headers: {
      'Content-Type': 'application/json',
    }
  }
}

/**
 * Updates an invite
 * 
 * @param inviteId The ID of the invite to update
 * @param data The updated invite data
 * @returns APIResponse containing the updated invite
 */
async function POST(inviteId: string, data: Partial<EventInvite>): Promise<APIResponse> {
  // Make sure the ID is correctly set in the data
  const normalizedId = Database.normalizeId(inviteId);
  
  try {
    // First, get the existing invite to make sure it exists
    const existingInvite = await Database.getInstance().readData('invites', normalizedId);
    
    if (!existingInvite) {
      return {
        data: { error: "Invite not found" },
        code: 404,
        headers: {
          'Content-Type': 'application/json',
        }
      };
    }
    
    // Prepare the updated data with the ID property
    const updatedData = {
      ...data,
      ID: normalizedId, // Make sure ID is in the correct format expected by addData
      // Ensure these required fields are present
      eventId: data.eventId || existingInvite.eventId,
      status: data.status || existingInvite.status,
      name: data.name || existingInvite.name,
      email: data.email || existingInvite.email,
      phone: data.phone || existingInvite.phone,
      // Preserve the original createdAt timestamp if it exists
      createdAt: existingInvite.createdAt || new Date().toISOString(),
      // Add updatedAt timestamp for the current update
      updatedAt: new Date().toISOString(),
      // Preserve the activityHistory if it exists
      activityHistory: existingInvite.activityHistory || [],
      // Preserve the response data if it exists
      response: data.response || existingInvite.response || null,
      // Preserve the message array if it exists
      message: data.message || existingInvite.message || []
    };

    // Update the invite - use addData which replaces the entire document
    await Database.getInstance().addData('invites', updatedData);
    
    log('Invite updated [' + inviteId + ']', updatedData);
    
    return {
      data: updatedData,
      code: 200,
      headers: {
        'Content-Type': 'application/json',
      }
    };
  } catch (error) {
    console.error('Error updating invite:', error);
    return {
      data: { error: "Failed to update invite" },
      code: 500,
      headers: {
        'Content-Type': 'application/json',
      }
    };
  }
}

/**
 * Deletes a specific invite by ID
 * 
 * @param inviteId The ID of the invite to delete
 * @returns APIResponse containing the deletion status and invite details
 */
async function DELETE(inviteId: string): Promise<APIResponse> {
  // First get the invite details before deleting
  const invite = await Database.getInstance().readData('invites', Database.normalizeId(inviteId)) as EventInvite;

  if (!invite) {
    return {
      data: null,
      code: 404,
      headers: {
        'Content-Type': 'application/json',
      }
    }
  }

  const name = invite?.name || 'Unknown';
  
  // Then delete the invite
  await Database.getInstance().deleteData('invites', Database.normalizeId(inviteId));

  log('Invite deleted [' + inviteId + ']');

  return {
    data: {
      success: true,
      message: "Invite deleted successfully",
      invite: {
        id: inviteId,
        name
      }
    },
    code: 200,
    headers: {
      'Content-Type': 'application/json',
    }
  }
}