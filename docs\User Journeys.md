# User Journeys

This document outlines the user journeys for both hosts and guests on the event management platform. Each journey includes the user type, goal, preconditions, postconditions, and steps to complete the journey.

The journeys are designed to provide a clear understanding of the user experience and the interactions with the platform.

## User Types
- **Host**: The person who creates and manages the event.
- **Event Manager**: A person who is added by the host to manage the event.
- **Guest**: A person who is invited to the event and can RSVP.

## User Journey Overview
- **Host/Event Owner**: Create and manage events, invite guests, and view analytics.
- **Event Manager**: Assist the host in managing the event, including invites and analytics.
- **Guest**: View event details, RSVP, and receive confirmation.
- **Event**: An event created by the host, which can be free or paid. The event can have multiple invites and guests can RSVP to the event.
- **Invite**: An invitation sent to guests for the event. Each invite can have a unique QR code and link.
- **RSVP**: A response from the guest indicating whether they will attend the event or not.
- **Analytics**: Data and insights related to the event, including guest responses and attendance.
- **Payment**: The process of paying for a paid event. The host can choose to pay using credit card or other payment methods.
- **Stripe**: A payment processing platform used for handling payments for paid events.
- **QR Code**: A unique code generated for each invite that can be scanned by guests to access the event details.
- **Event Details**: Information about the event, including title, description, date, time, location, and RSVP options.

## Host

### 1. Host creates an event
- **User**: Host
- **Goal**: Create an event 

- **Preconditions**: Host is logged into the platform and has access to the event creation feature.
- **Postconditions**: Event is created and listed on the platform.

- **Steps**:
  1. Host navigates to the "Create Event" page.
  2. Host fills in event details (title, description, date, time, location).
  3. Host selects event plan (Free or Paid).
  4. Host is navigated to the payment page if they select a paid plan. The are shown with the invoice details.
  5. Host is navigated to Stripe hosted payment page.
  6. Host enters payment details (credit card information) to complete payment.
  7. Host is redirected back to the platform after successful payment.
  8. Host is shown a Event Details page with updated status and tags.

### 2. Host manages event as Event Owner
- **User**: Host
- **Goal**: Manage event details
- **Preconditions**: Host is logged into the platform and has created an event.
- **Postconditions**: Event details are updated successfully.
- **Steps**:
  1. Host navigates to the "My Events" page.
  2. Host selects the event they want to manage with managed tag displayed.
  3. Host see the follwing on event page:
      - Event details (title, description, date, time, location).
      - Invite Options (Analytics, printing Invites/labels, View all invite and create invite).
      - Event managers section (Add event managers, remove event managers).
      - Event Deleteion section (Delete event).
  3. Host clicks on "Edit Event" button.
  4. Host updates event details (title, description, date, time, location).
  5. Host clicks on "Save Changes" button.
  6. Host is shown the updated Event Details page with new status and tags.

### 3. Host manages event as Event Manager
- **User**: Host
- **Goal**: Manage event details
- **Preconditions**: Host is logged into the platform and has beem added as mananger to some event.
- **Postconditions**: Event host cannot edit event details or delete the event. Can only view the event details, analytics and manange invites.
- **Steps**:
  1. Host navigates to the "My Events" page.
  2. Host selects the event they want to manage with managed tag displayed.
  3. Host see the follwing on event page:
      - Event details (title, description, date, time, location) without option to edit event.
      - Invite Options (Analytics, printing Invites/labels, View all invite and create invite).
      - Event managers section (Read Only).
  4. Host can click on View invites to manage invites.
  5. Host can View Analytics for the event.
  6. Host can print invites/labels for the event.
  7. Host can create new invites for the event.

### 4. Host deletes an event
- **User**: Host
- **Goal**: Delete an event
- **Preconditions**: Host is logged into the platform and has created an event.
- **Postconditions**: Event is deleted successfully.
- **Steps**:
  1. Host navigates to the "My Events" page.
  2. Host selects the event they want to delete.
  3. Host clicks on "Delete Event" button.
  4. Host is shown a confirmation dialog.
  5. Host confirms deletion.
  6. Event is deleted and removed from the "My Events" page.

### 5. Host or Event Mananger shares a event link
- **User**: Host or Event Manager
- **Goal**: Share event link
- **Preconditions**: Host or Event Manager is logged into the platform and has created an event.
- **Postconditions**: Event link is shared successfully.
- **Steps**:
  1. Host or Event Manager navigates to the "My Events" page.
  2. Host or Event Manager selects the event they want to share.
  3. Host or Event Manager View all the invites for the event.
  4. Host or Event Manager Searches for the invite they want to share.
  5. Host or Event Manager clicks on the invite.
  6. Host or Event Manager is shown the invite details page.
  7. Host or Event Manager can share the invite link by:
      - Clicking on "Share Invite" button.
      - Copying the invite link.
      - Scanning the QR code.

### 6. Host or Event Mananger prints invites
- **User**: Host or Event Manager
- **Goal**: Print invites
- **Preconditions**: Host or Event Manager is logged into the platform and has created an event.
- **Postconditions**: Invites are printed successfully.
- **Steps**:
  1. Host or Event Manager navigates to the "My Events" page.
  2. Host or Event Manager selects the event they want to print invites for.
  3. Host or Event Manager clicks on Print Invites button.
  4. Host or Event Manager is shown a list of QR labels for all invites for the event.
  5. Host or Event Manager toggles to invite mode.
  6. Host or Event Manager is shown with invite printing options panel.
      - Page Size (A4, A5).
      - Orientation (Portrait, Landscape).
      - Label Position (Horizontal %, Vertical %).
      - Upload Invite Image
  7. Host or Event Manager can preview all the invites.
  8. Host or Event Manager clicks on "Print" button.
  9. Host or Event Manager can print or save to PDF the invites using browser's print dialog.

## Guest

### 1. Guest views event details and RSVP
- **User**: Guest
- **Goal**: View event details
- **Preconditions**: Guest has the link to the event invite using a QR code or a link shared by host or event manager.
- **Postconditions**: Event details are displayed successfully.
- **Steps**:
  1. Guest opens the event link in a web browser.
  2. Guest is shown the event details page with the following information:
      - Event title
      - Event description
      - Event date and time
      - Event location
      - RSVP button
      - Add to Calendar button
  3. Guest can click on "RSVP" button to confirm attendance.
  4. Guest can click on "Add to Calendar" button to add the event to their Google calendar.
  5. Guest clicks on "RSVP" button.
  6. Guest is shown a RSVP dialog.
  7. Guest fills in 
      - RSVP status (Accept/ Declined).
      - Message (Optional).
      - Attendees (Adults, Children).
      - Email.
      - Phone Number (Optional).
  8. Guest clicks on "Submit" button.
  9. Guest is shown a confirmation message.
  10. Guest is shown a thank you message with the option to add the event to their calendar.
  11. Guest recvies a confirmation email with the event details and RSVP status.
  12. Guest can view the event details and RSVP status on the email and Us a link to Update response.