import admin from 'firebase-admin';

export type FireBaseAdminApp = admin.app.App;
export type FireBaseAdminFirestore = admin.firestore.Firestore;
export { firestore as FireStore } from 'firebase-admin';

export class FireBaseAdmin {
  public static admin: FireBaseAdminApp | undefined;

  public static initialize = () => {
    // Only initialize on the server side
    if (typeof window !== 'undefined') {
      return;
    }

    try {
      if (admin.apps.length === 0) {
        if (!process.env.FIREBASE_CLIENT_EMAIL || !process.env.FIREBASE_PRIVATE_KEY || !process.env.FIREBASE_PROJECT_ID) {
          throw new Error('Missing Firebase Admin credentials in environment variables');
        }

        FireBaseAdmin.admin = admin.initializeApp({
          credential: admin.credential.cert({
            clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
            privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
            projectId: process.env.FIREBASE_PROJECT_ID,
          }),
          storageBucket: 'iamcoming-universe.firebasestorage.app', // Add storage bucket configuration
        });
      } else {
        FireBaseAdmin.admin = admin.app();
      }
    } catch (error) {
      console.error('Failed to initialize Firebase Admin:', error);
      throw error;
    }
  }

  public static getInstance = () => {
    // Only get instance on the server side
    if (typeof window !== 'undefined') {
      throw new Error('Firebase Admin can only be used on the server side');
    }

    if (!FireBaseAdmin.admin) {
      FireBaseAdmin.initialize();
    }
    return FireBaseAdmin.admin;
  }

  public static close = async () => {
    // Only close on the server side
    if (typeof window !== 'undefined') {
      return;
    }

    if (FireBaseAdmin.admin) {
      await FireBaseAdmin.admin.delete();
      FireBaseAdmin.admin = undefined;
    }
  }
}