'use client'

import { useState } from "react"
import { PartnerLayout } from "@/components/layouts/PartnerLayout"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/router"
import { Calendar, Clock, Plus, Building2, MapPin, Trash2, Edit, Badge } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useSession } from "next-auth/react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import {
  Dialog,
  DialogContent,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

// Mock data for venues - in a real app, this would come from an API/database
const mockVenues = [
  {
    id: "venue-1",
    name: "Grand Ballroom",
    address: "123 Main St, Melbourne",
    locations: [
      { id: "loc-1", name: "Main Hall" },
      { id: "loc-2", name: "Terrace" }
    ]
  },
  {
    id: "venue-2",
    name: "Garden Pavilion",
    address: "456 Park Ave, Sydney",
    locations: [
      { id: "loc-3", name: "Garden Area" },
      { id: "loc-4", name: "Covered Pavilion" }
    ]
  }
];

// Mock data for time slots
const mockTimeSlots = [
  {
    id: "slot-1",
    venueId: "venue-1",
    locationId: "loc-1",
    date: new Date(2023, 11, 15),
    startTime: "18:00",
    endTime: "22:00",
    isBooked: false
  },
  {
    id: "slot-2",
    venueId: "venue-1",
    locationId: "loc-2",
    date: new Date(2023, 11, 16),
    startTime: "14:00",
    endTime: "18:00",
    isBooked: true,
    eventId: "event-123"
  },
  {
    id: "slot-3",
    venueId: "venue-2",
    locationId: "loc-3",
    date: new Date(2023, 11, 20),
    startTime: "12:00",
    endTime: "16:00",
    isBooked: false
  }
];

export default function TimeSlotManagement() {
  const router = useRouter()
  const { data: session } = useSession()
  const [timeSlots, setTimeSlots] = useState(mockTimeSlots)
  const [isAddSlotOpen, setIsAddSlotOpen] = useState(false)
  const [selectedVenue, setSelectedVenue] = useState<any>(null)
  const [selectedLocation, setSelectedLocation] = useState<any>(null)
  const [newSlot, setNewSlot] = useState({
    venueId: "",
    locationId: "",
    date: undefined as Date | undefined,
    startTime: "",
    endTime: ""
  })
  
  const handleVenueChange = (venueId: string) => {
    setNewSlot({...newSlot, venueId, locationId: ""});
    setSelectedVenue(mockVenues.find(v => v.id === venueId));
    setSelectedLocation(null);
  };
  
  const handleLocationChange = (locationId: string) => {
    setNewSlot({...newSlot, locationId});
    setSelectedLocation(selectedVenue?.locations.find((l: any) => l.id === locationId));
  };
  
  const handleAddTimeSlot = () => {
    if (!newSlot.date || !newSlot.startTime || !newSlot.endTime) return;
    
    const slot = {
      id: `slot-${Date.now()}`,
      venueId: newSlot.venueId,
      locationId: newSlot.locationId,
      date: newSlot.date,
      startTime: newSlot.startTime,
      endTime: newSlot.endTime,
      isBooked: false
    };
    
    setTimeSlots([...timeSlots, slot]);
    setNewSlot({
      venueId: "",
      locationId: "",
      date: undefined,
      startTime: "",
      endTime: ""
    });
    setIsAddSlotOpen(false);
  };
  
  const handleDeleteTimeSlot = (slotId: string) => {
    setTimeSlots(timeSlots.filter(slot => slot.id !== slotId));
  };
  
  const getVenueName = (venueId: string) => {
    return mockVenues.find(v => v.id === venueId)?.name || "Unknown Venue";
  };
  
  const getLocationName = (venueId: string, locationId: string) => {
    const venue = mockVenues.find(v => v.id === venueId);
    return venue?.locations.find(l => l.id === locationId)?.name || "Unknown Location";
  };

  return (
    <PartnerLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Time Slot Management</h1>
            <p className="text-muted-foreground">Manage available time slots for your venues</p>
          </div>
          <Dialog open={isAddSlotOpen} onOpenChange={setIsAddSlotOpen}>
            <DialogTrigger asChild>
              <Button variant="primary-button">
                <Plus className="mr-2 h-4 w-4" />
                Add Time Slot
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Time Slot</DialogTitle>
                <DialogDescription>
                  Create a new available time slot for a venue location.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="venue">Venue</Label>
                  <Select 
                    onValueChange={handleVenueChange} 
                    value={newSlot.venueId}
                  >
                    <SelectTrigger id="venue">
                      <SelectValue placeholder="Select a venue" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockVenues.map((venue) => (
                        <SelectItem key={venue.id} value={venue.id}>
                          {venue.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="location">Location/Area</Label>
                  <Select 
                    onValueChange={handleLocationChange} 
                    value={newSlot.locationId}
                    disabled={!selectedVenue}
                  >
                    <SelectTrigger id="location">
                      <SelectValue placeholder={selectedVenue ? "Select a location" : "Select a venue first"} />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedVenue?.locations.map((location: any) => (
                        <SelectItem key={location.id} value={location.id}>
                          {location.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !newSlot.date && "text-muted-foreground"
                        )}
                      >
                        {newSlot.date ? (
                          format(newSlot.date, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={newSlot.date}
                        onSelect={(date) => setNewSlot({...newSlot, date: date as Date})}
                        disabled={(date) =>
                          date < new Date(new Date().setHours(0, 0, 0, 0))
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start-time">Start Time</Label>
                    <Input 
                      id="start-time" 
                      type="time" 
                      value={newSlot.startTime}
                      onChange={(e) => setNewSlot({...newSlot, startTime: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="end-time">End Time</Label>
                    <Input 
                      id="end-time" 
                      type="time" 
                      value={newSlot.endTime}
                      onChange={(e) => setNewSlot({...newSlot, endTime: e.target.value})}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddSlotOpen(false)}>Cancel</Button>
                <Button 
                  variant="default" 
                  onClick={handleAddTimeSlot}
                  disabled={!newSlot.venueId || !newSlot.locationId || !newSlot.date || !newSlot.startTime || !newSlot.endTime}
                >
                  Add Time Slot
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        {/* Time Slots Table */}
        <Card>
          <CardHeader>
            <CardTitle>Available Time Slots</CardTitle>
            <CardDescription>
              Manage your venue time slots and availability
            </CardDescription>
          </CardHeader>
          <CardContent>
            {timeSlots.length === 0 ? (
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-muted-foreground">No time slots have been created yet.</p>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={() => setIsAddSlotOpen(true)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Time Slot
                </Button>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Venue</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {timeSlots.map((slot) => (
                    <TableRow key={slot.id}>
                      <TableCell>{getVenueName(slot.venueId)}</TableCell>
                      <TableCell>{getLocationName(slot.venueId, slot.locationId)}</TableCell>
                      <TableCell>{format(slot.date, "PPP")}</TableCell>
                      <TableCell>{slot.startTime} - {slot.endTime}</TableCell>
                      <TableCell>
                        {slot.isBooked ? (
                          <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Booked</Badge>
                        ) : (
                          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Available</Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button variant="ghost" size="sm" disabled={slot.isBooked}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" disabled={slot.isBooked}>
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  This will permanently delete this time slot. This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction 
                                  onClick={() => handleDeleteTimeSlot(slot.id)} 
                                  className="bg-red-500 hover:bg-red-600"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </PartnerLayout>
  )
}
