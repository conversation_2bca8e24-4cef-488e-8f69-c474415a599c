'use client';

import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MessageCircle, Plus, UploadIcon, Users, ArrowUpDown, Clock, LockIcon, AlertTriangle, Phone, Mail, Download, Trash2, MoreVertical, Edit, Bell, X, UserMinus, Copy, Share, Baby, FolderOpen, Filter } from "lucide-react";
import { EventInviteListItem } from "@/types";
import { useInvites } from "@/hooks/useInvites";
import { Input } from "@/components/ui/input";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

import { ProtectedLayout } from "@/components/layouts/ProtectedLayout";
import { Header } from "@/components/Header";
import { useEvent } from "@/hooks/useEvent";

import { formatTimeAgo } from "@/lib/dayjs";
import { isInviteManagementLocked, getInviteManagementLockMessage, isEventDatePassed } from "@/lib/event/eventLock";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

import { cn } from "@/lib/utils";
import SendReminderDialog from "@/components/SendReminderDialog";
import MoveToGroupDialog from "@/components/MoveToGroupDialog";
import { useToast } from "@/components/ui/use-toast";
import { useDropdownToModal } from "@/hooks/useDropdownToModal";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  RadioGroup,
  RadioGroupItem
} from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SavedContactGroupsSheet } from "@/components/SavedContactGroupsSheet";

function InvitesManagementScreen() {
  const router = useRouter();
  const { eventId } = router.query;
  const { event } = useEvent(eventId as string);
  const { loading, invites, error, refetch: refetchInvites } = useInvites(eventId as string, true);
  const [filter, setFilter] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("updated");

  // Advanced filter states
  const [advancedFilterOpen, setAdvancedFilterOpen] = useState(false);
  const [onlyWithMessage, setOnlyWithMessage] = useState(false);
  const [withEmail, setWithEmail] = useState(false);
  const [withPhone, setWithPhone] = useState(false);
  const [adultsCondition, setAdultsCondition] = useState("Greater Than");
  const [adultsValue, setAdultsValue] = useState("");
  const [childrenCondition, setChildrenCondition] = useState("Greater Than");
  const [childrenValue, setChildrenValue] = useState("");

  const [filteredInvites, setFilteredInvites] = useState<EventInviteListItem[]>([]);
  const [selectedInvites, setSelectedInvites] = useState<string[]>([]);
  const [reminderDialogOpen, setReminderDialogOpen] = useState(false);
  const [moveToGroupDialogOpen, setMoveToGroupDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [openAccordionItems, setOpenAccordionItems] = useState<string[]>([]);

  // Download dialog states
  const [downloadDialogOpen, setDownloadDialogOpen] = useState(false);
  const [downloadTab, setDownloadTab] = useState<string>("qr-labels");
  const [downloadFormat, setDownloadFormat] = useState<string>("pdf");

  // Group action states
  const [editGroupDialogOpen, setEditGroupDialogOpen] = useState(false);
  const [groupReminderDialogOpen, setGroupReminderDialogOpen] = useState(false);
  const [deleteGroupDialogOpen, setDeleteGroupDialogOpen] = useState(false);
  const [selectedGroupName, setSelectedGroupName] = useState<string>('');
  const [newGroupName, setNewGroupName] = useState<string>('');
  const [isEditingGroup, setIsEditingGroup] = useState(false);
  const [isDeletingGroup, setIsDeletingGroup] = useState(false);

  // Individual invite action states
  const [inviteActionSheetOpen, setInviteActionSheetOpen] = useState(false);
  const [selectedInviteForAction, setSelectedInviteForAction] = useState<EventInviteListItem | null>(null);
  const [isRemovingFromGroup, setIsRemovingFromGroup] = useState(false);
  const [isDeletingIndividualInvite, setIsDeletingIndividualInvite] = useState(false);

  // Saved contact groups sheet state
  const [savedGroupsSheetOpen, setSavedGroupsSheetOpen] = useState(false);
  const [hasContactGroups, setHasContactGroups] = useState<boolean | null>(null); // null = loading, true/false = result

  const { toast } = useToast();

  // Check if organization has saved contact groups
  useEffect(() => {
    const checkContactGroups = async () => {
      if (!event?.ownerAccountId) {
        return;
      }

      try {
        const response = await fetch(`/api/organizations/${event.ownerAccountId}/contact-groups`);
        if (response.ok) {
          const data = await response.json();
          setHasContactGroups(data.contactGroups && data.contactGroups.length > 0);
        } else {
          setHasContactGroups(false);
        }
      } catch (error) {
        console.error('Error checking contact groups:', error);
        setHasContactGroups(false);
      }
    };

    checkContactGroups();
  }, [event?.ownerAccountId]);
  const { transitionToModal } = useDropdownToModal();

  // Group invites by their group field
  const groupedInvites = React.useMemo(() => {
    const groups: Record<string, EventInviteListItem[]> = {};

    filteredInvites.forEach(invite => {
      const groupName = invite.group || 'Ungrouped';
      if (!groups[groupName]) {
        groups[groupName] = [];
      }
      groups[groupName].push(invite);
    });

    return groups;
  }, [filteredInvites]);

  // Effect to ensure all accordion items are open by default
  useEffect(() => {
    const groupNames = Object.keys(groupedInvites);
    if (groupNames.length > 0) {
      setOpenAccordionItems(groupNames);
    }
  }, [groupedInvites]);

  // Handler for individual invite selection
  const toggleInvite = (inviteId: string) => {
    setSelectedInvites(prev =>
      prev.includes(inviteId)
        ? prev.filter(id => id !== inviteId)
        : [...prev, inviteId]
    );
  };

  // Handler for group selection
  const toggleGroup = (group: string) => {
    const groupInvites = groupedInvites[group] || [];
    const groupInviteIds = groupInvites.map(invite => invite.ID);
    const allGroupSelected = groupInviteIds.every(id => selectedInvites.includes(id));

    if (allGroupSelected) {
      // Deselect all invites in this group
      setSelectedInvites(prev => prev.filter(id => !groupInviteIds.includes(id)));
    } else {
      // Select all invites in this group
      setSelectedInvites(prev => [...new Set([...prev, ...groupInviteIds])]);
    }
  };

  // Check if a group is fully selected
  const isGroupSelected = (group: string) => {
    const groupInvites = groupedInvites[group] || [];
    const groupInviteIds = groupInvites.map(invite => invite.ID);
    return groupInviteIds.length > 0 && groupInviteIds.every(id => selectedInvites.includes(id));
  };

  // Check if a group is partially selected
  const isGroupPartiallySelected = (group: string) => {
    const groupInvites = groupedInvites[group] || [];
    const groupInviteIds = groupInvites.map(invite => invite.ID);
    const selectedInGroup = groupInviteIds.filter(id => selectedInvites.includes(id));
    return selectedInGroup.length > 0 && selectedInGroup.length < groupInviteIds.length;
  };

  // Handler for main checkbox (select all)
  const toggleAllInvites = () => {
    const allInviteIds = filteredInvites.map(invite => invite.ID);
    const allSelected = allInviteIds.every(id => selectedInvites.includes(id));

    if (allSelected) {
      setSelectedInvites([]);
    } else {
      setSelectedInvites(allInviteIds);
    }
  };

  // Check if all invites are selected
  const isAllSelected = () => {
    const allInviteIds = filteredInvites.map(invite => invite.ID);
    return allInviteIds.length > 0 && allInviteIds.every(id => selectedInvites.includes(id));
  };

  // Check if some invites are selected
  const isSomeSelected = () => {
    return selectedInvites.length > 0 && !isAllSelected();
  };

  useEffect(() => {
    if (!loading) {
      // Apply text search filter
      let filtered = invites.filter(invite =>
        invite.name.toLowerCase().includes(filter.toLowerCase())
      );

      // Apply status filter
      if (statusFilter !== "all") {
        filtered = filtered.filter(invite => invite.status === statusFilter);
      }

      // Apply advanced filters
      if (onlyWithMessage) {
        filtered = filtered.filter(invite => 
          invite.hasMessage || (invite.response?.message && invite.response.message.trim() !== '')
        );
      }

      if (withEmail) {
        filtered = filtered.filter(invite => invite.email && invite.email.trim() !== '');
      }

      if (withPhone) {
        filtered = filtered.filter(invite => invite.phone && invite.phone.trim() !== '');
      }

      // Apply adults filter
      if (adultsValue && !isNaN(Number(adultsValue))) {
        const adultsNum = Number(adultsValue);
        filtered = filtered.filter(invite => {
          if (adultsCondition === "Greater Than") {
            return invite.adults > adultsNum;
          } else if (adultsCondition === "Less Than") {
            return invite.adults < adultsNum;
          } else if (adultsCondition === "Equal To") {
            return invite.adults === adultsNum;
          }
          return true;
        });
      }

      // Apply children filter
      if (childrenValue && !isNaN(Number(childrenValue))) {
        const childrenNum = Number(childrenValue);
        filtered = filtered.filter(invite => {
          if (childrenCondition === "Greater Than") {
            return invite.children > childrenNum;
          } else if (childrenCondition === "Less Than") {
            return invite.children < childrenNum;
          } else if (childrenCondition === "Equal To") {
            return invite.children === childrenNum;
          }
          return true;
        });
      }

      // Apply sorting - default to "updated" on small/medium screens
      const effectiveSortBy = window.innerWidth < 1024 ? "updated" : sortBy;

      if (effectiveSortBy === "name") {
        filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));
      } else if (effectiveSortBy === "updated") {
        filtered = [...filtered].sort((a, b) => {
          // Get all possible timestamps for each invite
          const aResponseTime = a.response?.timestamp ? new Date(a.response.timestamp).getTime() : 0;
          const bResponseTime = b.response?.timestamp ? new Date(b.response.timestamp).getTime() : 0;
          const aCreatedTime = a.createdAt ? new Date(a.createdAt).getTime() : 0;
          const bCreatedTime = b.createdAt ? new Date(b.createdAt).getTime() : 0;
          const aUpdatedTime = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
          const bUpdatedTime = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;

          // Use the most recent timestamp for each invite
          const aLatestTime = Math.max(aResponseTime, aCreatedTime, aUpdatedTime);
          const bLatestTime = Math.max(bResponseTime, bCreatedTime, bUpdatedTime);

          // Sort by the most recent timestamp (latest first)
          if (aLatestTime && bLatestTime) {
            return bLatestTime - aLatestTime;
          }
          // If only one has a timestamp, prioritize that one
          else if (aLatestTime) return -1;
          else if (bLatestTime) return 1;
          // If neither has a timestamp, fall back to name sorting
          else return a.name.localeCompare(b.name);
        });
      }

      setFilteredInvites(filtered);
    }
  }, [invites, loading, filter, statusFilter, sortBy, onlyWithMessage, withEmail, withPhone, adultsCondition, adultsValue, childrenCondition, childrenValue]);

  const navigateToInviteDetails = (id: string) => {
    router.push(`/event/${eventId}/invites/${id}`);
  };

  // Handle download selected invites - now opens dialog
  const openDownloadDialog = () => {
    if (selectedInvites.length === 0) {
      toast({
        title: "No invites selected",
        description: "Please select at least one invite to download.",
        variant: "destructive"
      });
      return;
    }
    setDownloadDialogOpen(true);
  };

  // Generate export URL based on selected options
  const getExportUrl = () => {
    const type = `${downloadTab}-${downloadFormat}`;
    return `/api/media/export?type=${type}&invites=${selectedInvites.join(',')}`;
  };

  // Handle actual download
  const handleDownload = () => {
    const apiUrl = getExportUrl();
    window.open(apiUrl, '_blank');

    const formatText = downloadTab === 'qr-labels' ? 'QR Labels' : 'Invitation Cards';
    toast({
      title: "Downloading...",
      description: `Downloading ${selectedInvites.length} ${formatText.toLowerCase()} as ${downloadFormat.toUpperCase()}.`,
    });

    setDownloadDialogOpen(false);
  };

  // Group action handlers
  const handleEditGroupName = (groupName: string) => {
    setSelectedGroupName(groupName);
    setNewGroupName(groupName);
    setEditGroupDialogOpen(true);
  };

  const handleSendReminderToGroup = (groupName: string) => {
    setSelectedGroupName(groupName);
    setGroupReminderDialogOpen(true);
  };

  const handleDeleteGroup = (groupName: string) => {
    setSelectedGroupName(groupName);
    setDeleteGroupDialogOpen(true);
  };

  // Edit group name functionality
  const handleConfirmEditGroup = async () => {
    if (!newGroupName.trim() || newGroupName.trim() === selectedGroupName) {
      setEditGroupDialogOpen(false);
      return;
    }

    setIsEditingGroup(true);
    try {
      const groupInvites = groupedInvites[selectedGroupName] || [];
      const inviteIds = groupInvites.map(invite => invite.ID);

      const response = await fetch(`/api/event/${eventId}/invites/bulk-update-group`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inviteIds: inviteIds,
          groupName: newGroupName.trim()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update group name');
      }

      const data = await response.json();

      toast({
        title: "Group name updated successfully!",
        description: `Renamed "${selectedGroupName}" to "${newGroupName.trim()}" for ${data.updatedCount} invite${data.updatedCount !== 1 ? 's' : ''}.`,
      });

      setEditGroupDialogOpen(false);
      window.location.reload(); // Refresh to show updated groups

    } catch (error) {
      console.error('Error updating group name:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update group name. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsEditingGroup(false);
    }
  };

  // Delete group functionality
  const handleConfirmDeleteGroup = async () => {
    setIsDeletingGroup(true);
    try {
      const groupInvites = groupedInvites[selectedGroupName] || [];
      const inviteIds = groupInvites.map(invite => invite.ID);

      const response = await fetch(`/api/event/${eventId}/invites/bulk-update-group`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inviteIds: inviteIds,
          groupName: '' // Empty string to remove group
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete group');
      }

      const data = await response.json();

      toast({
        title: "Group deleted successfully!",
        description: `Moved ${data.updatedCount} invite${data.updatedCount !== 1 ? 's' : ''} from "${selectedGroupName}" to ungrouped.`,
      });

      setDeleteGroupDialogOpen(false);
      window.location.reload(); // Refresh to show updated groups

    } catch (error) {
      console.error('Error deleting group:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete group. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeletingGroup(false);
    }
  };

  // Handle individual invite actions
  const handleInviteAction = (invite: EventInviteListItem) => {
    setSelectedInviteForAction(invite);
    setInviteActionSheetOpen(true);
  };

  // Handle remove from group for individual invite
  const handleRemoveFromGroup = async () => {
    if (!selectedInviteForAction) return;

    setIsRemovingFromGroup(true);
    try {
      const response = await fetch(`/api/event/${eventId}/invites/bulk-update-group`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inviteIds: [selectedInviteForAction.ID],
          groupName: '' // Empty string to remove from group
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove from group');
      }

      const data = await response.json();

      toast({
        title: "Removed from group",
        description: `${selectedInviteForAction.name} has been moved to ungrouped.`,
      });

      setInviteActionSheetOpen(false);
      window.location.reload(); // Refresh to show updated groups

    } catch (error) {
      console.error('Error removing from group:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove from group. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRemovingFromGroup(false);
    }
  };

  // Handle delete individual invite
  const handleDeleteIndividualInvite = async () => {
    if (!selectedInviteForAction) return;

    setIsDeletingIndividualInvite(true);
    try {
      const response = await fetch(`/api/event/${eventId}/invites/${selectedInviteForAction.ID}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete invite');
      }

      const data = await response.json();

      toast({
        title: "Invite deleted",
        description: `${selectedInviteForAction.name}'s invite has been deleted successfully.`,
      });

      setInviteActionSheetOpen(false);
      window.location.reload(); // Refresh to show updated list

    } catch (error) {
      console.error('Error deleting invite:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete invite. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeletingIndividualInvite(false);
    }
  };

  // Handle delete selected invites
  const handleDeleteSelectedInvites = async () => {
    if (selectedInvites.length === 0) {
      toast({
        title: "No invites selected",
        description: "Please select at least one invite to delete.",
        variant: "destructive"
      });
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/event/${eventId}/invites/bulk-delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inviteIds: selectedInvites
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete invites');
      }

      // Show success message
      toast({
        title: "Invites Deleted",
        description: data.message,
      });

      // Clear selected invites
      setSelectedInvites([]);

      // Close the dialog
      setDeleteDialogOpen(false);

      // Refresh the invites list by reloading the page or refetching data
      // Since we don't have a refetch function in useInvites, we'll reload the page
      window.location.reload();

    } catch (error) {
      console.error('Error deleting invites:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete invites. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (error) {
    return <p>Error: {error.message}</p>;
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col  bg-gray-50">
        {/* Use the shared Header component with breadcrumbs */}
        <Header
          title="Invites Management"
          breadcrumbs={[
            { label: 'Events', href: '/events' },
            { label: event?.eventName || 'Event Details', href: `/event/${eventId}` }
          ]}
        />

        {/* Main Content */}
        <div className="flex-1 p-4 pb-20">
          <div className="container mx-auto">
            <Card>
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <CardTitle className="mb-3 sm:mb-0">Invites</CardTitle>
                  <div className="flex w-full sm:w-auto gap-2">
                    {isInviteManagementLocked(event) ? (
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          className="flex-1 sm:flex-none cursor-not-allowed opacity-60"
                          disabled={true}
                          title={getInviteManagementLockMessage(event) || "Invite management locked"}
                        >
                          <LockIcon className="h-4 w-4" />
                          <span className="sm:hidden">CSV Upload</span>
                          <span className="hidden sm:inline">Create Bulk Invite</span>
                        </Button>
                      </div>
                    ) : (
                      <>
                        {/* Add Saved Groups button - show immediately, only hide if we know there are no contact groups */}
                        {hasContactGroups !== false && (
                          <Button
                            variant="outline"
                            className="flex-1 sm:flex-none"
                            onClick={() => setSavedGroupsSheetOpen(true)}
                          >
                            <FolderOpen className="h-4 w-4" />
                            <span className="sm:hidden">Saved Groups</span>
                            <span className="hidden sm:inline">Add Saved Groups</span>
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          className="flex-1 sm:flex-none"
                          onClick={() => router.push(`/event/${eventId}/invites/csv-upload`)}
                        >
                          <UploadIcon className="h-4 w-4" />
                          <span className="sm:hidden">CSV Upload</span>
                          <span className="hidden sm:inline">Create Bulk Invite</span>
                        </Button>
                        <Button
                          className="flex-1 sm:flex-none bg-[#F43F5E] hover:bg-[#F43F5E]/90"
                          onClick={() => router.push(`/event/${eventId}/invites/new/edit`)}
                        >
                          <Plus className="h-4 w-4" />
                          Create Invite
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                {/* Show warning if invites are locked due to RSVP date passing */}
                {event && isInviteManagementLocked(event) && !isEventDatePassed(event) && (
                  <div className="mt-4 bg-yellow-50 border border-yellow-300 rounded-md p-3">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-yellow-800">Event Management Limited</h3>
                        <p className="text-sm text-yellow-700">{getInviteManagementLockMessage(event)}</p>
                        <p className="text-sm text-yellow-700 mt-1">Invite management is disabled.</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Show message if event date has passed */}
                {event && isEventDatePassed(event) && (
                  <div className="mt-4 bg-blue-50 border border-blue-200 rounded-md p-3">
                    <div className="flex items-start">
                      <div className="h-5 w-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                          <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold text-blue-700">Event Completed</h3>
                        <p className="text-sm text-blue-600">{getInviteManagementLockMessage(event)}</p>
                        <p className="text-sm text-blue-600 mt-1">All event management features are now locked.</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardHeader>              <CardContent>
                {/* Search Bar */}
                <div className="mb-4 flex items-center gap-2">
                  <Input
                    type="text"
                    value={filter}
                    placeholder="Search invites..."
                    className="flex-1 h-10"
                    onChange={(e) => setFilter(e.target.value)}
                  />
                  <DropdownMenu open={advancedFilterOpen} onOpenChange={setAdvancedFilterOpen}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        className="h-10 px-3"
                      >
                        <Filter className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="p-4" style={{ width: '419px' }}>
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold">Advanced Filters</h3>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => {
                            setOnlyWithMessage(false);
                            setWithEmail(false);
                            setWithPhone(false);
                            setAdultsValue("");
                            setChildrenValue("");
                            setAdultsCondition("Greater Than");
                            setChildrenCondition("Greater Than");
                          }}
                          className="text-sm text-forground font-normal hover:text-gray-700"
                        >
                          Reset
                        </Button>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-4">
                        Select advanced filters, to filter out the desired invites
                      </p>

                      {/* Messages Section */}
                      <div className="mb-4">
                        <Label className="text-xs font-medium text-muted-foreground mb-2 block">Messages</Label>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="only-with-message"
                            checked={onlyWithMessage}
                            onCheckedChange={(checked) => setOnlyWithMessage(checked === true)}
                          />
                          <Label htmlFor="only-with-message" className="text-sm font-normal">
                            Only show invites with a message
                          </Label>
                        </div>
                      </div>

                      {/* Contact Section */}
                      <div className="mb-4">
                        <Label className="text-xs font-medium text-muted-foreground mb-2 block">Contact</Label>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="with-email"
                              checked={withEmail}
                              onCheckedChange={(checked) => setWithEmail(checked === true)}
                            />
                            <Label htmlFor="with-email" className="text-sm font-normal">
                              With Email
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="with-phone"
                              checked={withPhone}
                              onCheckedChange={(checked) => setWithPhone(checked === true)}
                            />
                            <Label htmlFor="with-phone" className="text-sm font-normal">
                              With Phone number
                            </Label>
                          </div>
                        </div>
                      </div>

                      {/* Filter By Conditions Section */}
                      <div className="mb-4">
                        <Label className="text-xs font-medium text-muted-foreground mb-3 block">Filter By Conditions</Label>
                        
                        {/* Adults Filter */}
                        <div className="mb-3">
                          <Label className="text-sm font-normal mb-2 block">Adults</Label>
                          <div className="flex gap-2">
                            <Select value={adultsCondition} onValueChange={setAdultsCondition}>
                              <SelectTrigger className="w-42">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Greater Than">Greater Than</SelectItem>
                                <SelectItem value="Less Than">Less Than</SelectItem>
                                <SelectItem value="Equal To">Equal To</SelectItem>
                              </SelectContent>
                            </Select>
                            <Input
                              type="number"
                              placeholder="Value"
                              value={adultsValue}
                              onChange={(e) => setAdultsValue(e.target.value)}
                              className="flex-1"
                            />
                          </div>
                        </div>

                        {/* Children Filter */}
                        <div className="mb-4">
                          <Label className="text-sm font-normal mb-2 block">Children</Label>
                          <div className="flex gap-2">
                            <Select value={childrenCondition} onValueChange={setChildrenCondition}>
                              <SelectTrigger className="w-42">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Greater Than">Greater Than</SelectItem>
                                <SelectItem value="Less Than">Less Than</SelectItem>
                                <SelectItem value="Equal To">Equal To</SelectItem>
                              </SelectContent>
                            </Select>
                            <Input
                              type="number"
                              placeholder="Value"
                              value={childrenValue}
                              onChange={(e) => setChildrenValue(e.target.value)}
                              className="flex-1"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <Button 
                          variant="outline" 
                          onClick={() => setAdvancedFilterOpen(false)}
                          className="flex-1"
                        >
                          Cancel
                        </Button>
                        <Button 
                          onClick={() => setAdvancedFilterOpen(false)}
                          className="flex-1 bg-[#F43F5E] hover:bg-[#F43F5E]/90"
                        >
                          Apply
                        </Button>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>                
                  {/* Filter Buttons and Sort Options */}
                <div className="mb-4">
                  {/* Mobile layout - stacked */}
                  <div className="lg:hidden">
                    {/* Filter Buttons - Grid on mobile */}
                    <div className="grid grid-cols-4 gap-2 mb-4">
                      <Button
                        variant={statusFilter === "all" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setStatusFilter("all")}
                        className="w-full text-sm font-medium"
                      >
                        All
                      </Button>
                      <Button
                        variant={statusFilter === "invited" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setStatusFilter("invited")}
                        className="w-full text-sm font-medium"
                      >
                        Invited
                      </Button>
                      <Button
                        variant={statusFilter === "accepted" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setStatusFilter("accepted")}
                        className="w-full text-sm font-medium"
                      >
                        Accepted
                      </Button>
                      <Button
                        variant={statusFilter === "declined" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setStatusFilter("declined")}
                        className="w-full text-sm font-medium"
                      >
                        Rejected
                      </Button>
                    </div>
                  </div>

                  {/* Desktop layout - same line */}
                  <div className="hidden lg:flex items-center justify-between">
                    {/* Filter Buttons */}
                    <div className="flex gap-2">
                      <Button
                        variant={statusFilter === "all" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setStatusFilter("all")}
                        className="text-sm font-medium"
                      >
                        All
                      </Button>
                      <Button
                        variant={statusFilter === "invited" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setStatusFilter("invited")}
                        className="text-sm font-medium"
                      >
                        Invited
                      </Button>
                      <Button
                        variant={statusFilter === "accepted" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setStatusFilter("accepted")}
                        className="text-sm font-medium"
                      >
                        Accepted
                      </Button>
                      <Button
                        variant={statusFilter === "declined" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setStatusFilter("declined")}
                        className="text-sm font-medium"
                      >
                        Rejected
                      </Button>
                    </div>

                    {/* Sort Options */}
                    <div className="flex items-center gap-2 text-sm text-muted-forground">
                      <ArrowUpDown className="h-4 w-4" />
                      <span>Sort By:</span>
                      <Tabs value={sortBy} onValueChange={setSortBy} className="w-auto">
                        <TabsList className="h-auto p-1">
                          <TabsTrigger value="name" className="px-3 text-sm font-normal py-1">Name</TabsTrigger>
                          <TabsTrigger value="updated" className="px-3 text-sm font-normal py-1">Latest Response</TabsTrigger>
                        </TabsList>
                      </Tabs>
                    </div>
                  </div>
                </div>

                {/* Results Count with Checkbox */}
                <div className="mb-4 px-6 py-4 border-t -mx-6" style={{backgroundColor: '#F9FAFB', borderColor: '#E2E8F0'}}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={isAllSelected()}
                        ref={(el) => {
                          if (el && isSomeSelected()) {
                            const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                            if (checkbox) {
                              checkbox.indeterminate = true;
                            }
                          }
                        }}
                        onCheckedChange={toggleAllInvites}
                        className="border-black"
                      />
                      <span className="text-base font-semibold text-foreground ml-2">
                        Showing {filteredInvites.length} of {invites.length} invites
                        {selectedInvites.length > 0 && (
                          <span className="ml-2 font-medium text-blue-600">
                            ({selectedInvites.length} selected)
                          </span>
                        )}
                      </span>
                    </div>

                    {/* Action buttons - only show when invites are selected on large screens */}
                    {selectedInvites.length > 0 && (
                      <div className="hidden lg:flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-sm font-normal"
                          onClick={openDownloadDialog}
                        >
                          <Download className="h-4 w-4" />
                          Download Invites
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-sm font-normal"
                          onClick={() => setMoveToGroupDialogOpen(true)}
                        >
                          <Plus className="h-4 w-4" />
                          <span className="hidden sm:inline">Move to Group</span>
                          <span className="sm:hidden">Move</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-sm font-normal"
                          onClick={() => setReminderDialogOpen(true)}
                        >
                          <Bell className="h-4 w-4" />
                          <span className="hidden sm:inline">Send Reminder</span>
                          <span className="sm:hidden">Remind</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 text-sm font-normal"
                          onClick={() => setDeleteDialogOpen(true)}
                          disabled={isInviteManagementLocked(event) || isDeleting}
                        >
                          {isDeleting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                              Deleting...
                            </>
                          ) : (
                            <>
                              <Trash2 className="h-4 w-4" />
                              Delete selected
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* Action buttons below on medium screens */}
                  {selectedInvites.length > 0 && (
                    <div className="hidden md:flex lg:hidden mt-3 flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-sm font-normal"
                        onClick={openDownloadDialog}
                      >
                        <Download className="h-4 w-4" />
                        Download Invites
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-sm font-normal"
                        onClick={() => setMoveToGroupDialogOpen(true)}
                      >
                        <Plus className="h-4 w-4" />
                        <span className="hidden sm:inline">Move to Group</span>
                        <span className="sm:hidden">Move</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-sm font-normal"
                        onClick={() => setReminderDialogOpen(true)}
                      >
                        <Bell className="h-4 w-4" />
                        <span className="hidden sm:inline">Send Reminder</span>
                        <span className="sm:hidden">Remind</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 text-sm font-normal"
                        onClick={() => setDeleteDialogOpen(true)}
                        disabled={isInviteManagementLocked(event) || isDeleting}
                      >
                        {isDeleting ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                            Deleting...
                          </>
                        ) : (
                          <>
                            <Trash2 className="h-4 w-4" />
                            Delete selected
                          </>
                        )}
                      </Button>
                    </div>
                  )}

                  {/* Action buttons for small screens - 2 buttons + dropdown */}
                  {selectedInvites.length > 0 && (
                    <div className="md:hidden mt-3 flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-sm font-normal flex-1"
                        onClick={() => setReminderDialogOpen(true)}
                      >
                        <Bell className="h-4 w-4" />
                        <span>Reminder</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-sm font-normal flex-1"
                        onClick={() => setMoveToGroupDialogOpen(true)}
                      >
                        <Plus className="h-4 w-4" />
                        <span>Move to Group</span>
                      </Button>                      {/* Dropdown for additional actions */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-sm font-normal"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-56">
                          <DropdownMenuLabel>More Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />                          <DropdownMenuItem
                            onClick={() => {
                              transitionToModal(() => {
                                openDownloadDialog();
                              });
                            }}
                          >
                            <Download className="mr-2 h-4 w-4" />
                            Download Invites
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              transitionToModal(() => {
                                setDeleteDialogOpen(true);
                              });
                            }}
                            disabled={isInviteManagementLocked(event) || isDeleting}
                            className="text-red-600 focus:text-red-600"
                          >
                            {isDeleting ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                                Deleting...
                              </>
                            ) : (
                              <>
                                <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                                Delete selected
                              </>
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  )}
                </div>                {/* Dynamic Grouped Invite Lists with Accordion */}
                <ScrollArea className="h-[calc(100vh-200px)]">
                  <div className="border border-gray-200 rounded-lg bg-white">
                    {loading ? (
                      <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                        <p className="text-muted-foreground">Loading...</p>
                      </div>
                    ) : invites.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
                        <Users className="h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No invites yet</h3>
                        <p className="text-muted-foreground text-sm">Add invites manually or upload a CSV to add multiple invites.</p>
                      </div>
                    ) : (
                    <Accordion type="multiple" className="w-full" value={openAccordionItems} onValueChange={setOpenAccordionItems}>
                      {Object.entries(groupedInvites).map(([groupName, groupInvites], index) => {
                        const isLastGroup = index === Object.keys(groupedInvites).length - 1;

                        return (
                          <AccordionItem key={groupName} value={groupName} className={cn("border-b-0", !isLastGroup && "border-b border-gray-200")}>
                            <div className="flex items-center py-3 px-4 border-b border-gray-200">
                              <div className="flex items-center space-x-3 flex-1">
                                <Checkbox
                                  checked={isGroupSelected(groupName)}
                                  ref={(el) => {
                                    if (el && isGroupPartiallySelected(groupName)) {
                                      const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                                      if (checkbox) {
                                        checkbox.indeterminate = true;
                                      }
                                    }
                                  }}
                                  onCheckedChange={() => toggleGroup(groupName)}
                                  onClick={(e) => e.stopPropagation()}
                                  className="shrink-0 border-black"
                                />
                                <span className="text-base font-medium text-muted-foreground text-left flex-1">
                                  {groupName}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <AccordionTrigger className="hover:no-underline p-2 border-0 rounded-full transition-colors" style={{backgroundColor: '#F8FAFC'}}>
                                </AccordionTrigger>                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                      }}
                                      className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                                      style={{backgroundColor: '#F8FAFC'}}
                                    >
                                      <MoreVertical className="h-4 w-4 text-muted-foreground" />
                                    </button>
                                  </DropdownMenuTrigger>                                  <DropdownMenuContent align="end" className="w-56">
                                    <DropdownMenuLabel>
                                      {groupName === 'Ungrouped' ? 'Ungrouped Actions' : `Group Action (${groupName})`}
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator />                                    {groupName !== 'Ungrouped' && (
                                      <DropdownMenuItem
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          transitionToModal(() => {
                                            handleEditGroupName(groupName);
                                          });
                                        }}
                                      >
                                        <Edit className="mr-2 h-4 w-4" />
                                        Edit Group Name
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuItem
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        transitionToModal(() => {
                                          handleSendReminderToGroup(groupName);
                                        });
                                      }}
                                    >
                                      <Bell className="mr-2 h-4 w-4" />
                                      {groupName === 'Ungrouped' ? 'Send Reminder to Ungrouped' : 'Send Reminder to Group'}
                                    </DropdownMenuItem>
                                    {groupName !== 'Ungrouped' && (
                                      <>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            transitionToModal(() => {
                                              handleDeleteGroup(groupName);
                                            });
                                          }}
                                          className="text-red-600 focus:text-red-600"
                                        >
                                          <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                                          Delete Group
                                        </DropdownMenuItem>
                                      </>
                                    )}
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </div>
                            <AccordionContent className="pb-0">
                              <div className="space-y-0 ml-4">
                                {groupInvites.map((invite, index) => (
                                  <div key={invite.ID} className={`py-4 px-4 hover:bg-gray-50 cursor-pointer -mx-4`}
                                       onClick={() => navigateToInviteDetails(invite.ID)}>

                                    {/* Desktop Layout */}
                                    <div className="hidden md:flex items-center space-x-3">
                                      <div className="flex items-center justify-between w-full pr-4">
                                        <div className="flex-1">
                                          <div className="flex items-center gap-2 mb-1">
                                            <Checkbox
                                              checked={selectedInvites.includes(invite.ID)}
                                              onCheckedChange={() => toggleInvite(invite.ID)}
                                              onClick={(e) => e.stopPropagation()}
                                              className="shrink-0 border-black"
                                            />
                                            <h3 className="font-semibold text-base text-gray-900 ml-1">{invite.name}</h3>
                                          </div>
                                          <div className="flex items-center gap-4 text-sm text-muted-foreground ml-7">
                                            <div className="flex items-center">
                                              <Users className="h-4 w-4 mr-1" />
                                              <span>{invite.adults} Adults</span>
                                            </div>
                                            <div className="flex items-center">
                                              <Baby className="h-4 w-4 mr-1" />
                                              <span>{invite.children} Children</span>
                                            </div>
                                          </div>
                                        </div>                                        <div className="flex items-center gap-4">
                                          <div className="flex items-center gap-1">
                                            {/* Show phone icon only if invite has phone */}
                                            {invite.phone && invite.phone.trim() && (
                                              <Tooltip>
                                                <TooltipTrigger asChild>
                                                  <div className="w-6 h-6 rounded-full bg-yellow-50 border border-yellow-200 flex items-center justify-center hover:bg-yellow-100 cursor-pointer transition-colors">
                                                    <Phone className="h-3 w-3 text-yellow-600" />
                                                  </div>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                  <p>Phone number is available</p>
                                                </TooltipContent>
                                              </Tooltip>
                                            )}
                                            {/* Show email icon only if invite has email */}
                                            {invite.email && invite.email.trim() && (
                                              <Tooltip>
                                                <TooltipTrigger asChild>
                                                  <div className="w-6 h-6 rounded-full bg-green-50 border border-green-200 flex items-center justify-center hover:bg-green-100 cursor-pointer transition-colors">
                                                    <Mail className="h-3 w-3 text-green-600" />
                                                  </div>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                  <p>Email is available</p>
                                                </TooltipContent>
                                              </Tooltip>
                                            )}
                                            {/* Show message icon if invite has messages or RSVP response message */}
                                            {(invite.hasMessage || (invite.response?.message && invite.response.message.trim() !== '')) && (
                                              <Tooltip>
                                                <TooltipTrigger asChild>
                                                  <div className="w-6 h-6 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center hover:bg-blue-100 cursor-pointer transition-colors">
                                                    <MessageCircle className="h-3 w-3 text-blue-600" />
                                                  </div>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                  <p>Guest has added a message</p>
                                                </TooltipContent>
                                              </Tooltip>
                                            )}
                                          </div>

                                          <div className="flex items-center text-xs text-muted-foreground">
                                            <Clock className="h-3 w-3 mr-1" />
                                            {(() => {
                                              const responseTime = invite.response?.timestamp ? new Date(invite.response.timestamp).getTime() : 0;
                                              const createdTime = invite.createdAt ? new Date(invite.createdAt).getTime() : 0;
                                              const updatedTime = invite.updatedAt ? new Date(invite.updatedAt).getTime() : 0;

                                              if (responseTime > updatedTime && responseTime > createdTime) {
                                                return formatTimeAgo(invite.response!.timestamp);
                                              } else if (updatedTime > responseTime && updatedTime > createdTime) {
                                                return formatTimeAgo(invite.updatedAt!);
                                              } else if (createdTime > 0) {
                                                return formatTimeAgo(invite.createdAt!);
                                              } else {
                                                return 'No timestamp';
                                              }
                                            })()}
                                          </div>

                                          <div className={`px-2 py-1 text-xs font-semibold rounded ${
                                            invite.status === 'accepted' ? 'bg-green-100 text-green-800' :
                                            invite.status === 'invited' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-red-100 text-red-800'
                                          }`}>
                                            {invite.status.toUpperCase()}
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Mobile Layout */}
                                    <div className="md:hidden">
                                      <div className="flex items-start justify-between pr-4">
                                        <div className="flex items-start gap-3 flex-1">
                                          <Checkbox
                                            checked={selectedInvites.includes(invite.ID)}
                                            onCheckedChange={() => toggleInvite(invite.ID)}
                                            onClick={(e) => e.stopPropagation()}
                                            className="shrink-0 border-black mt-1"
                                          />                                          <div className="flex-1 min-w-0">
                                            <h3 className="font-semibold text-base text-gray-900 mb-1">{invite.name}</h3>
                                            <div className="flex items-center gap-3 text-sm text-muted-foreground">
                                              <div className="flex items-center">
                                                <Users className="h-3 w-3 mr-1" />
                                                <span>{invite.adults}</span>
                                              </div>
                                              <div className="flex items-center">
                                                <Baby className="h-3 w-3 mr-1" />
                                                <span>{invite.children}</span>
                                              </div>
                                            </div>
                                            <div className="flex items-center gap-1 mt-2">
                                              {/* Show phone icon only if invite has phone */}
                                              {invite.phone && invite.phone.trim() && (
                                                <Tooltip>
                                                  <TooltipTrigger asChild>
                                                    <div className="w-5 h-5 rounded-full bg-yellow-50 border border-yellow-200 flex items-center justify-center hover:bg-yellow-100 cursor-pointer transition-colors">
                                                      <Phone className="h-2.5 w-2.5 text-yellow-600" />
                                                    </div>
                                                  </TooltipTrigger>
                                                  <TooltipContent>
                                                    <p>Phone number is available</p>
                                                  </TooltipContent>
                                                </Tooltip>
                                              )}
                                              {/* Show email icon only if invite has email */}
                                              {invite.email && invite.email.trim() && (
                                                <Tooltip>
                                                  <TooltipTrigger asChild>
                                                    <div className="w-5 h-5 rounded-full bg-green-50 border border-green-200 flex items-center justify-center hover:bg-green-100 cursor-pointer transition-colors">
                                                      <Mail className="h-2.5 w-2.5 text-green-600" />
                                                    </div>
                                                  </TooltipTrigger>
                                                  <TooltipContent>
                                                    <p>Email is available</p>
                                                  </TooltipContent>
                                                </Tooltip>
                                              )}
                                              {/* Show message icon if invite has messages or RSVP response message */}
                                              {(invite.hasMessage || (invite.response?.message && invite.response.message.trim() !== '')) && (
                                                <Tooltip>
                                                  <TooltipTrigger asChild>
                                                    <div className="w-5 h-5 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center hover:bg-blue-100 cursor-pointer transition-colors">
                                                      <MessageCircle className="h-2.5 w-2.5 text-blue-600" />
                                                    </div>
                                                  </TooltipTrigger>
                                                  <TooltipContent>
                                                    <p>Guest has added a message</p>
                                                  </TooltipContent>
                                                </Tooltip>
                                              )}
                                            </div>
                                          </div>
                                        </div>

                                        <div className="flex flex-col items-end gap-1 ml-2">
                                          <div className="flex items-center gap-2">
                                            <div className={`px-2 py-1 text-xs font-semibold rounded ${
                                              invite.status === 'accepted' ? 'bg-green-100 text-green-800' :
                                              invite.status === 'invited' ? 'bg-yellow-100 text-yellow-800' :
                                              'bg-red-100 text-red-800'
                                            }`}>
                                              {invite.status.toUpperCase()}
                                            </div>                                            <Button
                                              variant="outline"
                                              size="sm"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                transitionToModal(() => {
                                                  handleInviteAction(invite);
                                                });
                                              }}
                                              className="h-8 w-8 p-0 border-gray-200"
                                            >
                                              <MoreVertical className="h-4 w-4 text-muted-foreground" />
                                            </Button>
                                          </div>
                                          <div className="flex items-center text-xs text-muted-foreground">
                                            <Clock className="h-3 w-3 mr-1" />
                                            {(() => {
                                              const responseTime = invite.response?.timestamp ? new Date(invite.response.timestamp).getTime() : 0;
                                              const createdTime = invite.createdAt ? new Date(invite.createdAt).getTime() : 0;
                                              const updatedTime = invite.updatedAt ? new Date(invite.updatedAt).getTime() : 0;

                                              if (responseTime > updatedTime && responseTime > createdTime) {
                                                return formatTimeAgo(invite.response!.timestamp);
                                              } else if (updatedTime > responseTime && updatedTime > createdTime) {
                                                return formatTimeAgo(invite.updatedAt!);
                                              } else if (createdTime > 0) {
                                                return formatTimeAgo(invite.createdAt!);
                                              } else {
                                                return 'No timestamp';
                                              }
                                            })()}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </AccordionContent>                          </AccordionItem>
                        );
                      })}
                    </Accordion>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Download Invites & Labels Dialog */}
      <Dialog open={downloadDialogOpen} onOpenChange={setDownloadDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Download Invites & Labels</DialogTitle>
            <DialogDescription>
              Select and download QR labels or invitations in your preferred format. Choose from ready-to-print PDF sheets or individual image files.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Tabs value={downloadTab} onValueChange={setDownloadTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="qr-labels" className="px-3 text-sm font-normal py-1">QR Label</TabsTrigger>
                <TabsTrigger value="invitation-cards" className="px-3 text-sm font-normal py-1">Invitation</TabsTrigger>
              </TabsList>

              <TabsContent value="qr-labels" className="space-y-4 mt-4">
                <RadioGroup
                  value={downloadFormat}
                  onValueChange={setDownloadFormat}
                  className="gap-3"
                >
                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="pdf" id="qr-pdf" className="mt-[6px]" />
                    <div className="space-y-1 flex-1">
                      <Label htmlFor="qr-pdf" className="font-medium">
                        PDF Label Sheet (Recommended)
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Download a ready-to-print PDF where each invitation is on its own separate page.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="jpeg" id="qr-jpeg" className="mt-[6px]" />
                    <div className="space-y-1 flex-1">
                      <Label htmlFor="qr-jpeg" className="font-medium">
                        Download as Invite Label image (JPEG)
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Each Label for each invite selected. More than one labels selected will download a zip archive with all selected label images.
                      </p>
                    </div>
                  </div>
                </RadioGroup>
              </TabsContent>

              <TabsContent value="invitation-cards" className="space-y-4 mt-4">
                <RadioGroup
                  value={downloadFormat}
                  onValueChange={setDownloadFormat}
                  className="gap-3"
                >
                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="pdf" id="invite-pdf" className="mt-[6px]" />
                    <div className="space-y-1 flex-1">
                      <Label htmlFor="invite-pdf" className="font-medium">
                        PDF Invites (Recommended)
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Download printable invite label sheets, ready to cut and paste.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="jpeg" id="invite-jpeg" className="mt-[6px]" />
                    <div className="space-y-1 flex-1">
                      <Label htmlFor="invite-jpeg" className="font-medium">
                        Download as Invitation image (JPEG)
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Each selected invite downloads its individual invitation. Multiple selections will download a ZIP with all invitation images.
                      </p>
                    </div>
                  </div>
                </RadioGroup>
              </TabsContent>
            </Tabs>

            <div className="flex gap-2 pt-4">
              <Button variant="outline" onClick={() => setDownloadDialogOpen(false)} className="flex-1">
                Cancel
              </Button>
              <Button
                variant="primary-button"
                onClick={handleDownload}
                className="flex-1"
              >
                Download {selectedInvites.length} invites
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete these invites?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete {selectedInvites.length} invite{selectedInvites.length !== 1 ? 's' : ''} and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSelectedInvites}
              className="bg-red-600 hover:bg-red-700"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Deleting...
                </>
              ) : (
                `Delete ${selectedInvites.length} Invite${selectedInvites.length !== 1 ? 's' : ''}`
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Send Reminder Dialog */}
      {event && (
        <SendReminderDialog
          open={reminderDialogOpen}
          onOpenChange={setReminderDialogOpen}
          event={event}
          totalInvites={selectedInvites.length}
          invites={filteredInvites.filter(invite => selectedInvites.includes(invite.ID))}
        />
      )}

      {/* Move to Group Dialog */}
      {event && (
        <MoveToGroupDialog
          open={moveToGroupDialogOpen}
          onOpenChange={setMoveToGroupDialogOpen}
          event={event}
          selectedInviteIds={selectedInvites}
          invites={filteredInvites.filter(invite => selectedInvites.includes(invite.ID))}
          onSuccess={() => {
            // Clear selected invites and refresh the page
            setSelectedInvites([]);
            window.location.reload();
          }}
        />
      )}

      {/* Edit Group Name Dialog */}
      <AlertDialog open={editGroupDialogOpen} onOpenChange={(open) => {
        setEditGroupDialogOpen(open);
        if (!open) {
          // Reset state when dialog is closed
          setSelectedGroupName('');
          setNewGroupName('');
          setIsEditingGroup(false);
        }
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Edit Group Name</AlertDialogTitle>
            <AlertDialogDescription>
              Change the name of the group &quot;{selectedGroupName}&quot;. All invites in this group will be updated.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Input
              value={newGroupName}
              onChange={(e) => setNewGroupName(e.target.value)}
              placeholder="Enter new group name"
              className="w-full"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isEditingGroup}>Cancel</AlertDialogCancel>
            <Button
              variant="primary-button"
              onClick={handleConfirmEditGroup}
              disabled={isEditingGroup || !newGroupName.trim() || newGroupName.trim() === selectedGroupName}
            >
              {isEditingGroup ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Updating...
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  Update Group Name
                </>
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Send Reminder to Group Dialog */}
      {event && (
        <SendReminderDialog
          open={groupReminderDialogOpen}
          onOpenChange={(open) => {
            setGroupReminderDialogOpen(open);
            if (!open) {
              // Reset state when dialog is closed
              setSelectedGroupName('');
            }
          }}
          event={event}
          totalInvites={groupedInvites[selectedGroupName]?.length || 0}
          invites={groupedInvites[selectedGroupName] || []}
        />
      )}

      {/* Delete Group Dialog */}
      <AlertDialog open={deleteGroupDialogOpen} onOpenChange={(open) => {
        setDeleteGroupDialogOpen(open);
        if (!open) {
          // Reset state when dialog is closed
          setSelectedGroupName('');
          setIsDeletingGroup(false);
        }
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this group?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will remove the group &quot;{selectedGroupName}&quot; and move all {groupedInvites[selectedGroupName]?.length || 0} invite{(groupedInvites[selectedGroupName]?.length || 0) !== 1 ? 's' : ''} to &quot;Ungrouped&quot;. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeletingGroup}>Cancel</AlertDialogCancel>
            <Button
              variant="destructive"
              onClick={handleConfirmDeleteGroup}
              disabled={isDeletingGroup}
            >
              {isDeletingGroup ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Group
                </>
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>      {/* Individual Invite Action Sheet */}
      <Sheet open={inviteActionSheetOpen} onOpenChange={(open) => {
        setInviteActionSheetOpen(open);
        if (!open) {
          // Clear selected invite and ensure focus is properly managed
          setTimeout(() => {
            setSelectedInviteForAction(null);
          }, 100);
        }
      }}>
        <SheetContent side="bottom" className="h-auto">
          <SheetHeader className="pb-1 -mb-1">
            <SheetTitle className="text-left font-semibold text-sm">{selectedInviteForAction?.name}</SheetTitle>
          </SheetHeader>

          <Separator />

          <div className="space-y-0">
            <Button
              variant="ghost"
              className="w-full -mt-5 justify-start text-sm font-normal"
              onClick={handleRemoveFromGroup}
              disabled={isRemovingFromGroup || !selectedInviteForAction?.group}
            >
              {isRemovingFromGroup ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                  Removing...
                </>
              ) : (
                <>
                  <UserMinus className="h-5 w-5" />
                  Remove From Group
                </>
              )}
            </Button>

            <Button
              variant="ghost"
              className="w-full justify-start text-sm font-normal"
              onClick={() => {
                // Handle copy link
                setInviteActionSheetOpen(false);
                if (selectedInviteForAction) {
                  const rsvpLink = `${window.location.origin}/event/${selectedInviteForAction.eventId}/rsvp/${selectedInviteForAction.ID}`;
                  navigator.clipboard.writeText(rsvpLink);
                  toast({
                    title: "Link copied",
                    description: "RSVP link has been copied to clipboard.",
                  });
                }
              }}
            >
              <Copy className="h-5 w-5" />
              Copy Link
            </Button>

            <Button
              variant="ghost"
              className="w-full justify-start text-sm font-normal"
              onClick={() => {
                // Handle share invite
                setInviteActionSheetOpen(false);
                if (selectedInviteForAction) {
                  const rsvpLink = `${window.location.origin}/event/${selectedInviteForAction.eventId}/rsvp/${selectedInviteForAction.ID}`;
                  const shareText = `RSVP for ${event?.eventName}\n${rsvpLink}`;

                  if (navigator.share) {
                    navigator.share({
                      title: `RSVP for ${event?.eventName}`,
                      text: shareText,
                    });
                  } else {
                    // Fallback to copy the formatted text
                    navigator.clipboard.writeText(shareText);
                    toast({
                      title: "Text copied",
                      description: "Invite text has been copied to clipboard.",
                    });
                  }
                }
              }}
            >
              <Share className="h-5 w-5" />
              Share Invite
            </Button>

            <Separator className="my-1" />

            <Button
              variant="ghost"
              className="w-full mb-2 justify-start text-sm font-normal text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={handleDeleteIndividualInvite}
              disabled={isDeletingIndividualInvite || isInviteManagementLocked(event)}
            >
              {isDeletingIndividualInvite ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-5 w-5" />
                  Delete Invite
                </>
              )}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Saved Contact Groups Sheet - render if not explicitly false */}
      {hasContactGroups !== false && (
        <SavedContactGroupsSheet
          open={savedGroupsSheetOpen}
          onOpenChange={setSavedGroupsSheetOpen}
          eventId={eventId as string}
          existingInvites={invites}
          onImportComplete={() => {
            // Refresh the invites data to show new invites
            refetchInvites();
          }}
        />
      )}
    </ProtectedLayout>
  );
}

export default InvitesManagementScreen;