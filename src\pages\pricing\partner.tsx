'use client'

import { Head<PERSON> } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Separator } from '@/components/ui/separator'

export default function PartnerPricingPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Partner Pricing</h1>
        <Separator className="my-4" />
        <div className="prose max-w-none">
          <p className="text-muted-foreground mb-4">
            Our partner rate is <strong>A$30</strong> per event (regularly A$35) and is billed directly to your organization.
          </p>
          <h2 className="text-xl font-semibold mt-6 mb-4">Event Lifecycle</h2>
          <ol className="list-decimal pl-8 mb-4">
            <li><strong>Created</strong> &ndash; Event created by the partner for a client.</li>
            <li><strong>Assigned</strong> &ndash; Event host accepts the invitation to manage the event.</li>
            <li><strong>Completed</strong> &ndash; Event date has passed.</li>
          </ol>
          <h2 className="text-xl font-semibold mt-6 mb-4">Billing</h2>
          <p className="mb-4">
            Charges are applied when an event reaches the <strong>Assigned</strong> stage. Events that never get assigned are not billed.
          </p>
          <p className="mb-4">
            On the 1st of each month, an invoice for all events assigned in the previous month is generated and made available in the Partner Invoices section.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-4">Cancellation and Date Changes</h2>
          <p className="mb-4">
            Events can be cancelled at any time. If cancelled before they are <strong>Assigned</strong>, no charges apply. Once assigned, the event will be billed even if it is later cancelled.
          </p>
          <p className="mb-4">
            If an event date changes after assignment, billing remains based on the original assignment. The event is considered <strong>Completed</strong> once the updated date passes.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-4">Tracking and Support</h2>
          <p className="mb-4">
            Monitor each event&#39;s status in your partner dashboard. For questions about invoices or managing events, see the <a href="#partner-faq" className="underline">FAQ below</a> or <a href="mailto:<EMAIL>" className="underline ml-1">contact support</a>.
          </p>

          <h2 id="partner-faq" className="text-xl font-semibold mt-6 mb-4">Partner FAQ</h2>
          <dl className="space-y-4">
            <div>
              <dt className="font-medium">When will I be billed?</dt>
              <dd>Invoices are generated on the 1st of each month for all events assigned in the previous month.</dd>
            </div>
            <div>
              <dt className="font-medium">What if an event is cancelled after assignment?</dt>
              <dd>Events cancelled after reaching the <strong>Assigned</strong> stage still incur the A$30 charge.</dd>
            </div>
            <div>
              <dt className="font-medium">Where can I view my invoices?</dt>
              <dd>You can download invoices from the Partner Invoices section of your dashboard.</dd>
            </div>
            <div>
              <dt className="font-medium">How do I track event status?</dt>
              <dd>Event status is shown in your dashboard with Created, Assigned and Completed indicators.</dd>
            </div>
          </dl>
        </div>
      </main>
      <Footer type="marketing" />
    </div>
  )
}
