# IAC RBAC System

This directory contains the complete Role-Based Access Control (RBAC) implementation for the IAC platform, supporting both Partner Portal and Admin Portal.

## 📁 File Structure

```text
src/lib/auth/RBAC/
├── permissions.ts    # Core permissions and portal types (shared)
├── partner.ts        # Partner Portal roles, resources, and permissions
├── admin.ts          # Admin Portal roles, resources, and permissions
├── index.ts          # Main export file that imports and consolidates everything
├── types.ts          # TypeScript interfaces and types
├── utils.ts          # Utility functions for common operations
└── README.md         # This documentation
```

## 🏗️ **Architecture Overview**

Proper separation of concerns with portal-specific definitions:

- ✅ **permissions.ts**: Core permissions (`view`, `create`, `edit`, `delete`) and portal types
- ✅ **partner.ts**: All Partner Portal roles, resources, and role permissions
- ✅ **admin.ts**: All Admin Portal roles, resources, and role permissions
- ✅ **index.ts**: Imports from portal files and provides unified API
- ✅ **No Circular Dependencies**: Clean import hierarchy

## 🚀 Quick Start

```typescript
// Import everything from the main index.ts file
import {
  hasPermission,
  partnerRoles,
  adminRoles,
  partnerResources,
  adminResources,
  permissions,
} from '@/lib/auth/RBAC';

// Or import from specific portal files (backward compatibility)
import { partnerRoles } from '@/lib/auth/RBAC/partner';
import { adminRoles } from '@/lib/auth/RBAC/admin';

// Check partner permissions
const canEditVenue = hasPermission(
  partnerRoles.MANAGER,
  partnerResources.PARTNER_VENUES,
  permissions.EDIT
);

// Check admin permissions
const canViewUsers = hasPermission(
  adminRoles.STAFF,
  adminResources.ADMIN_USERS,
  permissions.VIEW
);
```

## 🔐 Permission System

### Standard Permissions

- **VIEW**: Read-only access to resources
- **CREATE**: Permission to create new resources
- **EDIT**: Permission to modify existing resources
- **DELETE**: Permission to remove resources

### Permission Hierarchy

Permissions are hierarchical - higher permissions include lower ones:
`DELETE` > `EDIT` > `CREATE` > `VIEW`

## 🏢 Partner Portal

### Roles

| Role              | Description                                | Use Case                           |
| ----------------- | ------------------------------------------ | ---------------------------------- |
| `partner:admin`   | Full administrative access                 | Partner owner, senior management   |
| `partner:manager` | Management access to venues, team, billing | Venue managers, operational leads  |
| `partner:staff`   | Customer-focused access                    | Front desk staff, customer service |
| `partner:viewer`  | Read-only access                           | Accountants, analysts, auditors    |

### Resources

- `partner:dashboard` - Main dashboard
- `partner:venues` - Venue management
- `partner:team` - Team member management
- `partner:billing` - Billing and invoices
- `partner:customers` - Customer management
- `partner:settings` - Partner settings
- `partner:venue:{venueId}` - Specific venue access
- `partner:venue:{venueId}:events` - Venue events
- `partner:venue:{venueId}:settings` - Venue settings

### Example Usage

```typescript
import { partnerUtils } from '@/lib/auth/RBAC/utils';

// Check specific permissions
const canManageTeam = partnerUtils.canManageTeam('partner:manager');
const canViewVenue = partnerUtils.canViewVenue('partner:staff', 'venue-123');
const canCreateEvents = partnerUtils.canCreateVenueEvents(
  'partner:admin',
  'venue-123'
);
```

## 👨‍💼 Admin Portal

### Admin Roles

| Role               | Description        | Use Case                             |
| ------------------ | ------------------ | ------------------------------------ |
| `admin:superadmin` | Full system access | System administrators, CTO, DevOps   |
| `admin:manager`    | Management focus   | Product managers, team leads         |
| `admin:staff`      | Support focus      | Customer support, content moderators |
| `admin:analyst`    | Analytics focus    | Data analysts, business intelligence |
| `admin:security`   | Security focus     | Security officers, compliance team   |

### Admin Resources

- `admin:users` - User management
- `admin:organizations` - Organization management
- `admin:partners` - Partner account management
- `admin:analytics` - System analytics
- `admin:security` - Security settings
- `admin:feedback` - User feedback
- `admin:links` - Shortlink management
- `admin:settings` - Admin settings

### Admin Usage Examples

```typescript
import { adminUtils } from '@/lib/auth/RBAC/utils';

// Check specific permissions
const canViewAnalytics = adminUtils.canViewAnalytics('admin:analyst');
const canManageUsers = adminUtils.canManageUsers('admin:manager');
const canViewSecurity = adminUtils.canViewSecurityLogs('admin:security');
```

## 🛠️ Utility Functions

### Basic Permission Checks

```typescript
import { canView, canEdit, canCreate, canDelete } from '@/lib/auth/RBAC/utils';

const role = 'partner:manager';
const resource = 'partner:venues';

if (canEdit(role, resource)) {
  // User can edit venues
}
```

### Multi-Role Support

```typescript
import {
  hasMultiRolePermission,
  getMultiRolePermissions,
} from '@/lib/auth/RBAC';

const userRoles = ['partner:staff', 'partner:viewer'];
const canAccess = hasMultiRolePermission(
  userRoles,
  'partner:customers',
  'edit'
);
```

### Permission Level Analysis

```typescript
import {
  getPermissionLevel,
  isAdministrativeRole,
} from '@/lib/auth/RBAC/utils';

const level = getPermissionLevel('partner:admin', 'partner:venues');
// Returns 'delete' (highest permission)

const isAdmin = isAdministrativeRole('partner:admin', 'partner:venues');
// Returns true
```

## 🔧 Implementation Examples

### API Route Protection

```typescript
// middleware/rbac.ts
import { hasPermission, permissions } from '@/lib/auth/RBAC';

export function requirePermission(resource: string, permission: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    const userRole = req.user.role;

    if (hasPermission(userRole, resource, permission)) {
      next();
    } else {
      res.status(403).json({ error: 'Insufficient permissions' });
    }
  };
}

// Usage in routes
app.get(
  '/api/venues',
  requirePermission('partner:venues', permissions.VIEW),
  getVenues
);
```

### React Component Protection

```typescript
// components/ProtectedComponent.tsx
import { hasPermission } from '@/lib/auth/RBAC';
import { useUser } from '@/hooks/useUser';

interface ProtectedProps {
  resource: string;
  permission: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function Protected({
  resource,
  permission,
  fallback,
  children,
}: ProtectedProps) {
  const { user } = useUser();

  if (!hasPermission(user.role, resource, permission)) {
    return fallback || <div>Access denied</div>;
  }

  return <>{children}</>;
}

// Usage
<Protected resource="partner:venues" permission="edit">
  <EditVenueButton />
</Protected>;
```

### Venue-Specific Permissions

```typescript
import { createVenueResource, hasPermission } from '@/lib/auth/RBAC';

const venueId = 'venue-123';
const venueResource = createVenueResource('partner:venue', venueId);

const canEditThisVenue = hasPermission(
  'partner:manager',
  venueResource,
  'edit',
  { venueId }
);
```

## 📊 Permission Matrix

### Partner Portal Matrix

| Resource  | Admin | Manager | Staff | Viewer |
| --------- | ----- | ------- | ----- | ------ |
| Dashboard | CRUD  | R       | R     | R      |
| Venues    | CRUD  | CRU     | CR    | R      |
| Team      | CRUD  | CRU     | -     | -      |
| Billing   | CRUD  | CRU     | -     | R      |
| Customers | CRUD  | -       | CRU   | R      |
| Settings  | CRUD  | -       | -     | R      |

### Admin Portal Matrix

| Resource      | SuperAdmin | Manager | Staff | Analyst | Security |
| ------------- | ---------- | ------- | ----- | ------- | -------- |
| Users         | CRUD       | CRU     | R     | R       | R        |
| Organizations | CRUD       | CRU     | R     | R       | R        |
| Partners      | CRUD       | CRU     | -     | -       | R        |
| Analytics     | CRUD       | R       | -     | R       | R        |
| Security      | CRUD       | -       | -     | -       | RU       |
| Feedback      | CRUD       | CRU     | RU    | R       | R        |

**Legend**: C = Create, R = Read, U = Update, D = Delete, - = No Access

## 🔍 Testing

```typescript
// Example test
import {
  hasPermission,
  partnerRoles,
  partnerResources,
  permissions,
} from '@/lib/auth/RBAC';

describe('Partner RBAC', () => {
  test('manager can edit venues', () => {
    expect(
      hasPermission(
        partnerRoles.MANAGER,
        partnerResources.PARTNER_VENUES,
        permissions.EDIT
      )
    ).toBe(true);
  });

  test('staff cannot delete venues', () => {
    expect(
      hasPermission(
        partnerRoles.STAFF,
        partnerResources.PARTNER_VENUES,
        permissions.DELETE
      )
    ).toBe(false);
  });
});
```

## 📝 Best Practices

1. **Principle of Least Privilege**: Assign minimum permissions needed
2. **Server-Side Validation**: Always validate permissions on the server
3. **Audit Logging**: Log all permission checks and changes
4. **Regular Reviews**: Periodically review role assignments
5. **Context Awareness**: Use venue/organization context when applicable
6. **UI/UX Integration**: Hide unavailable actions based on permissions

## 🔄 Migration Guide

If you're migrating from the old role system:

```typescript
// Old system
const oldRoles = {
  partnerOwner: 'partnerOwner',
  staff: 'staff',
  manager: 'manager',
};

// New system mapping
const roleMapping = {
  partnerOwner: partnerRoles.ADMIN, // 'partner:admin'
  manager: partnerRoles.MANAGER, // 'partner:manager'
  staff: partnerRoles.STAFF, // 'partner:staff'
};
```

## 📚 Related Documentation

- [RBAC Roles and Resources](../../../docs/RBAC_Roles_and_Resources.md) - Complete specification
- [Authentication Guide](../../../docs/Authentication-Redirect-Guide.md) - Auth implementation
- [API Documentation](../../../docs/API.md) - API endpoints and protection

## 🏢 Multi-Organization RBAC (Organization-Scoped Permissions)

### **Problem Statement**

Users can belong to multiple partner organizations with different roles in each. Current system has security vulnerabilities:

- Cross-organization permission bleed
- PartnerA admins affecting PartnerB access
- User role conflicts across organizations

### **Solution: Organization-Scoped Roles**

#### **Enhanced Role Structure**

```typescript
// Enhanced role format with organization context
type OrganizationScopedRole = {
  role: PartnerRole; // e.g., 'partner:manager'
  organizationId: string; // e.g., 'org_123'
  assignedBy: string; // User ID who assigned the role
  assignedAt: Date;
  expiresAt?: Date;
  isActive: boolean;
  context?: {
    venueIds?: string[]; // Specific venues user can access
    limitations?: string[]; // Any role limitations
  };
};

// User's complete role structure
interface UserOrganizationRoles {
  userId: string;
  roles: OrganizationScopedRole[];
  currentOrganizationId?: string; // Active organization context
}
```

#### **Database Schema Enhancement**

```sql
-- New table for organization-scoped role assignments
CREATE TABLE user_organization_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  role VARCHAR(50) NOT NULL, -- e.g., 'partner:manager'
  assigned_by UUID NOT NULL REFERENCES users(id),
  assigned_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  venue_restrictions JSONB, -- Array of venue IDs if role is venue-specific
  permissions_metadata JSONB, -- Additional context/limitations

  UNIQUE(user_id, organization_id, role), -- Prevent duplicate role assignments
  INDEX(user_id, organization_id, is_active)
);

-- Track current active organization context per user session
CREATE TABLE user_active_organization (
  user_id UUID PRIMARY KEY REFERENCES users(id),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### **Enhanced Permission Checking**

```typescript
import { hasOrganizationPermission } from '@/lib/auth/RBAC';

// Context-aware permission checking
interface PermissionContext {
  organizationId: string;
  venueId?: string;
  userId?: string;
}

function hasOrganizationPermission(
  userRoles: OrganizationScopedRole[],
  resource: string,
  permission: Permission,
  context: PermissionContext
): boolean {
  // Filter roles by organization context
  const orgRoles = userRoles.filter(
    (role) => role.organizationId === context.organizationId && role.isActive
  );

  // Check venue-specific restrictions if applicable
  if (context.venueId) {
    const venueRestrictedRoles = orgRoles.filter(
      (role) =>
        !role.context?.venueIds ||
        role.context.venueIds.includes(context.venueId!)
    );

    return venueRestrictedRoles.some((role) =>
      hasPermission(role.role, resource, permission)
    );
  }

  return orgRoles.some((role) =>
    hasPermission(role.role, resource, permission)
  );
}
```

#### **Session API Enhancement**

```typescript
// Updated session response with organization context
export interface SessionResponse {
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    profile?: Record<string, unknown>;
  };
  organizationRoles: OrganizationScopedRole[];
  currentOrganization: {
    id: string;
    name: string;
    type: 'partner' | 'admin';
    userRole: PartnerRole; // User's role in current org
  };
  availableOrganizations: Array<{
    id: string;
    name: string;
    type: 'partner' | 'admin';
    userRole: PartnerRole;
  }>;
  token: string;
  rbac: typeof rbacConfig;
}
```

#### **Organization Switching**

```typescript
// API endpoint for organization switching
POST /api/user/switch-organization
{
  "organizationId": "org_456"
}

// Frontend organization switcher
import { useOrganizationContext } from '@/hooks/useOrganizationContext';

export function OrganizationSwitcher() {
  const {
    currentOrganization,
    availableOrganizations,
    switchOrganization
  } = useOrganizationContext();

  return (
    <Select
      value={currentOrganization.id}
      onValueChange={switchOrganization}
    >
      {availableOrganizations.map(org => (
        <SelectItem key={org.id} value={org.id}>
          {org.name} ({org.userRole})
        </SelectItem>
      ))}
    </Select>
  );
}
```

#### **Role Management Security**

```typescript
// Only organization admins can manage roles within their organization
export function canManageUserRole(
  adminRole: OrganizationScopedRole,
  targetUserId: string,
  targetOrganizationId: string
): boolean {
  // Must be admin/manager in the same organization
  const isOrgAdmin = adminRole.organizationId === targetOrganizationId &&
    ['partner:admin', 'partner:manager'].includes(adminRole.role);

  // Cannot modify users in other organizations
  return isOrgAdmin;
}

// API endpoint with proper authorization
PUT /api/organizations/:orgId/users/:userId/roles
Authorization: Bearer <token>
X-Organization-Context: org_123

{
  "role": "partner:staff",
  "venueRestrictions": ["venue_789"],
  "expiresAt": "2025-12-31T23:59:59Z"
}
```

### **Implementation Examples**

#### **Frontend Permission Checks**

```typescript
import { usePermissions } from '@/hooks/usePermissions';

export function VenueManagementPage({ venueId }: { venueId: string }) {
  const { canEdit, canDelete, currentOrganization } = usePermissions();

  const canEditVenue = canEdit('partner:venues', {
    organizationId: currentOrganization.id,
    venueId,
  });

  const canDeleteVenue = canDelete('partner:venues', {
    organizationId: currentOrganization.id,
    venueId,
  });

  return (
    <div>
      <h1>Venue Management - {currentOrganization.name}</h1>
      {canEditVenue && <EditVenueButton venueId={venueId} />}
      {canDeleteVenue && <DeleteVenueButton venueId={venueId} />}
    </div>
  );
}
```

#### **API Route Protection**

```typescript
// Enhanced middleware with organization context
export function requireOrganizationPermission(
  resource: string,
  permission: Permission
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;
    const organizationId =
      req.headers['x-organization-context'] || req.params.organizationId;

    if (!organizationId) {
      return res.status(400).json({
        error: 'Organization context required',
      });
    }

    const hasPermission = await hasOrganizationPermission(
      user.organizationRoles,
      resource,
      permission,
      { organizationId }
    );

    if (!hasPermission) {
      return res.status(403).json({
        error: 'Insufficient permissions for this organization',
      });
    }

    req.organizationContext = { organizationId };
    next();
  };
}

// Usage
app.put(
  '/api/organizations/:orgId/venues/:venueId',
  requireOrganizationPermission('partner:venues', 'edit'),
  updateVenue
);
```

### **Benefits of This Approach**

✅ **Security Isolation**: Each organization's role assignments are completely isolated  
✅ **Granular Control**: Organization admins control only their own team  
✅ **Multi-Organization Support**: Users can have different roles in different orgs  
✅ **Venue-Specific Permissions**: Fine-grained access control per venue  
✅ **Audit Trail**: Complete tracking of who assigned what role when  
✅ **Flexible Email Policy**: Users can use personal emails across organizations  
✅ **Context Awareness**: System always knows which organization context user is in

### **Migration Strategy**

1. **Phase 1**: Add new database tables and maintain backward compatibility
2. **Phase 2**: Migrate existing role assignments to organization-scoped format
3. **Phase 3**: Update session API to include organization context
4. **Phase 4**: Update frontend components to use organization-aware permissions
5. **Phase 5**: Remove legacy role system
