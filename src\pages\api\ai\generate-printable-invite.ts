import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import geminiService from '@/lib/gemini/service';
import { EventFormData } from '@/types';
import { checkAIUsageLimit, incrementAIUsage } from '@/lib/ai-usage';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      eventId,
      eventName,
      eventDate,
      startTime,
      endTime,
      location,
      timezone,
      host,
      message,
      messageStyle = 'personalised',
      pageSize = 'A4',
      orientation = 'portrait'
    } = req.body;

    // Validate required fields
    if (!eventName) {
      return res.status(400).json({ error: 'Event name is required' });
    }

    if (!eventId) {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    // Get session for user authentication
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user has AI access
    if (!session.user.hasAiAccess) {
      return res.status(403).json({ error: 'AI access not available for this user' });
    }

    // Check AI usage limits
    try {
      const limitCheck = await checkAIUsageLimit(eventId);

      if (!limitCheck.canUseAI) {
        return res.status(429).json({
          error: 'AI usage limit exceeded',
          details: limitCheck.message,
          currentUsage: limitCheck.currentUsage,
          limit: limitCheck.limit
        });
      }
    } catch (error) {
      console.error('Error checking AI usage limit:', error);
      return res.status(500).json({ error: 'Failed to check AI usage limits' });
    }

    // Validate messageStyle if provided
    const validStyles = ['personalised', 'casual_friendly', 'formal_professional', 'fun_energetic', 'business_professional', 'creative_unique'];
    if (messageStyle && !validStyles.includes(messageStyle)) {
      return res.status(400).json({ 
        error: `Invalid message style. Must be one of: ${validStyles.join(', ')}` 
      });
    }

    // Validate pageSize if provided
    const validPageSizes = ['A4', 'A5', 'A6', 'photo4x6', 'photo5x7', 'photo6x8', 'photo8x10'];
    if (pageSize && !validPageSizes.includes(pageSize)) {
      return res.status(400).json({ 
        error: `Invalid page size. Must be one of: ${validPageSizes.join(', ')}` 
      });
    }

    // Validate orientation if provided
    const validOrientations = ['portrait', 'landscape'];
    if (orientation && !validOrientations.includes(orientation)) {
      return res.status(400).json({ 
        error: `Invalid orientation. Must be one of: ${validOrientations.join(', ')}` 
      });
    }

    // Prepare event data for AI image generation
    const eventData: EventFormData = {
      eventName,
      eventDate: eventDate ? new Date(eventDate) : new Date(),
      start: startTime || '09:00',
      end: endTime || '17:00',
      location: location || '',
      timezone: timezone,
      host: host || '',
      message: message,
      messageStyle: messageStyle
    };

    // Generate printable invite image using Gemini service
    const result = await geminiService.generatePrintableInviteImage(eventData, pageSize, orientation);

    // Record AI usage after successful generation
    await incrementAIUsage(eventId);

    return res.status(200).json({
      success: true,
      result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating printable invite image with AI:', error);
    return res.status(500).json({ 
      error: 'Failed to generate printable invite image with AI',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
