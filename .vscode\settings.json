{"cSpell.words": ["BUILDENV", "firestore", "iamcoming", "MAILERSEND", "<PERSON><PERSON><PERSON>", "Shortlink", "Shortlinks", "tailwindcss", "turbopack"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "always", "source.fixAll": "always"}, "editor.formatOnSave": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.autoFixOnSave": true, "eslint.run": "onSave", "eslint.alwaysShowStatus": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint"}