import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';
import { Event } from '@/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated and is an admin
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the current user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    // Get user ID from the query parameters
    const { userId } = req.query;

    if (!userId || Array.isArray(userId)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // Only accept GET method for fetching user events
    if (req.method !== 'GET') {
      res.setHeader('Allow', ['GET']);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get database instance
    const db = Database.getInstance();

    // Get the target user to find their email
    const user = await db.getUserById(userId);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get events created by this user (using their email)
    const events = await db.ListData<Event>('events', {
      field: 'ownerEmail',
      operator: '==',
      value: user.email
    });

    // Format the events for display
    const formattedEvents = events.map((event: Event) => ({
      id: event.ID,
      name: event.eventName,
      date: event.eventDate,
      location: event.location,
      status: event.status || 'active',
      guests: {
        total: 0, // These would be populated from invites in a real implementation
        confirmed: 0
      }
    }));

    // Return success response
    return res.status(200).json({
      success: true,
      events: formattedEvents
    });
  } catch (error) {
    console.error('Error fetching user events:', error);
    return res.status(500).json({ error: 'Failed to fetch user events' });
  }
}