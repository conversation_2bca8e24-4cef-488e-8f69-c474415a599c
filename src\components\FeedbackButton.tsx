'use client'

import { useState, useEffect, useCallback } from 'react'
import { MessageSquare, Send, X, ArrowRight, ArrowLeft } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import Script from 'next/script'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/use-toast'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import { cn } from "@/lib/utils"

// Google reCAPTCHA type definitions
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void
      execute: (siteKey: string, options: { action: string }) => Promise<string>
      reset: () => void
      render: (container: string | HTMLElement, parameters: object) => number
    }
  }
}

interface Option {
  label: string
  value: string
}

interface OptionButtonGroupProps {
  label: string
  options: Option[]
  selected: string
  onSelect: (value: string) => void
  className?: string
}

function OptionButtonGroup({
  label,
  options,
  selected,
  onSelect,
  className,
}: OptionButtonGroupProps) {
  return (
    <div className={cn("flex flex-col space-y-3", className)}>
      <label className="text-base font-medium">{label}</label>
      <div className="flex gap-2 flex-wrap">
        {options.map((option) => (
          <button
            key={option.value}
            type="button"
            onClick={() => onSelect(option.value)}
            className={cn(
              "px-4 py-2 rounded-lg border text-sm font-medium transition-colors",
              "border-border text-muted-foreground bg-background hover:text-foreground hover:border-rose-300",
              selected === option.value &&
              "border-rose-500 text-foreground bg-rose-50"
            )}
          >
            {option.label}
          </button>
        ))}
      </div>
    </div>
  )
}

export function FeedbackButton() {
  const [isOpen, setIsOpen] = useState(false)
  const [step, setStep] = useState(1)
  const [related, setRelated] = useState("")
  const [experience, setExperience] = useState("")
  const [feedbackType, setFeedbackType] = useState("")
  const [details, setDetails] = useState("")
  const [email, setEmail] = useState("")  // const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [sessionStartTime] = useState(Date.now())
  const [pageStartTime] = useState(Date.now())
  const [pagesVisited, setPagesVisited] = useState<string[]>([])
  const [recaptchaLoaded, setRecaptchaLoaded] = useState(false)
  const [loadingRecaptcha, setLoadingRecaptcha] = useState(false)
  const { toast } = useToast()
  const { data: session } = useSession()
  const router = useRouter()

  // Initialize reCAPTCHA when needed
  const handleRecaptchaLoad = useCallback(() => {
    setRecaptchaLoaded(true)
    setLoadingRecaptcha(false)
    console.log("reCAPTCHA loaded successfully")
  }, [])

  const totalSteps = 3

  const relatedOptions = [
    { label: "Yes", value: "yes" },
    { label: "No, It's something else", value: "no" },
  ]

  const experienceOptions = [
    { label: "😤 Frustrating", value: "Frustrating" },
    { label: "😕 Could be better", value: "Could be better" },
    { label: "😐 Just Okay", value: "Just Okay" },
    { label: "😊 Pretty Good", value: "Pretty Good" },
    { label: "😍 Loving it", value: "Loving it" },
  ]

  const feedbackTypeOptions = [
    { label: "Bug", value: "Bug" },
    { label: "Feature Request", value: "Feature Request" },
    { label: "Other", value: "Other" },
  ]

  // Collect session metadata
  const collectSessionData = () => {
    const now = Date.now()
    const timeOnPage = now - pageStartTime

    // Get device info
    const getDeviceType = () => {
      const userAgent = navigator.userAgent.toLowerCase()
      if (/mobile|android|iphone|ipad|phone/i.test(userAgent)) return 'mobile'
      if (/tablet|ipad/i.test(userAgent)) return 'tablet'
      return 'desktop'
    }

    const getBrowser = () => {
      const userAgent = navigator.userAgent
      if (userAgent.includes('Chrome')) return 'Chrome'
      if (userAgent.includes('Firefox')) return 'Firefox'
      if (userAgent.includes('Safari')) return 'Safari'
      if (userAgent.includes('Edge')) return 'Edge'
      return 'Unknown'
    }

    const getOS = () => {
      const userAgent = navigator.userAgent
      if (userAgent.includes('Windows')) return 'Windows'
      if (userAgent.includes('Mac')) return 'macOS'
      if (userAgent.includes('Linux')) return 'Linux'
      if (userAgent.includes('Android')) return 'Android'
      if (userAgent.includes('iOS')) return 'iOS'
      return 'Unknown'
    }

    return {
      // Session Metadata
      sessionStartTime,
      submittedAt: now,
      currentPage: router.asPath,
      referrer: document.referrer || 'direct',
      pagesVisited: [...new Set([...pagesVisited, router.asPath])],
      timeOnPage,

      // User Metadata
      isLoggedIn: !!session,
      userId: session?.user?.id || null,
      userEmail: session?.user?.email || email || null,
      userRole: session?.user?.isAdmin ? 'admin' : session ? 'user' : 'guest',

      // Device Metadata
      deviceType: getDeviceType(),
      browser: getBrowser(),
      os: getOS(),
      userAgent: navigator.userAgent,      // Feedback Form Metadata
      formData: {
        related,
        experience,
        feedbackType,
        details,
        email,
        // files: selectedFiles.map(file => ({
        //   name: file.name,
        //   size: file.size,
        //   type: file.type
        // }))
      }
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      // Collect comprehensive session data
      const sessionData = collectSessionData()

      // Generate recaptcha token for security validation
      let recaptchaToken = ''
      try {
        if (window.grecaptcha) {
          await new Promise((resolve, reject) => {
            window.grecaptcha.ready(() => {
              window.grecaptcha.execute(
                process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '',
                { action: 'feedback_submission' }
              ).then(resolve).catch(reject);
            });
          }).then((token) => {
            recaptchaToken = token as string;
          }).catch((error) => {
            console.error("reCAPTCHA error:", error);
            throw new Error("Security verification failed. Please try again.");
          });
        } else {
          throw new Error("reCAPTCHA library not loaded. Please refresh the page and try again.");
        }
      } catch (error) {
        console.error("reCAPTCHA error:", error)
        throw new Error("Security verification failed. Please try again.")
      }

      // Submit feedback to API
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: details,
          rating: experience === 'Loving it' ? 5 :
            experience === 'Pretty Good' ? 4 :
              experience === 'Just Okay' ? 3 :
                experience === 'Could be better' ? 2 :
                  experience === 'Frustrating' ? 1 : 3,
          category: feedbackType || 'general',
          source: 'feedback-widget',
          page: router.asPath,
          sessionData: sessionData, // Include all the session data
          recaptchaToken: recaptchaToken
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send feedback')
      }

      const data = await response.json()
      console.log("📊 Feedback submitted successfully:", data)

      toast({
        title: "Feedback Sent!",
        description: `Thank you for your feedback (ID: ${data.id}). We appreciate your input and will review it carefully.`,
      })

      // Reset form
      setStep(1)
      setRelated("")
      setExperience("")
      setFeedbackType("")
      setDetails("")
      setEmail("")
      // setSelectedFiles([])
      setIsOpen(false)

    } catch (error) {
      console.error("Feedback submission error:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send feedback. Please try again later.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   const files = event.target.files
  //   if (files) {
  //     const fileArray = Array.from(files)
  //     const maxFileSize = 10 * 1024 * 1024 // 10MB in bytes
  //     const maxFiles = 5

  //     // Check file count limit
  //     const totalFiles = selectedFiles.length + fileArray.length
  //     if (totalFiles > maxFiles) {
  //       toast({
  //         title: "Too Many Files",
  //         description: `You can only attach up to ${maxFiles} files. Please remove some files and try again.`,
  //         variant: "destructive"
  //       })
  //       return
  //     }

  //     // Check file size limit
  //     const oversizedFiles = fileArray.filter(file => file.size > maxFileSize)
  //     if (oversizedFiles.length > 0) {
  //       toast({
  //         title: "File Too Large",
  //         description: `Files must be smaller than 10MB. Please compress or choose smaller files.`,
  //         variant: "destructive"
  //       })
  //       return
  //     }

  //     // Add new files to existing ones
  //     setSelectedFiles(prev => [...prev, ...fileArray])
  //   }

  //   // Reset input value to allow selecting the same file again
  //   event.target.value = ''
  // }

  // const handleRemoveFile = (index: number) => {
  //   setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  // }

  // Validation functions for each step
  const validateStep1 = () => {
    return related !== "" && experience !== ""
  }

  const validateStep2 = () => {
    return feedbackType !== "" && details.trim() !== ""
  }

  const validateStep3 = () => {
    return email.trim() !== ""
  }

  const canProceedToNextStep = () => {
    switch (step) {
      case 1:
        return validateStep1()
      case 2:
        return validateStep2()
      case 3:
        return validateStep3()
      default:
        return false
    }
  }
  const cleanupRecaptcha = () => {
    // Reset reCAPTCHA states
    setRecaptchaLoaded(false)
    setLoadingRecaptcha(false)

    // Clean up reCAPTCHA script elements
    const recaptchaScripts = document.querySelectorAll('script[src*="recaptcha"], script[src*="grecaptcha"]')
    recaptchaScripts.forEach(script => script.remove())

    // Remove reCAPTCHA badge elements
    const recaptchaBadges = document.querySelectorAll('.grecaptcha-badge, .grecaptcha-logo, iframe[title="recaptcha challenge"], iframe[src*="recaptcha"]')
    recaptchaBadges.forEach(badge => badge.remove())

    // Remove other potential reCAPTCHA elements
    document.querySelectorAll('div.grecaptcha-overlay, div[style*="z-index: 2000000000"]').forEach(el => el.remove())

    // Reset reCAPTCHA if still available
    if (window.grecaptcha) {
      try {
        // Try to use the reset method first if available
        if (typeof window.grecaptcha.reset === 'function') {
          window.grecaptcha.reset()
        }

        // Set grecaptcha to undefined to effectively "delete" it
        window.grecaptcha = undefined as any
      } catch (e) {
        console.warn('Could not clear reCAPTCHA globals:', e)
      }
    }

    // Remove any global reCAPTCHA callback functions
    for (const key in window) {
      if (key.startsWith('___grecaptcha_cfg') || key.startsWith('recaptcha')) {
        try {
          // Using type assertion to handle dynamic property deletion
          delete (window as Record<string, unknown>)[key]
        } catch (e) {
          console.warn(`Could not delete ${key} from window`, e)
        }
      }
    }
  }

  const handleClose = () => {
    setIsOpen(false)
    // Reset form when closing
    setStep(1)
    setRelated("")
    setExperience("")
    setFeedbackType("")
    setDetails("")
    setEmail("")
    // setSelectedFiles([])

    // Clean up reCAPTCHA
    cleanupRecaptcha()
  }

  // Clean up reCAPTCHA when component unmounts
  useEffect(() => {
    return () => {
      if (isOpen) {
        cleanupRecaptcha()
      }
    }
  }, [isOpen])

  // Track page visits
  useEffect(() => {
    const currentPath = router.asPath
    setPagesVisited(prev => {
      if (!prev.includes(currentPath)) {
        return [...prev, currentPath]
      }
      return prev
    })
  }, [router.asPath])

  // Pre-fill email when modal opens if user is logged in
  useEffect(() => {
    if (isOpen && session?.user?.email && !email) {
      setEmail(session.user.email)
    }
  }, [isOpen, session?.user?.email, email])

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <div className="grid w-full items-center gap-6 py-6">
            <OptionButtonGroup
              label="Is it related to what you're doing right now? *"
              options={relatedOptions}
              selected={related}
              onSelect={setRelated}
            />
            <OptionButtonGroup
              label="How's your experience so far? *"
              options={experienceOptions}
              selected={experience}
              onSelect={setExperience}
            />
          </div>
        )

      case 2:
        return (
          <div className="grid w-full items-center gap-6 py-6">
            <OptionButtonGroup
              label="Feedback Type *"
              options={feedbackTypeOptions}
              selected={feedbackType}
              onSelect={setFeedbackType}
            />
            <div className="flex flex-col space-y-3">
              <Label htmlFor="details" className="text-base font-medium">
                Please share more details *
              </Label>
              <Textarea
                id="details"
                placeholder="Describe your feedback in detail..."
                value={details}
                onChange={(e) => setDetails(e.target.value)}
                className="min-h-[100px]"
              />            </div>
            {/* File upload section commented out
            <div className="flex flex-col space-y-3">
              <Label className="text-base font-medium">
                Anything to add? (Optional)
              </Label>
              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center text-muted-foreground">
                <p className="text-sm mb-2">Drop files here or click to browse</p>
                <input
                  type="file"
                  multiple
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-upload"
                  accept="image/*,.pdf,.doc,.docx,.txt"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('file-upload')?.click()}
                  type="button"
                >
                  Browse Files
                </Button>
              </div>

              {/* Display selected files */}
            {/* {selectedFiles.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Selected files:</p>
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-muted/30 rounded-md p-2">
                      <span className="text-sm truncate">{file.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveFile(index)}
                        className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )} */}
            {/* </div> */}
          </div>
        )

      case 3:
        return (
          <div className="grid w-full items-center gap-6 py-6">
            <div className="flex flex-col space-y-3">
              <Label htmlFor="email" className="text-base font-medium">
                Email *
              </Label>
              <Input
                id="email"
                type="email"
                placeholder={session?.user?.email || "<EMAIL>"}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                We&apos;ll only use this to follow up on your feedback if needed.
              </p>
            </div>
            <div className="space-y-6">
              {/* Related to current task */}
              <div className="space-y-2">
                <h4 className="font-medium text-base">Is it related to what you&apos;re doing right now?</h4>
                <div className="bg-muted/30 rounded-lg px-3 py-2 text-sm">
                  {related === "yes" ? "Yes" : related === "no" ? "No, It's something else" : "Not selected"}
                </div>
              </div>

              {/* Experience */}
              <div className="space-y-2">
                <h4 className="font-medium text-base">How&apos;s your experience so far?</h4>
                <div className="bg-muted/30 rounded-lg px-3 py-2 text-sm flex items-center gap-2">
                  {experienceOptions.find(opt => opt.value === experience)?.label || "Not selected"}
                </div>
              </div>

              {/* Feedback Type */}
              <div className="space-y-2">
                <h4 className="font-medium text-base">Feedback Type</h4>
                <div className="bg-muted/30 rounded-lg px-3 py-2 text-sm">
                  {feedbackType || "Not selected"}
                </div>
              </div>

              {/* Details */}
              {details && (
                <div className="space-y-2">
                  <h4 className="font-medium text-base">Please share more details</h4>
                  <div className="bg-muted/30 rounded-lg px-3 py-2 text-sm">
                    {details}
                  </div>
                </div>)}

              {/* Added Files section commented out */}
              {/* {selectedFiles.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-base">Added Files</h4>
                  <div className="space-y-2">
                    {selectedFiles.map((file, index) => (
                      <div key={index} className="bg-muted/30 rounded-lg px-3 py-2 text-sm flex items-center gap-2">
                        <div className="w-6 h-6 bg-muted rounded flex items-center justify-center">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="truncate">{file.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )} */}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <>
      {/* Floating Feedback Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 bg-[#F43F5E] hover:bg-[#E11D48] text-white border-0"
          size="icon"
        >
          <MessageSquare className="h-6 w-6" />
        </Button>
      </div>

      {/* Feedback Modal */}
      {isOpen && (
        <>
          {/* Hide reCAPTCHA badge but retain attribution */}
          <style jsx global>{`
            .grecaptcha-badge {
              visibility: hidden !important;
              opacity: 0 !important;
              pointer-events: none !important;
            }
          `}</style>

          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 z-50"
            onClick={handleClose}
          />

          {/* Modal Content */}
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            <Card
              className="w-[420px] mx-auto shadow-lg rounded-lg overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <CardHeader className="bg-slate-50 px-5 py-5">
                <div className="flex items-start justify-between w-full">
                  {/* Title + Description */}
                  <div className="flex flex-col">
                    <CardTitle className="text-lg">
                      {step === totalSteps ? "Preview & Submit" : "Got feedback? We're listening."}
                    </CardTitle>
                    <CardDescription>
                      Step {step} of {totalSteps}
                    </CardDescription>
                  </div>

                  {/* Close Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground hover:text-foreground"
                    onClick={handleClose}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              <Separator />
              <CardContent className="px-6 h-[440px] overflow-y-auto">
                <form onSubmit={(e) => e.preventDefault()}>{renderStepContent()}</form>
              </CardContent>
              <Separator />
              <CardFooter className="flex flex-col space-y-4 px-6 py-4 bg-background">
                <div className="flex justify-between w-full">
                  {step > 1 ? (
                    <Button
                      variant="outline"
                      onClick={() => setStep(step - 1)}
                      type="button"
                      className="group transition-colors duration-300 flex items-center gap-2"
                    >
                      <span className="transition-transform duration-300 group-hover:-translate-x-1">
                        <ArrowLeft size={16} />
                      </span>
                      Back
                    </Button>
                  ) : (
                    <div />
                  )}
                  {step === totalSteps ? (
                    <Button
                      onClick={() => {
                        // Load reCAPTCHA if not already loaded
                        if (!recaptchaLoaded && !loadingRecaptcha) {
                          setLoadingRecaptcha(true);
                          // Will trigger the script to load via useEffect
                        } else {
                          handleSubmit();
                        }
                      }}
                      type="button"
                      disabled={isSubmitting || !canProceedToNextStep() || (loadingRecaptcha)}
                      className="group bg-rose-500 hover:bg-rose-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition duration-300 transform hover:scale-[1.01] flex items-center gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Submitting...
                        </>
                      ) : loadingRecaptcha ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Verifying...
                        </>
                      ) : (
                        "Submit Feedback"
                      )}
                    </Button>) : (
                    <Button
                      onClick={() => setStep(step + 1)}
                      type="button"
                      disabled={!canProceedToNextStep()}
                      variant="outline"
                      className="group disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors duration-300 flex items-center gap-2"
                    >
                      Next
                      <span className="transition-transform duration-300 group-hover:translate-x-1">
                        <ArrowRight size={16} />
                      </span>
                    </Button>
                  )}
                </div>

                {/* reCAPTCHA Terms of Service Notice */}
                {step === totalSteps && (
                  <div className="text-sm text-muted-foreground text-center">
                    This site is protected by reCAPTCHA and the Google{" "}
                    <a
                      href="https://policies.google.com/privacy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-rose-500 hover:underline"
                    >
                      Privacy Policy
                    </a>{" "}
                    and{" "}
                    <a
                      href="https://policies.google.com/terms"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-rose-500 hover:underline"
                    >
                      Terms of Service
                    </a>{" "}
                    apply.
                  </div>
                )}
              </CardFooter>
            </Card>
          </div>
        </>
      )}

      {/* Only load reCAPTCHA when needed */}
      {(isOpen && (step === totalSteps || loadingRecaptcha)) && (
        <Script
          src={`https://www.google.com/recaptcha/api.js?render=${process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || ''}`}
          onLoad={handleRecaptchaLoad}
          strategy="lazyOnload"
          id="recaptcha-script"
        />
      )}
    </>
  )
}
