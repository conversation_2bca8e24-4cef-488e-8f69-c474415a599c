import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authConfig } from '@/auth';
import { setIacCorsHeaders } from '@/lib/cors';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Apply CORS headers for all requests
  setIacCorsHeaders(req, res);

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST', 'OPTIONS']);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const DEBUG = process.env.IAC_DEBUG === 'true' || process.env.NODE_ENV === 'development';

  if (DEBUG) {
    console.log('🔍 Signout endpoint DEBUG - Request details:');
    console.log('- Method:', req.method);
    console.log('- Origin:', req.headers.origin);
    console.log('- User-Agent:', req.headers['user-agent']);
    console.log('- Cookie header present:', !!req.headers.cookie);
  }

  try {
    // Get the current session to verify user is authenticated
    const session = await getServerSession(req, res, authConfig);

    if (DEBUG) {
      console.log('- Session exists:', !!session);
      console.log('- User ID:', session?.user?.id);
    }

    // Clear the cross-domain session cookie
    res.setHeader('Set-Cookie', [
      'cross-domain-session=; Path=/; HttpOnly; SameSite=None; Secure; Max-Age=0',
      'cross-domain-session=; Path=/; HttpOnly; SameSite=Lax; Max-Age=0'
    ]);

    if (DEBUG) {
      console.log('✅ Cross-domain session cookies cleared');
    }

    // For NextAuth session cookies, we need to redirect to the NextAuth signout endpoint
    // but we'll return success here for the API call
    return res.status(200).json({
      success: true,
      message: 'Signed out successfully',
      nextAuthSignoutUrl: '/api/auth/sign-out'
    });

  } catch (error) {
    console.error('Signout endpoint error:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to sign out',
      success: false
    });
  }
}
