import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { archiveEvent, deleteOldArchivedEvents, processExpiredArchives } from '@/lib/eventArchive';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Note: This endpoint can be called by anyone, but it's safe because:
  // 1. It only processes events that are 90+ days old
  // 2. It only sends archive emails to event owners
  // 3. No sensitive data is exposed
  // 4. The worst case is duplicate emails, which are prevented by database flags

  try {
    const db = Database.getInstance();

    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development' ||
                         process.env.NEXT_PUBLIC_APP_URL?.includes('localhost');

    let eventsToArchive;
    let archivesToDelete;

    if (isDevelopment) {
      // In development, only process specific test events to avoid archiving real data
      const testEventIds = ['E2kj2g5ef']; // Add more test event IDs here as needed

      console.log('🧪 DEVELOPMENT MODE: Only processing test events for archival:', testEventIds);

      eventsToArchive = [];
      for (const eventId of testEventIds) {
        try {
          const event = await db.readData('events', eventId);
          if (event) {
            // Check if this test event should be archived (90+ days past event date)
            const eventDate = new Date(event.eventDate);
            const ninetyDaysAgo = new Date();
            ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

            if (eventDate < ninetyDaysAgo && event.status !== 'archived') {
              eventsToArchive.push({ id: eventId, ...event });
            }
          }
        } catch (error) {
          console.error(`Error checking test event ${eventId}:`, error);
        }
      }

      // For development, only delete test archives (if any)
      const testArchiveIds = testEventIds; // Same test event IDs
      archivesToDelete = [];
      
      for (const eventId of testArchiveIds) {
        try {
          const archive = await db.readData('event_archives', eventId);
          if (archive) {
            const expiresAt = new Date(archive.expiresAt);
            const now = new Date();
            if (now >= expiresAt) {
              archivesToDelete.push({ id: eventId, ...archive });
            }
          }
        } catch (error) {
          console.error(`Error checking test archive ${eventId}:`, error);
        }
      }
    } else {
      // In production, get all events that should be archived (90+ days old)
      eventsToArchive = await db.getEventsToArchive();
      
      // Get archived events that should be deleted (104+ days old)
      archivesToDelete = await db.getArchivesToDelete();
    }
    
    let archivedCount = 0;
    let deletedCount = 0;
    let errorCount = 0;
    const results = [];

    // Process events for archival
    for (const event of eventsToArchive) {
      try {
        // Archive the event
        await archiveEvent(event.id, event.ownerAccountId);
        
        archivedCount++;
        results.push({
          eventId: event.id,
          eventName: event.eventName,
          action: 'archived',
          status: 'processed'
        });
        
        console.log(`Archived event: ${event.eventName} (${event.id})`);
        
      } catch (error) {
        errorCount++;
        results.push({
          eventId: event.id,
          eventName: event.eventName,
          action: 'archive',
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        
        console.error(`Error archiving event ${event.id}:`, error);
      }
    }

    // Process old archives for deletion (Day 104 cleanup)
    if (archivesToDelete.length > 0) {
      console.log(`🗑️ Processing ${archivesToDelete.length} expired archives for deletion...`);
      
      try {
        const cleanupResults = await processExpiredArchives();
        deletedCount = cleanupResults.deletedCount;
        errorCount += cleanupResults.errorCount;
        
        // Add cleanup results to overall results
        cleanupResults.results.forEach(result => {
          results.push({
            eventId: result.eventId,
            eventName: 'Archive',
            action: 'deleted_archive',
            status: result.status === 'deleted' ? 'processed' : 'error',
            error: result.error
          });
        });
        
        console.log(`✅ Archive cleanup completed: ${cleanupResults.deletedCount} deleted, ${cleanupResults.errorCount} errors`);
        
      } catch (error) {
        console.error('Error in bulk archive cleanup:', error);
        errorCount++;
        results.push({
          eventId: 'bulk_cleanup',
          eventName: 'Bulk Archive Cleanup',
          action: 'bulk_cleanup',
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    } else {
      console.log('📁 No expired archives found for deletion');
    }

    return res.status(200).json({
      success: true,
      message: `Archived ${archivedCount} events, deleted ${deletedCount} old archives, ${errorCount} errors`,
      totalEventsToArchive: eventsToArchive.length,
      totalArchivesToDelete: archivesToDelete.length,
      archivedCount,
      deletedCount,
      errorCount,
      results,
      environment: isDevelopment ? 'development' : 'production',
      testMode: isDevelopment,
      note: isDevelopment
        ? '🧪 Development mode: Only processing test events to avoid archiving real user data'
        : '🚀 Production mode: Processing all events eligible for archival'
    });

  } catch (error) {
    console.error('Error in archive-old-events cron job:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
