// =============================================================================
// CORE RBAC PERMISSIONS AND TYPES
// =============================================================================

// Standard RBAC permissions
export const permissions = {
  VIEW: 'view',
  CREATE: 'create',
  EDIT: 'edit',
  DELETE: 'delete',
} as const;

export type Permission = typeof permissions[keyof typeof permissions];

// Portal types
export const portals = {
  PARTNER: 'partner',
  ADMIN: 'admin',
} as const;

export type Portal = typeof portals[keyof typeof portals];
