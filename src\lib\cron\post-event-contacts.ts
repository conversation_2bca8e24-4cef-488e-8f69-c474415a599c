import { Database } from '../database';
import { extractContactGroupsFromEvent, hasContactGroupsToSave } from '../saved-contact-groups';
import { sendPostEventContactGroupEmail } from '../mailer';
import { Event, Organization } from '@/types';

/**
 * Check if an event is complete based on its date
 * An event is considered complete if:
 * 1. It has a valid event date
 * 2. The event date was more than 24 hours ago
 * 3. The event status is not already marked as 'completed'
 */
export function isEventComplete(event: Event | null): boolean {
  if (!event || !event.eventDate) {
    return false;
  }

  // Check if already marked as completed
  if ((event as any).status === 'completed') {
    return true;
  }

  const eventDate = new Date(event.eventDate);
  const twentyFourHoursAgo = new Date();
  twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

  // Event is complete if it happened more than 24 hours ago
  return eventDate < twentyFourHoursAgo;
}

/**
 * Check if a post-event contact group save email should be sent
 * Conditions:
 * 1. Event is complete
 * 2. Organization has never been asked before (hasBeenAsked = false or undefined)
 * 3. Event has contact groups that can be saved
 */
export async function shouldSendPostEventContactEmail(
  eventId: string,
  organizationId: string
): Promise<{
  shouldSend: boolean;
  reason: string;
  eventComplete: boolean;
  hasGroups: boolean;
  alreadyAsked: boolean;
}> {
  try {
    const db = Database.getInstance();

    // Get event and organization
    const event = await db.readData('events', eventId) as Event;
    const organization = await db.getOrganizationById(organizationId) as Organization;

    if (!event) {
      return {
        shouldSend: false,
        reason: 'Event not found',
        eventComplete: false,
        hasGroups: false,
        alreadyAsked: false
      };
    }

    if (!organization) {
      return {
        shouldSend: false,
        reason: 'Organization not found',
        eventComplete: false,
        hasGroups: false,
        alreadyAsked: false
      };
    }

    // Check if event is complete
    const eventComplete = isEventComplete(event);
    if (!eventComplete) {
      return {
        shouldSend: false,
        reason: 'Event is not yet complete (must be 24+ hours after event date)',
        eventComplete: false,
        hasGroups: false,
        alreadyAsked: false
      };
    }

    // Check if organization has already been asked
    const alreadyAsked = organization.contactGroupSettings?.hasBeenAsked === true;
    if (alreadyAsked) {
      return {
        shouldSend: false,
        reason: 'Organization has already been asked before',
        eventComplete: true,
        hasGroups: false,
        alreadyAsked: true
      };
    }

    // Check if event has contact groups to save
    const hasGroups = await hasContactGroupsToSave(eventId);
    if (!hasGroups) {
      return {
        shouldSend: false,
        reason: 'Event has no contact groups to save',
        eventComplete: true,
        hasGroups: false,
        alreadyAsked: false
      };
    }

    return {
      shouldSend: true,
      reason: 'All conditions met for sending post-event contact email',
      eventComplete: true,
      hasGroups: true,
      alreadyAsked: false
    };

  } catch (error) {
    console.error('Error checking if post-event contact email should be sent:', error);
    return {
      shouldSend: false,
      reason: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      eventComplete: false,
      hasGroups: false,
      alreadyAsked: false
    };
  }
}

/**
 * Process a completed event and send post-event contact group save email if conditions are met
 * This is the main function to call for post-event contact processing
 */
export async function processCompletedEventForContacts(
  eventId: string,
  organizationId?: string
): Promise<{
  success: boolean;
  emailSent: boolean;
  message: string;
  eventComplete: boolean;
  hasGroups: boolean;
  alreadyAsked: boolean;
}> {
  try {
    const db = Database.getInstance();

    // Get event to determine organization if not provided
    const event = await db.readData('events', eventId) as Event;
    if (!event) {
      return {
        success: false,
        emailSent: false,
        message: 'Event not found',
        eventComplete: false,
        hasGroups: false,
        alreadyAsked: false
      };
    }

    const orgId = organizationId || event.ownerAccountId;
    if (!orgId) {
      return {
        success: false,
        emailSent: false,
        message: 'No organization ID found for event',
        eventComplete: false,
        hasGroups: false,
        alreadyAsked: false
      };
    }

    // Check if email should be sent
    const emailCheck = await shouldSendPostEventContactEmail(eventId, orgId);

    if (!emailCheck.shouldSend) {
      return {
        success: true,
        emailSent: false,
        message: emailCheck.reason,
        eventComplete: emailCheck.eventComplete,
        hasGroups: emailCheck.hasGroups,
        alreadyAsked: emailCheck.alreadyAsked
      };
    }

    // Send the email
    const emailSent = await sendPostEventContactGroupEmail(orgId, eventId);

    if (emailSent) {
      // Mark organization as having been asked
      await db.updateData('organizations', orgId, {
        'contactGroupSettings.hasBeenAsked': true,
        lastUpdatedOn: new Date().toISOString()
      });

      return {
        success: true,
        emailSent: true,
        message: 'Post-event contact group save email sent successfully',
        eventComplete: emailCheck.eventComplete,
        hasGroups: emailCheck.hasGroups,
        alreadyAsked: false
      };
    } else {
      return {
        success: false,
        emailSent: false,
        message: 'Failed to send post-event contact group save email',
        eventComplete: emailCheck.eventComplete,
        hasGroups: emailCheck.hasGroups,
        alreadyAsked: emailCheck.alreadyAsked
      };
    }

  } catch (error) {
    console.error('Error processing completed event for contacts:', error);
    return {
      success: false,
      emailSent: false,
      message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      eventComplete: false,
      hasGroups: false,
      alreadyAsked: false
    };
  }
}

/**
 * Bulk process multiple events for post-event contact processing
 * Useful for cron jobs and batch processing
 */
export async function processMultipleEventsForContacts(
  eventIds: string[]
): Promise<Array<{
  eventId: string;
  eventName: string;
  result: Awaited<ReturnType<typeof processCompletedEventForContacts>>;
}>> {
  const results = [];
  const db = Database.getInstance();

  for (const eventId of eventIds) {
    try {
      const event = await db.readData('events', eventId) as Event;
      const eventName = event?.eventName || 'Unknown Event';
      
      const result = await processCompletedEventForContacts(eventId);
      
      results.push({
        eventId,
        eventName,
        result
      });
    } catch (error) {
      results.push({
        eventId,
        eventName: 'Unknown Event',
        result: {
          success: false,
          emailSent: false,
          message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          eventComplete: false,
          hasGroups: false,
          alreadyAsked: false
        }
      });
    }
  }

  return results;
}
