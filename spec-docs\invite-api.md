# Invite API Documentation

## Endpoint
`GET /api/media/invite`

## Description
Generates an invitation image with a QR code. The image can be customized with various parameters and fetched for download.

## Query Parameters

### Required
- `eventId` (string): The ID of the event. Required if `inviteId` is not provided.
- `inviteId` (string): The ID of the invite. Required if `eventId` is not provided.

### Optional
- `name` (string): Name to display on the invite. Used for testing purposes.
- `format` (string): Output format of the image. Supported values: `jpeg`, `png`. Default: `jpeg`.
- `qrxp` (string): X-position of the QR code on the invite. Default: `50`.
- `qryp` (string): Y-position of the QR code on the invite. Default: `80`.
- `size` (string): Page size of the invite. Default: `A5`.
- `orientation` (string): Orientation of the invite. Supported values: `portrait`, `landscape`. Default: `portrait`.
- `labelScale` (number): Scale of the label. Default: `1`.
- `download` (boolean): Whether to download the image. Default: `false`.
- `filename` (string): Custom filename for the downloaded image.
- `bg-qr` (string): Background color of the QR code. Default: `#FFFFFF`.
- `color-qr` (string): Color of the QR code. Default: `#000000`.
- `label-orientation` (string): Orientation of the label. Supported values: `horizontal`, `vertical`. Default: `vertical`.
- `showBranding` (boolean): Whether to show branding on the label. Default: `true`.

## Responses

### Success

- **200 OK**: Returns the generated image.
  - Headers:
    - `Content-Type`: `image/jpeg` or `image/png`
    - `Cache-Control`: `public, max-age=3600`
    - `Content-Disposition`: (if `download=true`) `attachment; filename="<filename>"`

### Errors

- **400 Bad Request**: Missing required parameters or invalid input.
- **404 Not Found**: Event or invite not found.
- **405 Method Not Allowed**: Request method is not `GET`.
- **500 Internal Server Error**: Failed to generate the image.

## Example Request

```http
GET /api/media/invite?eventId=12345&name=JohnDoe&format=png&download=true
```