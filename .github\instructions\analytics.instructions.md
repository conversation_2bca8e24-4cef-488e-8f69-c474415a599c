# Analytics Tracking Instructions

This project includes robust analytics tracking to monitor user engagement and platform success.

## Key Events to Track
1. **Event Creation**:
   - `event_creation_started`: When a host starts creating an event.
   - `event_creation_completed`: When an event is successfully created.
   - `event_creation_failed`: When an event creation fails.
   - `event_creation_edited`: When a host edits an existing event.

2. **RSVP Flow**:
   - `rsvp_initiated`: When a guest clicks the RSVP button.
   - `rsvp_confirmed`: When a guest confirms their RSVP.
    - `rsvp_declined`: When a guest declines the RSVP.
    - `rsvp_qr_code_scanned`: When a guest scans the QR code for entry.
    - `rsvp_email_opened`: When a guest opens the RSVP confirmation email.
    - `rsvp_email_clicked`: When a guest clicks a link in the RSVP confirmation email.
    - `rsvp_calendar_added`: When a guest adds the event to their calendar.
    - `rsvp_calendar_event_created`: When a calendar event is created for the RSVP.

3. **Invite Management**:
   - `invite_created`: When a new invite is created.
   - `invite_shared`: When an invite is shared via link or QR code.
   - `invite_updated`: When an invite is updated.
   - `invite_deleted`: When an invite is deleted.
   - `invite_csv_uploaded`: When a CSV file is uploaded for invite creation.
   - `invite_contact_imported`: When contacts are imported for invite creation.

4. **Payment Flow**:
   - `payment_initiated`: When a payment is initiated.
   - `payment_successful`: When a payment is successfully completed.
   - `payment_failed`: When a payment fails.
   - `invoice_viewed`: When a partner views an invoice.
   - `invoice_paid`: When an invoice is marked as paid.

5. **User Engagement**:
    - `user_logged_in`: When a user logs in.
    - `user_logged_out`: When a user logs out.
    - `user_profile_updated`: When a user updates their profile.

6. **Partner Management**:
   - `partner_event_created`: When a partner creates an event.
   - `partner_event_updated`: When a partner updates an event.
   - `partner_event_deleted`: When a partner deletes an event.
   - `partner_invoice_viewed`: When a partner views an invoice.
   - `partner_invoice_paid`: When a partner marks an invoice as paid.
   - `partner_venue_created`: When a partner creates a venue.
   - `partner_venue_updated`: When a partner updates a venue.
   - `partner_venue_deleted`: When a partner deletes a venue.
   - `partner_location_created`: When a partner creates a location/area.
   - `partner_location_updated`: When a partner updates a location/area.
   - `partner_location_deleted`: When a partner deletes a location/area.
   - `partner_time_slot_created`: When a partner creates a time slot.
   - `partner_time_slot_updated`: When a partner updates a time slot.
   - `partner_time_slot_deleted`: When a partner deletes a time slot.
   - `partner_guest_scanned`: When a partner scans a guest's QR code for entry.
   - `partner_guest_entry_granted`: When a partner grants entry to a guest.
   - `partner_guest_entry_denied`: When a partner denies entry to a guest.
   - `partner_guest_entry_updated`: When a partner updates a guest's entry status.

## Tools and Services
- **Google Analytics**: For tracking user behavior.
- **Mixpanel**: For detailed event tracking.
- **QR Code Tracking**: To monitor invite engagement.

## Implementation Details
- Use UTM parameters for tracking traffic sources.
- Log key actions in the database for analysis.
