#!/bin/bash

# Remove set -e to prevent log collapsing
# set -e

BASE_PATH="$(dirname $(dirname $(realpath $0)))"

# Default environment is local
ENV=${1:-local}

echo " Generating .env file for environment: ${ENV}"
echo "---------------------------"
echo "BASE_PATH: ${BASE_PATH}"
echo "ENVIRONMENT: ${ENV}"
echo "---------------------------"

# Check if environment-specific file exists
ENV_FILE=".env.${ENV}"
if [ -f "${BASE_PATH}/${ENV_FILE}" ]; then
  echo "Using environment-specific file: ${ENV_FILE}"
  cp "${BASE_PATH}/${ENV_FILE}" "${BASE_PATH}/.env" || {
    echo "Error: Failed to copy ${ENV_FILE}"
    exit 1
  }
else
  echo "Environment file ${ENV_FILE} not found, falling back to .env.local"
  cp "${BASE_PATH}/.env.local" "${BASE_PATH}/.env" || {
    echo "Error: Failed to copy .env.local"
    exit 1
  }
fi

echo "" >> .env
echo "" >> .env

# check if secrets.env exists
if [ -f "${BASE_PATH}/secrets.env" ]; then
  echo "# Secrets variables" >> .env
  cat "${BASE_PATH}/secrets.env" >> .env || {
    echo "Error: Failed to append secrets.env"
    exit 1
  }
else
  echo "Secrets file not found. Skipping..."
fi

echo "# Build environment variables" >> .env

# Add build environment variables with error checking
{
  echo "NEXT_PUBLIC_BUILDENV_IMAGE_NAME=${IMAGE_NAME}"
  echo "NEXT_PUBLIC_BUILDENV_IMAGE_TAG=${IMAGE_TAG}"
  echo "NEXT_PUBLIC_BUILDENV_DOCKERFILE_PATH=${DOCKERFILE_PATH}"
  echo "NEXT_PUBLIC_BUILDENV_GITHUB_USERNAME=${GITHUB_USERNAME}"
  echo "NEXT_PUBLIC_BUILDENV_GITHUB_REPO=${GITHUB_REPO}"
  echo "NEXT_PUBLIC_BUILDENV_PUBLISH=${PUBLISH}"
  echo "NEXT_PUBLIC_BUILDENV_VERSION=$(git rev-parse --short HEAD)"
  echo "NEXT_PUBLIC_BUILDENV_BRANCH=$(git rev-parse --abbrev-ref HEAD)"
  echo "NEXT_PUBLIC_BUILDENV_COMMIT=$(git rev-parse HEAD)"
  echo "NEXT_PUBLIC_BUILDENV_TIMESTAMP=$(date +%s)"
  echo "NEXT_PUBLIC_BUILDENV_BUILD_ON=$(date)"
} >> .env || {
  echo "Error: Failed to append build environment variables"
  exit 1
}

echo "Environment file generation completed successfully"


