# Utility Types - IAC Universe v1

This document catalogues utility types used across the codebase for data passing, enums, constants, and configuration objects.

## Enum & Status Types

### InviteStatus
**File**: `src/types/index.ts`
**Usage**: Event invite status tracking

```typescript
type InviteStatus = "invited" | "accepted" | "declined"
```

### FeedbackStatus
**File**: `src/types/feedback.ts`
**Usage**: Feedback workflow management

```typescript
type FeedbackStatus = 'new' | 'in-triage' | 'in-progress' | 'resolved' | 'rejected';
```

### Permission (RBAC)
**File**: `src/types/session-api.ts`
**Usage**: Base permission actions

```typescript
type Permission = 'view' | 'create' | 'edit' | 'delete';
```

### OrganizationType
**File**: `src/types/session-api.ts`
**Usage**: Organization categorization

```typescript
type OrganizationType = 'partner';
```

### AdminRole
**File**: `src/types/session-api.ts`
**Usage**: System-level admin roles

```typescript
type AdminRole =
  | 'admin:super-admin'
  | 'admin:user-manager'
  | 'admin:content-moderator'
  | 'admin:analytics-viewer';
```

### PartnerRole
**File**: `src/types/session-api.ts`
**Usage**: Partner organization roles

```typescript
type PartnerRole =
  | 'partner:venue-owner'
  | 'partner:venue-manager'
  | 'partner:staff'
  | 'partner:viewer';
```

### AdminResource
**File**: `src/types/session-api.ts`
**Usage**: Admin system resources

```typescript
type AdminResource =
  | 'admin:dashboard'
  | 'admin:users'
  | 'admin:organizations'
  | 'admin:content'
  | 'admin:analytics'
  | 'admin:system'
  | 'admin:settings';
```

### PartnerResource
**File**: `src/types/session-api.ts`
**Usage**: Partner organization resources

```typescript
type PartnerResource =
  | 'partner:dashboard'
  | 'partner:venues'
  | 'partner:venue'
  | 'partner:venue:events'
  | 'partner:venue:settings'
  | 'partner:team'
  | 'partner:billing'
  | 'partner:customers'
  | 'partner:settings';
```

## Print & Media Types

### InvitePageSize
**File**: `src/types/index.ts`
**Usage**: Print page size configuration

```typescript
type InvitePageSize = 'A4' | 'A5' | 'A6' | 'photo4x6' | 'photo5x7' | 'photo6x8' | 'photo8x10';
```

### Orientation
**File**: `src/types/index.ts`
**Usage**: Print orientation

```typescript
type Orientation = 'portrait' | 'landscape';
```

### PrintSettings
**File**: `src/types/index.ts`
**Usage**: Comprehensive print configuration

```typescript
interface PrintSettings {
  inviteSettings?: {
    pageSize: InvitePageSize;
    orientation: Orientation;
    labelPosition: {
      x: number; // percentage value
      y: number; // percentage value
    };
    labelScale: number; // percentage value
  };
  labelSettings?: {
    bgColor: string; // Background color for QR label
    qrColor: string; // QR code color
    orientation: Orientation; // Label orientation
  };
}
```

### LabelPrintingOptions
**File**: `src/types/index.ts`
**Usage**: QR label printing configuration

```typescript
interface LabelPrintingOptions {
  orientation: 'portrait' | 'landscape';
  theme: {
    name: string;
    qr: string;
    background: string;
  },
  showBranding: boolean;
}
```

### LabelDimensions
**File**: `src/types/index.ts`
**Usage**: Label size and positioning

```typescript
interface LabelDimensions {
  dpi: number;
  QR: {
    width: number; // in mm
    height: number; // in mm
    x: number; // percentage value
    y: number; // percentage value
  };
  LANDSCAPE: {
    width: number; // in mm
    height: number; // in mm
  };
  PORTRAIT: {
    width: number; // in mm
    height: number; // in mm
  };
}
```

### ImageBuffer
**File**: `src/types/index.ts`
**Usage**: Image processing and storage

```typescript
interface ImageBuffer {
  fileContents: Buffer<ArrayBufferLike>;
  mimeType: string;
  dimensions: {
    width: number;
    height: number;
  };
}
```

## Event Management Utility Types

### EventManager
**File**: `src/types/index.ts`
**Usage**: Event delegation and management

```typescript
interface EventManager {
  userId?: string;
  name?: string;
  email: string;
  image?: string | null;
  role: 'owner' | 'manager' | 'invited';
}
```

### EventFormData
**File**: `src/types/index.ts`
**Usage**: Event creation and editing forms

```typescript
interface EventFormData {
  eventName: string;
  eventDate: Date;
  start: string;
  end: string;
  location: string;
  host: string;
  message?: string;
  timezone?: string;
  venueId?: string;
  locationId?: string;
  venueName?: string;
  locationName?: string;
  maxInvites?: number;
  hostEmail?: string;
  messageStyle?: 'personalised' | 'casual_friendly' | 'formal_professional' | 'fun_energetic' | 'business_professional' | 'creative_unique';
}
```

### EventListItem
**File**: `src/types/index.ts`
**Usage**: Event list display and pagination

```typescript
interface EventListItem {
  id: string;
  name: string;
  date: Date | null;
  start?: string;
  end?: string;
  location: string;
  timezone?: string;
  message?: string;
  host: string;
  ownerAccountId: string;
  ownerEmail?: string;
  guestCount: number;
  plan?: 'free' | 'host_plus' | 'host_pro';
  status?: 'pending_payment' | 'active';
  paymentStatus?: 'pending' | 'paid' | 'failed';
  isManager?: boolean;
  raw?: any;
  organizationId?: string;
}
```

### EventInviteListItem
**File**: `src/types/index.ts`
**Usage**: Invite list display and bulk operations

```typescript
interface EventInviteListItem {
  ID: string;
  eventId: string;
  name: string;
  email?: string;
  phone?: string;
  group?: string;
  status: InviteStatus;
  adults: number;
  children: number;
  hasMessage: boolean;
  createdAt?: string;
  updatedAt?: string;
  response?: {
    adults: number;
    children: number;
    timestamp: string;
    message?: string;
  } | null;
}
```

### RSVPReport
**File**: `src/types/index.ts`
**Usage**: RSVP analytics and reporting

```typescript
interface RSVPReport {
  invites: {
    total: number;
    accepted: number;
    declined: number;
  };
  adults: {
    invited: number;
    accepted: number;
    declined: number;
  };
  children: {
    invited: number;
    accepted: number;
    declined: number;
  };
  total: {
    invited: number;
    accepted: number;
    declined: number;
  };
}
```

## Contact Management Types

### CSVInvite
**File**: `src/types/index.ts`
**Usage**: CSV import/export operations

```typescript
interface CSVInvite {
  ID?: string;
  name: string;
  group?: string;
  email: string;
  phone: string;
  adults: number;
  children: number;
}
```

### ContactGroupData
**File**: `src/types/index.ts`
**Usage**: Contact group creation and management

```typescript
interface ContactGroupData {
  name: string;
  contacts: Array<{
    email: string;
    name?: string;
    phone?: string;
  }>;
}
```

### Recipient
**File**: `src/types/index.ts`
**Usage**: Email communication

```typescript
interface Recipient {
  email: string;
  name: string;
}
```

## Authentication & Session Utility Types

### UserProfile (Session API)
**File**: `src/types/session-api.ts`
**Usage**: Extended user profile metadata

```typescript
interface UserProfile {
  department?: string;
  title?: string;
  phone?: string;
  [key: string]: any; // Allow additional profile fields
}
```

### PermissionContext
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Context for permission checks

```typescript
interface PermissionContext {
  venueId?: string;
  organizationId?: string;
  userId?: string;
}
```

### OrganizationPermissionContext
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization-specific permission context

```typescript
interface OrganizationPermissionContext {
  organizationId: string;
  venueId?: string;
  userId?: string;
}
```

## Analytics & Tracking Types

### AnalyticsEvent
**File**: `src/types/index.ts`
**Usage**: Event tracking and analytics

```typescript
interface AnalyticsEvent {
  name: string;
  userId: string | null;
  userType: 'guest' | 'host';
  timestamp: Date;
  properties: Record<string, any>;
}
```

### FeedbackSessionData
**File**: `src/types/feedback.ts`
**Usage**: User session tracking with feedback

```typescript
interface FeedbackSessionData {
  sessionStartTime: number;
  submittedAt: number;
  currentPage: string;
  referrer: string;
  pagesVisited: string[];
  timeOnPage: number;
  isLoggedIn: boolean;
  userId: string | null;
  userEmail: string | null;
  userRole: 'admin' | 'user' | 'guest';
  deviceType: string;
  browser: string;
  os: string;
  userAgent: string;
  formData: {
    related: string;
    experience: string;
    feedbackType: string;
    details: string;
    email: string;
  };
}
```

## External Service Utility Types

### SL_AnalyticsLog
**File**: `src/types/shortlinks-api.ts`
**Usage**: Shortlink analytics tracking

```typescript
interface SL_AnalyticsLog {
  timestamp: string;
  ua: string;
  location: string;
  os: string;
}
```

### SL_AnalyticsStats
**File**: `src/types/shortlinks-api.ts`
**Usage**: Shortlink analytics aggregation

```typescript
interface SL_AnalyticsStats {
  hits: number;
  locations: Record<string, number>;
  devices: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  os: Record<string, number>;
  logs: SL_AnalyticsLog[];
}
```

### SL_Env
**File**: `src/types/shortlinks-api.ts`
**Usage**: Cloudflare Workers environment

```typescript
interface SL_Env {
  URL_MAP: KVNamespace;
  URL_HITS: KVNamespace;
  URL_STATS: KVNamespace;
  JWT_SECRET: string;
}
```

## Plan & Subscription Types

### Plan (from lib/plans.ts)
**Usage**: Subscription plan configuration

```typescript
interface Plan {
  id: string;
  name: string;
  description: string;
  features: string[];
  pricing: {
    monthly?: number;
    yearly?: number;
    perEvent?: number;
  };
  limits: {
    eventsPerMonth?: number;
    invitesPerEvent?: number;
    managers?: number;
    venues?: number;
  };
}
```

## Utility Function Types

### UTMParams (from lib/utm.ts)
**Usage**: Campaign tracking parameters

```typescript
interface UTMParams {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
}
```

### EnvVar (from lib/utils.ts)
**Usage**: Environment variable validation

```typescript
type EnvVar = 'NEXTAUTH_SECRET' | 'GOOGLE_CLIENT_ID' | 'GOOGLE_CLIENT_SECRET' | /* ... other env vars */;
```

## Constants and Configuration

### DATABASE_COLLECTIONS
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Database collection name constants

```typescript
const DATABASE_COLLECTIONS = {
  USER_ORGANIZATION_ROLES: 'userOrganizationRoles',
  USER_ACTIVE_ORGANIZATION: 'userActiveOrganization',
  ORGANIZATIONS: 'organizations',
  ORGANIZATION_ROLE_AUDIT: 'organizationRoleAudit',
} as const;
```

### feedbackStatusColors
**File**: `src/types/feedback.ts`
**Usage**: UI styling for feedback status

```typescript
const feedbackStatusColors: Record<FeedbackStatus, string> = {
  'new': 'bg-blue-100 text-blue-800',
  'in-triage': 'bg-yellow-100 text-yellow-800',
  'in-progress': 'bg-purple-100 text-purple-800',
  'resolved': 'bg-green-100 text-green-800',
  'rejected': 'bg-red-100 text-red-800',
};
```

## Usage Patterns

### Type Guards and Validation
Many utility types include associated type guard functions:

```typescript
// Permission checking
function hasAdminPermissions(permissions: SystemPermissions): boolean
function hasOrganizationAccess(organizations: SessionOrganization[]): boolean

// Status formatting
function formatFeedbackStatus(status: FeedbackStatus): string
```

### Configuration Objects
Utility types often serve as configuration objects:
- `PrintSettings` for print job configuration
- `LabelPrintingOptions` for QR label generation
- `PermissionContext` for access control scoping

### Data Transformation
Many utility types facilitate data transformation between layers:
- Database entities → List items for display
- Form data → Database entities for persistence
- API requests → Internal processing formats

## Migration Considerations

When migrating utility types to the new universe/packages/types system:

1. **Preserve Enums**: Status types and enums should maintain compatibility
2. **Configuration Objects**: Print and media settings should remain flexible
3. **Permission Types**: RBAC utility types form the foundation of security
4. **Form Types**: UI interaction types should support both v1 and new patterns
5. **Analytics Types**: Tracking types should be enhanced, not replaced
6. **Constants**: Migrate constants as shared configuration values