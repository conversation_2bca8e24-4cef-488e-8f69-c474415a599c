'use client';
import { SignInForm } from '@/components/SignIn';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { Footer } from '@/components/Footer';
import { isAllowedSite, type AllowedSite } from '@/lib/auth/config';
import { generateCallbackUrl, getAndClearRedirectInfo } from '@/lib/auth/redirect';

export default function SignInPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [hasProcessedRedirect, setHasProcessedRedirect] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Mark as initialized once the router is ready
    if (router.isReady && !isInitialized) {
      setIsInitialized(true);
    }
  }, [router.isReady, isInitialized]);

  useEffect(() => {
    // Only process redirect once, when fully initialized and user is authenticated
    if (status === 'authenticated' && isInitialized && !hasProcessedRedirect) {
      setHasProcessedRedirect(true);

      // Add a small delay to prevent race conditions with NextAuth's own redirect logic
      const timeoutId = setTimeout(() => {
        // Check for site parameter in query params (direct access)
        const site = router.query.site as string;
        const callbackUrl = router.query.callbackUrl as string;
        const redirectUrl = router.query.redirectUrl as string;

        // Priority 1: If site parameter exists and is valid, redirect to secure callback
        if (site && isAllowedSite(site)) {
          router.replace(generateCallbackUrl(site as AllowedSite));
          return;
        }

        // Priority 2: If there's a specific redirectUrl parameter, use it
        if (redirectUrl && redirectUrl !== '/auth/signin' && !redirectUrl.includes('/auth/callback')) {
          router.replace(redirectUrl);
          return;
        }

        // Priority 3: Check for stored redirect info from session storage
        const storedRedirectInfo = getAndClearRedirectInfo();
        if (storedRedirectInfo.site && isAllowedSite(storedRedirectInfo.site)) {
          router.replace(generateCallbackUrl(storedRedirectInfo.site));
          return;
        }

        // Priority 4: If there's a stored redirectUrl, use it
        if (storedRedirectInfo.redirectUrl && storedRedirectInfo.redirectUrl !== '/auth/signin' && !storedRedirectInfo.redirectUrl.includes('/auth/callback')) {
          router.replace(storedRedirectInfo.redirectUrl);
          return;
        }

        // Priority 5: If there's a callbackUrl with site parameter, use it (but not if it's just a generic callback)
        if (callbackUrl && callbackUrl.includes('site=') && !callbackUrl.endsWith('/auth/callback')) {
          router.replace(callbackUrl);
          return;
        }

        // Priority 6: Check for stored redirectUrl in sessionStorage (for Google sign-in from partners page)
        const storedRedirectUrl = sessionStorage.getItem('redirectUrl');
        if (storedRedirectUrl && storedRedirectUrl !== '/auth/signin' && !storedRedirectUrl.includes('/auth/callback')) {
          console.log("Using stored redirectUrl:", storedRedirectUrl);
          sessionStorage.removeItem('redirectUrl'); // Clean up
          router.replace(storedRedirectUrl);
          return;
        }

        // Priority 7: If callbackUrl is provided and it's not a generic auth callback, use it
        if (callbackUrl && !callbackUrl.includes('/auth/') && callbackUrl !== window.location.href) {
          router.replace(callbackUrl);
          return;
        }

        // Default: Redirect to events dashboard for successful authentication
        // Don't leave users on the sign-in page after they've successfully signed in
        router.replace('/events');
      }, 100); // Small delay to prevent race conditions

      return () => clearTimeout(timeoutId);
    }
  }, [session, status, router, hasProcessedRedirect, isInitialized]);

  // Reset the redirect flag when the session changes (e.g., user signs out)
  useEffect(() => {
    if (status === 'unauthenticated') {
      setHasProcessedRedirect(false);
    }
  }, [status]);

  // Prevent NextAuth's automatic redirect by intercepting navigation
  useEffect(() => {
    if (status === 'authenticated' && isInitialized) {
      // Override any automatic redirects to /auth/callback unless specifically needed
      const handleRouteChange = (url: string) => {
        if (url === '/auth/callback' && !hasProcessedRedirect) {
          router.replace('/auth/signin', undefined, { shallow: true });
          return false;
        }
      };

      router.events.on('routeChangeStart', handleRouteChange);
      return () => {
        router.events.off('routeChangeStart', handleRouteChange);
      };
    }
  }, [status, router, isInitialized, hasProcessedRedirect]);

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1 flex items-center justify-center">
        <SignInForm />
      </main>
      <Footer type="app" />
    </div>
  );
}