/**
 * ORGANIZATION-SCOPED RBAC TYPES (DATABASE IMPLEMENTATION)
 * 
 * This file contains the type definitions for supporting
 * multi-organization RBAC where users can have different roles
 * across different partner organizations using the Database class.
 */

import { Role } from './index';
import { Permission } from './permissions';

// Enhanced role structure with organization context
export interface OrganizationScopedRole {
  id: string;                        // Document ID
  userId: string;                    // User this role is assigned to
  organizationId: string;            // Organization this role applies to
  role: Role;                        // The actual role (e.g., 'partner:manager')
  assignedBy: string;                // User ID who assigned this role
  assignedAt: string;                // When the role was assigned (ISO string)
  expiresAt?: string;                // Optional expiration date (ISO string)
  isActive: boolean;                 // Whether the role is currently active
  context?: {
    venueIds?: string[];             // Specific venues user can access
    limitations?: string[];          // Any role limitations or restrictions
    metadata?: Record<string, any>;  // Additional context data
  };
}

// User's complete role structure across all organizations
export interface UserOrganizationRoles {
  userId: string;
  roles: OrganizationScopedRole[];
  currentOrganizationId?: string;    // Active organization context
  lastUpdated: string;               // ISO string
}

// Organization context for permission checks
export interface OrganizationPermissionContext {
  organizationId: string;            // Required: which organization
  venueId?: string;                  // Optional: specific venue context
  userId?: string;                   // Optional: for user-specific checks
}

// Organization details with RBAC configuration
export interface OrganizationWithRBAC {
  id: string;
  name: string;
  type: 'partner' | 'admin';
  rbacConfig: any;                   // Organization-specific RBAC configuration - contains all role/permission info
  canAccess: boolean;
  isActive: boolean;
}

// Enhanced session response with organization context
export interface OrganizationAwareSessionResponse {
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    profile?: Record<string, unknown>;
  };

  // All organizations user has access to
  organizations: OrganizationWithRBAC[];

  // Current active organization (if any)
  currentOrganization?: OrganizationWithRBAC;

  // Legacy fields for backward compatibility (deprecated)
  roles?: Role[];                    // @deprecated: Use organizations[].userRoles instead
  accountId?: string;                // @deprecated: Use currentOrganization.id instead

  token: string;
  rbac: any;                         // Global RBAC configuration
}

// Database collection documents
export interface UserOrganizationRoleDocument {
  userId: string;
  organizationId: string;
  role: string;
  assignedBy: string;
  assignedAt: string;
  expiresAt?: string;
  isActive: boolean;
  venueRestrictions?: string[];      // Array of venue IDs
  permissionsMetadata?: Record<string, any>; // Additional context
}

export interface UserActiveOrganizationDocument {
  userId: string;
  organizationId: string;
  updatedAt: string;
}

export interface OrganizationDocument {
  id: string;
  name: string;
  type: 'partner' | 'admin';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  settings?: Record<string, any>;
}

// Permission checking functions for organization context
export interface OrganizationRBACFunctions {
  hasOrganizationPermission(
    userRoles: OrganizationScopedRole[],
    resource: string,
    permission: Permission,
    context: OrganizationPermissionContext
  ): boolean;

  getUserRolesInOrganization(
    userRoles: OrganizationScopedRole[],
    organizationId: string
  ): OrganizationScopedRole[];

  canManageUserInOrganization(
    adminRole: OrganizationScopedRole,
    targetUserId: string,
    organizationId: string
  ): boolean;

  getPermissionsInOrganization(
    userRoles: OrganizationScopedRole[],
    organizationId: string
  ): Record<string, Permission[]>;
}

// API request/response types for organization management
export interface AssignOrganizationRoleRequest {
  userId: string;
  organizationId: string;
  role: Role;
  venueRestrictions?: string[];
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface SwitchOrganizationRequest {
  organizationId: string;
}

export interface OrganizationRoleAuditLog {
  id: string;
  action: 'assign' | 'revoke' | 'modify' | 'expire';
  userId: string;
  organizationId: string;
  role: Role;
  performedBy: string;
  performedAt: string;
  previousRole?: Role;
  newRole?: Role;
  reason?: string;
  metadata?: Record<string, any>;
}

// Utility types for type safety
export type PartnerOrganizationRole = OrganizationScopedRole & {
  role: `partner:${string}`;
};

export type AdminOrganizationRole = OrganizationScopedRole & {
  role: `admin:${string}`;
};

// Database collection names (constants)
export const DATABASE_COLLECTIONS = {
  USER_ORGANIZATION_ROLES: 'userOrganizationRoles',
  USER_ACTIVE_ORGANIZATION: 'userActiveOrganization',
  ORGANIZATIONS: 'organizations',
  ORGANIZATION_ROLE_AUDIT: 'organizationRoleAudit',
} as const;
