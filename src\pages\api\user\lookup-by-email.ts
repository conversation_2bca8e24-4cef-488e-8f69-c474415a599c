import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Authenticate the request
    const session = await getServerSession(req, res, authConfig);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Validate request body
    const { email } = req.body;
    if (!email || typeof email !== 'string') {
      return res.status(400).json({ error: 'Invalid request - email is required' });
    }

    // Get database instance
    const db = Database.getInstance();

    // Look up user by email
    const userProfile = await db.getUserByEmail(email);
    
    if (!userProfile) {
      return res.status(200).json({
        success: true,
        user: null
      });
    }

    // Return only the necessary information
    return res.status(200).json({
      success: true,
      user: {
        id: userProfile.id,
        email: userProfile.email,
        name: userProfile.name || undefined
      }
    });
  } catch (error) {
    console.error('Error looking up user by email:', error);
    return res.status(500).json({ error: 'Failed to look up user by email' });
  }
}