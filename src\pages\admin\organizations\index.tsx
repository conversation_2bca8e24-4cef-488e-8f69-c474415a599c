import { useEffect } from "react";
import Link from "next/link";
import { AdminLayout } from "@/components/layouts/AdminLayout";
import { useOrganizations } from "@/hooks/useOrganizations";
import { formatTimeAgo } from "@/lib/dayjs";
import { withAdminAuth } from "@/lib/auth/admin";
import { GetServerSideProps } from "next";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Skeleton
} from "@/components/ui/skeleton";
import {
  Search,
  Building2,
  ChevronLeft,
  ChevronRight,
  Shield,
  ExternalLink,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useDebounce } from "@/hooks/useDebounce";

export default function OrganizationsPage() {
  const {
    organizations,
    loading,
    error,
    searchQuery,
    pagination,
    handleSearch,
    handlePageChange
  } = useOrganizations();

  const debouncedSearch = useDebounce(handleSearch, 500);

  return (
    <AdminLayout pageTitle="Organizations">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Organizations</h1>
        <div className="relative w-64">
          <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search organizations..."
            className="pl-8"
            defaultValue={searchQuery}
            onChange={(e) => debouncedSearch(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableCaption>
            {pagination.total > 0
              ? `Showing ${(pagination.page - 1) * pagination.limit + 1} to ${Math.min(pagination.page * pagination.limit, pagination.total)} of ${pagination.total} organizations`
              : "No organizations found"}
          </TableCaption>
          <TableHeader>
            <TableRow>
              {/* Partner icon column - no header */}
              <TableHead className="w-10"></TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Members</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              // Loading state
              Array.from({ length: pagination.limit || 5 }).map((_, i) => (
                <TableRow key={`skeleton-${i}`}>
                  <TableCell><Skeleton className="h-5 w-5" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-40" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-9 w-20 ml-auto" /></TableCell>
                </TableRow>
              ))
            ) : error ? (
              // Error state
              <TableRow>
                <TableCell colSpan={6} className="text-center text-red-500">
                  Error loading organizations: {error.message}
                </TableCell>
              </TableRow>
            ) : organizations.length === 0 ? (
              // Empty state
              <TableRow>
                <TableCell colSpan={6} className="text-center text-muted-foreground h-32">
                  {searchQuery ? "No organizations found matching your search" : "No organizations found in the system"}
                </TableCell>
              </TableRow>
            ) : (
              // Data state
              organizations.map((organization) => (
                <TableRow key={organization.id}>
                  {/* Partner Shield Icon Column */}
                  <TableCell className="text-center">
                    {organization.type === 'partner' && (
                      <span title="Partner Organization">
                        <Shield className="h-4 w-4 text-blue-600 mx-auto" />
                      </span>
                    )}
                  </TableCell>

                  {/* Organization Name Column */}
                  <TableCell className="font-medium">
                    {organization.name || "Unnamed Organization"}
                  </TableCell>

                  {/* Type Column */}
                  <TableCell>
                    <Badge
                      variant={organization.type === 'partner' ? "default" : "outline"}
                      className={
                        organization.type === 'partner'
                          ? 'bg-blue-100 text-blue-800 hover:bg-blue-100'
                          : ''
                      }
                    >
                      {organization.type === 'partner' ? 'Partner' : 'Individual'}
                    </Badge>
                  </TableCell>

                  {/* Members Count Column */}
                  <TableCell>
                    {organization.members?.length || 0}
                  </TableCell>

                  {/* Created Date Column */}
                  <TableCell>
                    {formatTimeAgo(organization.createdOn)}
                  </TableCell>

                  {/* Actions Column */}
                  <TableCell className="text-right">
                    <Link href={`/admin/organizations/${organization.id}`}>
                      <Button variant="outline" size="sm">
                        <Building2 className="w-4 h-4 mr-1" />
                        Details
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {pagination.total > 0 && (
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-muted-foreground">
            Showing {(pagination.page - 1) * pagination.limit + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} organizations
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1 || loading}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous page</span>
            </Button>
            <div className="flex items-center gap-1">
              {[...Array(pagination.totalPages)].map((_, i) => {
                const pageNumber = i + 1;
                // Show only a window of pages for better UX
                if (
                  pageNumber === 1 ||
                  pageNumber === pagination.totalPages ||
                  (pageNumber >= pagination.page - 1 && pageNumber <= pagination.page + 1)
                ) {
                  return (
                    <Button
                      key={`page-${pageNumber}`}
                      variant={pageNumber === pagination.page ? "default" : "outline"}
                      size="sm"
                      className="w-9"
                      onClick={() => handlePageChange(pageNumber)}
                      disabled={loading}
                    >
                      {pageNumber}
                    </Button>
                  );
                } else if (
                  (pageNumber === pagination.page - 2 && pageNumber > 1) ||
                  (pageNumber === pagination.page + 2 && pageNumber < pagination.totalPages)
                ) {
                  // Show ellipsis for skipped pages
                  return (
                    <span key={`ellipsis-${pageNumber}`} className="px-2">
                      ...
                    </span>
                  );
                }
                return null;
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages || loading}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next page</span>
            </Button>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}

// Server-side protection for admin routes
export const getServerSideProps: GetServerSideProps = withAdminAuth();
