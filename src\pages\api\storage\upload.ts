import { NextApiRequest, NextApiResponse } from 'next';
import { IncomingForm } from 'formidable';
import { promises as fs } from 'fs';
import path from 'path';
import { FireBaseAdmin } from '@/lib/firebase';
import { compressImage, smartCompressImage, ultraCompressImage, getOptimalCompressionOptions, validateImageForCompression } from '@/lib/imageCompression';

// Disable the default body parser
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {    // Parse the form data
    const form = new IncomingForm({
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB limit per file
      maxTotalFileSize: 10 * 1024 * 1024, // 10MB limit total
    });    const [fields, files] = await new Promise<[any, any]>((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) {
          // Handle formidable-specific errors
          if (err.code === 1009 || err.message.includes('maxTotalFileSize')) {
            reject(new Error('File size exceeds the 10MB limit. Please upload a smaller image.'));
          } else if (err.code === 1015 || err.message.includes('maxFileSize')) {
            reject(new Error('File size exceeds the 10MB limit. Please upload a smaller image.'));
          } else {
            reject(err);
          }
        } else {
          resolve([fields, files]);
        }
      });
    });// Get the eventId from the request
    const eventId = Array.isArray(fields.eventId) ? fields.eventId[0] : fields.eventId;
    const imageType = Array.isArray(fields.imageType) ? fields.imageType[0] : fields.imageType;
    
    if (!eventId) {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    // Validate imageType
    const validImageTypes = ['invitation-card', 'digital-invite'];
    if (imageType && !validImageTypes.includes(imageType)) {
      return res.status(400).json({ error: 'Invalid image type. Must be invitation-card or digital-invite' });
    }

    // Get the file object
    const file = Array.isArray(files.file) ? files.file[0] : files.file;
    
    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Validate file type (only allow images)
    const validTypes = ['image/png', 'image/jpeg', 'image/jpg'];
    if (!validTypes.includes(file.mimetype)) {
      return res.status(400).json({ error: 'Invalid file type. Only PNG and JPEG are allowed.' });
    }

    // Initialize Firebase Admin
    const admin = FireBaseAdmin.getInstance();
    if (!admin) {
      throw new Error('Failed to initialize Firebase Admin');
    }

    // Get a reference to Storage with explicit bucket name
    const bucket = admin.storage().bucket('iamcoming-universe.firebasestorage.app');
    
    // Read the file
    const fileContent = await fs.readFile(file.filepath);
    
    // Validate file size before compression
    if (!validateImageForCompression(fileContent, 10)) {
      throw new Error('File size exceeds the 10MB limit. Please upload a smaller image.');
    }    // Compress the image for optimal storage using smart compression
    const compressionOptions = await getOptimalCompressionOptions(fileContent);
    const fileSizeMB = fileContent.length / (1024 * 1024);
    const fileSizeKB = fileContent.length / 1024;
    
    let compressionResult;
    if (fileSizeKB < 200) {
      // For very small files, use minimal compression to preserve quality
      const lightOptions = { ...compressionOptions, quality: 95, aggressiveCompression: false };
      compressionResult = await compressImage(fileContent, lightOptions);
    } else if (fileSizeMB > 5) {
      // Use ultra compression for very large files (>5MB)
      compressionResult = await ultraCompressImage(fileContent, 800); // Target 800KB
    } else if (fileSizeMB > 2) {
      // Use smart compression for moderately large files (>2MB)
      compressionResult = await smartCompressImage(fileContent, compressionOptions);
    } else {
      // Use regular aggressive compression for normal-sized files
      compressionResult = await compressImage(fileContent, compressionOptions);
    }

    // Use compressed image data
    const finalImageData = compressionResult.buffer;
    const finalContentType = `image/${compressionResult.format}`;
    const finalFileExtension = compressionResult.format === 'jpeg' ? 'jpg' : compressionResult.format;      // Upload to Firebase Storage
    // Default to invitation-card if no imageType specified (backward compatibility)
    const imageTypePath = imageType || 'invitation-card';
    const storagePath = `event/${eventId}/${imageTypePath}.${finalFileExtension}`;
    const fileUpload = bucket.file(storagePath);
    
    await fileUpload.save(finalImageData, {
      public: true,
      metadata: {
        contentType: finalContentType,        metadata: {
          eventId,
          imageType: imageTypePath,
          uploadedBy: req.headers['x-user-email'] || 'api-upload',
          originalSize: fileContent.length,
          compressedSize: compressionResult.size,
          compressionRatio: compressionResult.compressionRatio,
          dimensions: `${compressionResult.width}x${compressionResult.height}`,
          compressionFormat: compressionResult.format,
          processedAt: new Date().toISOString()
        }
      }
    });

    // Get the public URL
    const [url] = await fileUpload.getSignedUrl({
      action: 'read',
      expires: '03-01-2500', // Far in the future
    });

    // Clean up the temp file
    await fs.unlink(file.filepath);    // Return success response with the URL and compression info
    return res.status(200).json({ 
      success: true, 
      url,
      compressionInfo: {
        originalSize: fileContent.length,
        compressedSize: compressionResult.size,
        compressionRatio: compressionResult.compressionRatio,
        format: compressionResult.format,
        dimensions: {
          width: compressionResult.width,
          height: compressionResult.height
        }
      }
    });} catch (error) {
      console.error('Error uploading file:', error);
      
      // Handle specific error types
      let errorMessage = 'Failed to upload file';
      
      if (error instanceof Error) {
        // Check for file size error
        if (error.message.includes('maxTotalFileSize') || error.message.includes('exceeded')) {
          errorMessage = 'File size exceeds the 10MB limit. Please upload a smaller image.';
        }
        // Check for file type errors
        else if (error.message.includes('Invalid file type')) {
          errorMessage = 'Invalid file type. Only PNG and JPEG images are allowed.';
        }
        // Use the original error message if it's descriptive
        else if (error.message && error.message !== 'Upload failed') {
          errorMessage = error.message;
        }
      }
      
      return res.status(500).json({ 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined
      });
    }
}