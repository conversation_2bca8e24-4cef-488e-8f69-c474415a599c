// =============================================================================
// ADMIN PORTAL RBAC DEFINITIONS
// =============================================================================

// Import shared permissions
import { permissions, type Permission } from './permissions';

// Admin Portal Resources
export const adminResources = {
  // Root resource
  ADMIN: 'admin',

  // User management
  ADMIN_USERS: 'admin:users',
  ADMIN_USERS_ACTIVITY: 'admin:users:activity',
  ADMIN_USERS_EVENTS: 'admin:users:events',
  ADMIN_USERS_FEATURES: 'admin:users:features',

  // Organization and partner management
  ADMIN_ORGANIZATIONS: 'admin:organizations',
  ADMIN_PARTNERS: 'admin:partners',

  // System management
  ADMIN_SETTINGS: 'admin:settings',
  ADMIN_FEEDBACK: 'admin:feedback',
  ADMIN_LINKS: 'admin:links',
  ADMIN_ANALYTICS: 'admin:analytics',
  ADMIN_SECURITY: 'admin:security',
} as const;

export type AdminResource = typeof adminResources[keyof typeof adminResources];

// Admin Portal Roles
export const adminRoles = {
  SUPERADMIN: 'admin:superadmin',
  MANAGER: 'admin:manager',
  STAFF: 'admin:staff',
  ANALYST: 'admin:analyst',
  SECURITY: 'admin:security',
} as const;

export type AdminRole = typeof adminRoles[keyof typeof adminRoles];

// Permission configuration for each admin role
export const adminRolePermissions: Record<AdminRole, Record<string, Permission[]>> = {
  [adminRoles.SUPERADMIN]: {
    // Full access to all admin resources
    [adminResources.ADMIN]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_USERS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_USERS_ACTIVITY]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_USERS_EVENTS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_USERS_FEATURES]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_ORGANIZATIONS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_PARTNERS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_SETTINGS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_FEEDBACK]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_LINKS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_ANALYTICS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [adminResources.ADMIN_SECURITY]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
  },

  [adminRoles.MANAGER]: {
    // Management focus: users, organizations, partners, feedback, analytics
    [adminResources.ADMIN]: [permissions.VIEW],
    [adminResources.ADMIN_USERS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
    [adminResources.ADMIN_ORGANIZATIONS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
    [adminResources.ADMIN_PARTNERS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
    [adminResources.ADMIN_FEEDBACK]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
    [adminResources.ADMIN_ANALYTICS]: [permissions.VIEW],
  },

  [adminRoles.STAFF]: {
    // Support focus: view most resources, edit feedback
    [adminResources.ADMIN]: [permissions.VIEW],
    [adminResources.ADMIN_USERS]: [permissions.VIEW],
    [adminResources.ADMIN_ORGANIZATIONS]: [permissions.VIEW],
    [adminResources.ADMIN_FEEDBACK]: [permissions.VIEW, permissions.EDIT],
  },

  [adminRoles.ANALYST]: {
    // Analytics focus: view analytics, user activity, organizations
    [adminResources.ADMIN]: [permissions.VIEW],
    [adminResources.ADMIN_ANALYTICS]: [permissions.VIEW],
    [adminResources.ADMIN_USERS_ACTIVITY]: [permissions.VIEW],
    [adminResources.ADMIN_ORGANIZATIONS]: [permissions.VIEW],
  },

  [adminRoles.SECURITY]: {
    // Security focus: security settings, user activity, view all resources
    [adminResources.ADMIN]: [permissions.VIEW],
    [adminResources.ADMIN_USERS]: [permissions.VIEW],
    [adminResources.ADMIN_USERS_ACTIVITY]: [permissions.VIEW, permissions.EDIT],
    [adminResources.ADMIN_USERS_EVENTS]: [permissions.VIEW],
    [adminResources.ADMIN_USERS_FEATURES]: [permissions.VIEW],
    [adminResources.ADMIN_ORGANIZATIONS]: [permissions.VIEW],
    [adminResources.ADMIN_PARTNERS]: [permissions.VIEW],
    [adminResources.ADMIN_SETTINGS]: [permissions.VIEW],
    [adminResources.ADMIN_FEEDBACK]: [permissions.VIEW],
    [adminResources.ADMIN_LINKS]: [permissions.VIEW],
    [adminResources.ADMIN_ANALYTICS]: [permissions.VIEW],
    [adminResources.ADMIN_SECURITY]: [permissions.VIEW, permissions.EDIT],
  },
};

/**
 * Helper function to check if an admin role has permission for a resource
 */
export function hasAdminPermission(
  role: AdminRole,
  resource: string,
  permission: Permission
): boolean {
  const rolePermissions = adminRolePermissions[role];
  return rolePermissions[resource]?.includes(permission) || false;
}

/**
 * Get all permissions for an admin role
 */
export function getAdminRolePermissions(role: AdminRole): Record<string, Permission[]> {
  return adminRolePermissions[role] || {};
}
