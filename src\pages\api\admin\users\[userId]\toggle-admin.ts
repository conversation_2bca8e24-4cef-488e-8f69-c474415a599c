import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated and is an admin
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the current user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    // Get database instance
    const db = Database.getInstance();

    // Get user ID from the query parameters
    const { userId } = req.query;

    if (!userId || Array.isArray(userId)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // Only accept POST method for toggling admin status
    if (req.method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get the user to toggle admin status
    const user = await db.getUserById(userId);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Security check: prevent users from modifying their own admin status
    if (user.id === session.user.id) {
      return res.status(403).json({
        error: 'Forbidden - You cannot modify your own admin status'
      });
    }

    // Get the requested admin status from the request body
    const { isAdmin } = req.body;

    if (typeof isAdmin !== 'boolean') {
      return res.status(400).json({ error: 'isAdmin must be a boolean value' });
    }

    // Update the user's admin status
    await db.setUserAdminStatus(userId, isAdmin);

    // Return success response
    return res.status(200).json({
      success: true,
      message: `User admin status ${isAdmin ? 'enabled' : 'disabled'} successfully`,
      user: {
        ...user,
        isAdmin
      }
    });
  } catch (error) {
    console.error('Error toggling user admin status:', error);
    return res.status(500).json({ error: 'Failed to update admin status' });
  }
}