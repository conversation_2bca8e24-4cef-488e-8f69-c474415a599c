// filepath: /home/<USER>/kitchen-sink/iamcoming/universe/src/pages/api/auth/link-google.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { getToken } from 'next-auth/jwt';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Support both GET (for OAuth callback redirects) and POST (for client-side linking)
  if (req.method !== 'GET' && req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get the current user's token from the session
    const token = await getToken({ req, secret: process.env.AUTH_SECRET });
    if (!token) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the Google profile info from the token
    const googleId = token.sub;
    const googleEmail = token.email;
    const googleImage = token.picture as string | undefined;

    if (!googleId || !googleEmail) {
      return res.status(400).json({ error: 'Missing Google profile information' });
    }

    // Get userId based on request method
    let userId: string;
    
    if (req.method === 'GET') {
      // For GET requests, userId comes from query parameters (used in OAuth redirect)
      const queryUserId = req.query.userId;
      if (!queryUserId || Array.isArray(queryUserId)) {
        return res.status(400).json({ error: 'Invalid userId in query parameters' });
      }
      userId = queryUserId;
    } else {
      // For POST requests, userId comes from the request body (used in client-side linking)
      const { userId: bodyUserId } = req.body;
      if (!bodyUserId) {
        return res.status(400).json({ error: 'Missing userId in request body' });
      }
      userId = bodyUserId;
    }

    // Get database instance
    const db = Database.getInstance();
    
    // Check if this Google account is already linked to another user
    const isLinked = await db.isGoogleAccountLinked(googleId);
    if (isLinked) {
      // For GET requests, redirect with error
      if (req.method === 'GET') {
        return res.redirect('/account?linkError=true&message=This Google account is already linked to another user');
      }
      // For POST requests, return JSON error
      return res.status(400).json({ 
        success: false, 
        error: 'This Google account is already linked to another user' 
      });
    }

    // Check if the user exists
    const userExists = await db.userExists(userId);
    if (!userExists) {
      if (req.method === 'GET') {
        return res.redirect('/account?linkError=true&message=User not found');
      }
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    // Link the Google account
    await db.linkGoogleAccount(userId, {
      id: googleId,
      email: googleEmail,
      image: googleImage
    });

    // Return response based on request method
    if (req.method === 'GET') {
      // Redirect to the account page with a success message
      return res.redirect('/account?linkSuccess=true');
    } else {
      // Return JSON success response
      return res.status(200).json({ success: true });
    }
  } catch (error) {
    console.error('Error linking Google account:', error);
    
    // Return error response based on request method
    if (req.method === 'GET') {
      return res.redirect('/account?linkError=true');
    } else {
      return res.status(500).json({ 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      });
    }
  }
}