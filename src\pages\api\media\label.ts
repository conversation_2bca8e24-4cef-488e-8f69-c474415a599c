import { NextApiRequest, NextApiResponse } from 'next';
import crypto from 'crypto';
import { LRUCache } from 'lru-cache';
import { RenderLabel } from '@/lib/media/label';
import { StringArrayToString } from '@/lib/utils';

// Cache for generated labels with 10-minute TTL
const labelCache = new LRUCache<string, Buffer>({
  max: 100,
  ttl: 10 * 60 * 1000 // 10 minutes
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Extract query parameters
  const { 
    name, 
    'qr-content': qrContent,
    orientation = 'portrait',
    'color-qr': colorQr = '#000000',
    'bg-qr': bgQr = '#FFFFFF',
    format = 'png',
    download,
    filename
  } = req.query;

  // Validate required parameters
  if (!name || typeof name !== 'string') {
    return res.status(400).json({ error: 'Missing required parameter: name' });
  }
  
  if (!qrContent || typeof qrContent !== 'string') {
    return res.status(400).json({ error: 'Missing required parameter: qr-content' });
  }
  
  // Validate orientation
  const validOrientation = typeof orientation === 'string' && 
    (orientation === 'portrait' || orientation === 'landscape');
  if (!validOrientation) {
    return res.status(400).json({ error: 'Invalid orientation: must be either "portrait" or "landscape"' });
  }
  
  // Validate format
  const formatStr = typeof format === 'string' ? format.toLowerCase() : 'png';
  const supportedFormats = ['jpg', 'jpeg', 'png'];
  if (!supportedFormats.includes(formatStr)) {
    return res.status(400).json({
      error: `Unsupported format: ${format}. Supported formats are: ${supportedFormats.join(', ')}`
    });
  }

  // Check if download is requested
  const shouldDownload = download === 'true' || download === '1' || download === 'yes';

  try {
    // Generate cache key from all parameters
    const cacheKey = JSON.stringify({
      name,
      qrContent,
      orientation,
      colorQr,
      bgQr,
      format
    });
    const cacheHash = crypto.createHash('md5').update(cacheKey).digest('hex');

    const isLocalHost = req.headers.host?.includes('localhost') || false;

    if (isLocalHost) {
      console.warn('Running on localhost, cache disabled');
      labelCache.clear(); // Clear cache for local development
    }

    // Check if result is cached
    if (!isLocalHost && labelCache.has(cacheHash)) {
      console.log(`Label found in cache: ${cacheHash}`);
      const cachedLabel = labelCache.get(cacheHash);

      if (!cachedLabel) {
        throw new Error('Cache item exists but returned null');
      }

      // Determine the content type based on format
      const contentType = `image/${formatStr === 'jpg' ? 'jpeg' : formatStr}`;
      
      // Set the filename for download if requested
      const finalFilename = shouldDownload ? 
        (filename ? `${filename}.${formatStr}` : `label-${cacheHash}.${formatStr}`) : 
        null;

      // Set response headers
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'public, max-age=600'); // 10 minute cache
      
      if (shouldDownload && finalFilename) {
        res.setHeader('Content-Disposition', `attachment; filename="${finalFilename}"`);
      }
      
      return res.send(cachedLabel);
    }

    // Generate label image using RenderLabel
    const labelBuffer = await RenderLabel(name, qrContent, {
      orientation: orientation as 'portrait' | 'landscape',
      theme: {
        qr: StringArrayToString(colorQr),
        name: StringArrayToString(colorQr),
        background: StringArrayToString(bgQr),
      },
      showBranding: true, // Assuming branding is always shown
    });

    // Cache the result
    labelCache.set(cacheHash, labelBuffer.fileContents);

    // Set the filename for download if requested
    const finalFilename = shouldDownload ? 
      (filename ? `${filename}.${formatStr}` : `label-${cacheHash}.${formatStr}`) : 
      null;

    // Set response headers
    res.setHeader('Content-Type', labelBuffer.mimeType);
    res.setHeader('Content-Length', labelBuffer.fileContents.length);
    res.setHeader('Cache-Control', 'public, max-age=600'); // 10 minute cache
    
    if (shouldDownload && finalFilename) {
      res.setHeader('Content-Disposition', `attachment; filename="${finalFilename}"`);
    }
    
    return res.send(labelBuffer.fileContents);
  } catch (err) {
    console.error('Error generating label:', err);
    return res.status(500).json({
      error: 'Failed to generate label', 
      details: err instanceof Error ? err.message : String(err)
    });
  }
}