import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { SavedContactGroup } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { ProtectedLayout } from '@/components/layouts/ProtectedLayout';
import { Header } from '@/components/Header';
import { Trash2, Edit, Users, Search, MoreVertical, Mail, Phone, Clock, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ContactGroupsPageProps {
  organization: any;
  initialContactGroups: SavedContactGroup[];
}

export default function ContactGroupsPage({
  organization,
  initialContactGroups
}: ContactGroupsPageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [contactGroups, setContactGroups] = useState<SavedContactGroup[]>(initialContactGroups);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [openAccordionItems, setOpenAccordionItems] = useState<string[]>([]);
  const [hasInitializedAccordion, setHasInitializedAccordion] = useState(false);
  const [editingContact, setEditingContact] = useState<{groupId: string, contactIndex: number, contact: any} | null>(null);

  // Open all accordion items by default on initial load
  useEffect(() => {
    // Only initialize accordion items if we haven't done so yet and there are groups
    if (contactGroups.length > 0 && !hasInitializedAccordion) {
      // Filter out any undefined groups and ensure each group has an id
      const validGroupIds = contactGroups
        .filter(group => group && group.id)
        .map(group => group.id);
      
      setOpenAccordionItems(validGroupIds);
      setHasInitializedAccordion(true);
    }
  }, [contactGroups, hasInitializedAccordion]);
  const [isRemovingContact, setIsRemovingContact] = useState(false);
  const [isUpdatingContact, setIsUpdatingContact] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState<{[groupId: string]: number[]}>({});
  const [isBulkRemoving, setIsBulkRemoving] = useState(false);
  const [openDropdowns, setOpenDropdowns] = useState<{[key: string]: boolean}>({});
  const [addingContactToGroup, setAddingContactToGroup] = useState<string | null>(null);
  const [isAddingContact, setIsAddingContact] = useState(false);
  const [createGroupDialogOpen, setCreateGroupDialogOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState<{id: string, name: string} | null>(null);
  const [isCreatingGroup, setIsCreatingGroup] = useState(false);

  // Cleanup dropdowns on component unmount or route change
  useEffect(() => {
    return () => {
      closeAllDropdowns();
    };
  }, []);

  const filteredGroups = contactGroups.filter(group =>
    group && group.name && group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (group && group.description && group.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Selection handlers
  const toggleGroup = (groupId: string) => {
    console.log('toggleGroup called with groupId:', groupId);
    console.log('Current selectedGroups:', selectedGroups);
    setSelectedGroups(prev => {
      const newSelection = prev.includes(groupId)
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId];
      console.log('New selectedGroups will be:', newSelection);
      return newSelection;
    });
  };

  const isAllSelected = () => {
    return filteredGroups.length > 0 && selectedGroups.length === filteredGroups.length;
  };

  const isSomeSelected = () => {
    return selectedGroups.length > 0 && selectedGroups.length < filteredGroups.length;
  };

  const toggleAllGroups = () => {
    if (isAllSelected()) {
      // Deselect all groups and all contacts
      setSelectedGroups([]);
      setSelectedContacts({});
    } else {
      // Select all groups and all their contacts
      // Filter out any undefined groups or groups without an id
      const validGroups = filteredGroups.filter(group => group && group.id);
      
      setSelectedGroups(validGroups.map(group => group.id));
      
      const allContactSelections: {[groupId: string]: number[]} = {};
      validGroups.forEach(group => {
        if (group && group.id && Array.isArray(group.contacts)) {
          allContactSelections[group.id] = group.contacts.map((_, index) => index);
        }
      });
      
      setSelectedContacts(allContactSelections);
    }
  };

  // Contact selection handlers
  const toggleContactSelection = (groupId: string, contactIndex: number) => {
    setSelectedContacts(prev => {
      const groupSelections = prev[groupId] || [];
      const isSelected = groupSelections.includes(contactIndex);

      let newGroupSelections;
      if (isSelected) {
        newGroupSelections = groupSelections.filter(index => index !== contactIndex);
      } else {
        newGroupSelections = [...groupSelections, contactIndex];
      }

      const newSelectedContacts = {
        ...prev,
        [groupId]: newGroupSelections
      };

      // Update group selection based on contact selection
      const group = contactGroups.find(g => g.id === groupId);
      if (group) {
        const allContactsSelected = newGroupSelections.length === group.contacts.length;
        const noContactsSelected = newGroupSelections.length === 0;

        setSelectedGroups(prevGroups => {
          if (allContactsSelected && !prevGroups.includes(groupId)) {
            // All contacts selected, select the group
            return [...prevGroups, groupId];
          } else if (!allContactsSelected && prevGroups.includes(groupId)) {
            // Not all contacts selected, deselect the group
            return prevGroups.filter(id => id !== groupId);
          }
          return prevGroups;
        });
      }

      return newSelectedContacts;
    });
  };

  const isContactSelected = (groupId: string, contactIndex: number) => {
    return selectedContacts[groupId]?.includes(contactIndex) || false;
  };

  const isGroupPartiallySelected = (groupId: string) => {
    const group = contactGroups.find(g => g.id === groupId);
    if (!group) return false;

    const selectedContactsInGroup = selectedContacts[groupId]?.length || 0;
    return selectedContactsInGroup > 0 && selectedContactsInGroup < group.contacts.length;
  };

  const getSelectedContactsCount = () => {
    return Object.values(selectedContacts).reduce((total, contacts) => total + contacts.length, 0);
  };

  const clearContactSelections = () => {
    setSelectedContacts({});
    setSelectedGroups([]);
  };

  // Dropdown control functions
  const closeDropdown = (dropdownKey: string) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [dropdownKey]: false
    }));
  };

  const closeAllDropdowns = () => {
    setOpenDropdowns({});
  };

  const handleDeleteGroup = async (groupId: string, groupName: string) => {
    // Close all dropdowns first
    closeAllDropdowns();

    if (!confirm(`Are you sure you want to delete the contact group "${groupName}"? This action cannot be undone.`)) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/organizations/${organization.id}/contact-groups/${groupId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete contact group');
      }

      // Remove the group from the local state
      setContactGroups(prev => prev.filter(group => group.id !== groupId));

      toast({
        title: "Contact group deleted",
        description: `"${groupName}" has been deleted successfully.`
      });

      // Ensure dropdowns are closed after successful deletion
      closeAllDropdowns();

    } catch (error) {
      console.error('Error deleting contact group:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete contact group",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };



  // Handle removing individual contact from group
  const handleRemoveContact = async (groupId: string, contactIndex: number) => {
    const group = contactGroups.find(g => g.id === groupId);
    if (!group) return;

    const contact = group.contacts[contactIndex];
    const contactName = contact.name || contact.email || contact.phone || 'Unknown Contact';

    // Close all dropdowns first
    closeAllDropdowns();

    if (!confirm(`Are you sure you want to remove "${contactName}" from the group "${group.name}"?`)) {
      return;
    }

    setIsRemovingContact(true);

    try {
      const response = await fetch(`/api/organizations/${organization.id}/contact-groups/${groupId}/contacts/${contactIndex}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove contact');
      }

      // Update local state - remove contact from group
      setContactGroups(prev => prev.map(g => {
        if (g.id === groupId) {
          const updatedContacts = [...g.contacts];
          updatedContacts.splice(contactIndex, 1);
          return { ...g, contacts: updatedContacts };
        }
        return g;
      }));

      toast({
        title: "Contact removed",
        description: `"${contactName}" has been removed from "${group.name}".`
      });

    } catch (error) {
      console.error('Error removing contact:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove contact",
        variant: "destructive"
      });
    } finally {
      setIsRemovingContact(false);
    }
  };

  // Handle editing individual contact
  const handleEditContact = (groupId: string, contactIndex: number) => {
    const group = contactGroups.find(g => g.id === groupId);
    if (!group) return;

    // Close all dropdowns first
    closeAllDropdowns();

    setEditingContact({
      groupId,
      contactIndex,
      contact: { ...group.contacts[contactIndex] }
    });
  };

  // Handle updating contact
  const handleUpdateContact = async () => {
    if (!editingContact) return;

    setIsUpdatingContact(true);

    try {
      const response = await fetch(`/api/organizations/${organization.id}/contact-groups/${editingContact.groupId}/contacts/${editingContact.contactIndex}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: editingContact.contact.name || '',
          email: editingContact.contact.email || '',
          phone: editingContact.contact.phone || ''
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update contact');
      }

      const data = await response.json();

      // Update local state
      setContactGroups(prev => prev.map(g => {
        if (g.id === editingContact.groupId) {
          const updatedContacts = [...g.contacts];
          updatedContacts[editingContact.contactIndex] = data.updatedContact;
          return { ...g, contacts: updatedContacts };
        }
        return g;
      }));

      toast({
        title: "Contact updated",
        description: "Contact information has been updated successfully."
      });

      setEditingContact(null);

    } catch (error) {
      console.error('Error updating contact:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update contact",
        variant: "destructive"
      });
    } finally {
      setIsUpdatingContact(false);
    }
  };

  // Handle bulk remove contacts
  const handleBulkRemoveContacts = async () => {
    const selectedCount = getSelectedContactsCount();
    if (selectedCount === 0) return;

    if (!confirm(`Are you sure you want to remove ${selectedCount} selected contact(s) from their groups? This action cannot be undone.`)) {
      return;
    }

    setIsBulkRemoving(true);

    try {
      const promises = [];

      // Process each group's selected contacts
      for (const [groupId, contactIndices] of Object.entries(selectedContacts)) {
        if (contactIndices.length === 0) continue;

        // Sort indices in descending order to remove from the end first
        // This prevents index shifting issues
        const sortedIndices = [...contactIndices].sort((a, b) => b - a);

        for (const contactIndex of sortedIndices) {
          promises.push(
            fetch(`/api/organizations/${organization.id}/contact-groups/${groupId}/contacts/${contactIndex}`, {
              method: 'DELETE'
            })
          );
        }
      }

      const responses = await Promise.all(promises);

      // Check if all requests were successful
      const failedRequests = responses.filter(response => !response.ok);
      if (failedRequests.length > 0) {
        throw new Error(`Failed to remove ${failedRequests.length} contact(s)`);
      }

      // Update local state by removing selected contacts
      setContactGroups(prev => prev.map(group => {
        const contactIndices = selectedContacts[group.id];
        if (!contactIndices || contactIndices.length === 0) return group;

        // Remove contacts in descending order to avoid index shifting
        const updatedContacts = [...group.contacts];
        const sortedIndices = [...contactIndices].sort((a, b) => b - a);

        for (const index of sortedIndices) {
          updatedContacts.splice(index, 1);
        }

        return { ...group, contacts: updatedContacts };
      }));

      // Clear selections
      clearContactSelections();

      toast({
        title: "Contacts removed",
        description: `Successfully removed ${selectedCount} contact(s) from their groups.`
      });

    } catch (error) {
      console.error('Error removing contacts:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove some contacts",
        variant: "destructive"
      });
    } finally {
      setIsBulkRemoving(false);
    }
  };

  // Handle adding contact to group
  const handleAddContact = (groupId: string) => {
    closeAllDropdowns();
    setAddingContactToGroup(groupId);
  };

  // Handle creating new group
  const handleCreateGroup = async (groupName: string) => {
    if (!groupName.trim()) {
      toast({
        title: "Validation Error",
        description: "Group name is required.",
        variant: "destructive"
      });
      return;
    }

    setIsCreatingGroup(true);
    try {
      const response = await fetch(`/api/organizations/${organization.id}/contact-groups`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: groupName.trim(),
          contacts: []
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        toast({
          title: "Unable to create group",
          description: errorData.error || 'Failed to create group',
          variant: "destructive"
        });
        return;
      }

      // Get the newly created group from the response
      const newGroup = await response.json();
      
      // Update the state with the new group, ensuring we have valid data
      if (newGroup && newGroup.contactGroup) {
        const newContactGroup = newGroup.contactGroup;
        setContactGroups(prev => [...prev, newContactGroup]);
        
        // Add the new group's ID to openAccordionItems to ensure it's open by default
        if (newContactGroup.id) {
          setOpenAccordionItems(prev => [...prev, newContactGroup.id]);
        }
      } else {
        console.error('Invalid response format when creating group:', newGroup);
        // Fallback: create a valid group object that matches SavedContactGroup interface
        const fallbackGroup: SavedContactGroup = {
          id: Date.now().toString(), // Temporary ID
          organizationId: organization.id,
          name: groupName.trim(),
          contacts: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdFromEventId: 'manual' // Indicate this was manually created
        };
        setContactGroups(prev => [...prev, fallbackGroup]);
        
        // Add the fallback group's ID to openAccordionItems
        setOpenAccordionItems(prev => [...prev, fallbackGroup.id]);
      }
      
      toast({
        title: "Group created",
        description: `"${groupName}" has been created successfully.`
      });

      setCreateGroupDialogOpen(false);

    } catch (error) {
      console.error('Unexpected error creating group:', error);
      toast({
        title: "Unexpected Error",
        description: "An unexpected error occurred while creating the group. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingGroup(false);
    }
  };

  // Handle editing group
  const handleEditGroup = (groupId: string, currentName: string) => {
    closeAllDropdowns();
    setEditingGroup({ id: groupId, name: currentName });
  };

  // Handle updating group name
  const handleUpdateGroup = async (groupName: string) => {
    if (!editingGroup || !groupName.trim()) {
      toast({
        title: "Validation Error",
        description: "Group name is required.",
        variant: "destructive"
      });
      return;
    }

    setIsCreatingGroup(true);
    try {
      const response = await fetch(`/api/organizations/${organization.id}/contact-groups/${editingGroup.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: groupName.trim()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        toast({
          title: "Unable to update group",
          description: errorData.error || 'Failed to update group',
          variant: "destructive"
        });
        return;
      }

      // Get the updated group from the response
      const updatedGroupData = await response.json();
      
      // Update the state with the updated group
      setContactGroups(prev => prev.map(group => {
        // Skip undefined groups
        if (!group) return group;
        
        return group.id === editingGroup.id 
          ? { ...group, name: groupName.trim() } 
          : group;
      }));
      
      toast({
        title: "Group updated",
        description: `Group name has been updated to "${groupName}".`
      });

      setEditingGroup(null);

    } catch (error) {
      console.error('Unexpected error updating group:', error);
      toast({
        title: "Unexpected Error",
        description: "An unexpected error occurred while updating the group. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingGroup(false);
    }
  };

  // Handle saving new contact
  const handleSaveNewContact = async (contactData: {name: string, email: string, phone: string}) => {
    if (!addingContactToGroup) return;

    setIsAddingContact(true);

    try {
      const response = await fetch(`/api/organizations/${organization.id}/contact-groups/${addingContactToGroup}/contacts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(contactData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        // Handle specific error cases with user-friendly messages
        const errorMessage = errorData.error || 'Failed to add contact';

        // Show user-friendly toast instead of throwing error
        toast({
          title: "Unable to add contact",
          description: errorMessage,
          variant: "destructive"
        });

        // Don't close the dialog on error so user can fix the issue
        return; // Exit early instead of throwing
      }

      const data = await response.json();

      // Update local state
      setContactGroups(prev => prev.map(g => {
        if (g.id === addingContactToGroup) {
          return { ...g, contacts: [...g.contacts, data.newContact] };
        }
        return g;
      }));

      toast({
        title: "Contact added",
        description: "Contact has been added to the group successfully."
      });

      setAddingContactToGroup(null);
      closeAllDropdowns(); // Ensure dropdowns are closed after successful addition

    } catch (error) {
      console.error('Unexpected error adding contact:', error);
      toast({
        title: "Unexpected Error",
        description: "An unexpected error occurred while adding the contact. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsAddingContact(false);
    }
  };

  // Handle bulk delete groups
  const handleBulkDeleteGroups = async () => {
    const selectedCount = selectedGroups.length;
    if (selectedCount === 0) return;

    const groupNames = selectedGroups.map(groupId => {
      const group = contactGroups.find(g => g.id === groupId);
      return group ? group.name : 'Unknown Group';
    }).join(', ');

    if (!confirm(`Are you sure you want to delete ${selectedCount} selected group(s): ${groupNames}? This action cannot be undone.`)) {
      return;
    }

    setIsLoading(true);

    try {
      const promises = selectedGroups.map(groupId =>
        fetch(`/api/organizations/${organization.id}/contact-groups/${groupId}`, {
          method: 'DELETE'
        })
      );

      const responses = await Promise.all(promises);

      // Check if all requests were successful
      const failedRequests = responses.filter(response => !response.ok);
      if (failedRequests.length > 0) {
        throw new Error(`Failed to delete ${failedRequests.length} group(s)`);
      }

      // Update local state by removing selected groups
      setContactGroups(prev => prev.filter(group => !selectedGroups.includes(group.id)));

      // Clear selections
      setSelectedGroups([]);
      setSelectedContacts({});

      toast({
        title: "Groups deleted",
        description: `Successfully deleted ${selectedCount} group(s).`
      });

    } catch (error) {
      console.error('Error deleting groups:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete some groups",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProtectedLayout>
      <div className="flex flex-col bg-gray-50">
        <Header
          title="Saved Contact Groups"
          breadcrumbs={[
            { label: organization.name, href: '#' }
          ]}
        />

        {/* Main Content */}
        <div className="flex-1 p-4 pb-20">
          <div className="container mx-auto">
            <Card>
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <CardTitle className="mb-3 sm:mb-0">Contact Groups</CardTitle>
                  <div className="flex w-full sm:w-auto gap-2">
                    <Button
                      variant="primary-button"
                      className="flex-1 sm:flex-none"
                      onClick={() => setCreateGroupDialogOpen(true)}
                    >
                      <Plus className="h-4 w-4" />
                      <span className="sm:hidden">New Group</span>
                      <span className="hidden sm:inline">Create New Group</span>
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Search Bar */}
                <div className="mb-4">
                  <Input
                    type="text"
                    value={searchQuery}
                    placeholder="Search contact groups..."
                    className="w-full"
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                {/* Results Count with Checkbox */}
                <div className="mb-4 px-6 py-4 border-t -mx-6" style={{backgroundColor: '#F9FAFB', borderColor: '#E2E8F0'}}>
                  {/* Desktop Layout */}
                  <div className="hidden md:flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={isAllSelected()}
                        ref={(el) => {
                          if (el && isSomeSelected()) {
                            const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                            if (checkbox) {
                              checkbox.indeterminate = true;
                            }
                          }
                        }}
                        onCheckedChange={(checked) => {
                          console.log('Select all checkbox clicked, checked:', checked);
                          toggleAllGroups();
                        }}
                        className="border-black"
                      />
                      <span className="text-base font-semibold text-foreground ml-2">
                        Showing {filteredGroups.length} of {contactGroups.length} groups
                        {(selectedGroups.length > 0 || getSelectedContactsCount() > 0) && (
                          <span className="ml-2 font-medium text-blue-600">
                            ({getSelectedContactsCount()} selected)
                          </span>
                        )}
                      </span>
                    </div>

                    {/* Action buttons - show when groups or contacts are selected */}
                    {(selectedGroups.length > 0 || getSelectedContactsCount() > 0) && (
                      <div className="flex items-center gap-2">
                        {selectedGroups.length > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 text-sm font-normal"
                            onClick={handleBulkDeleteGroups}
                            disabled={isLoading}
                          >
                            <Trash2 className="h-4 w-4" />
                            Delete Group
                          </Button>
                        )}

                        {getSelectedContactsCount() > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 text-sm font-normal"
                            onClick={handleBulkRemoveContacts}
                            disabled={isBulkRemoving}
                          >
                            <Trash2 className="h-4 w-4" />
                            {isBulkRemoving ? 'Removing...' : 'Remove Contact'}
                          </Button>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Mobile Layout - Stacked */}
                  <div className="md:hidden space-y-3">
                    {/* Summary text with checkbox */}
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={isAllSelected()}
                        ref={(el) => {
                          if (el && isSomeSelected()) {
                            const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                            if (checkbox) {
                              checkbox.indeterminate = true;
                            }
                          }
                        }}
                        onCheckedChange={(checked) => {
                          console.log('Select all checkbox clicked, checked:', checked);
                          toggleAllGroups();
                        }}
                        className="border-black"
                      />
                      <span className="text-base font-semibold text-foreground ml-2">
                        Showing {filteredGroups.length} of {contactGroups.length} groups
                        {(selectedGroups.length > 0 || getSelectedContactsCount() > 0) && (
                          <span className="block mt-1 font-medium text-blue-600 text-sm">
                            ({getSelectedContactsCount()} selected)
                          </span>
                        )}
                      </span>
                    </div>

                    {/* Action buttons - stacked below on mobile */}
                    {(selectedGroups.length > 0 || getSelectedContactsCount() > 0) && (
                      <div className="flex flex-col sm:flex-row gap-2">
                        {selectedGroups.length > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 text-sm font-normal w-full sm:w-auto"
                            onClick={handleBulkDeleteGroups}
                            disabled={isLoading}
                          >
                            <Trash2 className="h-4 w-4" />
                            Delete Group
                          </Button>
                        )}

                        {getSelectedContactsCount() > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 text-sm font-normal w-full sm:w-auto"
                            onClick={handleBulkRemoveContacts}
                            disabled={isBulkRemoving}
                          >
                            <Trash2 className="h-4 w-4" />
                            {isBulkRemoving ? 'Removing...' : 'Remove Contact'}
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Contact Groups List */}
                <ScrollArea className="h-[calc(100vh-200px)]">
                  <div className="border border-gray-200 rounded-lg bg-white">
                    {isLoading ? (
                      <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                        <p className="text-muted-foreground">Loading...</p>
                      </div>
                    ) : filteredGroups.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
                        <Users className="h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          {searchQuery ? 'No groups found' : 'No saved contact groups'}
                        </h3>
                        <p className="text-muted-foreground text-sm">
                          {searchQuery
                            ? 'Try adjusting your search terms.'
                            : 'Create your first contact group to get started.'
                          }
                        </p>
                      </div>
                    ) : (
                      <Accordion type="multiple" className="w-full" value={openAccordionItems} onValueChange={setOpenAccordionItems}>
                        {filteredGroups.map((group, index) => {
                          const isLastGroup = index === filteredGroups.length - 1;

                          return (
                            <AccordionItem key={group.id} value={group.id} className={cn("border-b-0", !isLastGroup && "border-b border-gray-200")}>
                              <div className="flex items-center py-3 px-4 border-b border-gray-200">
                                <div className="flex items-center space-x-3 flex-1">
                                  <Checkbox
                                    checked={selectedGroups.includes(group.id)}
                                    ref={(el) => {
                                      if (el && isGroupPartiallySelected(group.id)) {
                                        const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                                        if (checkbox) {
                                          checkbox.indeterminate = true;
                                        }
                                      }
                                    }}
                                    onCheckedChange={(checked) => {
                                      console.log('Checkbox onCheckedChange called for group:', group.id, 'checked:', checked);
                                      console.log('Current checked state:', selectedGroups.includes(group.id));
                                      if (checked) {
                                        // Select the group
                                        setSelectedGroups(prev => [...prev, group.id]);
                                        // Also select all contacts in this group
                                        setSelectedContacts(prev => ({
                                          ...prev,
                                          [group.id]: group.contacts.map((_, index) => index)
                                        }));
                                      } else {
                                        // Deselect the group
                                        setSelectedGroups(prev => prev.filter(id => id !== group.id));
                                        // Also deselect all contacts in this group
                                        setSelectedContacts(prev => {
                                          const newSelected = { ...prev };
                                          delete newSelected[group.id];
                                          return newSelected;
                                        });
                                      }
                                    }}
                                    onClick={(e) => {
                                      console.log('Checkbox onClick called for group:', group.id);
                                      e.stopPropagation();
                                    }}
                                    className="shrink-0 border-black"
                                  />
                                  <span className="text-base font-medium text-muted-foreground text-left flex-1">
                                    {group.name}
                                  </span>
                                  <Badge variant="secondary" className="ml-2">
                                    {group.contacts.length} contacts
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-2">
                                  <AccordionTrigger className="hover:no-underline p-2 border-0 rounded-full transition-colors" style={{backgroundColor: '#F8FAFC'}}>
                                  </AccordionTrigger>
                                  <DropdownMenu
                                    open={openDropdowns[`group-${group.id}`] || false}
                                    onOpenChange={(open) => setOpenDropdowns(prev => ({
                                      ...prev,
                                      [`group-${group.id}`]: open
                                    }))}
                                  >
                                    <DropdownMenuTrigger asChild>
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                        }}
                                        className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                                        style={{backgroundColor: '#F8FAFC'}}
                                      >
                                        <MoreVertical className="h-4 w-4 text-muted-foreground" />
                                      </button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="w-56">
                                      <DropdownMenuLabel>Group Actions ({group.name})</DropdownMenuLabel>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleAddContact(group.id);
                                        }}
                                      >
                                        <Plus className="mr-2 h-4 w-4" />
                                        Add Contact
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleEditGroup(group.id, group.name);
                                        }}
                                      >
                                        <Edit className="mr-2 h-4 w-4" />
                                        Edit Group
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDeleteGroup(group.id, group.name);
                                        }}
                                        className="text-red-600 focus:text-red-600"
                                      >
                                        <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                                        Delete Group
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>
                              <AccordionContent className="pb-0">
                                <div className="space-y-0 ml-4">
                                  {group.contacts.length === 0 ? (
                                    <div className="py-8 px-4 text-center text-muted-foreground">
                                      <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                                      <p className="text-sm mb-3">No contacts in this group yet</p>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleAddContact(group.id)}
                                      >
                                        <Plus className="h-4 w-4 mr-1" />
                                        Add First Contact
                                      </Button>
                                    </div>
                                  ) : (
                                    group.contacts.map((contact, contactIndex) => (
                                    <div key={contactIndex} className="py-4 px-4 hover:bg-gray-50 -mx-4">
                                      {/* Desktop Layout */}
                                      <div className="hidden md:flex items-center space-x-3">
                                        <div className="flex items-center justify-between w-full pr-4">
                                          <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                              <Checkbox
                                                checked={isContactSelected(group.id, contactIndex)}
                                                onCheckedChange={(checked) => {
                                                  console.log('Contact checkbox clicked for group:', group.id, 'contact:', contactIndex, 'checked:', checked);
                                                  toggleContactSelection(group.id, contactIndex);
                                                }}
                                                onClick={(e) => e.stopPropagation()}
                                                className="shrink-0 border-black"
                                              />
                                              <h3 className="font-semibold text-base text-gray-900 ml-1">
                                                {contact.name || contact.email || contact.phone || 'Unknown Contact'}
                                              </h3>
                                            </div>
                                            <div className="flex items-center gap-4 text-sm text-muted-foreground ml-7">
                                              {contact.email && (
                                                <div className="flex items-center">
                                                  <Mail className="h-4 w-4 mr-1" />
                                                  <span>{contact.email}</span>
                                                </div>
                                              )}
                                              {contact.phone && (
                                                <div className="flex items-center">
                                                  <Phone className="h-4 w-4 mr-1" />
                                                  <span>{contact.phone}</span>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                          <div className="flex items-center gap-4">
                                            <div className="flex items-center gap-1">
                                              {contact.phone && (
                                                <div className="w-6 h-6 rounded-full bg-yellow-50 border border-yellow-200 flex items-center justify-center">
                                                  <Phone className="h-3 w-3 text-yellow-600" />
                                                </div>
                                              )}
                                              {contact.email && (
                                                <div className="w-6 h-6 rounded-full bg-green-50 border border-green-200 flex items-center justify-center">
                                                  <Mail className="h-3 w-3 text-green-600" />
                                                </div>
                                              )}
                                            </div>

                                            {/* Contact Actions Dropdown */}
                                            <DropdownMenu
                                              open={openDropdowns[`contact-${group.id}-${contactIndex}`] || false}
                                              onOpenChange={(open) => setOpenDropdowns(prev => ({
                                                ...prev,
                                                [`contact-${group.id}-${contactIndex}`]: open
                                              }))}
                                            >
                                              <DropdownMenuTrigger asChild>
                                                <Button
                                                  variant="ghost"
                                                  size="sm"
                                                  className="h-8 w-8 p-0 hover:bg-gray-100"
                                                  onClick={(e) => e.stopPropagation()}
                                                >
                                                  <MoreVertical className="h-4 w-4 text-muted-foreground" />
                                                </Button>
                                              </DropdownMenuTrigger>
                                              <DropdownMenuContent align="end" className="w-48">
                                                <DropdownMenuLabel>Contact Actions</DropdownMenuLabel>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleEditContact(group.id, contactIndex);
                                                  }}
                                                >
                                                  <Edit className="mr-2 h-4 w-4" />
                                                  Edit Contact
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleRemoveContact(group.id, contactIndex);
                                                  }}
                                                  className="text-red-600 focus:text-red-600"
                                                  disabled={isRemovingContact}
                                                >
                                                  <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                                                  Remove from Group
                                                </DropdownMenuItem>
                                              </DropdownMenuContent>
                                            </DropdownMenu>
                                          </div>
                                        </div>
                                      </div>

                                      {/* Mobile Layout */}
                                      <div className="md:hidden">
                                        <div className="flex items-start justify-between pr-4">
                                          <div className="flex items-start gap-3 flex-1">
                                            <Checkbox
                                              checked={isContactSelected(group.id, contactIndex)}
                                              onCheckedChange={(checked) => {
                                                console.log('Mobile contact checkbox clicked for group:', group.id, 'contact:', contactIndex, 'checked:', checked);
                                                toggleContactSelection(group.id, contactIndex);
                                              }}
                                              onClick={(e) => e.stopPropagation()}
                                              className="shrink-0 border-black mt-1"
                                            />
                                            <div className="flex-1 min-w-0">
                                              <h3 className="font-semibold text-base text-gray-900 mb-1">
                                                {contact.name || contact.email || contact.phone || 'Unknown Contact'}
                                              </h3>
                                              <div className="flex flex-col gap-1 text-sm text-muted-foreground">
                                                {contact.email && (
                                                  <div className="flex items-center">
                                                    <Mail className="h-3 w-3 mr-1" />
                                                    <span>{contact.email}</span>
                                                  </div>
                                                )}
                                                {contact.phone && (
                                                  <div className="flex items-center">
                                                    <Phone className="h-3 w-3 mr-1" />
                                                    <span>{contact.phone}</span>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>

                                          {/* Mobile Contact Actions */}
                                          <DropdownMenu
                                            open={openDropdowns[`contact-mobile-${group.id}-${contactIndex}`] || false}
                                            onOpenChange={(open) => setOpenDropdowns(prev => ({
                                              ...prev,
                                              [`contact-mobile-${group.id}-${contactIndex}`]: open
                                            }))}
                                          >
                                            <DropdownMenuTrigger asChild>
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-8 w-8 p-0 hover:bg-gray-100 ml-2"
                                                onClick={(e) => e.stopPropagation()}
                                              >
                                                <MoreVertical className="h-4 w-4 text-muted-foreground" />
                                              </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end" className="w-48">
                                              <DropdownMenuLabel>Contact Actions</DropdownMenuLabel>
                                              <DropdownMenuSeparator />
                                              <DropdownMenuItem
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  handleEditContact(group.id, contactIndex);
                                                }}
                                              >
                                                <Edit className="mr-2 h-4 w-4" />
                                                Edit Contact
                                              </DropdownMenuItem>
                                              <DropdownMenuSeparator />
                                              <DropdownMenuItem
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  handleRemoveContact(group.id, contactIndex);
                                                }}
                                                className="text-red-600 focus:text-red-600"
                                                disabled={isRemovingContact}
                                              >
                                                <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                                                Remove from Group
                                              </DropdownMenuItem>
                                            </DropdownMenuContent>
                                          </DropdownMenu>
                                        </div>
                                      </div>
                                    </div>
                                    ))
                                  )}


                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          );
                        })}
                      </Accordion>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Edit Contact Dialog */}
      <Dialog
        open={!!editingContact}
        onOpenChange={(open) => {
          if (!open) {
            setEditingContact(null);
            closeAllDropdowns(); // Close any remaining dropdowns when modal closes
          }
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Contact</DialogTitle>
            <DialogDescription>
              Update the contact information for this person.
            </DialogDescription>
          </DialogHeader>

          {editingContact && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="contact-name">Name</Label>
                <Input
                  id="contact-name"
                  value={editingContact.contact.name || ''}
                  onChange={(e) => setEditingContact(prev => prev ? {
                    ...prev,
                    contact: { ...prev.contact, name: e.target.value }
                  } : null)}
                  placeholder="Enter contact name"
                />
              </div>

              <div>
                <Label htmlFor="contact-email">Email</Label>
                <Input
                  id="contact-email"
                  type="email"
                  value={editingContact.contact.email || ''}
                  onChange={(e) => setEditingContact(prev => prev ? {
                    ...prev,
                    contact: { ...prev.contact, email: e.target.value }
                  } : null)}
                  placeholder="Enter email address"
                />
              </div>

              <div>
                <Label htmlFor="contact-phone">Phone</Label>
                <Input
                  id="contact-phone"
                  type="tel"
                  value={editingContact.contact.phone || ''}
                  onChange={(e) => setEditingContact(prev => prev ? {
                    ...prev,
                    contact: { ...prev.contact, phone: e.target.value }
                  } : null)}
                  placeholder="Enter phone number"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setEditingContact(null)}
              disabled={isUpdatingContact}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateContact}
              disabled={isUpdatingContact || !editingContact?.contact.name && !editingContact?.contact.email && !editingContact?.contact.phone}
            >
              {isUpdatingContact ? 'Updating...' : 'Update Contact'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Contact Dialog */}
      <Dialog
        open={!!addingContactToGroup}
        onOpenChange={(open) => {
          if (!open) {
            setAddingContactToGroup(null);
            closeAllDropdowns(); // Close any remaining dropdowns when modal closes
          }
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Contact to Group</DialogTitle>
            <DialogDescription>
              Add a new contact to {addingContactToGroup ? contactGroups.find(g => g.id === addingContactToGroup)?.name : 'this group'}.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            const contactData = {
              name: formData.get('name') as string || '',
              email: formData.get('email') as string || '',
              phone: formData.get('phone') as string || ''
            };

            // Validate at least one field
            if (!contactData.name && !contactData.email && !contactData.phone) {
              toast({
                title: "Validation Error",
                description: "Please provide at least one contact field (name, email, or phone).",
                variant: "destructive"
              });
              return;
            }

            handleSaveNewContact(contactData);
          }}>
            <div className="space-y-4">
              <div>
                <Label htmlFor="new-contact-name">Name</Label>
                <Input
                  id="new-contact-name"
                  name="name"
                  placeholder="Enter contact name"
                  disabled={isAddingContact}
                />
              </div>

              <div>
                <Label htmlFor="new-contact-email">Email</Label>
                <Input
                  id="new-contact-email"
                  name="email"
                  type="email"
                  placeholder="Enter email address"
                  disabled={isAddingContact}
                />
              </div>

              <div>
                <Label htmlFor="new-contact-phone">Phone</Label>
                <Input
                  id="new-contact-phone"
                  name="phone"
                  type="tel"
                  placeholder="Enter phone number"
                  disabled={isAddingContact}
                />
              </div>

              <p className="text-sm text-muted-foreground">
                At least one field (name, email, or phone) is required.
              </p>
            </div>

            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setAddingContactToGroup(null)}
                disabled={isAddingContact}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isAddingContact}
              >
                {isAddingContact ? 'Adding...' : 'Add Contact'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create Group Dialog */}
      <Dialog open={createGroupDialogOpen} onOpenChange={setCreateGroupDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Contact Group</DialogTitle>
            <DialogDescription>
              Enter a name for your new contact group. You can add contacts to it later.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            const groupName = formData.get('groupName') as string;
            handleCreateGroup(groupName);
          }}>
            <div className="space-y-4">
              <div>
                <Label htmlFor="group-name">Group Name</Label>
                <Input
                  id="group-name"
                  name="groupName"
                  placeholder="Enter group name"
                  disabled={isCreatingGroup}
                  required
                />
              </div>
            </div>

            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCreateGroupDialogOpen(false)}
                disabled={isCreatingGroup}
              >
                Cancel
              </Button>
              <Button
                variant={"primary-button"}
                type="submit"
                disabled={isCreatingGroup}
              >
                {isCreatingGroup ? 'Creating...' : 'Create Group'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Group Dialog */}
      <Dialog open={!!editingGroup} onOpenChange={(open) => !open && setEditingGroup(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Contact Group</DialogTitle>
            <DialogDescription>
              Update the name of your contact group.
            </DialogDescription>
          </DialogHeader>

          {editingGroup && (
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.currentTarget);
              const groupName = formData.get('groupName') as string;
              handleUpdateGroup(groupName);
            }}>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-group-name">Group Name</Label>
                  <Input
                    id="edit-group-name"
                    name="groupName"
                    defaultValue={editingGroup.name}
                    placeholder="Enter group name"
                    disabled={isCreatingGroup}
                    required
                  />
                </div>
              </div>

              <DialogFooter className="mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setEditingGroup(null)}
                  disabled={isCreatingGroup}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isCreatingGroup}
                >
                  {isCreatingGroup ? 'Updating...' : 'Update Group'}
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </ProtectedLayout>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  try {
    const { organizationId } = context.query;

    if (!organizationId) {
      return {
        notFound: true
      };
    }

    // Check user session
    const session = await getServerSession(context.req, context.res, authConfig);
    if (!session?.user?.id) {
      return {
        redirect: {
          destination: '/auth/signin',
          permanent: false
        }
      };
    }

    const db = Database.getInstance();

    // Check organization membership
    const organization = await db.getOrganizationById(organizationId as string);
    if (!organization) {
      return {
        notFound: true
      };
    }

    const isMember = organization.members?.some(member => member.userId === session.user.id);
    if (!isMember) {
      return {
        notFound: true
      };
    }

    // Get saved contact groups
    const { getSavedContactGroups } = await import('@/lib/saved-contact-groups');
    const contactGroups = await getSavedContactGroups(organizationId as string);

    return {
      props: {
        organization,
        initialContactGroups: contactGroups
      }
    };

  } catch (error) {
    console.error('Error in getServerSideProps:', error);
    return {
      notFound: true
    };
  }
};
