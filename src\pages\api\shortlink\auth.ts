import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authConfig } from '../../../auth';
import crypto from 'crypto';

/**
 * Generate JWT token for shortlink service authentication
 * Only accessible to admin users
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if user is authenticated and is admin
    const session = await getServerSession(req, res, authConfig);
    if (!session || !session.user?.isAdmin) {
      return res.status(401).json({ error: 'Unauthorized - Admin access required' });
    }

    // Generate JWT token for shortlink service
    const token = generateJWTToken();

    return res.status(200).json({ token });
  } catch (error) {
    console.error('Error generating JWT token:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

function generateJWTToken(secret = process.env.SHORTLINK_JWT_SECRET || '', expiresIn = '2h') {
  console.log(`Generating JWT token with secret: ${secret} and expiresIn: ${expiresIn}`);


  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  // Convert expiry to seconds
  let exp;
  if (expiresIn.endsWith('h')) {
    exp = Math.floor(Date.now() / 1000) + (parseInt(expiresIn) * 3600);
  } else if (expiresIn.endsWith('d')) {
    exp = Math.floor(Date.now() / 1000) + (parseInt(expiresIn) * 86400);
  } else {
    exp = Math.floor(Date.now() / 1000) + parseInt(expiresIn);
  }

  const payload = {
    user: 'admin',
    iat: Math.floor(Date.now() / 1000),
    exp: exp
  };

  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));

  const signature = crypto
    .createHmac('sha256', secret)
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

function base64UrlEncode(str: string): string {
  return Buffer.from(str)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}
