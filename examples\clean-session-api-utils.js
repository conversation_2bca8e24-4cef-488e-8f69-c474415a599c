/**
 * Frontend Permission Utilities for Clean Session API Structure
 * 
 * These utilities work with the new clean session response that separates:
 * - Organization-level permissions (partner organizations)
 * - System-level permissions (admin privileges)
 */

/**
 * Check if user has a specific permission in an organization
 */
export function hasOrganizationPermission(organization, resource, permission) {
  const roles = Object.keys(organization.rbacConfig.rolePermissions || {});

  return roles.some(role => {
    const rolePermissions = organization.rbacConfig.rolePermissions[role];
    return rolePermissions?.[resource]?.includes(permission);
  });
}

/**
 * Check if user has a specific admin permission (system-level)
 */
export function hasAdminPermission(permissions, resource, permission) {
  const roles = Object.keys(permissions.rolePermissions || {});

  return roles.some(role => {
    const rolePermissions = permissions.rolePermissions[role];
    return rolePermissions?.[resource]?.includes(permission);
  });
}

/**
 * Get user roles in an organization
 */
export function getOrganizationRoles(organization) {
  return Object.keys(organization.rbacConfig.rolePermissions || {});
}

/**
 * Get user admin roles (system-level)
 */
export function getAdminRoles(permissions) {
  return Object.keys(permissions.rolePermissions || {});
}

/**
 * Get all computed permissions for an organization
 */
export function getOrganizationPermissions(organization) {
  const roles = getOrganizationRoles(organization);
  const allPermissions = new Set();

  roles.forEach(role => {
    const rolePermissions = organization.rbacConfig.rolePermissions[role] || {};
    Object.entries(rolePermissions).forEach(([resource, perms]) => {
      perms.forEach(perm => {
        allPermissions.add(`${resource}:${perm}`);
      });
    });
  });

  return Array.from(allPermissions);
}

/**
 * Get all computed admin permissions (system-level)
 */
export function getAdminPermissions(permissions) {
  const roles = getAdminRoles(permissions);
  const allPermissions = new Set();

  roles.forEach(role => {
    const rolePermissions = permissions.rolePermissions[role] || {};
    Object.entries(rolePermissions).forEach(([resource, perms]) => {
      perms.forEach(perm => {
        allPermissions.add(`${resource}:${perm}`);
      });
    });
  });

  return Array.from(allPermissions);
}

/**
 * Check if user has any admin access
 */
export function isAdmin(permissions) {
  return Object.keys(permissions.rolePermissions || {}).length > 0;
}

/**
 * Check multiple organization permissions at once (user needs ANY of these)
 */
export function hasAnyOrganizationPermission(organization, checks) {
  return checks.some(([resource, permission]) =>
    hasOrganizationPermission(organization, resource, permission)
  );
}

/**
 * Check if user has all specified organization permissions
 */
export function hasAllOrganizationPermissions(organization, checks) {
  return checks.every(([resource, permission]) =>
    hasOrganizationPermission(organization, resource, permission)
  );
}

/**
 * Check multiple admin permissions at once (user needs ANY of these)
 */
export function hasAnyAdminPermission(permissions, checks) {
  return checks.some(([resource, permission]) =>
    hasAdminPermission(permissions, resource, permission)
  );
}

/**
 * Check if user has all specified admin permissions
 */
export function hasAllAdminPermissions(permissions, checks) {
  return checks.every(([resource, permission]) =>
    hasAdminPermission(permissions, resource, permission)
  );
}

// ===== Usage Examples =====

// Example session data from new API structure
const sessionData = {
  user: { id: 'user_123', name: 'John Doe', email: '<EMAIL>' },
  organizations: [
    {
      id: 'org_456',
      name: 'Acme Events',
      type: 'partner',
      rbacConfig: {
        rolePermissions: {
          'partner:venue-manager': {
            'partner:dashboard': ['view'],
            'partner:venue': ['view', 'edit'],
            'partner:venue:events': ['view', 'create', 'edit', 'delete']
          }
        }
      }
    }
  ],
  permissions: {
    rolePermissions: {
      'admin:super-admin': {
        'admin:users': ['view', 'create', 'edit', 'delete'],
        'admin:organizations': ['view', 'edit']
      }
    }
  },
  token: 'jwt-token-here'
};

// Organization permission checks
const org = sessionData.organizations[0];

// Get organization roles
const orgRoles = getOrganizationRoles(org);
console.log('Organization roles:', orgRoles);
// Output: ["partner:venue-manager"]

// Check specific organization permission
const canEditVenue = hasOrganizationPermission(org, 'partner:venue', 'edit');
console.log('Can edit venue:', canEditVenue); // true

// Check multiple organization permissions (ANY)
const canManageEvents = hasAnyOrganizationPermission(org, [
  ['partner:venue:events', 'create'],
  ['partner:venue:events', 'edit'],
  ['partner:venue:events', 'delete']
]);
console.log('Can manage events:', canManageEvents); // true

// Get all organization permissions
const allOrgPermissions = getOrganizationPermissions(org);
console.log('All org permissions:', allOrgPermissions);
// Output: ["partner:dashboard:view", "partner:venue:view", "partner:venue:edit", ...]

// Admin permission checks
const adminPerms = sessionData.permissions;

// Check if user is admin
const userIsAdmin = isAdmin(adminPerms);
console.log('Is admin:', userIsAdmin); // true

// Get admin roles
const adminRoles = getAdminRoles(adminPerms);
console.log('Admin roles:', adminRoles);
// Output: ["admin:super-admin"]

// Check specific admin permission
const canDeleteUsers = hasAdminPermission(adminPerms, 'admin:users', 'delete');
console.log('Can delete users:', canDeleteUsers); // true

// Check multiple admin permissions (ALL required)
const canFullyManageOrgs = hasAllAdminPermissions(adminPerms, [
  ['admin:organizations', 'view'],
  ['admin:organizations', 'edit']
]);
console.log('Can fully manage orgs:', canFullyManageOrgs); // true

// React hook example for new API structure
import { useState, useEffect } from 'react';

export function useIACSession() {
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadSession() {
      try {
        const response = await fetch('/api/id/session', {
          credentials: 'include',
        });

        if (response.ok) {
          const sessionData = await response.json();
          setSession(sessionData);
        } else {
          setSession(null);
        }
      } catch (error) {
        console.error('Session load failed:', error);
        setSession(null);
      } finally {
        setLoading(false);
      }
    }

    loadSession();
  }, []);

  // Helper functions for this session
  const hasOrgPermission = (orgId, resource, permission) => {
    const org = session?.organizations.find(o => o.id === orgId);
    return org ? hasOrganizationPermission(org, resource, permission) : false;
  };

  const hasSystemPermission = (resource, permission) => {
    return session ? hasAdminPermission(session.permissions, resource, permission) : false;
  };

  const isUserAdmin = () => {
    return session ? isAdmin(session.permissions) : false;
  };

  return {
    session,
    loading,
    hasOrgPermission,
    hasSystemPermission,
    isUserAdmin
  };
}

// Usage in React component:
function Dashboard() {
  const { session, loading, hasOrgPermission, hasSystemPermission, isUserAdmin } = useIACSession();

  if (loading) return <div>Loading...</div>;
  if (!session) return <div>Please login</div>;

  return (
    <div>
      <h1>Welcome, {session.user.name}</h1>

      {/* Show admin panel if user has admin access */}
      {isUserAdmin() && (
        <div>
          <h2>Admin Panel</h2>
          {hasSystemPermission('admin:users', 'view') && (
            <button>Manage Users</button>
          )}
        </div>
      )}

      {/* Show organization-specific features */}
      {session.organizations.map(org => (
        <div key={org.id}>
          <h2>{org.name}</h2>
          {hasOrgPermission(org.id, 'partner:venues', 'view') && (
            <button>View Venues</button>
          )}
          {hasOrgPermission(org.id, 'partner:venue:events', 'create') && (
            <button>Create Event</button>
          )}
        </div>
      ))}
    </div>
  );
}
