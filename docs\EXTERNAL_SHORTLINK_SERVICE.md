# External Shortlink Service Integration Guide

This guide explains how to use the external shortlink service hosted at `l.iamcoming.io` for URL shortening and analytics.

## Setup Instructions

1. **Update Environment Variables**

   Ensure the following environment variables are set in both `.env.local` and `.env.production`:

   ```
   SHORTLINK_JWT_SECRET=your-shortlink-jwt-secret-key-here
   SHORTLINK_JWT_TOKEN=your-generated-jwt-token
   ```

2. **Generate JWT Token**

   Use the provided script to generate a JWT token:

   ```bash
   node scripts/generate-jwt.js
   ```

   Add the generated token to your environment variables.

3. **No External Dependencies**

   The shortlink service uses the native `fetch` API, so no external dependencies are required.

## Using the Shortlink Service

Import the shortlink service in your code:

```typescript
import { shortlinkService } from '@/lib/shortlink-service';
```

### Create a Short Link

```typescript
try {
  const result = await shortlinkService.createShortLink(
    'example',
    'https://example.com'
  );
  console.log('Short link created:', result);
} catch (error) {
  console.error('Failed to create short link:', error);
}
```

### List All Short Links

```typescript
try {
  const links = await shortlinkService.listShortLinks();
  console.log('All short links:', links);
} catch (error) {
  console.error('Failed to list short links:', error);
}
```

### Get Analytics for a Short Link

```typescript
try {
  const analytics = await shortlinkService.getShortLinkAnalytics('example');
  console.log('Analytics for short link:', analytics);
} catch (error) {
  console.error('Failed to get analytics:', error);
}
```

### Delete a Short Link

```typescript
try {
  const result = await shortlinkService.deleteShortLink('example');
  console.log('Short link deleted:', result);
} catch (error) {
  console.error('Failed to delete short link:', error);
}
```

### Get Full URL for a Short Link

```typescript
const fullUrl = shortlinkService.getFullUrl('example');
console.log('Full short URL:', fullUrl); // https://l.iamcoming.io/example
```

## Reserved Short Codes

The following short codes are reserved and cannot be used:

- `list`
- `health`
- `admin`
- `analytics`

## Implementation Notes

1. JWT tokens should be kept secure and rotated regularly
2. All administrative actions require JWT authentication
3. Short links are publicly accessible without authentication
4. The service automatically collects analytics when links are accessed

For full API documentation, refer to [SHORTLINK_API.md](./SHORTLINK_API.md).

## Migration Guide

### From Internal to External Shortlink Service

If you're migrating from the internal shortlink implementation to the external service:

1. **Backward Compatibility**

   The system maintains backward compatibility through a proxy endpoint at `/api/s/[shortCode]` which redirects to the external service. This ensures existing short links continue to work.

2. **Data Migration**

   If you have existing short links, you'll need to migrate them to the external service:

   ```typescript
   import { Database } from '@/lib/database';
   import { shortlinkService } from '@/lib/shortlink-service';

   async function migrateShortlinks() {
     const db = Database.getInstance();
     // Get all existing shortlinks
     const shortlinks = await db.getData('shortlinks');

     // Migrate each shortlink to the external service
     for (const [shortCode, data] of Object.entries(shortlinks)) {
       try {
         await shortlinkService.createShortLink(shortCode, data.originalUrl);
         console.log(`Migrated shortlink: ${shortCode}`);
       } catch (error) {
         console.error(`Failed to migrate shortlink ${shortCode}:`, error);
       }
     }
   }
   ```

   You can also use the provided migration script:

   ```bash
   npx ts-node scripts/migrate-shortlinks.ts
   ```

3. **Update All References**

   Update any code that creates or manages shortlinks to use the new service instead of direct database operations.
