#!/bin/bash

NODE_VERSION="22"

# Ensure the script exits if any command fails
set -e

# make sure nvm is loaded. if not, load it/install it
if [ -s "$HOME/.nvm/nvm.sh" ]; then
    # Load NVM
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
else
    echo "NVM is not installed. Installing NVM..."
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash
    echo "NVM installed successfully. Please restart your terminal or run the following command to load NVM:"
    echo "export NVM_DIR=\"$HOME/.nvm\" && [ -s \"$NVM_DIR/nvm.sh\" ] && \. \"$NVM_DIR/nvm.sh\""
fi

# check if correct node version is installed via NVM. Check using nvm use <version>
if nvm ls "$NODE_VERSION" &> /dev/null; then
    echo "Node version $NODE_VERSION is already installed."
else
    echo "Node version $NODE_VERSION is not installed. Installing..."
    nvm install "$NODE_VERSION"
    echo "Node version $NODE_VERSION installed successfully."
fi
nvm use "$NODE_VERSION"

echo "Starting deployment script as $(whoami) on $(date)"
echo "Current working directory: $(pwd)"
echo "Node version: $(node -v)"
echo "NPM version: $(npm -v)"

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null
then
    echo "pnpm could not be found, installing pnpm."
    npm i -g pnpm
    echo "pnpm installed successfully."
    echo "Verifying pnpm installation..."
    pnpm --version
    echo "pnpm installation verified."
fi

pnpm run publish

cd /home/<USER>/code-mini/iamcoming/
./env.sh up

