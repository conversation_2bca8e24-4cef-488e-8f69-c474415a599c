import { useState } from "react"
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form"

interface NumericStepperProps {
  name: string;
  defaultValue: number;
}

export function NumericStepper({ name, defaultValue }: NumericStepperProps) {
  const { control } = useFormContext()

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      render={({ field: { onChange, value } }) => (
        <div className="flex space-x-4">
          <button onClick={() => onChange(value - 1)}>-</button>
          <span>{value}</span>
          <button onClick={() => onChange(value + 1)}>+</button>
        </div>
      )}
    />
  )
}
