import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authConfig } from '@/auth';
import { sendPartnerRequestEmails, PartnerRequestData } from '@/lib/partnerRequest';
import { log } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if user is authenticated
    const session = await getServerSession(req, res, authConfig);
    if (!session || !session.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Validate request body
    const { businessName, taxId, phoneNumber, address, hasMultipleVenues } = req.body;

    if (!businessName || !taxId || !phoneNumber) {
      return res.status(400).json({ 
        error: 'Missing required fields: businessName, taxId, and phoneNumber are required' 
      });
    }

    // Check if user is already a partner
    if (session.user.organization?.type === 'partner') {
      return res.status(400).json({ 
        error: 'User is already a partner' 
      });
    }

    // Prepare partner request data
    const requestData: PartnerRequestData = {
      businessName: businessName.trim(),
      taxId: taxId.trim(),
      phoneNumber: phoneNumber.trim(),
      address: address?.trim(),
      hasMultipleVenues: Boolean(hasMultipleVenues),
      userEmail: session.user.email!,
      userName: session.user.name || 'Unknown User',
      userImage: session.user.image || undefined
    };

    // Send partner request emails
    await sendPartnerRequestEmails(requestData);

    // Log the request
    log(`Partner request submitted for ${businessName} by ${session.user.email}`);

    // Return success response
    res.status(200).json({ 
      success: true, 
      message: 'Partner request submitted successfully. You will receive a confirmation email shortly.' 
    });

  } catch (error) {
    console.error('Error processing partner request:', error);
    res.status(500).json({ 
      error: 'Failed to submit partner request. Please try again later.' 
    });
  }
}
