'use client';
import { use, useEffect } from "react";
import Container from "@/components/Container"
import { useEvent } from "@/hooks/useEvent"
import { FormatDate } from "@/lib/dayjs"
import { Format24to12 } from "@/lib/time"
import { useRouter } from "next/router"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Printer, AlertTriangle } from "lucide-react"
import { useState } from "react"
import { useRSVP } from "@/hooks/useRSVP";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import { useInvites } from "@/hooks/useInvites";
import QRCode from "react-qr-code";
import LabelPrinter from "@/components/LabelPrinter";
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout";
import { Header } from "@/components/Header";
import { isReminderLocked, getReminderLockMessage } from "@/lib/event/eventLock";

interface EventProps {
  id: string
  title: string
  description: string
  startDate: Date
  endDate?: Date
  location: string
  invitationsSent: number
  rsvpsReceived: number
  adultsInvited: number
  adultsConfirmed: number
  childrenInvited: number
  childrenConfirmed: number
}

export default function PrintInvitesScreen() {
  const router = useRouter();
  const { eventId } = router.query;
  const { event, loading: eventLoading } = useEvent(eventId as string);
  const { invites, loading, error } = useInvites(eventId as string);

  // Redirect if event date has passed
  useEffect(() => {
    if (event && isReminderLocked(event)) {
      router.push(`/event/${eventId}`);
    }
  }, [event, eventId, router]);

  const handlePrint = () => {
    window.print();
  };

  return (
    <ProtectedLayout>
      <div className="flex flex-col  bg-gray-50">
        {/* Use the shared Header component with breadcrumbs */}
        <Header 
          title="Print Invite Labels"
          breadcrumbs={[
            { label: 'Events', href: '/events' },
            { label: event?.eventName || 'Event Details', href: `/event/${eventId}` },
            { label: 'Invites', href: `/event/${eventId}/invites` },
          ]}
          buttons={[]}
          showUserProfile={true}
        />
        
        {/* Event date passed warning */}
        {event && isReminderLocked(event) && (
          <div className="p-4">
            <div className="container mx-auto">
              <div className="bg-red-50 border border-red-300 rounded-lg p-4 mb-4">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-red-800">Event Completed</h3>
                    <p className="text-sm text-red-700">{getReminderLockMessage(event)}</p>
                    <p className="text-sm text-red-700 mt-1">Printing labels is no longer available. Redirecting to event page...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <LabelPrinter invites={invites} />
      </div>
    </ProtectedLayout>
  );
}