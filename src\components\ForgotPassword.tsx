"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Form, FormField, FormMessage } from "@/components/ui/form"
import { Separator } from "@/components/ui/separator"
import { Mail, Loader2, ArrowLeft, CheckCircle2, KeyRound, ShieldCheck } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { signIn, useSession } from "next-auth/react"
import { useRouter } from "next/router"

const emailSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export function ForgotPasswordForm() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState<'reset' | 'magic'>('reset');
  const [magicLinkSent, setMagicLinkSent] = useState(false);
  const [email, setEmail] = useState("");

  // Redirect to events page if user is authenticated
  useEffect(() => {
    if (status === 'authenticated') {
      router.push('/events');
    }
  }, [status, router]);

  // Initialize form outside of any conditional rendering
  const form = useForm<z.infer<typeof emailSchema>>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: ""
    }
  });

  // Show loading state when checking authentication status
  if (status === 'loading') {
    return (
      <div className="container flex min-h-screen w-full flex-col items-center justify-center py-12">
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-rose-500" />
          <p className="mt-2 text-sm text-muted-foreground">Checking your authentication status...</p>
        </div>
      </div>
    );
  }

  const handleResetPassword: SubmitHandler<z.infer<typeof emailSchema>> = async (formData) => {
    setIsLoading(true);
    setError("");
    setSuccess(false);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send password reset email');
      }

      setSuccess(true);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to send password reset email");
    } finally {
      setIsLoading(false);
    }
  };

  const handleMagicLink: SubmitHandler<z.infer<typeof emailSchema>> = async (formData) => {
    setIsLoading(true);
    setError("");
    setMagicLinkSent(false);

    try {
      const result = await signIn("email", {
        email: formData.email,
        redirect: false,
        callbackUrl: "/events", // Add callback URL to redirect to events page after authentication
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      setEmail(formData.email);
      setMagicLinkSent(true);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to send magic link");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container flex min-h-screen w-full flex-col items-center justify-center py-12">
      <div className="mb-8">
        <Image
          src="/iac-logo-large.svg"
          alt="I am Coming - Event RSVP Management Platform"
          className="h-10 w-auto cursor-pointer"
          width={200}
          height={40}
          onClick={() => window.location.href = '/'}
        />
      </div>
      <Card className="w-full max-w-md mx-auto border border-[#E2E8F0]">
        <CardHeader className="pb-4">
          <div className="flex flex-col items-center justify-center">
            <div className="mb-2 p-3 bg-rose-50 rounded-full">
              <ShieldCheck className="h-8 w-8 text-rose-500" />
            </div>
            <CardTitle className="text-2xl font-semibold text-center">Account Recovery</CardTitle>
            <CardDescription className="text-center mt-1">
              Choose how you&apos;d like to access your account
            </CardDescription>
          </div>
        </CardHeader>
        <Separator className="mb-4" />
        <CardContent className="space-y-5">
          <div className="flex justify-center space-x-2">
            <Button
              variant={activeTab === 'reset' ? "primary-button" : "outline"}
              className="w-1/2 shadow-none"
              onClick={() => setActiveTab('reset')}
            >
              <KeyRound className="mr-2 h-4 w-4" />
              Reset Password
            </Button>
            <Button
              variant={activeTab === 'magic' ? "primary-button" : "outline"}
              className="w-1/2 shadow-none"
              onClick={() => setActiveTab('magic')}
            >
              <Mail className="mr-2 h-4 w-4" />
              Magic Link
            </Button>
          </div>

          <Separator className="my-4" />

          {/* Single Form Component */}
          <Form {...form}>
            {/* Reset Password Tab */}
            {activeTab === 'reset' && !success && (
              <div>
                <p className="text-sm text-muted-foreground mb-4">
                  We&apos;ll send you a link to reset your password. Enter your email address below.
                </p>
                <form onSubmit={form.handleSubmit(handleResetPassword)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium">Email</Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            className="h-11 pl-10 shadow-none border-[#E2E8F0]"
                            {...field}
                          />
                        </div>
                        <FormMessage />
                      </div>
                    )}
                  />
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}
                  <Button type="submit" variant="primary-button" className="w-full h-11 mt-4 shadow-none" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending reset link...
                      </>
                    ) : (
                      <>
                        <KeyRound className="mr-2 h-4 w-4" />
                        Send reset link
                      </>
                    )}
                  </Button>
                </form>
              </div>
            )}

            {/* Magic Link Tab */}
            {activeTab === 'magic' && !magicLinkSent && (
              <div>
                <p className="text-sm text-muted-foreground mb-4">
                  We&apos;ll send you a magic link to sign in instantly. No password needed.
                </p>
                <form onSubmit={form.handleSubmit(handleMagicLink)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium">Email</Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            className="h-11 pl-10 shadow-none border-[#E2E8F0]"
                            {...field}
                          />
                        </div>
                        <FormMessage />
                      </div>
                    )}
                  />
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}
                  <Button type="submit" variant="primary-button" className="w-full h-11 mt-4 shadow-none" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending magic link...
                      </>
                    ) : (
                      <>
                        <Mail className="mr-2 h-4 w-4" />
                        Send magic link
                      </>
                    )}
                  </Button>
                </form>
              </div>
            )}
          </Form>

          {/* Success States */}
          {success && (
            <Alert className="mt-4">
              <div className="flex items-center">
                <CheckCircle2 className="h-4 w-4 mr-2" />
                <div>
                  <h4 className="font-medium mb-1">Email Sent</h4>
                  <AlertDescription>
                    If your email exists in our system, you will receive a password reset link shortly. Please check your email.
                  </AlertDescription>
                </div>
              </div>
            </Alert>
          )}

          {magicLinkSent && (
            <Alert className="mt-4">
              <div className="flex items-center">
                <CheckCircle2 className="h-4 w-4 mr-2" />
                <div>
                  <h4 className="font-medium mb-1">Magic Link Sent</h4>
                  <AlertDescription>
                    Magic link sent to <span className="font-medium">{email}</span>. Please check your email to sign in.
                  </AlertDescription>
                </div>
              </div>
            </Alert>
          )}
        </CardContent>
        <CardFooter className="flex justify-center pt-2">
          <Link href="/auth/signin?callbackUrl=/events" className="flex items-center text-sm text-muted-foreground hover:text-rose-500 transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to sign in
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
