interface KVNamespace {
  get(key: string): Promise<string | null>;
  put(key: string, value: string): Promise<void>;
  delete(key: string): Promise<void>;
  list(): Promise<{ keys: { name: string }[] }>;
}

export interface SL_Env {
  URL_MAP: KVNamespace;
  URL_HITS: KVNamespace;
  URL_STATS: KVNamespace;
  JWT_SECRET: string;
}

export interface SL_AnalyticsLog {
  timestamp: string;
  ua: string;
  location: string;
  os: string;
}

export interface SL_AnalyticsStats {
  hits: number;
  locations: Record<string, number>;
  devices: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  os: Record<string, number>;
  logs: SL_AnalyticsLog[];
}

export interface SL_ShortLinkRequest {
  shortCode: string;
  redirectUrl: string;
}

export interface SL_ShortLinkResponse {
  shortCode: string;
  redirectUrl: string;
}

export interface SL_AnalyticsResponse {
  shortCode: string;
  analytics: SL_AnalyticsStats;
}

export interface SL_GroupedAnalytics {
  shortCode: string;
  summary: SL_AnalyticsStats;
  grouped: {
    daily: Record<string, number>;
    weekly: Record<string, number>;
  };
}
