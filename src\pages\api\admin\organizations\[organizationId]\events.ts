import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';
import { Event } from '@/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const db = Database.getInstance();

    // Get organization ID from the query parameters
    const { organizationId } = req.query;

    if (!organizationId || Array.isArray(organizationId)) {
      return res.status(400).json({ error: 'Invalid organization ID' });
    }

    // Handle GET request - fetch organization events
    if (req.method === 'GET') {
      // Get the organization
      const organization = await db.getOrganizationById(organizationId);

      if (!organization) {
        return res.status(404).json({ error: 'Organization not found' });
      }

      // Fetch all events for this organization
      const events = await db.ListData<Event>('events', {
        field: 'organizationId',
        operator: '==',
        value: organizationId
      });

      // Return events list
      return res.status(200).json({
        success: true,
        events
      });
    }

    // If not GET, return method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error fetching organization events:', error);
    return res.status(500).json({ error: 'Failed to fetch organization events' });
  }
}
