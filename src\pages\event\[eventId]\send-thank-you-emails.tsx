"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/router"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { useEvent } from "@/hooks/useEvent"
import { useInvites } from "@/hooks/useInvites"
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout"
import { Header } from "@/components/Header"
import { useToast } from "@/components/ui/use-toast"
import { Mail, Users, CheckCircle, AlertCircle, ArrowLeft } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

export default function SendThankYouEmailsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { data: session } = useSession()
  const { eventId } = router.query
  const { event, loading: eventLoading } = useEvent(eventId as string)
  const { invites, loading: invitesLoading } = useInvites(eventId as string)
  
  const [selectedGuests, setSelectedGuests] = useState<string[]>([])
  const [selectAll, setSelectAll] = useState(false)
  const [sending, setSending] = useState(false)
  const [emailsSent, setEmailsSent] = useState(0)

  // Filter invites to only those who attended (accepted) and have email
  const attendedGuestsWithEmail = invites.filter(invite =>
    invite.email &&
    invite.email.trim() !== '' &&
    invite.status === 'accepted'
  )

  useEffect(() => {
    if (!router.isReady) return
    if (!eventId) {
      router.push('/events')
      return
    }
  }, [router.isReady, eventId, router])

  useEffect(() => {
    // Auto-select all eligible guests by default
    if (attendedGuestsWithEmail.length > 0 && selectedGuests.length === 0) {
      const allIds = attendedGuestsWithEmail.map(invite => invite.ID)
      setSelectedGuests(allIds)
      setSelectAll(true)
    }
  }, [attendedGuestsWithEmail, selectedGuests.length])

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked)
    if (checked) {
      setSelectedGuests(attendedGuestsWithEmail.map(invite => invite.ID))
    } else {
      setSelectedGuests([])
    }
  }

  const handleGuestToggle = (guestId: string) => {
    setSelectedGuests(prev => {
      const newSelected = prev.includes(guestId)
        ? prev.filter(id => id !== guestId)
        : [...prev, guestId]
      
      // Update select all state
      setSelectAll(newSelected.length === attendedGuestsWithEmail.length)
      
      return newSelected
    })
  }

  const handleSendEmails = async () => {
    if (selectedGuests.length === 0) {
      toast({
        title: "No guests selected",
        description: "Please select at least one guest to send thank you emails to.",
        variant: "destructive"
      })
      return
    }

    setSending(true)
    setEmailsSent(0)

    try {
      const response = await fetch(`/api/event/${eventId}/send-thank-you-emails`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          onlyToSelectedGuests: true,
          selectedGuestIds: selectedGuests
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send thank you emails')
      }

      setEmailsSent(data.sentCount)

      toast({
        title: "Thank you emails sent!",
        description: `Successfully sent ${data.sentCount} thank you email${data.sentCount !== 1 ? 's' : ''}.`,
      })

      // Show errors if any
      if (data.errors && data.errors.length > 0) {
        console.warn('Some emails failed to send:', data.errors)
        toast({
          title: "Some emails failed",
          description: `${data.errors.length} email${data.errors.length !== 1 ? 's' : ''} could not be sent. Check the console for details.`,
          variant: "destructive"
        })
      }

    } catch (error) {
      console.error('Error sending thank you emails:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send thank you emails",
        variant: "destructive"
      })
    } finally {
      setSending(false)
    }
  }

  if (eventLoading || invitesLoading) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading event details...</p>
          </div>
        </div>
      </ProtectedLayout>
    )
  }

  if (!event) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Event Not Found</h2>
            <p className="text-muted-foreground mb-4">The event you&apos;re looking for doesn&apos;t exist or you don&apos;t have access to it.</p>
            <Button onClick={() => router.push('/events')} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Events
            </Button>
          </div>
        </div>
      </ProtectedLayout>
    )
  }
  return (
    <ProtectedLayout>
      <div className="flex flex-col bg-gray-50">
        <Header 
          title="Send Thank You Emails"
          breadcrumbs={[
            { label: 'Events', href: '/events' },
            { label: event.eventName, href: `/event/${eventId}` }
          ]}
        />

        <div className="flex-1 p-4 pb-20">
          <div className="container mx-auto max-w-4xl">
            
            {/* Event Info */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Post-Event Thank You Emails
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg">{event.eventName}</h3>
                    <p className="text-muted-foreground">{new Date(event.eventDate).toLocaleDateString()}</p>
                  </div>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 mb-2">Email Preview</h4>
                    <div className="text-sm text-blue-800">
                      <p><strong>Subject:</strong> Thanks for making {event.eventName} special! 🎉</p>
                      <div className="mt-2 p-4 bg-white rounded border text-gray-700 space-y-3">
                        <div className="text-center">
                          <h2 className="text-xl font-bold text-gray-900">🎉 Thank You!</h2>
                        </div>
                        
                        <div className="space-y-2">
                          <p>Dear <strong>[Guest Name]</strong>,</p>
                          <p>Thank you for attending <strong>{event.eventName}</strong> on <strong>{new Date(event.eventDate).toLocaleDateString()}</strong>. Your presence made the event truly special and memorable.</p>
                          <p>We hope you enjoyed the experience and had the opportunity to connect with fellow attendees. Events like these are only successful because of wonderful participants like you.</p>
                          <p>If you&apos;re inspired to host your own event, we&apos;d be delighted to help you create something equally memorable.</p>
                        </div>

                        <div className="text-center py-2">
                          <span className="inline-block bg-[#F43F5E] text-white px-4 py-2 rounded text-sm font-medium">
                            Create Your Event
                          </span>
                        </div>

                        <div className="text-center text-sm">
                          <p>Visit <span className="text-[#F43F5E] underline">iamcoming.io</span> to get started.</p>
                          <p className="mt-2">Best regards,<br /><strong>{event.host}</strong></p>
                        </div>

                        <div className="border-t pt-3 mt-4 text-xs text-muted-foreground">
                          <p><strong>About IAMCOMING.IO</strong></p>
                          <p className="mt-1">We make event planning simple! With just a few clicks, you can create an event, send out invites via link or QR code, and track responses seamlessly.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Guest Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Select Guests
                  </div>
                  <Badge variant="secondary" className="self-start sm:self-auto">
                    {attendedGuestsWithEmail.length} eligible guest{attendedGuestsWithEmail.length !== 1 ? 's' : ''}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {attendedGuestsWithEmail.length === 0 ? (
                  <div className="text-center py-8">
                    <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Eligible Guests</h3>
                    <p className="text-muted-foreground">
                      No guests with email addresses who attended this event were found.
                      Only guests who accepted their invitation and have email addresses can receive thank you emails.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Select All */}
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <Checkbox
                        id="select-all"
                        checked={selectAll}
                        onCheckedChange={handleSelectAll}
                      />
                      <label htmlFor="select-all" className="font-medium cursor-pointer text-sm sm:text-base">
                        Select all guests ({attendedGuestsWithEmail.length})
                      </label>
                    </div>

                    <Separator />

                    {/* Guest List */}
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {attendedGuestsWithEmail.map((invite) => (
                        <div key={invite.ID} className="flex items-start sm:items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg">
                          <Checkbox
                            id={invite.ID}
                            checked={selectedGuests.includes(invite.ID)}
                            onCheckedChange={() => handleGuestToggle(invite.ID)}
                            className="mt-1 sm:mt-0"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                              <label htmlFor={invite.ID} className="font-medium cursor-pointer text-sm sm:text-base truncate">
                                {invite.name}
                              </label>
                              <Badge variant="outline" className="text-green-700 border-green-200 text-xs w-fit">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Attended
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground break-all sm:break-normal">{invite.email}</p>
                            {invite.response && (
                              <p className="text-xs text-muted-foreground mt-1">
                                {invite.response.adults} Adults, {invite.response.children} Children
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
              {attendedGuestsWithEmail.length > 0 && (
                <CardFooter className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0">
                  <div className="text-sm text-muted-foreground text-center sm:text-left">
                    {selectedGuests.length} of {attendedGuestsWithEmail.length} guests selected
                  </div>
                  <Button
                    onClick={handleSendEmails}
                    disabled={sending || selectedGuests.length === 0}
                    className="bg-[#F43F5E] hover:bg-[#F43F5E]/90 w-full sm:w-auto"
                  >
                    {sending ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        <span className="hidden sm:inline">Sending...</span>
                        <span className="sm:hidden">Sending</span>
                      </>
                    ) : (
                      <>
                        <Mail className="h-4 w-4 mr-2" />
                        <span className="hidden sm:inline">Send Thank You Emails ({selectedGuests.length})</span>
                        <span className="sm:hidden">Send Emails ({selectedGuests.length})</span>
                      </>
                    )}
                  </Button>
                </CardFooter>
              )}
            </Card>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
