# CORS and Redirect Implementation Summary

## 🛠️ Changes Made

### 1. Global CORS Implementation

#### Created `/src/lib/cors.ts`

- Centralized CORS configuration
- Support for IAC domains and localhost
- Comprehensive headers including credentials support
- Utility functions for easy integration

#### Updated `/src/middleware.ts`

- Global CORS handling for all `/api/*` routes
- Automatic preflight request handling
- Support for common HTTP methods and headers

#### Updated API Endpoints

- `/api/auth/[...nextauth].ts` - Added CORS for NextAuth (including signout)
- `/api/id/session.ts` - Updated to use centralized CORS
- `/api/auth/[...nextauth].ts` - Added CORS support and unified callback handling
- `/api/auth/signup.ts` - Added CORS support

### 2. Authentication Redirect Flow

#### Created `/src/lib/auth/redirect.ts`

- Utility functions for handling redirects with site parameters
- Type-safe site validation
- URL generation helpers

#### Created `/src/hooks/useAuthRedirect.ts`

- React hooks for authentication redirects
- External site authentication support
- Session storage management

#### Updated `/src/pages/auth/signin.tsx`

- Enhanced site parameter handling
- Better redirect logic after authentication
- Session storage integration

#### Updated `/src/auth.ts`

- Improved redirect callback with site parameter preservation
- Better error handling and logging

### 3. External Site Integration

#### Created `/src/lib/external-auth-client.ts`

- Complete authentication client for external sites
- Proper signout handling with CSRF tokens
- React hook for easy integration

#### Updated `/docs/ExternalSiteAuthExamples.tsx`

- Comprehensive examples for external site integration
- Proper signout implementation
- React and vanilla JavaScript examples

#### Created `/docs/Authentication-Redirect-Guide.md`

- Complete guide for implementing redirects
- Best practices and security considerations
- Testing and debugging instructions

## 🚀 How It Works

### For External Sites (e.g., partner.iamcoming.io)

1. **Authentication Check:**

   ```javascript
   const response = await fetch('https://app.iamcoming.io/api/id/session', {
     credentials: 'include',
   });
   ```

2. **Redirect to Auth:**

   ```javascript
   const signInUrl = `https://app.iamcoming.io/auth/signin?site=partner&redirectUrl=${encodeURIComponent(
     returnUrl
   )}`;
   window.location.href = signInUrl;
   ```

3. **Sign Out:**

   ```javascript
   // Get CSRF token first
   const csrfResponse = await fetch('https://app.iamcoming.io/api/auth/csrf', {
     credentials: 'include',
   });
   const { csrfToken } = await csrfResponse.json();

   // Then sign out
   await fetch('https://app.iamcoming.io/api/auth/sign-out', {
     method: 'POST',
     credentials: 'include',
     headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
     body: `csrfToken=${encodeURIComponent(csrfToken)}`,
   });
   ```

### For IAC Application

1. **Using the Hook:**

   ```typescript
   const { signInWithSite, isAuthenticated } = useAuthRedirect();

   const handlePartnerRedirect = () => {
     signInWithSite('partner', '/dashboard');
   };
   ```

2. **Using the External Client:**

   ```typescript
   import { IACAuthClient } from '@/lib/external-auth-client';

   const client = new IACAuthClient();
   const authResult = await client.checkAuth();
   ```

## 🔧 CORS Configuration

### Allowed Origins

- `*.iamcoming.io` (all IAC subdomains)
- `localhost:3000`, `localhost:3001`, `localhost:8080` (development)
- `127.0.0.1` (local development)

### Supported Methods

- GET, HEAD, PUT, PATCH, POST, DELETE, OPTIONS

### Headers

- Content-Type, Authorization, X-Requested-With
- Accept, Origin, Cache-Control, X-File-Name
- X-CSRF-Token (for NextAuth)

### Features

- Credentials support (`Access-Control-Allow-Credentials: true`)
- Proper preflight handling
- 24-hour max age for preflight caching

## 🐛 Debugging

### Enable Debug Mode

```bash
./scripts/enable-session-debug.sh
```

### Check CORS Headers

```bash
curl -X OPTIONS 'https://app.iamcoming.io/api/auth/sign-out' \
  -H 'Origin: https://partner.iamcoming.io' \
  -H 'Access-Control-Request-Method: POST' \
  --verbose
```

### Test Authentication Flow

```bash
# Check session
curl -X GET 'https://app.iamcoming.io/api/id/session' \
  -H 'Origin: https://partner.iamcoming.io' \
  -H 'Cookie: cross-domain-session=...' \
  --verbose
```

## 🔒 Security Features

### HTTP-Only Cookies

- Session tokens stored in HTTP-only cookies
- Not accessible via JavaScript
- Secure transmission (HTTPS in production)

### CSRF Protection

- NextAuth CSRF tokens for signout
- Proper token validation

### Domain Validation

- Only allowed domains can receive auth cookies
- Origin validation in CORS middleware

### Secure Headers

- SameSite cookie attributes
- Secure flag in production
- Proper domain scoping

## 📝 Testing

### Manual Testing URLs

1. **Sign-in with site parameter:**

   ```
   https://app.iamcoming.io/auth/signin?site=partner&redirectUrl=https%3A//partner.iamcoming.io/dashboard
   ```

2. **Session check:**

   ```
   https://app.iamcoming.io/api/id/session
   ```

3. **Signout:**

   ```
   POST https://app.iamcoming.io/api/auth/sign-out
   ```

### External Site Testing

Create a simple HTML file to test external site integration:

```html
<!DOCTYPE html>
<html>
  <head>
    <title>Test External Auth</title>
  </head>
  <body>
    <div id="status">Checking authentication...</div>
    <button onclick="login()">Sign In</button>
    <button onclick="logout()">Sign Out</button>

    <script>
      async function checkAuth() {
        try {
          const response = await fetch(
            'https://app.iamcoming.io/api/id/session',
            {
              credentials: 'include',
            }
          );

          if (response.ok) {
            const data = await response.json();
            document.getElementById(
              'status'
            ).innerHTML = `Authenticated as: ${data.user.email}`;
          } else {
            document.getElementById('status').innerHTML = 'Not authenticated';
          }
        } catch (error) {
          document.getElementById('status').innerHTML = 'Error checking auth';
        }
      }

      function login() {
        const returnUrl = encodeURIComponent(window.location.href);
        const signInUrl = `https://app.iamcoming.io/auth/signin?site=partner&redirectUrl=${returnUrl}`;
        window.location.href = signInUrl;
      }

      async function logout() {
        try {
          // Get CSRF token
          const csrfResponse = await fetch(
            'https://app.iamcoming.io/api/auth/csrf',
            {
              credentials: 'include',
            }
          );
          const { csrfToken } = await csrfResponse.json();

          // Sign out
          await fetch('https://app.iamcoming.io/api/auth/sign-out', {
            method: 'POST',
            credentials: 'include',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `csrfToken=${encodeURIComponent(csrfToken)}`,
          });

          checkAuth(); // Refresh status
        } catch (error) {
          console.error('Logout failed:', error);
        }
      }

      // Check auth on load
      checkAuth();
    </script>
  </body>
</html>
```

## ✅ Next Steps

1. **Test the complete flow** from external site to IAC and back
2. **Verify CORS headers** are properly set for all endpoints
3. **Test signout functionality** from external sites
4. **Monitor debug logs** for any authentication issues
5. **Update any remaining API endpoints** to use the centralized CORS utility

The CORS and redirect implementation is now complete and should resolve the signout CORS errors you were experiencing.
