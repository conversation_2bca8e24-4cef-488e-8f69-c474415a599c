export function Format24to12(time: string | undefined): string {
  if (!time) {
    return '';
  }

  const [hour, minute] = time.split(':');
  const h = parseInt(hour, 10);
  const m = parseInt(minute, 10);
  if (h > 12) {
    return `${h - 12}:${m.toString().padStart(2, '0')} PM`;
  }
  return `${h}:${m.toString().padStart(2, '0')} AM`;
}

export function Format12to24(time: string | undefined): string {
  if (!time) {
    return '';
  }

  const [hour, minute] = time.split(':');
  const h = parseInt(hour, 10);
  const m = parseInt(minute, 10);
  if (time.includes('PM')) {
    return `${h + 12}:${m.toString().padStart(2, '0')}`;
  }
  return `${h}:${m.toString().padStart(2, '0')}`;
}