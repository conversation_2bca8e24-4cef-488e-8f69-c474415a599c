import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { AdminLayout } from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { shortlinkService } from '@/lib/shortlink-service';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';
import { ShortlinkForm } from '@/components/admin/ShortlinkForm';

interface ShortlinkData {
  shortCode: string;
  redirectUrl: string;
  hits?: number;
  createdAt?: string;
}

export default function EditShortlinkPage() {
  const router = useRouter();
  const { shortCode } = router.query;
  const [shortlink, setShortlink] = useState<ShortlinkData | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    if (shortCode && typeof shortCode === 'string') {
      loadShortlinkDetails();
    }
  }, [shortCode]);

  const loadShortlinkDetails = async () => {
    try {
      setLoading(true);
      // Since we don't have a direct API to get a single shortlink, 
      // we'll get all and filter
      const data = await shortlinkService.listShortLinks();
      const foundShortlink = data.find((link: ShortlinkData) => link.shortCode === shortCode);

      if (foundShortlink) {
        setShortlink(foundShortlink);
      } else {
        toast({
          title: "Error",
          description: "Shortlink not found.",
          variant: "destructive",
        });
        router.push('/admin/links');
      }
    } catch (error) {
      console.error('Failed to load shortlink details:', error);
      toast({
        title: "Error",
        description: "Failed to load shortlink details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditSuccess = () => {
    toast({
      title: "Success",
      description: "Shortlink updated successfully!",
    });
    router.push(`/admin/links/${shortCode}`);
  };

  if (loading) {
    return (
      <AdminLayout pageTitle="Loading...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!shortlink) {
    return (
      <AdminLayout pageTitle="Shortlink Not Found">
        <div className="text-center py-8">
          <p className="text-muted-foreground">Shortlink not found.</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout pageTitle={`Edit: ${shortlink.shortCode}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col items-start space-x-4">
          <Link href={`/admin/links/${shortCode}`} className='mb-2'>
            <Button variant="link" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Details
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              Edit Shortlink
            </h1>
            <p className="text-sm text-muted-foreground">
              Update the destination URL and UTM parameters for{' '}
              <code className="px-2 py-1 bg-muted rounded text-xs">
                {shortlink.shortCode}
              </code>
            </p>
          </div>
        </div>

        {/* Edit Form */}
        <div className="max-w-full">
          <ShortlinkForm
            onSuccess={handleEditSuccess}
            initialData={{
              shortCode: shortlink.shortCode,
              redirectUrl: shortlink.redirectUrl
            }}
            isEditing={true}
          />
        </div>
      </div>
    </AdminLayout>
  );
}
