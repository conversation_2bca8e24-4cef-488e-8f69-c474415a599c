import React from 'react';
import { useRouter } from 'next/router';
import { AdminLayout } from '@/components/layouts/AdminLayout';
import { ShortlinkForm } from '@/components/admin/ShortlinkForm';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';

export default function CreateShortlinkPage() {
  const router = useRouter();
  const { toast } = useToast();

  const handleCreateSuccess = () => {
    toast({
      title: "Success",
      description: "Shortlink created successfully!",
    });
    router.push('/admin/links');
  };

  return (
    <AdminLayout pageTitle="Create Shortlink">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col items-start space-x-4">
          <Link href="/admin/links" className='mb-2'>
            <Button variant="link" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Links
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Create New Shortlink</h1>
            <p className="text-sm text-muted-foreground">
              Create a new short URL with optional UTM parameters for tracking
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="max-w-full">
          <ShortlinkForm onSuccess={handleCreateSuccess} />
        </div>
      </div>
    </AdminLayout>
  );
}
