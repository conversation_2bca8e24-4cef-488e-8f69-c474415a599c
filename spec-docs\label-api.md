# Label API Documentation

## Endpoint
`GET /api/media/label`

## Description
Generates a label image with a QR code. The image can be customized with various parameters and fetched for download.

## Query Parameters

### Required
- `name` (string): Name to display on the label.
- `qr-content` (string): Content to encode in the QR code.

### Optional
- `orientation` (string): Orientation of the label. Supported values: `portrait`, `landscape`. Default: `portrait`.
- `color-qr` (string): Color of the QR code. Default: `#000000`.
- `bg-qr` (string): Background color of the QR code. Default: `#FFFFFF`.
- `format` (string): Output format of the image. Supported values: `jpg`, `jpeg`, `png`. Default: `png`.
- `download` (boolean): Whether to download the image. Default: `false`.
- `filename` (string): Custom filename for the downloaded image.

## Responses

### Success
- **200 OK**: Returns the generated label image.
  - Headers:
    - `Content-Type`: `image/png`, `image/jpeg`, or `image/jpg`
    - `Cache-Control`: `public, max-age=600`
    - `Content-Disposition`: (if `download=true`) `attachment; filename="<filename>"`

### Errors
- **400 Bad Request**: Missing required parameters or invalid input.
- **405 Method Not Allowed**: Request method is not `GET`.
- **500 Internal Server Error**: Failed to generate the label.

## Example Request
```http
GET /api/media/label?name=JohnDoe&qr-content=https://example.com&format=png&download=true
```