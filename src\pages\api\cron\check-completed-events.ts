import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Note: This endpoint can be called by anyone, but it's safe because:
  // 1. It only reads public event data
  // 2. It only sends emails to event owners
  // 3. No sensitive data is exposed
  // 4. The worst case is duplicate emails, which are prevented by database flags

  try {
    const db = Database.getInstance();

    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development' ||
                         process.env.NEXT_PUBLIC_APP_URL?.includes('localhost');

    let eventsToCheck;

    if (isDevelopment) {
      // In development, only process specific test events to avoid emailing real users
      const testEventIds = ['E2i8pe45d']; // Add more test event IDs here as needed

      console.log('🧪 DEVELOPMENT MODE: Only processing test events:', testEventIds);

      eventsToCheck = [];
      for (const eventId of testEventIds) {
        try {
          const event = await db.readData('events', eventId);
          if (event) {
            // Check if this test event should be processed (same logic as production)
            const eventDate = new Date(event.eventDate);
            const twentyFourHoursAgo = new Date();
            twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

            if (eventDate < twentyFourHoursAgo && event.status !== 'completed') {
              eventsToCheck.push({ id: eventId, ...event });
            }
          }
        } catch (error) {
          console.error(`Error checking test event ${eventId}:`, error);
        }
      }
    } else {
      // In production, get all events that should be checked for completion
      eventsToCheck = await db.getEventsToCheckForCompletion();
    }
    
    let processedCount = 0;
    let errorCount = 0;
    const results = [];

    for (const event of eventsToCheck) {
      try {
        // Mark event as completed and trigger post-event actions
        await db.markEventAsCompleted(event.id, event.ownerAccountId);
        
        processedCount++;
        results.push({
          eventId: event.id,
          eventName: event.eventName,
          status: 'processed'
        });
        
        console.log(`Processed completed event: ${event.eventName} (${event.id})`);
        
      } catch (error) {
        errorCount++;
        results.push({
          eventId: event.id,
          eventName: event.eventName,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        
        console.error(`Error processing event ${event.id}:`, error);
      }
    }

    return res.status(200).json({
      success: true,
      message: `Processed ${processedCount} events, ${errorCount} errors`,
      totalEvents: eventsToCheck.length,
      processedCount,
      errorCount,
      results,
      environment: isDevelopment ? 'development' : 'production',
      testMode: isDevelopment,
      note: isDevelopment
        ? '🧪 Development mode: Only processing test events to avoid emailing real users'
        : '🚀 Production mode: Processing all completed events'
    });

  } catch (error) {
    console.error('Error in check-completed-events cron job:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
