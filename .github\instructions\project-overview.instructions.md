# Project Overview

This project is an event management platform designed to simplify the process of creating, managing, and attending events. It includes features for hosts, guests, and partners (venues or event booking centers).

## Key Features
- **Event Creation and Management**: Hosts can create events, manage guest lists, and track RSVPs.
- **Partner Panel**: Partners can create events for their clients and manage venues, locations, and payments.
- **Guest Experience**: Guests can RSVP to events, receive QR codes, and access event details.
- **Analytics**: Track user engagement, RSVP rates, and event success metrics.
- **Payment Integration**: Stripe is used for processing payments for paid events.

## Tech Stack
- **Frontend**: Next.js, Tailwind CSS
- **Backend**: Firebase, Node.js
- **Database**: Firestore
- **Analytics**: Google Analytics, Mixpanel
- **Payment**: Stripe
