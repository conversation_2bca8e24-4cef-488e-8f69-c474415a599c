# Repo Overview

This project is a **Next.js** application written in TypeScript.  It uses **pnpm** as the package manager and is configured for Firebase/Firestore with NextAuth for authentication.  Analytics, short link generation, UTM tracking and invitation management are implemented in the `src/lib` folder.  Most pages are under `src/pages` and Next.js API routes live inside `src/pages/api`.

Key directories:

- `src/components` – React components.
- `src/lib` – utilities (database, auth, analytics, shortlink service, etc.).
- `src/pages` – Next.js pages and API routes.
- `docs` – design documents and feature notes.
- `spec-docs` – specification documents for APIs and features.
- `cypress` – end‑to‑end tests using Cypress + Cucumber.
- `scripts` – helper scripts (deployment, JWT generation, etc.).

## Getting Started

1. Install dependencies using **pnpm**:
   ```bash
   pnpm install
   ```
2. Run the development server:
   ```bash
   pnpm dev       # alias for `next dev`
   ```
3. Build for production:
   ```bash
   pnpm build
   ```
4. Lint the code:
   ```bash
   pnpm lint
   ```
5. Run Storybook for UI components:
   ```bash
   pnpm storybook
   ```
6. End‑to‑end tests (headless CI run):
   ```bash
   pnpm test:e2e:ci
   ```
   For interactive mode use `pnpm test:e2e` or `pnpm test:e2e:dev`.
7. Storybook tests are configured via **vitest** – run them with:
   ```bash
   pnpm vitest run
   ```

Environment variables are stored in `.env.local` (for development) and `.env.production` (for production).  See `scripts/generate-jwt.js` for generating a JWT token for the shortlink service.

Additional documentation is available in the `docs/` and `spec-docs/` folders.
