/**
 * Simple test for URL grouping functionality
 */

// Inline implementation for testing
function groupUrl(url) {
  if (!url || typeof url !== 'string') {
    return url
  }

  // Remove query parameters and hash for pattern matching
  const urlPath = url.split('?')[0].split('#')[0]

  const patterns = [
    // Event-related routes
    { pattern: /^\/event\/[^\/]+$/, replacement: '/event/*' },
    { pattern: /^\/event\/[^\/]+\/invites$/, replacement: '/event/*/invites' },
    { pattern: /^\/event\/[^\/]+\/invites\/[^\/]+$/, replacement: '/event/*/invites/*' },
    { pattern: /^\/event\/[^\/]+\/invites\/[^\/]+\/edit$/, replacement: '/event/*/invites/*/edit' },
    { pattern: /^\/event\/[^\/]+\/rsvp\/[^\/]+$/, replacement: '/event/*/rsvp/*' },
    { pattern: /^\/event\/[^\/]+\/settings$/, replacement: '/event/*/settings' },
    { pattern: /^\/event\/[^\/]+\/analytics$/, replacement: '/event/*/analytics' },
    { pattern: /^\/event\/[^\/]+\/guests$/, replacement: '/event/*/guests' },
    { pattern: /^\/event\/[^\/]+\/messages$/, replacement: '/event/*/messages' },

    // Standalone RSVP routes (if they exist separately from event context)
    { pattern: /^\/rsvp\/[^\/]+$/, replacement: '/rsvp/*' },
    { pattern: /^\/rsvp\/[^\/]+\/success$/, replacement: '/rsvp/*/success' },

    // User-related routes
    { pattern: /^\/user\/[^\/]+$/, replacement: '/user/*' },
    { pattern: /^\/user\/[^\/]+\/profile$/, replacement: '/user/*/profile' },
    { pattern: /^\/user\/[^\/]+\/events$/, replacement: '/user/*/events' }
  ]

  // Apply each grouping pattern
  for (const { pattern, replacement } of patterns) {
    if (pattern.test(urlPath)) {
      return urlPath.replace(pattern, replacement)
    }
  }

  // If no specific pattern matches, return the original path
  return urlPath
}

function extractUrlMetadata(originalUrl, groupedUrl) {
  const metadata = {
    page_group: groupedUrl,
    original_path: originalUrl
  }

  // Extract event ID from various event-related URLs
  if (groupedUrl.includes('/event/')) {
    const eventIdMatch = originalUrl.match(/\/event\/([^\/\?]+)/)
    if (eventIdMatch) {
      metadata.event_id = eventIdMatch[1]
      metadata.custom_parameter_1 = eventIdMatch[1] // For GA4 custom parameters

      // Add event context for better filtering
      if (groupedUrl.includes('/invites')) {
        metadata.event_context = 'invites_management'
      } else if (groupedUrl.includes('/rsvp')) {
        metadata.event_context = 'rsvp_flow'
      } else if (groupedUrl.includes('/settings')) {
        metadata.event_context = 'event_settings'
      } else if (groupedUrl.includes('/analytics')) {
        metadata.event_context = 'event_analytics'
      } else if (groupedUrl.includes('/guests')) {
        metadata.event_context = 'guest_management'
      } else if (groupedUrl.includes('/messages')) {
        metadata.event_context = 'event_messaging'
      } else {
        metadata.event_context = 'event_details'
      }
    }
  }

  // Extract invite ID from invite-related URLs
  if (groupedUrl.includes('/invites/') && !groupedUrl.endsWith('/invites')) {
    const inviteIdMatch = originalUrl.match(/\/invites\/([^\/\?]+)/)
    if (inviteIdMatch) {
      metadata.invite_id = inviteIdMatch[1]
      metadata.custom_parameter_2 = inviteIdMatch[1] // For GA4 custom parameters

      // Also extract event ID if present in invite URLs
      const eventIdFromInviteMatch = originalUrl.match(/\/event\/([^\/]+)\/invites/)
      if (eventIdFromInviteMatch) {
        metadata.event_id = eventIdFromInviteMatch[1]
        metadata.custom_parameter_1 = eventIdFromInviteMatch[1]
        metadata.event_context = 'invite_details'
      }
    }
  }

  // Extract invite ID from RSVP URLs that follow /event/[eventId]/rsvp/[inviteId] pattern
  if (groupedUrl.includes('/event/*/rsvp/*')) {
    const rsvpInviteIdMatch = originalUrl.match(/\/event\/[^\/]+\/rsvp\/([^\/\?]+)/)
    if (rsvpInviteIdMatch) {
      metadata.invite_id = rsvpInviteIdMatch[1]
      metadata.custom_parameter_2 = rsvpInviteIdMatch[1] // For GA4 custom parameters

      // Event ID should already be extracted above in the event section
      // But let's ensure it's captured for RSVP context
      const eventIdFromRsvpMatch = originalUrl.match(/\/event\/([^\/]+)\/rsvp/)
      if (eventIdFromRsvpMatch) {
        metadata.event_id = eventIdFromRsvpMatch[1]
        metadata.custom_parameter_1 = eventIdFromRsvpMatch[1]
        metadata.event_context = 'rsvp_flow'
      }
    }
  }

  // Handle standalone RSVP URLs (if they exist outside of event context)
  else if (groupedUrl.includes('/rsvp/') && !groupedUrl.includes('/event/')) {
    const standaloneRsvpIdMatch = originalUrl.match(/\/rsvp\/([^\/\?]+)/)
    if (standaloneRsvpIdMatch) {
      metadata.rsvp_id = standaloneRsvpIdMatch[1]
      metadata.custom_parameter_3 = standaloneRsvpIdMatch[1] // For GA4 custom parameters
    }
  }

  // Add page type for easier filtering
  if (groupedUrl.includes('/event/')) {
    metadata.page_type = 'event'
  } else if (groupedUrl.includes('/rsvp/')) {
    metadata.page_type = 'rsvp'
  } else if (groupedUrl.includes('/user/')) {
    metadata.page_type = 'user'
  } else {
    metadata.page_type = 'general'
  }

  return metadata
}

function createPageTitle(groupedUrl) {
  const titleMap = {
    '/': 'Home',
    '/about': 'About',
    '/contact': 'Contact',
    '/pricing': 'Pricing',
    '/login': 'Login',
    '/signup': 'Sign Up',
    '/dashboard': 'Dashboard',

    // Event pages
    '/event/*': 'Event Details',
    '/event/*/invites': 'Event Invites',
    '/event/*/invites/*': 'Invite Details',
    '/event/*/invites/*/edit': 'Edit Invite',
    '/event/*/rsvp/*': 'Event RSVP',
    '/event/*/settings': 'Event Settings',
    '/event/*/analytics': 'Event Analytics',
    '/event/*/guests': 'Event Guests',
    '/event/*/messages': 'Event Messages',

    // RSVP pages
    '/rsvp/*': 'RSVP Form',
    '/rsvp/*/success': 'RSVP Success',

    // User pages
    '/user/*': 'User Profile',
    '/user/*/profile': 'User Profile Settings',
    '/user/*/events': 'User Events'
  }

  return titleMap[groupedUrl] || groupedUrl || 'Unknown Page'
}

// Test URLs
const testUrls = [
  '/event/abc123',
  '/event/xyz789/invites',
  '/event/def456/invites/inv001',
  '/event/ghi789/invites/inv002/edit',
  '/event/jkl012/rsvp/inv003',
  '/event/mno345/settings',
  '/rsvp/rsvp123456',
  '/rsvp/rsvp789012/success',
  '/user/user123',
  '/user/user456/profile',
  '/user/user789/events',
  '/',
  '/about',
  '/dashboard'
]

console.log('=== URL Grouping Test Results ===\n')

testUrls.forEach((url, index) => {
  const grouped = groupUrl(url)
  const title = createPageTitle(grouped)
  const metadata = extractUrlMetadata(url, grouped)

  console.log(`${index + 1}. Original: ${url}`)
  console.log(`   Grouped:  ${grouped}`)
  console.log(`   Title:    ${title}`)
  console.log(`   Event ID: ${metadata.event_id || 'N/A'}`)
  console.log(`   Invite ID: ${metadata.invite_id || 'N/A'}`)
  console.log(`   RSVP ID: ${metadata.rsvp_id || 'N/A'}`)
  console.log(`   Context: ${metadata.event_context || 'N/A'}`)
  console.log(`   Custom Params: ${JSON.stringify({
    param1: metadata.custom_parameter_1,
    param2: metadata.custom_parameter_2,
    param3: metadata.custom_parameter_3
  })}`)
  console.log('')
})

console.log('✅ URL grouping test completed successfully!')
console.log('\nEnhanced Analytics Features:')
console.log('- Event IDs are passed as custom_parameter_1 for filtering')
console.log('- Invite IDs are passed as custom_parameter_2 for filtering')
console.log('- RSVP IDs are passed as custom_parameter_3 for filtering')
console.log('- Event context provides additional segmentation')
console.log('\nGoogle Analytics Benefits:')
console.log('- Filter reports by specific event ID: custom_parameter_1 = "abc123"')
console.log('- Filter reports by specific invite ID: custom_parameter_2 = "inv456"')
console.log('- Filter reports by context: event_context = "invites_management"')
console.log('- Group similar pages while preserving ability to drill down by ID')
