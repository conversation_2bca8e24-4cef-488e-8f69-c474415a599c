export interface PartnerRequestFormData {
  businessName: string;
  taxId: string;
  phoneNumber: string;
  address?: string;
  hasMultipleVenues: boolean;
}

export interface PartnerRequestResponse {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Submit a partner request to the API
 */
export async function submitPartnerRequest(formData: PartnerRequestFormData): Promise<PartnerRequestResponse> {
  try {
    const response = await fetch('/api/partner/request', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || 'Failed to submit partner request'
      };
    }

    return {
      success: true,
      message: data.message || 'Partner request submitted successfully'
    };
  } catch (error) {
    console.error('Error submitting partner request:', error);
    return {
      success: false,
      error: 'Network error. Please check your connection and try again.'
    };
  }
}
