// lib/page-auth.ts
import { getServerSession } from "next-auth/next"
import { authConfig } from "@/auth"
import { NextPageContext } from "next"
import { IncomingMessage, ServerResponse } from "http"
import { Session } from "next-auth"

type GetServerSidePropsContext = {
  req: IncomingMessage & { cookies: { [key: string]: string } }
  res: ServerResponse
}

export function withPageAuth(getServerSidePropsFunc?: (context: NextPageContext, session: Session) => Promise<any>) {
  return async (context: NextPageContext) => {
    // Only run on the server side
    if (typeof window !== 'undefined') {
      throw new Error('withPageAuth can only be used on the server side');
    }

    if (!context.req || !context.res) {
      throw new Error('Missing request or response object');
    }

    try {
      const session = await getServerSession(
        context.req as GetServerSidePropsContext['req'],
        context.res as GetServerSidePropsContext['res'],
        authConfig
      );

      if (!session) {
        return {
          redirect: {
            destination: '/auth/signin',
            permanent: false,
          },
        };
      }

      const props = getServerSidePropsFunc
        ? await getServerSidePropsFunc(context, session)
        : {};

      return {
        props: {
          ...props,
          session,
        },
      };
    } catch (error) {
      console.error('Error in withPageAuth:', error);
      return {
        redirect: {
          destination: '/auth/error',
          permanent: false,
        },
      };
    }
  };
}
