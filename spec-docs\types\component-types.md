# Component Types - IAC Universe v1

This document catalogues React component prop types and interfaces used throughout the IAC Universe v1 application.

## UI Component Types

### Base Component Props

Most UI components extend these base patterns:

```typescript
// Common base props for all components
interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  id?: string;
}

// Interactive component base
interface InteractiveComponentProps extends BaseComponentProps {
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent) => void;
}

// Form component base
interface FormComponentProps extends BaseComponentProps {
  name?: string;
  required?: boolean;
  error?: string;
  placeholder?: string;
}
```

### Button Component Types

**File**: `src/components/ui/button.tsx`

```typescript
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  asChild?: boolean;
  loading?: boolean;
}
```

**Usage Patterns**:
```typescript
// Primary action button
<Button variant="default" size="lg">Save Event</Button>

// Destructive action
<Button variant="destructive" onClick={handleDelete}>Delete</Button>

// Loading state
<Button loading={isSubmitting}>Creating Event...</Button>
```

### Input Component Types

**File**: `src/components/ui/input.tsx`

```typescript
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helper?: string;
  leftAddon?: React.ReactNode;
  rightAddon?: React.ReactNode;
}
```

### Form Component Types

**File**: `src/components/ui/form.tsx`

```typescript
interface FormFieldProps {
  control: Control<any>;
  name: string;
  render: ({ field }: { field: any }) => React.ReactElement;
}

interface FormItemProps extends BaseComponentProps {
  children: React.ReactNode;
}

interface FormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean;
}

interface FormMessageProps extends BaseComponentProps {
  children?: React.ReactNode;
}
```

### Dialog/Modal Component Types

**File**: `src/components/ui/dialog.tsx`

```typescript
interface DialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  modal?: boolean;
  children: React.ReactNode;
}

interface DialogContentProps extends BaseComponentProps {
  children: React.ReactNode;
  forceMount?: true;
}

interface DialogHeaderProps extends BaseComponentProps {
  children: React.ReactNode;
}

interface DialogTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
}
```

### Table Component Types

**File**: `src/components/ui/table.tsx`

```typescript
interface TableProps extends React.TableHTMLAttributes<HTMLTableElement> {
  children: React.ReactNode;
}

interface TableHeaderProps extends React.HTMLAttributes<HTMLTableSectionElement> {
  children: React.ReactNode;
}

interface TableBodyProps extends React.HTMLAttributes<HTMLTableSectionElement> {
  children: React.ReactNode;
}

interface TableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  children: React.ReactNode;
}

interface TableHeadProps extends React.ThHTMLAttributes<HTMLTableCellElement> {
  children: React.ReactNode;
}

interface TableCellProps extends React.TdHTMLAttributes<HTMLTableCellElement> {
  children: React.ReactNode;
}
```

## Feature Component Types

### Event Management Components

#### EventForm Component
**File**: `src/components/EventForm.tsx` (implied)

```typescript
interface EventFormProps {
  event?: Event;
  onSubmit: (data: EventFormData) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  venues?: Venue[]; // For partner-created events
}

interface EventFormData {
  eventName: string;
  eventDate: Date;
  start: string;
  end: string;
  location: string;
  host: string;
  message?: string;
  timezone?: string;
  venueId?: string;
  locationId?: string;
  venueName?: string;
  locationName?: string;
  maxInvites?: number;
  hostEmail?: string;
  messageStyle?: 'personalised' | 'casual_friendly' | 'formal_professional' | 'fun_energetic' | 'business_professional' | 'creative_unique';
}
```

#### EventList Component
**File**: `src/components/EventList.tsx` (implied)

```typescript
interface EventListProps {
  events: EventListItem[];
  loading?: boolean;
  onEventClick?: (eventId: string) => void;
  onEventEdit?: (eventId: string) => void;
  onEventDelete?: (eventId: string) => void;
  filter?: EventFilter;
  onFilterChange?: (filter: EventFilter) => void;
}

interface EventFilter {
  status?: 'active' | 'pending_payment' | 'completed';
  dateRange?: {
    start: Date;
    end: Date;
  };
  search?: string;
}
```

### Invite Management Components

#### InviteForm Component
```typescript
interface InviteFormProps {
  eventId: string;
  onSubmit: (invites: CSVInvite[]) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  maxInvites?: number;
}
```

#### InviteList Component
```typescript
interface InviteListProps {
  invites: EventInviteListItem[];
  loading?: boolean;
  onInviteSelect?: (inviteIds: string[]) => void;
  onInviteEdit?: (inviteId: string) => void;
  onInviteDelete?: (inviteIds: string[]) => void;
  onBulkAction?: (action: BulkAction, inviteIds: string[]) => void;
  filter?: InviteFilter;
}

interface BulkAction {
  type: 'delete' | 'move_group' | 'send_email' | 'send_reminder';
  payload?: any;
}

interface InviteFilter {
  status?: InviteStatus[];
  group?: string;
  search?: string;
  hasResponse?: boolean;
}
```

#### RSVPDialog Component
**File**: `src/components/RSVPDialog.tsx`

```typescript
interface RSVPDialogProps {
  invite: EventInvite;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (response: RSVPResponse) => Promise<void>;
  loading?: boolean;
}

interface RSVPResponse {
  status: 'accepted' | 'declined';
  adults: number;
  children: number;
  message?: string;
  dietaryRestrictions?: string;
}
```

### Contact Management Components

#### GroupSelector Component
**File**: `src/components/GroupSelector.tsx`

```typescript
interface GroupSelectorProps {
  groups: string[];
  selectedGroups: string[];
  onGroupSelect: (groups: string[]) => void;
  onCreateGroup?: (groupName: string) => void;
  allowMultiple?: boolean;
  placeholder?: string;
}
```

#### SavedContactGroupsSheet Component
**File**: `src/components/SavedContactGroupsSheet.tsx`

```typescript
interface SavedContactGroupsSheetProps {
  organizationId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGroupSelect?: (group: SavedContactGroup) => void;
  onGroupImport?: (contacts: ContactGroupData) => void;
}
```

### User & Organization Components

#### UserProfile Component
**File**: `src/components/UserProfile.tsx`

```typescript
interface UserProfileProps {
  user: UserProfile;
  onUpdate: (updates: Partial<UserProfile>) => Promise<void>;
  onLinkGoogle?: () => Promise<void>;
  onUnlinkGoogle?: () => Promise<void>;
  onChangePassword?: (oldPassword: string, newPassword: string) => Promise<void>;
  loading?: boolean;
  editable?: boolean;
}
```

#### ProfileCompletionForm Component
**File**: `src/components/ProfileCompletionForm.tsx`

```typescript
interface ProfileCompletionFormProps {
  user: Partial<UserProfile>;
  onSubmit: (profile: UserProfile) => Promise<void>;
  onSkip?: () => void;
  loading?: boolean;
  required?: (keyof UserProfile)[];
}
```

### Authentication Components

#### SignIn Component
**File**: `src/components/SignIn.tsx`

```typescript
interface SignInProps {
  onSuccess?: (user: UserProfile) => void;
  onError?: (error: Error) => void;
  redirectTo?: string;
  providers?: AuthProvider[];
}

interface AuthProvider {
  id: string;
  name: string;
  type: 'oauth' | 'email' | 'credentials';
  enabled: boolean;
}
```

#### GoogleSignInButton Component
**File**: `src/components/GoogleSignInButton.tsx`

```typescript
interface GoogleSignInButtonProps {
  onSuccess?: (user: UserProfile) => void;
  onError?: (error: Error) => void;
  loading?: boolean;
  disabled?: boolean;
  text?: string;
  variant?: 'signin' | 'signup' | 'link';
}
```

#### PasswordSignIn Component
**File**: `src/components/PasswordSignIn.tsx`

```typescript
interface PasswordSignInProps {
  onSubmit: (credentials: LoginCredentials) => Promise<void>;
  onForgotPassword?: () => void;
  loading?: boolean;
  error?: string;
}

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}
```

### Admin Components

#### ShortlinkForm Component
**File**: `src/components/admin/ShortlinkForm.tsx`

```typescript
interface ShortlinkFormProps {
  shortlink?: SL_ShortLinkResponse;
  onSubmit: (request: SL_ShortLinkRequest) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
}
```

### Partner Portal Components

#### VenueForm Component (implied)
```typescript
interface VenueFormProps {
  venue?: Venue;
  onSubmit: (venue: VenueFormData) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  organizationId: string;
}

interface VenueFormData {
  name: string;
  address: string;
  description?: string;
  capacity: number;
  locations: VenueLocationData[];
}

interface VenueLocationData {
  name: string;
  description?: string;
}
```

#### TimeSlotSelector Component (implied)
```typescript
interface TimeSlotSelectorProps {
  venueId: string;
  locationId?: string;
  selectedDate: Date;
  onSlotSelect: (slot: TimeSlot) => void;
  bookedSlots?: TimeSlot[];
  loading?: boolean;
}
```

### Layout Components

#### Layout Component
**File**: `src/components/Layout.tsx`

```typescript
interface LayoutProps {
  children: React.ReactNode;
  user?: UserProfile;
  navigation?: NavigationItem[];
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

interface NavigationItem {
  label: string;
  href: string;
  icon?: string;
  active?: boolean;
  permissions?: string[];
}
```

#### ProtectedLayout Component
**File**: `src/components/layouts/ProtectedLayout.tsx`

```typescript
interface ProtectedLayoutProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
}
```

#### PartnerLayout Component
**File**: `src/components/layouts/PartnerLayout.tsx`

```typescript
interface PartnerLayoutProps {
  children: React.ReactNode;
  organizationId: string;
  user: UserProfile;
  navigation?: PartnerNavigationItem[];
}

interface PartnerNavigationItem extends NavigationItem {
  venueRestricted?: boolean;
  venueIds?: string[];
}
```

### Media & Print Components

#### InvitePreview Component
**File**: `src/components/invitePreview.tsx`

```typescript
interface InvitePreviewProps {
  invite: EventInvite;
  event: Event;
  printSettings?: PrintSettings;
  showQR?: boolean;
  onSettingsChange?: (settings: PrintSettings) => void;
}
```

#### LabelPreview Component
**File**: `src/components/labelPreview.tsx`

```typescript
interface LabelPreviewProps {
  invite: EventInvite;
  event: Event;
  options: LabelPrintingOptions;
  onOptionsChange?: (options: LabelPrintingOptions) => void;
}
```

#### QRCode Component
**File**: `src/components/ui/qrcode.tsx`

```typescript
interface QRCodeProps {
  value: string;
  size?: number;
  level?: 'L' | 'M' | 'Q' | 'H';
  includeMargin?: boolean;
  imageSettings?: {
    src: string;
    height: number;
    width: number;
    excavate: boolean;
  };
}
```

### Feedback Components

#### FeedbackButton Component
**File**: `src/components/FeedbackButton.tsx`

```typescript
interface FeedbackButtonProps {
  position?: 'fixed' | 'inline';
  variant?: 'button' | 'fab' | 'link';
  onSubmit?: (feedback: FeedbackFormData) => Promise<void>;
  disabled?: boolean;
}
```

## Hook Return Types

### Custom Hook Types

```typescript
// Event management hooks
interface UseEventsReturn {
  events: EventListItem[];
  loading: boolean;
  error: Error | null;
  createEvent: (data: EventFormData) => Promise<Event>;
  updateEvent: (id: string, data: Partial<EventFormData>) => Promise<Event>;
  deleteEvent: (id: string) => Promise<void>;
  refetch: () => Promise<void>;
}

// Invite management hooks
interface UseInvitesReturn {
  invites: EventInviteListItem[];
  loading: boolean;
  error: Error | null;
  createInvites: (invites: CSVInvite[]) => Promise<EventInvite[]>;
  updateInvite: (id: string, data: Partial<EventInvite>) => Promise<EventInvite>;
  deleteInvites: (ids: string[]) => Promise<void>;
  sendInviteEmails: (ids: string[]) => Promise<void>;
  refetch: () => Promise<void>;
}

// User management hooks
interface UseUsersReturn {
  users: UserProfile[];
  currentUser: UserProfile | null;
  loading: boolean;
  error: Error | null;
  updateProfile: (updates: Partial<UserProfile>) => Promise<UserProfile>;
  linkGoogle: () => Promise<void>;
  unlinkGoogle: () => Promise<void>;
}

// Organization management hooks
interface UseOrganizationsReturn {
  organizations: Organization[];
  currentOrganization: Organization | null;
  loading: boolean;
  error: Error | null;
  switchOrganization: (orgId: string) => Promise<void>;
  updateOrganization: (id: string, data: Partial<Organization>) => Promise<Organization>;
}
```

## Component Pattern Types

### Render Props Pattern
```typescript
interface RenderPropsComponent<T> {
  children: (data: T) => React.ReactNode;
  loading?: React.ReactNode;
  error?: React.ReactNode;
}

// Example: Data fetching component
interface DataFetcherProps<T> extends RenderPropsComponent<T> {
  url: string;
  params?: Record<string, any>;
}
```

### Compound Component Pattern
```typescript
// Example: Card compound component
interface CardCompoundComponent extends React.FC<CardProps> {
  Header: React.FC<CardHeaderProps>;
  Body: React.FC<CardBodyProps>;
  Footer: React.FC<CardFooterProps>;
}
```

### Higher-Order Component Types
```typescript
interface WithAuthProps {
  user: UserProfile;
  permissions: string[];
  isAuthenticated: boolean;
}

type WithAuth = <P extends object>(
  Component: React.ComponentType<P>
) => React.ComponentType<Omit<P, keyof WithAuthProps>>;

interface WithPermissionsConfig {
  requiredPermissions: string[];
  fallback?: React.ComponentType;
  requireAll?: boolean;
}

type WithPermissions = <P extends object>(
  config: WithPermissionsConfig
) => (Component: React.ComponentType<P>) => React.ComponentType<P>;
```

## Form Component Patterns

### Controlled Component Types
```typescript
interface ControlledInputProps<T> {
  value: T;
  onChange: (value: T) => void;
  name?: string;
  error?: string;
  disabled?: boolean;
}

// Specific controlled components
interface ControlledSelectProps extends ControlledInputProps<string> {
  options: SelectOption[];
  placeholder?: string;
}

interface ControlledDatePickerProps extends ControlledInputProps<Date> {
  minDate?: Date;
  maxDate?: Date;
  format?: string;
}

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}
```

### Form Validation Types
```typescript
interface FormValidation<T> {
  rules: ValidationRules<T>;
  errors: ValidationErrors<T>;
  isValid: boolean;
  validate: (field?: keyof T) => boolean;
  reset: () => void;
}

type ValidationRules<T> = {
  [K in keyof T]?: ValidationRule<T[K]>[];
};

type ValidationErrors<T> = {
  [K in keyof T]?: string;
};

interface ValidationRule<T> {
  validator: (value: T) => boolean;
  message: string;
}
```

## Event Handler Types

### Common Event Handlers
```typescript
// Click handlers
type ClickHandler = (event: React.MouseEvent<HTMLElement>) => void;
type ButtonClickHandler = (event: React.MouseEvent<HTMLButtonElement>) => void;

// Form handlers
type FormSubmitHandler<T> = (data: T) => Promise<void> | void;
type InputChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => void;
type SelectChangeHandler = (value: string) => void;

// Custom event handlers
type EventSelectHandler = (eventId: string) => void;
type InviteSelectHandler = (inviteIds: string[]) => void;
type BulkActionHandler = (action: BulkAction, itemIds: string[]) => void;
```

## State Management Types

### Component State Types
```typescript
// Loading states
interface LoadingState {
  loading: boolean;
  error: Error | null;
  data: any;
}

// Form states
interface FormState<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isValid: boolean;
  isSubmitting: boolean;
}

// Modal states
interface ModalState {
  isOpen: boolean;
  content?: React.ReactNode;
  props?: Record<string, any>;
}

// Filter states
interface FilterState<T> {
  filters: T;
  appliedFilters: T;
  hasChanges: boolean;
  apply: () => void;
  reset: () => void;
  clear: () => void;
}
```

## Migration Considerations

When migrating component types to the new universe system:

1. **Props Standardization**: Standardize common props across similar components
2. **Generic Patterns**: Create reusable generic component patterns
3. **Type Safety**: Improve type safety with stricter prop validation
4. **Composition**: Favor composition over inheritance for component types
5. **Performance**: Use React.memo and callback optimization types
6. **Accessibility**: Include ARIA props and accessibility types
7. **Testing**: Add testable props and test utility types
8. **Documentation**: Include JSDoc comments for complex prop types

## Best Practices

1. **Prop Naming**: Use consistent prop naming conventions
2. **Optional Props**: Make props optional when reasonable defaults exist
3. **Event Handlers**: Use specific event handler types for better IntelliSense
4. **Generic Components**: Use generics for reusable data-bound components
5. **Default Props**: Define default props with proper TypeScript patterns
6. **Ref Forwarding**: Use proper ref forwarding types for compound components
7. **Children Props**: Use specific children prop types when possible
8. **Component Composition**: Design types that support component composition patterns