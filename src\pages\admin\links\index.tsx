import React, { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { shortlinkService } from '@/lib/shortlink-service';
import { Plus, Search, ExternalLink, Edit, Trash2, Eye, Copy } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';

interface ShortlinkData {
  shortCode: string;
  redirectUrl: string;
  hits?: number;
  createdAt?: string;
}

export default function LinksAdminPage() {
  const [shortlinks, setShortlinks] = useState<ShortlinkData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  // Load shortlinks on component mount
  useEffect(() => {
    loadShortlinks();
  }, []);

  const loadShortlinks = async () => {
    try {
      setLoading(true);
      const data = await shortlinkService.listShortLinks();
      setShortlinks(data);
    } catch (error) {
      console.error('Failed to load shortlinks:', error);
      toast({
        title: "Error",
        description: "Failed to load shortlinks. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (shortCode: string) => {
    if (!window.confirm(`Are you sure you want to delete the shortlink "${shortCode}"?`)) {
      return;
    }

    try {
      await shortlinkService.deleteShortLink(shortCode);
      toast({
        title: "Success",
        description: "Shortlink deleted successfully!",
      });
      loadShortlinks(); // Refresh the list
    } catch (error) {
      console.error('Failed to delete shortlink:', error);
      toast({
        title: "Error",
        description: "Failed to delete shortlink. Please try again.",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = async (shortCode: string) => {
    const url = shortlinkService.getFullUrl(shortCode);
    try {
      await navigator.clipboard.writeText(url);
      toast({
        title: "Copied!",
        description: `Short URL copied to clipboard: ${url}`,
      });
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      toast({
        title: "Error",
        description: "Failed to copy URL to clipboard.",
        variant: "destructive",
      });
    }
  };

  // Filter shortlinks based on search term
  const filteredShortlinks = shortlinks.filter(link =>
    link.shortCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
    link.redirectUrl.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <AdminLayout pageTitle="Shortlinks Management">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Shortlinks</h1>
            <p className="text-sm text-muted-foreground">
              Manage your short URLs and track their performance
            </p>
          </div>
          <Link href="/admin/links/create">
            <Button size={'sm'} variant="primary-button">
              <Plus className="mr-2 h-4 w-4" />
              Create Shortlink
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Links</CardTitle>
              <ExternalLink className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{shortlinks.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {shortlinks.reduce((acc, link) => acc + (link.hits || 0), 0)}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Links</CardTitle>
              <Badge variant="outline" className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{shortlinks.length}</div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <div className="flex items-center space-x-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search shortlinks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w"
          />
        </div>

        {/* Shortlinks Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Shortlinks</CardTitle>
            <CardDescription>
              A list of all your shortlinks and their statistics.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : filteredShortlinks.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  {searchTerm ? 'No shortlinks match your search.' : 'No shortlinks found. Create your first one!'}
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Short Code</TableHead>
                    <TableHead>Destination URL</TableHead>
                    <TableHead>Clicks</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredShortlinks.map((link) => (
                    <TableRow key={link.shortCode}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <code className="px-2 py-1 bg-muted rounded text-xs">
                            {link.shortCode}
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(link.shortCode)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <a
                          href={link.redirectUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline flex items-center max-w truncate"
                        >
                          {link.redirectUrl}
                          <ExternalLink className="ml-1 h-3 w-3" />
                        </a>
                      </TableCell>
                      <TableCell className="text-right">
                        {link.hits || 0}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Link href={`/admin/links/${link.shortCode}`}>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link href={`/admin/links/${link.shortCode}/edit`}>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(link.shortCode)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
