# Google Analytics URL Grouping Implementation

## Overview

This implementation configures Google Analytics to group dynamic URLs with different IDs so they appear as unified patterns rather than individual pages. This is crucial for meaningful analytics reporting when you have many pages with similar patterns but different dynamic segments.

## Problem Solved

**Before:** Google Analytics would show hundreds of separate page entries like:

- `/event/abc123/invites`
- `/event/def456/invites`
- `/event/xyz789/invites`
- etc.

**After:** These now appear grouped as:

- `/event/*/invites` (with aggregate data)

## Implementation Details

### 1. URL Grouping Logic (`src/lib/url-grouping.ts`)

The URL grouping system includes:

- **Pattern Matching**: Regex patterns that identify dynamic URL segments
- **URL Replacement**: Converts dynamic segments to wildcards (\*)
- **Page Titles**: Descriptive titles for each grouped pattern
- **Metadata Extraction**: Extracts IDs for custom dimensions

### 2. Supported URL Patterns

| Original Pattern                      | Grouped Pattern           | Page Title            |
| ------------------------------------- | ------------------------- | --------------------- |
| `/event/[id]`                         | `/event/*`                | Event Details         |
| `/event/[id]/invites`                 | `/event/*/invites`        | Event Invites         |
| `/event/[id]/invites/[inviteId]`      | `/event/*/invites/*`      | Invite Details        |
| `/event/[id]/invites/[inviteId]/edit` | `/event/*/invites/*/edit` | Edit Invite           |
| `/event/[id]/rsvp/[inviteId]`         | `/event/*/rsvp/*`         | Event RSVP            |
| `/event/[id]/settings`                | `/event/*/settings`       | Event Settings        |
| `/event/[id]/analytics`               | `/event/*/analytics`      | Event Analytics       |
| `/event/[id]/guests`                  | `/event/*/guests`         | Event Guests          |
| `/event/[id]/messages`                | `/event/*/messages`       | Event Messages        |
| `/user/[id]`                          | `/user/*`                 | User Profile          |
| `/user/[id]/profile`                  | `/user/*/profile`         | User Profile Settings |
| `/user/[id]/events`                   | `/user/*/events`          | User Events           |

### 3. Google Analytics Configuration

The implementation sends data to Google Analytics with:

1. **Grouped Page Path**: `page_path: "/event/*/invites"` instead of `"/event/abc123/invites"`
2. **Custom Dimensions**: Original URLs and extracted IDs are stored as custom dimensions
3. **Page Titles**: Descriptive titles for better reporting
4. **Enhanced Events**: All events include grouping metadata

### 4. Custom Dimensions Setup

The following custom dimensions are configured:

- `custom_dimension_1`: Page Group (grouped URL)
- `custom_dimension_2`: Original Path (full original URL)
- `custom_dimension_3`: Event ID (extracted from event URLs)
- `custom_dimension_4`: Invite ID (extracted from invite and RSVP URLs)
- `custom_dimension_5`: Event Context (page context within events)
- `custom_dimension_6`: Page Type (general page category)
- `custom_dimension_7`: User ID (extracted from user URLs)
- `custom_dimension_8`: Organization ID (extracted from org URLs)

### 5. Custom Parameters for Filtering (GA4)

For enhanced filtering capabilities, the following custom parameters are sent:

- `custom_parameter_1`: Event ID (for filtering by specific events)
- `custom_parameter_2`: Invite ID (for filtering by specific invites)
- `custom_parameter_3`: User ID (for filtering by specific users)
- `custom_parameter_4`: Organization ID (for filtering by specific organizations)

**🎯 Key Benefit**: You can now filter Google Analytics reports by specific event IDs while still benefiting from grouped URL patterns. For example:

- View all analytics for event "wedding-abc123": Filter by `custom_parameter_1 = "wedding-abc123"`
- Analyze specific invite performance: Filter by `custom_parameter_1 = "event-id"` AND `custom_parameter_2 = "invite-id"`

See [Event ID Filtering Analytics Guide](./Event_ID_Filtering_Analytics.md) for detailed filtering examples.

## Google Analytics Setup

### Step 1: Configure Custom Dimensions

In your Google Analytics property:

1. Go to **Admin > Custom Definitions > Custom Dimensions**
2. Create the following custom dimensions:

| Dimension Name  | Scope | Description                                             |
| --------------- | ----- | ------------------------------------------------------- |
| Page Group      | Event | Grouped URL pattern (e.g., /event/\*/invites)           |
| Original Path   | Event | Full original URL with IDs                              |
| Event ID        | Event | Extracted event identifier                              |
| Invite ID       | Event | Extracted invite identifier (from invite and RSVP URLs) |
| Event Context   | Event | Context within event pages                              |
| Page Type       | Event | General page category                                   |
| User ID         | Event | Extracted user identifier                               |
| Organization ID | Event | Extracted organization identifier                       |

### Step 2: Create Custom Reports

1. **Page Group Performance Report**:

   - Primary dimension: Page Group (custom dimension 1)
   - Metrics: Page views, Users, Sessions, Bounce rate

2. **Event Analysis Report**:

   - Primary dimension: Page Group
   - Secondary dimension: Event ID
   - Filter: Page Group contains "/event/"

### Step 3: Set Up Enhanced Ecommerce (Optional)

If tracking goals/conversions:

```javascript
// Example: Track event creation as conversion
gtag('event', 'conversion', {
  send_to: 'G-0N9CPV0Z0R',
  page_group: '/event/*',
  event_id: 'abc123',
});
```

## Usage Examples

### Viewing Grouped Analytics

In Google Analytics, you'll now see:

**Pages and Screens Report**:

- `/event/*` - 1,250 page views
- `/event/*/invites` - 340 page views
- `/event/*/rsvp/*` - 890 page views
- `/user/*` - 156 page views

Instead of hundreds of individual pages with low counts.

### Custom Explorations

Create custom explorations to:

1. **Analyze user journeys** through grouped page patterns
2. **Track conversion funnels** from event creation to RSVP completion
3. **Monitor popular event features** (invites, settings, analytics pages)

## Testing the Implementation

### Manual Testing

1. Navigate to different pages in your app
2. Check browser's Network tab for GA events
3. Verify `page_path` shows grouped URLs (e.g., `/event/*`)

### Debug Mode

Add `?gtm_debug=1` to your URL to see Google Analytics debug information.

### Test URLs

Use the test file `src/test/url-grouping-test.js` to verify grouping logic:

```bash
node src/test/url-grouping-test.js
```

## Benefits

1. **Cleaner Reports**: Group similar pages for better insights
2. **Better Analysis**: Understand user behavior patterns
3. **Easier Monitoring**: Track performance of page types, not individual pages
4. **Preserved Detail**: Original URLs available in custom dimensions
5. **UTM Compatibility**: Works with existing UTM parameter tracking

## Maintenance

### Adding New URL Patterns

To add new grouping patterns, edit `src/lib/url-grouping.ts`:

```typescript
const URL_GROUPING_PATTERNS = [
  // Add new pattern
  { pattern: /^\/new-feature\/[^\/]+$/, replacement: '/new-feature/*' },
  // ... existing patterns
];
```

### Updating Page Titles

Add corresponding titles in the `createPageTitle` function:

```typescript
const titleMap = {
  '/new-feature/*': 'New Feature Page',
  // ... existing titles
};
```

## Troubleshooting

### Common Issues

1. **URLs not grouping**: Check regex patterns in `URL_GROUPING_PATTERNS`
2. **Missing custom dimensions**: Verify GA custom dimension setup
3. **Data not appearing**: Check GA Real-time reports for immediate feedback

### Debug Commands

```bash
# Test URL grouping logic
node src/test/url-grouping-test.js

# Check TypeScript compilation
npx tsc --noEmit

# Test GA events in browser console
gtag('event', 'page_view', { page_path: '/event/*' })
```

## Next Steps

1. **Monitor Implementation**: Check Google Analytics after 24-48 hours
2. **Create Dashboards**: Build custom dashboards using grouped data
3. **Set Up Alerts**: Monitor unusual patterns in grouped page performance
4. **A/B Testing**: Use grouped URLs for better experiment analysis

---

**Note**: Changes to Google Analytics configuration may take 24-48 hours to appear in reports. Real-time reports will show immediate data.
