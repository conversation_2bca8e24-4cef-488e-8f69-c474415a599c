import { NextApiRequest, NextApiResponse } from 'next';
import sharp from 'sharp';
import QRCode from 'qrcode';
import fetch from 'node-fetch';
import { LRUCache } from 'lru-cache';
import { Database } from '@/lib/database';
import { getEventInviteImageUrl } from '@/lib/storage';
import path from 'path';
import fs from 'fs';
import { RenderLabel } from '@/lib/media/label';
import { getInviteUrl, StringArrayToString } from '@/lib/utils';
import { get } from 'http';
import { Event, InvitePageSize, Orientation } from '@/types';
import { getImageDimensions, RenderInvite } from '@/lib/media/invite';

// Define type for invite data
interface InviteData {
  eventId: string;
  name: string;
  [key: string]: any;
}

// We're no longer using a cache - direct fetch from storage

async function getEventFromDatabase(eventId: string): Promise<Event | null> {
  try {
    const db = Database.getInstance();
    // Normalize the event ID as per the database standards
    const normalizedId = Database.normalizeId(eventId);

    // Get the event document from Firestore
    const eventDoc = await Database.getInstance().readData('events', eventId);    
    
    if (!eventDoc) {
      return null;
    }
    
    return eventDoc as Event;
  } catch (error) {
    console.error('Error fetching event from database:', error);
    throw error;
  }
}

/**
 * Fetches invite details from the database
 * @param inviteId - Invite ID to lookup
 * @returns Invite object with eventId or null if not found
 */
async function getInviteFromDatabase(inviteId: string): Promise<InviteData | null> {
  try {
    const db = Database.getInstance();
    // Normalize the invite ID as per the database standards
    const normalizedId = Database.normalizeId(inviteId);
    
    // Get the invite document from Firestore
    const inviteDoc = await db.readData('invites', normalizedId);
    
    if (!inviteDoc) {
      return null;
    }
    
    return inviteDoc as InviteData;
  } catch (error) {
    console.error('Error fetching invite from database:', error);
    throw error;
  }
}

/**
 * Fetches event background image directly from storage without caching
 * @param eventId - Event ID to fetch image for
 * @returns Image buffer
 */
async function getEventImage(eventId: string): Promise<Buffer> {
  console.log(`Fetching image for event ${eventId} directly from storage`);
  // Get the URL from storage - use invitation-card type for this API
  const result = await getEventInviteImageUrl(eventId, 'invitation-card');
  const url = result?.url || null;

  if (!url) {
    console.warn(`No image URL found for event ${eventId}, using default image`);
    return createDefaultEventImage();
  }

  try {    // Add a timestamp to the URL to prevent caching - make it more aggressive
    const timestamp = Date.now();
    const urlWithTimestamp = url.includes('?')
      ? `${url}&_t=${timestamp}&_cache=false`
      : `${url}?_t=${timestamp}&_cache=false`;

    console.log(`Fetching image from URL: ${urlWithTimestamp}`);

    // Add a timeout of 5 seconds to avoid hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const res = await fetch(urlWithTimestamp, {
      signal: controller.signal,
      // Disable SSL verification for development purposes
      // In production, this should be properly handled with valid certificates
      agent: function(_parsedURL) {
        if (_parsedURL.protocol === 'https:') {
          const https = require('https');
          return new https.Agent({
            rejectUnauthorized: false
          });
        }
        return null;
      }
    });
    
    clearTimeout(timeoutId);
    
    if (!res.ok) throw new Error(`Failed to fetch event image: ${res.statusText}`);
    const buffer = await res.buffer();
    return buffer;
  } catch (error) {
    console.warn(`Failed to fetch event image for ${eventId}: ${error instanceof Error ? error.message : String(error)}`);
    // Return a default/fallback image instead
    return createDefaultEventImage();
  }
}

/**
 * Creates a default background image when the event image cannot be fetched
 * @returns Default image buffer
 */
async function createDefaultEventImage(): Promise<Buffer> {
  try {
    // Create a simple gradient background as fallback
    const width = 800;
    const height = 600;
    
    return await sharp({
      create: {
        width,
        height,
        channels: 4,
        background: { r: 245, g: 247, b: 250, alpha: 1 }
      }
    })
    .composite([
      {
        input: Buffer.from(
          `<svg width="${width}" height="${height}">
            <defs>
              <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#4776E6;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#8E54E9;stop-opacity:1" />
              </linearGradient>
            </defs>
            <rect width="${width}" height="${height}" fill="url(#grad)" />
            <text x="50%" y="50%" font-family="sans-serif" font-size="24" text-anchor="middle" fill="white">Event Background</text>
          </svg>`
        )
      }
    ])
    .png()
    .toBuffer();
  } catch (error) {
    console.error('Error creating default image:', error);
    // If creating a default image fails, create an even simpler fallback
    return await sharp({
      create: {
        width: 800,
        height: 600,
        channels: 3,
        background: { r: 100, g: 100, b: 200 }
      }
    })
    .png()
    .toBuffer();
  }
}

/**
 * API handler to generate an invitation image with QR code
 * @param req - Next.js API request object
 * @param res - Next.js API response object
 * @returns Generated image or error response
 * */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Extract query parameters
  let { 
    // For Dummy Event Invite use only
    eventId,
    name, // optional name for testing

    // For actual invite use only
    inviteId,

    // Override parameters
    format,
    qrxp,
    qryp,
    size,
    orientation,
    labelScale = 1,
    download = false,
    filename,
    cache, // Cache-busting parameter

    // Label customization parameters
    'bg-qr': bgQr,
    'color-qr': colorQr,
    'label-orientation': labelOrientation,
    showBranding
  } = req.query;

  // We're no longer using cache, so we can ignore the cache parameter

  labelScale = parseFloat(StringArrayToString(labelScale)) || 1;

  // Check if inviteId is provided
  if (inviteId) {
    // fetch invite from database and update query parameters
    const invite = await getInviteFromDatabase(String(inviteId));
    if (invite) {
      eventId = invite.eventId;
      name = invite.name;
    } else {
      return res.status(404).json({ error: 'Invite not found' });
    }
  } else {
    // If no inviteId is provided, use the eventId and name from the query
    if (!eventId) {
      return res.status(400).json({ error: 'Missing required parameters: eventId and name are required' });
    }

    // Use the provided name or get the event name from the database
    if (!name) {
      try {
        const event = await getEventFromDatabase(String(eventId));
        name = event?.eventName || "Event Name";
      } catch (error) {
        console.error("Error fetching event name:", error);
        name = "Event Name";
      }
    }

    inviteId = "sample-invite-id"; // Dummy invite ID for testing
  }

  
  // get event from database
  const event = await getEventFromDatabase(StringArrayToString(eventId));
  
  if (!event) {
    return res.status(404).json({ error: 'Event not found' });
  }
  
  // update query parameters
  const inviteSettings = event.printSettings?.inviteSettings;
  
  const inviteUrl = getInviteUrl(StringArrayToString(eventId || ''), StringArrayToString(inviteId || ''));
  
  format = 'jpeg';
  qrxp = StringArrayToString(qrxp || inviteSettings?.labelPosition.x.toString() || '50');
  qryp = StringArrayToString(qryp || inviteSettings?.labelPosition.y.toString() || '80');
  size = StringArrayToString(size || inviteSettings?.pageSize || 'A5');
  orientation = StringArrayToString(orientation || inviteSettings?.orientation || 'portrait');

  // Generate label with customization options
  const labelBuffer = await RenderLabel(
    StringArrayToString(name),
    inviteUrl,
    {
      orientation: StringArrayToString(labelOrientation || event.printSettings?.labelSettings?.orientation || 'portrait') as 'portrait' | 'landscape',
      theme: {
        qr: StringArrayToString(colorQr || event.printSettings?.labelSettings?.qrColor || '#000000'),
        name: StringArrayToString(colorQr || event.printSettings?.labelSettings?.qrColor || '#000000'),
        background: StringArrayToString(bgQr || event.printSettings?.labelSettings?.bgColor || '#FFFFFF'),
      },
      showBranding: true,
    }
  );

  // Get the background image directly from storage
  const backgroundImage = await getEventImage(eventId as string);
  
  try {
    if (format === 'jpeg' || format === 'png') {
      const finalImage = await RenderInvite(
        {
          fileContents: backgroundImage,
          mimeType: `image/png`,
          dimensions: getImageDimensions(size, orientation)
        },
        labelBuffer,
        {
          inviteSettings: {
            pageSize: size as InvitePageSize,
            orientation: orientation as Orientation,
            labelPosition: {
              x: parseFloat(qrxp),
              y: parseFloat(qryp)
            },
            labelScale
          }
        });
      // Set proper content type and return image
      res.setHeader('Content-Type', `image/${format}`);
      res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour on client side
      
      if (download) {
        filename = filename || `invite-${StringArrayToString(eventId)}-${StringArrayToString(inviteId)}.${format}`;
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      }
      
      return res.send(finalImage.fileContents);
    } else {
      return res.status(400).json({ error: `Unsupported output format: ${format}` });
    }
  } catch (err) {
    console.error('Error generating image:', err);
    return res.status(500).json({
      error: 'Failed to generate image',
      details: err instanceof Error ? err.message : String(err)
    });
  }
}