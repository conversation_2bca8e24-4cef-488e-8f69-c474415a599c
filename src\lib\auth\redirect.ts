/**
 * Utility functions for handling authentication redirects with site parameters
 */

import { ALLOWED_SITES, AllowedSite, isAllowedSite, getSiteUrl } from './config';

export type { AllowedSite } from './config';

export interface SignInParams {
  site?: AllowedSite;
  redirectUrl?: string;
  callbackUrl?: string;
}

/**
 * Generate a sign-in URL with proper redirect handling
 */
export function generateSignInUrl(params: SignInParams = {}): string {
  const { site, redirectUrl, callbackUrl } = params;
  const baseSignInUrl = '/auth/signin';
  const searchParams = new URLSearchParams();

  // If site is specified, add it to the URL
  if (site && ALLOWED_SITES[site]) {
    searchParams.set('site', site);
  }

  // If redirectUrl is specified, add it
  if (redirectUrl) {
    searchParams.set('redirectUrl', redirectUrl);
  }

  // If callbackUrl is specified, add it
  if (callbackUrl) {
    searchParams.set('callbackUrl', callbackUrl);
  }

  const queryString = searchParams.toString();
  return queryString ? `${baseSignInUrl}?${queryString}` : baseSignInUrl;
}

/**
 * Generate a callback URL for NextAuth with site parameter
 */
export function generateCallbackUrl(site: AllowedSite): string {
  return `/api/auth/callback?site=${site}`;
}

/**
 * Extract site parameter from URL
 */
export function extractSiteFromUrl(url: string): AllowedSite | null {
  try {
    const urlObj = new URL(url);
    const site = urlObj.searchParams.get('site');
    return site && isAllowedSite(site) ? site : null;
  } catch {
    return null;
  }
}

/**
 * Store redirect information in session storage (client-side only)
 */
export function storeRedirectInfo(params: SignInParams): void {
  if (typeof window === 'undefined') return;

  if (params.site) {
    sessionStorage.setItem('redirectSite', params.site);
  }
  if (params.redirectUrl) {
    sessionStorage.setItem('redirectUrl', params.redirectUrl);
  }
}

/**
 * Retrieve and clear redirect information from session storage
 */
export function getAndClearRedirectInfo(): SignInParams {
  if (typeof window === 'undefined') return {};

  const site = sessionStorage.getItem('redirectSite') as AllowedSite | null;
  const redirectUrl = sessionStorage.getItem('redirectUrl');

  // Clear the stored values
  sessionStorage.removeItem('redirectSite');
  sessionStorage.removeItem('redirectUrl');

  return {
    site: site && isAllowedSite(site) ? site : undefined,
    redirectUrl: redirectUrl || undefined
  };
}
