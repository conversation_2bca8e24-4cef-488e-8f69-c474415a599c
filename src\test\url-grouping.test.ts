/**
 * Test file for URL grouping functionality
 * Run this to verify that dynamic URLs are being grouped correctly
 */

import { groupUrl, createPageTitle, extractUrlMetadata, debugUrlGrouping } from '../lib/url-grouping'

// Test URLs that should be grouped
const testUrls = [
  // Event pages
  '/event/abc123',
  '/event/xyz789/invites',
  '/event/def456/invites/inv001',
  '/event/ghi789/invites/inv002/edit',
  '/event/jkl012/rsvp/inv123',  // Event-based RSVP with invite ID
  '/event/mno345/settings',
  '/event/pqr678/analytics',
  '/event/stu901/guests',
  '/event/vwx234/messages',

  // User pages
  '/user/user123',
  '/user/user456/profile',
  '/user/user789/events',

  // Static pages (should remain unchanged)
  '/',
  '/about',
  '/contact',
  '/pricing',
  '/login',
  '/signup',
  '/dashboard',

  // URLs with query parameters
  '/event/abc123?utm_source=facebook&utm_medium=post',
  '/event/def456/rsvp/inv789?invited_by=friend',  // Event-based RSVP with query params

  // Edge cases
  '/event/very-long-uuid-12345678901234567890/invites',
  '/event/123456789012/settings',
  '/unknown/route/with/params'
]

console.log('=== URL Grouping Test Results ===\n')

// Test each URL
testUrls.forEach((url, index) => {
  const grouped = groupUrl(url)
  const title = createPageTitle(grouped, url)
  const metadata = extractUrlMetadata(url, grouped)

  console.log(`${index + 1}. Testing: ${url}`)
  console.log(`   Grouped: ${grouped}`)
  console.log(`   Title: ${title}`)
  console.log(`   Metadata:`, metadata)
  console.log('')
})

// Test the debug function
console.log('\n=== Debug Output ===')
debugUrlGrouping([
  '/event/test123/invites',
  '/event/test456/rsvp/rsvp789',
  '/user/user123/profile'
])

export { testUrls }
