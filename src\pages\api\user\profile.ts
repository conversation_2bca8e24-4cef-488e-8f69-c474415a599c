import { NextApiRequest, NextApiResponse } from 'next';
import { getToken } from 'next-auth/jwt';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get the current user's token
    const token = await getToken({ req, secret: process.env.AUTH_SECRET });
    if (!token) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get database instance
    const db = Database.getInstance();

    // Handle GET request - fetch user profile
    if (req.method === 'GET') {
      // Get user profile
      const userProfile = await db.getUserProfile(token.sub as string);

      if (!userProfile) {
        return res.status(404).json({ error: 'User profile not found' });
      }

      // Return user profile with Google account info
      return res.status(200).json({
        success: true,
        id: userProfile.id,
        name: userProfile.name,
        email: userProfile.email,
        image: userProfile.image,
        isProfileComplete: userProfile.isProfileComplete,
        hasGoogleLinked: !!(userProfile as any).googleId, // Check if googleId exists
        googleEmail: (userProfile as any).googleEmail || null,
      });
    }

    // Handle PUT request - update user profile
    if (req.method === 'PUT') {
      const { name, email } = req.body;

      if (!name || !email) {
        return res.status(400).json({ error: 'Name and email are required' });
      }

      // Update user profile
      await db.updateUserProfile(token.sub as string, {
        name,
        email,
        isProfileComplete: true
      });

      return res.status(200).json({
        success: true,
        message: 'Profile updated successfully'
      });
    }

    // If not GET or PUT, return method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error handling user profile request:', error);
    return res.status(500).json({ error: 'Failed to process user profile request' });
  }
}