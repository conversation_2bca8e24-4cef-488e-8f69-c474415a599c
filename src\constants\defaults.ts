import { LabelDimensions, LabelPrintingOptions } from "@/types";
import path from "path";

export const DEFAULT_LABEL_PRINT_OPTIONS: LabelPrintingOptions = {
  orientation: 'portrait',
  theme: {
    background: '#ffffff',
    qr: '#000000',
    name: '#000000',
  },
  showBranding: true,
};

export const GetLabelPrintOptions = (options: Partial<LabelPrintingOptions>): LabelPrintingOptions => {
  return {
    ...DEFAULT_LABEL_PRINT_OPTIONS,
    ...options,
    theme: {
      ...DEFAULT_LABEL_PRINT_OPTIONS.theme,
      ...options.theme,
    },
  };
};


export const DEFAULT_LABEL_DIMENSIONS: LabelDimensions = {
  dpi: 300,
  QR: {
    width: 27,
    height: 27,
    x: 1.5,
    y: 1.5,
  },
  LANDSCAPE: {
    width: 60,
    height: 30
  },
  PORTRAIT: {
    width: 30,
    height: 50
  }
};

export function GetLabelDimensions(dimensions: Partial<LabelDimensions> = {}): LabelDimensions {
  return {
    ...DEFAULT_LABEL_DIMENSIONS,
    ...dimensions,
    QR: {
      ...DEFAULT_LABEL_DIMENSIONS.QR,
      ...dimensions.QR,
    },
    LANDSCAPE: {
      ...DEFAULT_LABEL_DIMENSIONS.LANDSCAPE,
      ...dimensions.LANDSCAPE,
    },
    PORTRAIT: {
      ...DEFAULT_LABEL_DIMENSIONS.PORTRAIT,
      ...dimensions.PORTRAIT,
    }
  };
}

// Conversion factor from millimeters to inches (1 inch = 25.4 mm)
export const MM_TO_INCH = 25.4;

// Utility function to resolve paths relative to the project root
export const resolvePath = (relativePath: string): string => {
  return path.join(process.cwd(), relativePath);
};

// Path to the default logo used in label generation
export const DEFAULT_LOGO_PATH = resolvePath("public/iac-logo.png");
