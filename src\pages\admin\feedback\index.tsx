import { useState, useEffect, useCallback } from 'react';
import { GetServerSideProps } from 'next';
import { AdminLayout } from '@/components/layouts/AdminLayout';
import { withAdminAuth } from '@/lib/auth/admin';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, Search, ArrowUpDown, ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { useToast } from '@/components/ui/use-toast';
import {
  Feedback,
  FeedbackListResponse,
  FeedbackStatus,
  feedbackStatusColors,
  formatFeedbackStatus
} from '@/types/feedback';

export default function AdminFeedbackInbox() {
  const [feedback, setFeedback] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [limit, setLimit] = useState(50);
  const [offset, setOffset] = useState(0);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortField, setSortField] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  const fetchFeedback = async () => {
    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('limit', limit.toString());
      params.append('offset', offset.toString());
      if (statusFilter) params.append('status', statusFilter);
      params.append('sortBy', sortField);
      params.append('sortOrder', sortOrder);

      const response = await fetch(`/api/admin/feedback?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch feedback');
      }

      const data: FeedbackListResponse = await response.json();
      setFeedback(data.feedback);
      setTotal(data.pagination.total);
    } catch (error) {
      console.error('Error fetching feedback:', error);
      toast({
        title: 'Error',
        description: 'Failed to load feedback data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const memoizedFetchFeedback = useCallback(fetchFeedback, [
    limit, offset, statusFilter, sortField, sortOrder, searchQuery
  ]);

  useEffect(() => {
    memoizedFetchFeedback();
  }, [memoizedFetchFeedback]);

  const handleSort = (field: string) => {
    if (field === sortField) {
      // Toggle sort order if already sorting by this field
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Default to descending for new sort field
      setSortField(field);
      setSortOrder('desc');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Reset pagination when searching
    setOffset(0);
    fetchFeedback();
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy h:mm a');
  };

  // Truncate long text
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Handle status filter change
  const handleStatusChange = (value: string) => {
    // Use empty string for API filtering when "all" is selected
    setStatusFilter(value === 'all' ? '' : value);
    setOffset(0); // Reset pagination when filter changes
  };

  return (
    <AdminLayout pageTitle="Feedback Inbox">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Feedback Inbox</h1>
          <Button
            size="sm"
            variant="outline"
            onClick={fetchFeedback}
            disabled={loading}
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search feedback..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </form>

          <Select
            value={statusFilter}
            onValueChange={handleStatusChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All statuses</SelectItem>
              <SelectItem value="new">New</SelectItem>
              <SelectItem value="in-triage">In Triage</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Feedback List */}
        <div className="border rounded-md overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px] cursor-pointer" onClick={() => handleSort('createdAt')}>
                  <div className="flex items-center">
                    Date
                    <ArrowUpDown size={16} className="ml-1" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort('message')}>
                  <div className="flex items-center">
                    Message
                    <ArrowUpDown size={16} className="ml-1" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort('category')}>
                  <div className="flex items-center">
                    Category
                    <ArrowUpDown size={16} className="ml-1" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort('status')}>
                  <div className="flex items-center">
                    Status
                    <ArrowUpDown size={16} className="ml-1" />
                  </div>
                </TableHead>
                <TableHead className="w-[100px]">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    Loading feedback...
                  </TableCell>
                </TableRow>
              ) : feedback.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    No feedback found
                  </TableCell>
                </TableRow>
              ) : (
                feedback.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {formatDate(item.createdAt)}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {item.userEmail || 'Anonymous'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{truncateText(item.message, 100)}</TableCell>
                    <TableCell>{item.category}</TableCell>
                    <TableCell>
                      <Badge className={feedbackStatusColors[item.status as FeedbackStatus] || 'bg-gray-100 text-gray-800'}>
                        {formatFeedbackStatus(item.status as FeedbackStatus)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Link href={`/admin/feedback/${item.id}`} passHref>
                        <Button size="sm" variant="outline">
                          View
                        </Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {total > limit && (
          <div className="flex items-center justify-end space-x-2 py-4">
            <span className="text-sm text-muted-foreground">
              Showing {offset + 1}-{Math.min(offset + feedback.length, total)} of {total}
            </span>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setOffset(Math.max(0, offset - limit))}
                    disabled={offset === 0}
                    className="cursor-pointer"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    <span className="sr-only">Previous page</span>
                  </Button>
                </PaginationItem>
                <PaginationItem>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setOffset(offset + limit)}
                    disabled={offset + feedback.length >= total}
                    className="cursor-pointer"
                  >
                    <ChevronRight className="h-4 w-4" />
                    <span className="sr-only">Next page</span>
                  </Button>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}

// Server-side protection for admin routes
export const getServerSideProps: GetServerSideProps = withAdminAuth();
