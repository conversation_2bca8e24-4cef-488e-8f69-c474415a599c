import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Footer } from "@/components/Footer";
import { Header } from "@/components/Header";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/router";
import { HeartHandshake, MapPin } from "lucide-react";
import { Label } from '@/components/ui/label';
import { submitPartnerRequest, PartnerRequestFormData } from '@/lib/partnerRequestClient';
import { useToast } from '@/hooks/use-toast';

export default function PartnersPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Helper function to get user initials
  const getInitials = (name?: string | null) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  // Handle account switching by signing out and redirecting to sign in
  const handleSwitchAccount = async () => {
    try {
      // Store the current page URL in sessionStorage for Google sign-in
      const currentUrl = window.location.pathname + window.location.search;
      sessionStorage.setItem("redirectUrl", currentUrl);
      
      console.log("Switch account - storing currentUrl in sessionStorage:", currentUrl);
      
      await signOut({
        redirect: false
      });
      
      // Redirect to sign in - the stored redirectUrl will be picked up by Google sign-in
      router.push('/auth/signin');
    } catch (error) {
      console.error("Error switching account:", error);
    }
  };
  
  const [formData, setFormData] = useState({
    businessName: '',
    taxId: '',
    phoneNumber: '',
    address: '',
    hasMultipleVenues: false
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!session?.user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to submit a partner request.",
        variant: "destructive",
      });
      return;
    }

    // Validate required fields
    if (!formData.businessName.trim() || !formData.taxId.trim() || !formData.phoneNumber.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields: Business Name, Tax ID, and Phone Number.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await submitPartnerRequest(formData);
      
      if (result.success) {
        toast({
          title: "Request Submitted Successfully! 🎉",
          description: result.message || "We'll contact you within 24 hours.",
        });
        
        // Reset form
        setFormData({
          businessName: '',
          taxId: '',
          phoneNumber: '',
          address: '',
          hasMultipleVenues: false
        });
      } else {
        toast({
          title: "Submission Failed",
          description: result.error || "Please try again later.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Network Error",
        description: "Please check your connection and try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const benefits = [
    {
      iconSvg: '/ExcluciveEventDiscount.svg', // Replace with your SVG file name
      title: 'Exclusive Event Discounts',
      description: 'Offer your customers discounted rates on IAC events when they book through you—boost value and increase loyalty.'
    },
    {
      iconSvg: '/CustomBranding.svg', // Replace with your SVG file name
      title: 'Custom Branding',
      description: 'Run events under your own name with white-labeled branding for a seamless and professional brand experience.'
    },
    {
      iconSvg: '/VerifiedGuestInvites.svg', // Replace with your SVG file name
      title: 'Verified Guest Invites',
      description: 'Prevent misuse with invite verification tools that ensure only the right guests get in.'
    },
    {
      iconSvg: '/RealTimeGuestInsights.svg', // Replace with your SVG file name
      title: 'Real-Time Guest Insights',
      description: 'Track guest activity and attendance live with actionable analytics at your fingertips.'
    },
    {
      iconSvg: '/ZeroCostOnboarding.svg', // Replace with your SVG file name
      title: 'Zero Cost Onboarding',
      description: 'Start with 10 free events to explore how IAC can fit your business needs—risk-free and commitment-free.'
    },
    {
      iconSvg: '/EventTransparencyInsights.svg', // Replace with your SVG file name
      title: 'Event Transparency & Insights',
      description: 'Compare expected vs. actual turnout, flag extra attendees, and keep events smooth for both hosts and teams.'
    }
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Use existing Header component */}
      <Header 
        showUserProfile={true}
        buttons={[
          {
            label: 'Manage Events',
            onClick: () => window.location.href = '/events',
            variant: 'outline'
          }
        ]}
      />
      
      <div className="relative w-full">
        {/* Hero Container */}
        <div className="bg-[#FFF3EB] relative overflow-hidden pb-20">
          {/* Background SVG */}
          <div className="absolute inset-0 w-full h-full">
            <img
              src="/HostMomentsBackground.svg"
              alt="Background pattern"
              className="absolute w-full h-full object-cover"
            />
          </div>
          
          {/* Decorative background elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none z-10">
            {/* Star decorations matching Figma positions */}
            <div className="absolute top-[130px] right-[117px] w-[46px] h-[46px]">
              <svg className="w-full h-full" fill="none" viewBox="0 0 40 40">
                <path d="M18.1243 2.06901C18.7686 0.327775 21.2314 0.327775 21.8757 2.06901L25.8929 12.9254C26.0955 13.4729 26.5271 13.9045 27.0746 14.1071L37.931 18.1243C39.6722 18.7686 39.6722 21.2314 37.931 21.8757L27.0746 25.8929C26.5271 26.0955 26.0955 26.5271 25.8929 27.0746L21.8757 37.931C21.2314 39.6722 18.7686 39.6722 18.1243 37.931L14.1071 27.0746C13.9045 26.5271 13.4729 26.0955 12.9254 25.8929L2.06901 21.8757C0.327775 21.2314 0.327775 18.7686 2.06901 18.1243L12.9254 14.1071C13.4729 13.9045 13.9045 13.4729 14.1071 12.9254L18.1243 2.06901Z" fill="#FFCB86"/>
              </svg>
            </div>
            <div className="absolute top-[438px] right-[8px] w-[43px] h-[43px]">
              <svg className="w-full h-full" fill="none" viewBox="0 0 37 37">
                <path d="M16.6243 2.06901C17.2686 0.327777 19.7314 0.327775 20.3757 2.06901L23.9878 11.8306C24.1904 12.378 24.622 12.8096 25.1694 13.0122L34.931 16.6243C36.6722 17.2686 36.6722 19.7314 34.931 20.3757L25.1694 23.9878C24.622 24.1904 24.1904 24.622 23.9878 25.1694L20.3757 34.931C19.7314 36.6722 17.2686 36.6722 16.6243 34.931L13.0122 25.1694C12.8096 24.622 12.378 24.1904 11.8306 23.9878L2.06901 20.3757C0.327777 19.7314 0.327775 17.2686 2.06901 16.6243L11.8306 13.0122C12.378 12.8096 12.8096 12.378 13.0122 11.8306L16.6243 2.06901Z" fill="#FFCB86"/>
              </svg>
            </div>
          </div>

          {/* Main content grid */}
          <div className="container mx-auto px-4 py-16 relative z-20">
            <div className="xl:grid xl:grid-cols-2 xl:gap-16">
              {/* Left Side - Hero Section */}
              <div className="flex flex-col justify-start xl:pt-16 sm:pt-16">
                {/* Header Badges */}
                <div className="flex items-start gap-2 mb-8">
                  <div className="flex items-center justify-center gap-1 h-9 min-w-20 px-3.5 py-2 bg-[#FEFCE8] border border-[#FDBA74] rounded-full">
                    <HeartHandshake className="w-4 h-4 text-foreground sm:block hidden" />
                    <span className="text-xs sm:text-sm font-medium text-foreground">I am Coming Partner Program</span>
                  </div>
                  <div className="flex items-center justify-center h-9 min-w-20 px-3.5 py-2 bg-[#FEFCE8] border border-[#FDBA74] rounded-full">
                    <span className="text-xs sm:text-sm font-medium text-foreground">Limited Time Beta</span>
                  </div>
                </div>

                {/* Main Heading */}
                <div>
                  <h1 className="text-4xl sm:text-5xl font-bold text-foreground leading-tight mb-4">
                    Grow Your Event Business with Us.
                  </h1>
                  <div className="flex items-center gap-3 mb-6">
                    <p className="text-lg sm:text-xl font-semibold sm:font-bold text-foreground">
                      Start with 10 events free—try it out risk-free.
                    </p>
                  </div>
                </div>

                {/* Description */}
                <p className="text-base sm:text-lg font-medium sm:font-normal text-muted-foreground mb-8 xl:mb-0">
                  Partner with &apos;I am Coming&apos; to manage RSVPs, attract high-value clients, and unlock exclusive perks.
                </p>
              </div>

              {/* Right Side - Empty space for form overlap on xl+ screens */}
              <div className="relative hidden xl:block">
                {/* Avatar decorations - matching Figma positions */}
                <div className="absolute -top-8 -right-8 w-16 h-16 rounded-full overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                    <div className="w-12 h-12 bg-white rounded-full"></div>
                  </div>
                </div>
                <div className="absolute -top-4 -left-4 w-12 h-12 rounded-full overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-pink-400 to-purple-400 rounded-full"></div>
                </div>
                <div className="absolute top-4 left-16 w-8 h-8 rounded-full overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-yellow-300 to-orange-300 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Overlapping Form Section - for all screen sizes */}
        <div className="relative -mt-30 xl:-mt-114 z-20">
          <div className="container mx-auto px-4">
            <div className="xl:grid xl:grid-cols-2 xl:gap-65">
              {/* Empty left column on xl+ */}
              <div className="hidden xl:block"></div>
              
              {/* Form - spans full width below xl, right column on xl+ */}
              <div className="relative">
                <Card className="p-6 xl:p-8 bg-white relative z-10 shadow-[0px_20px_25px_-5px_rgba(0,0,0,0.10),0px_8px_10px_-6px_rgba(0,0,0,0.10)] xl:h-[715px]">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-2xl font-semibold text-foreground mb-2">Partner With Us</h3>
                      <p className="text-muted-foreground text-sm leading-5 mb-4">
                        Share your business details to request a partnership. We&apos;ll respond within 24 hours.
                      </p>
                      <hr className="border-t border-gray-300 my-4" />
                    </div>

                    {/* User Info Section */}
                    <div className="space-y-3">
                      {status === 'loading' ? (
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-slate-300 rounded-full animate-pulse"></div>
                          <div>
                            <div className="h-4 bg-slate-300 rounded animate-pulse mb-1"></div>
                            <div className="h-3 bg-slate-300 rounded animate-pulse w-32"></div>
                          </div>
                        </div>
                      ) : session?.user ? (
                        <>
                          <div>
                            <div className="text-slate-900 -mt-1 text-sm font-medium">Logged in as</div>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg border">
                            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 w-full">
                              <div className="flex items-center gap-3 flex-1">
                                <Avatar className="size-6">
                                  <AvatarImage src={session.user.image || "/profile.png"} alt={session.user.name || "User"} />
                                  <AvatarFallback className="text-xs bg-rose-500 text-white">
                                    {getInitials(session.user.name)}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="min-w-0 flex-1">
                                  <div className="text-slate-900 text-sm font-medium truncate">
                                    {session.user.name || "User"}
                                  </div>
                                  <div className="text-muted-foreground text-sm truncate">
                                    {session.user.email || "No email"}
                                  </div>
                                </div>
                              </div>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="text-foreground border-slate-300 hover:bg-slate-100 w-full sm:w-auto" 
                                onClick={handleSwitchAccount}
                              >
                                Switch account
                              </Button>
                            </div>
                          </div>
                          <div className="text-foreground text-sm">
                            We recommend using your &apos;I am Coming&apos; account that&apos;s signed in with your business email.
                          </div>
                        </>
                      ) : (
                        <div className="text-left -mt-2">
                          <p className="text-base font-semibold text-foreground mb-3">Please sign in with I Am Coming Account</p>
                          <p className="text-sm font-normal text-muted-foreground mb-3">We recommend using your business email to create an &apos;I am Coming&apos; account and send the request.</p>
                          <Button variant="primary-button" className="mb-5 h-10 w-full" onClick={() => window.location.href = '/auth/signin'}>
                            Sign In / Sign Up
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Form or Already Partner Message */}
                    {session?.user?.organization?.type === 'partner' ? (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                        <div className="flex items-center justify-center mb-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <h3 className="text-lg font-semibold text-green-700">You&apos;re Already a Partner!</h3>
                        </div>
                        <p className="text-green-700 text-sm mb-4">
                          Your organization already has partner access. You can access the partner dashboard to manage your events and venues.
                        </p>
                        <Button 
                          variant="primary-button"
                          className="w-full h-11 text-white font-medium text-sm rounded-md"
                          onClick={() => router.push('/partner')}
                        >
                          Go to Partner Dashboard
                        </Button>
                      </div>
                    ) : (
                      session?.user ? (
                        <form onSubmit={handleSubmit} className="space-y-4">
                          <div>
                            <Label className="block text-sm font-medium text-foreground mb-1">
                              Business Name
                            </Label>
                            <Input
                              value={formData.businessName}
                              onChange={(e) => setFormData({ ...formData, businessName: e.target.value })}
                              className="w-full border-slate-300"
                            />
                          </div>

                          <div>
                            <Label className="block text-sm font-medium text-foreground mb-1">
                              Tax ID
                            </Label>
                            <Input
                              value={formData.taxId}
                              onChange={(e) => setFormData({ ...formData, taxId: e.target.value })}
                              className="w-full border-slate-300"
                            />
                          </div>

                          <div>
                            <Label className="block text-sm font-medium text-foreground mb-1">
                              Phone Number
                            </Label>
                            <div className="flex gap-2">
                              <Input
                                value={formData.phoneNumber}
                                onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
                                className="flex-1 border-slate-300"
                              />
                            </div>
                          </div>

                          <div>
                            <Label className="block text-sm font-medium text-foreground mb-1">
                              Registered Business Address (Optional)
                            </Label>
                            <div className="relative">
                              <Input
                                placeholder="Enter Address"
                                value={formData.address}
                                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                                className="w-full border-slate-300 pl-10"
                              />
                              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
                                <MapPin className="w-4 h-4" />
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="multiple-venues"
                              checked={formData.hasMultipleVenues}
                              onCheckedChange={(checked) => 
                                setFormData({ ...formData, hasMultipleVenues: checked as boolean })
                              }
                            />
                            <label
                              htmlFor="multiple-venues"
                              className="text-sm font-medium text-foreground"
                            >
                              I have multiple venue
                            </label>
                          </div>

                          <Button 
                            variant={"primary-button"}
                            className="w-full h-11 text-white font-medium text-sm rounded-md"
                            type="submit"
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? 'Sending Request...' : 'Send Request to Partner'}
                          </Button>
                        </form>
                      ) : (
                        <form className="space-y-4 opacity-50 pointer-events-none">
                          <div>
                            <Label className="block text-sm font-medium text-foreground mb-1">
                              Business Name
                            </Label>
                            <Input
                              value={formData.businessName}
                              className="w-full border-slate-300"
                              disabled
                            />
                          </div>

                          <div>
                            <Label className="block text-sm font-medium text-foreground mb-1">
                              Tax ID
                            </Label>
                            <Input
                              value={formData.taxId}
                              className="w-full border-slate-300"
                              disabled
                            />
                          </div>

                          <div>
                            <Label className="block text-sm font-medium text-foreground mb-1">
                              Phone Number
                            </Label>
                            <div className="flex gap-2">
                              <Input
                                value={formData.phoneNumber}
                                className="flex-1 border-slate-300"
                                disabled
                              />
                            </div>
                          </div>

                          <div>
                            <Label className="block text-sm font-medium text-foreground mb-1">
                              Registered Business Address (Optional)
                            </Label>
                            <div className="relative">
                              <Input
                                placeholder="Enter Address"
                                value={formData.address}
                                className="w-full border-slate-300 pl-10"
                                disabled
                              />
                              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
                                <MapPin className="w-4 h-4" />
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="multiple-venues"
                              checked={formData.hasMultipleVenues}
                              disabled
                            />
                            <label
                              htmlFor="multiple-venues"
                              className="text-sm font-medium text-foreground"
                            >
                              I have multiple venue
                            </label>
                          </div>

                          <Button 
                            variant={"primary-button"}
                            className="w-full h-11 text-white font-medium text-sm rounded-md"
                            disabled
                          >
                            Send Request to Partner
                          </Button>
                        </form>
                      ))}
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="container mx-auto px-4 xl:-mt-33 mt-16 sl:-mt-35 pb-24">
          {/* Header Container */}
          <div className="mb-12 text-center xl:text-left">
            <h2 className="text-3xl font-semibold text-foreground mb-4 tracking-tight">
              Benefits of joining &apos;I am Coming&apos; as a partner?
            </h2>
            <p className="text-base font-normal text-muted-foreground leading-7 max-w-[586px] mx-auto xl:mx-0">
              You bring your expertise, we provide the tools. From planners to hospitality brands, our partners enhance their services with seamless RSVPs, added value, and new revenue opportunities.
            </p>
          </div>

          {/* Benefits Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 xl:grid-cols-3 gap-x-8 gap-y-12 max-w-4xl mx-auto xl:max-w-none xl:mx-0">
            {benefits.map((benefit, index) => {
              return (
                <div key={index} className="flex xl:flex-row flex-col xl:items-start items-center xl:text-left text-center xl:gap-4 gap-0">
                  {/* Icon */}
                  <div className="flex p-[8px] items-center justify-center rounded-[12px] bg-rose-500 xl:mb-0 mb-3">
                    <img 
                      src={benefit.iconSvg} 
                      alt={benefit.title}
                      className="w-7 h-7 xl:w-8 xl:h-8"
                      style={{ filter: 'brightness(0) invert(1)' }} // Makes SVG white
                    />
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1">
                    <h3 className="text-base font-semibold text-foreground mb-2">{benefit.title}</h3>
                    <p className="text-base text-muted-foreground leading-relaxed text-sm">{benefit.description}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <Footer type="marketing" />
    </div>
  );
}
