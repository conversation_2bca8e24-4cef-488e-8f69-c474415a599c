import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface EventLimits {
  canCreateEvent: boolean;
  hasReachedUpcomingLimit: boolean;
  hasReachedTotalLimit: boolean;
  limits: {
    upcomingEvents: number;
    totalEvents: number;
  };
  counts: {
    upcomingEvents: number;
    totalEvents: number;
  };
  loading: boolean;
  error: Error | null;
}

/**
 * Hook to check if user has reached event creation limits
 * Uses the event limits API to determine if the user can create more events
 */
export function useEventLimits() {
  const { data: session } = useSession();
  const [limits, setLimits] = useState<EventLimits>({
    canCreateEvent: true, // Default to true until we know otherwise
    hasReachedUpcomingLimit: false,
    hasReachedTotalLimit: false,
    limits: {
      upcomingEvents: 3,
      totalEvents: 6
    },
    counts: {
      upcomingEvents: 0,
      totalEvents: 0
    },
    loading: true,
    error: null
  });

  useEffect(() => {
    const checkLimits = async () => {
      if (!session?.user?.id) {
        setLimits(prev => ({ ...prev, loading: false }));
        return;
      }

      try {
        const response = await fetch('/api/events/check-limits');
        
        if (!response.ok) {
          throw new Error('Failed to check event limits');
        }
        
        const data = await response.json();
        setLimits({
          ...data,
          loading: false,
          error: null
        });
      } catch (err) {
        setLimits(prev => ({
          ...prev,
          loading: false,
          error: err instanceof Error ? err : new Error('Failed to check event limits')
        }));
      }
    };

    checkLimits();
  }, [session?.user?.id]); // Updated to depend on user ID instead of email

  return limits;
}