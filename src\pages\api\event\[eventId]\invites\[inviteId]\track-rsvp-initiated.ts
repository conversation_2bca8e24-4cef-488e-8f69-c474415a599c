import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import analytics from '@/lib/analytics';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { eventId, inviteId } = req.query;

    if (!eventId || typeof eventId !== 'string' || !inviteId || typeof inviteId !== 'string') {
      return res.status(400).json({ error: 'Invalid event or invite ID' });
    }

    // Get user info from session
    const session = await getServerSession(req, res, authConfig);
    const userId = session?.user?.id || null;

    // Track the RSVP initiation
    await analytics.trackRSVPInitiated(eventId, inviteId, userId);

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error tracking RSVP initiation:', error);
    return res.status(500).json({ error: 'Failed to track RSVP initiation' });
  }
}