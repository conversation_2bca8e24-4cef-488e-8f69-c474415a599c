import { useEffect } from "react";
import Link from "next/link";
import { AdminLayout } from "@/components/layouts/AdminLayout";
import { useUsers } from "@/hooks/useUsers";
import { formatTimeAgo } from "@/lib/dayjs";
import { withAdminAuth } from "@/lib/auth/admin";
import { GetServerSideProps } from "next";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Skeleton
} from "@/components/ui/skeleton";
import {
  Search,
  UserCog,
  ChevronLeft,
  ChevronRight,
  Shield,
  RefreshCw,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useDebounce } from "@/hooks/useDebounce";

export default function AdminUsers() {
  const {
    users,
    loading,
    error,
    searchQuery,
    pagination,
    sorting,
    handleSearch,
    handlePageChange,
    handleSort,
    refreshUsers
  } = useUsers();

  // Debounce search input to avoid too many requests
  const debouncedSearch = useDebounce(handleSearch, 300);

  // Helper function to render sort icon
  const getSortIcon = (column: 'name' | 'email' | 'createdOn') => {
    if (sorting.sortBy !== column) {
      return <ArrowUpDown className="h-4 w-4 ml-1 opacity-50" />;
    }
    return sorting.sortOrder === 'asc'
      ? <ArrowUp className="h-4 w-4 ml-1" />
      : <ArrowDown className="h-4 w-4 ml-1" />;
  };

  // Google icon component
  const GoogleIcon = () => (
    <svg
      className="h-4 w-4"
      viewBox="0 0 24 24"
      fill="currentColor"
    >
      <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4" />
      <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853" />
      <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05" />
      <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335" />
    </svg>
  );

  return (
    <AdminLayout pageTitle="Users">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Users</h1>
        <div className="flex items-center gap-4">
          <div className="relative w-64">
            <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              className="pl-8"
              defaultValue={searchQuery}
              onChange={(e) => debouncedSearch(e.target.value)}
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshUsers}
            disabled={loading}
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableCaption>
            {pagination.total > 0
              ? `Showing ${(pagination.page - 1) * pagination.limit + 1} to ${Math.min(pagination.page * pagination.limit, pagination.total)} of ${pagination.total} users`
              : "No users found"}
          </TableCaption>
          <TableHeader>
            <TableRow>
              {/* Admin icon column - no header */}
              <TableHead className="w-10"></TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                  onClick={() => handleSort('name')}
                >
                  Name
                  {getSortIcon('name')}
                </Button>
              </TableHead>
              {/* Google icon column - no header */}
              <TableHead className="w-10"></TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                  onClick={() => handleSort('email')}
                >
                  Email
                  {getSortIcon('email')}
                </Button>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                  onClick={() => handleSort('createdOn')}
                >
                  Created
                  {getSortIcon('createdOn')}
                </Button>
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              // Loading state
              Array.from({ length: pagination.limit }).map((_, i) => (
                <TableRow key={`skeleton-${i}`}>
                  <TableCell><Skeleton className="h-5 w-5" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-5" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-9 w-20 ml-auto" /></TableCell>
                </TableRow>
              ))
            ) : error ? (
              // Error state
              <TableRow>
                <TableCell colSpan={7} className="text-center text-red-500">
                  Error loading users: {error.message}
                </TableCell>
              </TableRow>
            ) : users.length === 0 ? (
              // Empty state
              <TableRow>
                <TableCell colSpan={7} className="text-center text-muted-foreground h-32">
                  {searchQuery ? "No users found matching your search" : "No users found in the system"}
                </TableCell>
              </TableRow>
            ) : (
              // Data state
              users.map((user) => (
                <TableRow key={user.id}>
                  {/* Admin Shield Icon Column */}
                  <TableCell className="text-center">
                    {user.isAdmin && (
                      <span title="Admin User">
                        <Shield className="h-4 w-4 text-purple-600 mx-auto" />
                      </span>
                    )}
                  </TableCell>

                  {/* User Name Column */}
                  <TableCell className="font-medium">
                    {user.name || "Unnamed User"}
                  </TableCell>

                  {/* Google Authentication Icon Column */}
                  <TableCell className="text-center">
                    {user.hasGoogleLinked && (
                      <span className="text-blue-500 inline-flex justify-center" title="Google account linked">
                        <GoogleIcon />
                      </span>
                    )}
                  </TableCell>

                  {/* Email Column */}
                  <TableCell>
                    {user.email || "No email"}
                  </TableCell>

                  <TableCell>
                    <Badge
                      variant={user.isProfileComplete ? "default" : "outline"}
                      className={user.isProfileComplete ? "bg-green-100 text-green-800 hover:bg-green-100" : ""}
                    >
                      {user.isProfileComplete ? "Complete" : "Incomplete"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.createdOn
                      ? formatTimeAgo(user.createdOn)
                      : "Unknown"}
                  </TableCell>
                  <TableCell className="text-right">
                    <Link href={`/admin/users/${user.id}`}>
                      <Button variant="outline" size="sm">
                        <UserCog className="w-4 h-4 mr-1" />
                        Details
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {pagination.total > 0 && (
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-muted-foreground">
            Showing {(pagination.page - 1) * pagination.limit + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} users
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1 || loading}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous page</span>
            </Button>
            <div className="flex items-center gap-1">
              {[...Array(pagination.totalPages)].map((_, i) => {
                const pageNumber = i + 1;
                // Show only a window of pages for better UX
                if (
                  pageNumber === 1 ||
                  pageNumber === pagination.totalPages ||
                  (pageNumber >= pagination.page - 1 && pageNumber <= pagination.page + 1)
                ) {
                  return (
                    <Button
                      key={`page-${pageNumber}`}
                      variant={pageNumber === pagination.page ? "default" : "outline"}
                      size="sm"
                      className="w-9"
                      onClick={() => handlePageChange(pageNumber)}
                      disabled={loading}
                    >
                      {pageNumber}
                    </Button>
                  );
                } else if (
                  (pageNumber === pagination.page - 2 && pageNumber > 1) ||
                  (pageNumber === pagination.page + 2 && pageNumber < pagination.totalPages)
                ) {
                  // Show ellipsis for skipped pages
                  return (
                    <span key={`ellipsis-${pageNumber}`} className="px-2">
                      ...
                    </span>
                  );
                }
                return null;
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages || loading}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next page</span>
            </Button>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}

// Server-side protection for admin routes
export const getServerSideProps: GetServerSideProps = withAdminAuth();