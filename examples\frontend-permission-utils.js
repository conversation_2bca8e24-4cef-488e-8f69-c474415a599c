/**
 * Frontend Permission Utilities for Organization-Scoped RBAC
 * 
 * These utilities demonstrate how to work with the ultra-streamlined
 * session response that uses only rolePermissions as the single source of truth.
 */

/**
 * Get user roles from rolePermissions (derived data)
 */
export function getUserRoles(organization) {
  const { rbacConfig } = organization;
  return Object.keys(rbacConfig.rolePermissions || {});
}

/**
 * Check if user has a specific permission in an organization
 */
export function hasPermission(organization, resource, permission) {
  const { rbacConfig } = organization;
  const userRoles = getUserRoles(organization);

  return userRoles.some(role => {
    const rolePermissions = rbacConfig.rolePermissions[role];
    return rolePermissions?.[resource]?.includes(permission);
  });
}

/**
 * Get all computed permissions for an organization (convenience function)
 */
export function getComputedPermissions(organization) {
  const { rbacConfig } = organization;
  const userRoles = getUserRoles(organization);
  const allPermissions = new Set();

  userRoles.forEach(role => {
    const rolePermissions = rbacConfig.rolePermissions[role] || {};
    Object.entries(rolePermissions).forEach(([resource, perms]) => {
      perms.forEach(perm => {
        allPermissions.add(`${resource}:${perm}`);
      });
    });
  });

  return Array.from(allPermissions);
}

/**
 * Check multiple permissions at once
 */
export function hasAnyPermission(organization, checks) {
  return checks.some(([resource, permission]) =>
    hasPermission(organization, resource, permission)
  );
}

/**
 * Check if user has all specified permissions
 */
export function hasAllPermissions(organization, checks) {
  return checks.every(([resource, permission]) =>
    hasPermission(organization, resource, permission)
  );
}

// Usage Examples:

// Get user roles for current organization  
const userRoles = getUserRoles(sessionData.currentOrganization);
console.log('User roles:', userRoles);
// Output: ["partner:venue-manager"]

// Basic permission check
const canEditVenue = hasPermission(
  sessionData.currentOrganization,
  'partner:venue',
  'edit'
);

// Multiple permission check (user needs ANY of these)
const canManageEvents = hasAnyPermission(sessionData.currentOrganization, [
  ['partner:venue:events', 'create'],
  ['partner:venue:events', 'edit'],
  ['partner:venue:events', 'delete']
]);

// Multiple permission check (user needs ALL of these)
const canFullyManageVenue = hasAllPermissions(sessionData.currentOrganization, [
  ['partner:venue', 'view'],
  ['partner:venue', 'edit'],
  ['partner:venue:events', 'create']
]);

// Get all permissions for debugging or UI generation
const allUserPermissions = getComputedPermissions(sessionData.currentOrganization);
console.log('User can:', allUserPermissions);
// Output: ["partner:dashboard:view", "partner:venue:edit", "partner:venue:events:create", ...]

// React hook example
import { useState, useEffect } from 'react';

export function useOrganizationPermissions(organization) {
  const [permissions, setPermissions] = useState(new Set());

  useEffect(() => {
    if (organization) {
      const computed = getComputedPermissions(organization);
      setPermissions(new Set(computed));
    }
  }, [organization]);

  const can = (resource, permission) => {
    return permissions.has(`${resource}:${permission}`);
  };

  return { can, permissions: Array.from(permissions) };
}

// Usage in React component:
function VenueManager() {
  const { can } = useOrganizationPermissions(sessionData.currentOrganization);

  return (
    <div>
      {can('partner:venue', 'edit') && <EditVenueButton />}
      {can('partner:venue:events', 'create') && <CreateEventButton />}
      {can('partner:dashboard', 'view') && <DashboardLink />}
    </div>
  );
}
