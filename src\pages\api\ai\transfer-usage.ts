import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { transferTempEventUsage } from '@/lib/ai-usage';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { tempEventId, realEventId } = req.body;

    if (!tempEventId || !realEventId) {
      return res.status(400).json({ error: 'Both tempEventId and realEventId are required' });
    }

    // Transfer AI usage from temporary event to real event
    await transferTempEventUsage(tempEventId, realEventId);

    return res.status(200).json({
      success: true,
      message: 'AI usage transferred successfully'
    });

  } catch (error) {
    console.error('Error transferring AI usage:', error);
    return res.status(500).json({ 
      error: 'Failed to transfer AI usage',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
