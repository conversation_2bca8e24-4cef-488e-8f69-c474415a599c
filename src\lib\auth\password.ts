import * as bcrypt from 'bcryptjs';
import { z } from 'zod';

/**
 * Hash a password using bcrypt
 * @param password The plain text password to hash
 * @returns Promise<string> The hashed password
 */
export async function hashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
}

/**
 * Verify a password against a hash
 * @param password The plain text password to verify
 * @param hash The hash to verify against
 * @returns Promise<boolean> True if the password matches the hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

/**
 * Password validation schema
 * - At least 8 characters
 * - At least one uppercase letter
 * - At least one lowercase letter
 * - At least one number
 */
export const passwordSchema = z
  .string()
  .min(8, { message: "Password must be at least 8 characters" })
  .refine((password) => /[A-Z]/.test(password), {
    message: "Password must contain at least one uppercase letter",
  })
  .refine((password) => /[a-z]/.test(password), {
    message: "Password must contain at least one lowercase letter",
  })
  .refine((password) => /[0-9]/.test(password), {
    message: "Password must contain at least one number",
  });

/**
 * Validate a password against the password schema
 * @param password The password to validate
 * @returns { success: boolean, error?: string } Validation result
 */
export function validatePassword(password: string): { success: boolean; error?: string } {
  const result = passwordSchema.safeParse(password);
  if (!result.success) {
    return {
      success: false,
      error: result.error.errors[0].message,
    };
  }
  return { success: true };
}
