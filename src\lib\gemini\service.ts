import { GoogleGenAI, HarmCategory, HarmBlockThreshold } from '@google/genai';
import { 
  generateEventDescriptionPrompt,
  generatePrintableInviteImagePrompt,
  generateDigitalInviteImagePrompt,
  generateReminderMessagePrompt
} from './prompts/index';
import { EventFormData } from '../../types';
import { formatEventDate } from '../../utils/dateFormatter';

/**
 * Google Gemini AI Service
 * Provides AI capabilities for event management
 */
class GeminiService {
  private genAI: GoogleGenAI;
  private defaultModel: string = 'gemini-2.0-flash-lite';

  constructor() {
    const apiKey = process.env.GOOGLE_GEMINI_API_KEY || process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_GEMINI_API_KEY or GEMINI_API_KEY environment variable is required');
    }

    this.genAI = new GoogleGenAI({ apiKey });
  }

  private getSafetySettings() {
    return [
      {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
    ];
  }

  /**
   * Generate text content using Gemini AI
   * @param prompt - The input prompt for text generation
   * @param options - Additional options for generation
   * @returns Generated text response
   */
  private async generateText(
    prompt: string,
    options?: {
      maxOutputTokens?: number;
      temperature?: number;
      topK?: number;
      topP?: number;
    }
  ): Promise<string> {
    try {
      const config = {
        maxOutputTokens: options?.maxOutputTokens || 1000,
        temperature: options?.temperature || 0.7,
        topK: options?.topK || 40,
        topP: options?.topP || 0.95,
        safetySettings: this.getSafetySettings(),
      };

      const result = await this.genAI.models.generateContent({
        model: this.defaultModel,
        contents: prompt,
        config,
      });

      return result.text || '';
    } catch (error) {
      console.error('Error generating text with Gemini:', error);
      throw new Error('Failed to generate text with Gemini AI');
    }
  }

  /**
   * 1. API Lib Generate Description text - Complete implementation
   * Generate enhanced event description based on form data
   * @param eventData - Complete event form data
   * @returns Enhanced event description
   */  async generateEventDescription(eventData: EventFormData): Promise<string> {
    try {
      // Format date using utility function
      const formattedDate = formatEventDate(new Date(eventData.eventDate));
      
      // Create comprehensive prompt based on all form data
      const prompt = generateEventDescriptionPrompt(eventData, formattedDate);

      return await this.generateText(prompt, {
        temperature: 0.7,
        maxOutputTokens: 800
      });

    } catch (error) {
      console.error('Error generating event description:', error);
      throw new Error('Failed to generate event description');
    }
  }  /**
   * 2. API Lib Generate Printable Invite Image - Complete implementation
   * Generate printable invite image for the event using Gemini 2.0 Flash
   * @param eventData - Complete event form data
   * @param pageSize - Page size for the invitation (A4, A5, etc.)
   * @param orientation - Orientation (portrait or landscape)
   * @returns Promise with generated image data
   */  async generatePrintableInviteImage(
    eventData: EventFormData, 
    pageSize: string = 'A4', 
    orientation: string = 'portrait'
  ): Promise<any> {
    try {
      // Generate prompt for printable invite with page size and orientation
      // Only pass event type (detected from name/message), not event details
      const prompt = generatePrintableInviteImagePrompt(eventData, pageSize, orientation);
      
      // Use Gemini 2.0 Flash for native image generation (same as digital invite)
      const response = await this.genAI.models.generateContent({
        model: 'gemini-2.0-flash-preview-image-generation',
        contents: prompt,
        config: {
          responseModalities: ['TEXT', 'IMAGE'],
          maxOutputTokens: 1000,
          temperature: 0.7,
          safetySettings: this.getSafetySettings(),
        },
      });

      // Process the response to extract image data (same as digital invite)
      const result = response.candidates?.[0]?.content?.parts;
      if (!result) {
        throw new Error('No content generated from Gemini');
      }

      let generatedText = '';
      let imageData = null;

      for (const part of result) {
        if (part.text) {
          generatedText += part.text;
        } else if (part.inlineData) {
          imageData = part.inlineData.data;
        }
      }

      if (!imageData) {
        throw new Error('No image data generated from Gemini');
      }

      return {
        success: true,
        message: 'Printable invite image generated successfully',
        imageData: imageData,
        mimeType: 'image/png',
        description: generatedText,
        pageSize: pageSize,
        orientation: orientation,
        prompt: prompt // Include prompt for debugging/testing
      };
    } catch (error) {
      console.error('Error generating printable invite image:', error);
      throw new Error('Failed to generate printable invite image: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  }
  /**
   * 3. Digital invite image generation - Complete implementation
   * Generate digital invite image for the event using Gemini 2.0 Flash
   * @param eventData - Complete event form data
   * @returns Promise with generated image data
   */  async generateDigitalInviteImage(eventData: EventFormData): Promise<any> {
    try {
      // Generate prompt for digital invite (formattedDate is now handled inside the prompt function)
      const prompt = generateDigitalInviteImagePrompt(eventData);
      
      // Use Gemini 2.0 Flash for native image generation
      const response = await this.genAI.models.generateContent({
        model: 'gemini-2.0-flash-preview-image-generation',
        contents: prompt,
        config: {
          responseModalities: ['TEXT', 'IMAGE'],
          maxOutputTokens: 1000,
          temperature: 0.7,
          safetySettings: this.getSafetySettings(),
        },
      });

      // Process the response to extract image data
      const result = response.candidates?.[0]?.content?.parts;
      if (!result) {
        throw new Error('No content generated from Gemini');
      }

      let generatedText = '';
      let imageData = null;

      for (const part of result) {
        if (part.text) {
          generatedText += part.text;
        } else if (part.inlineData) {
          imageData = part.inlineData.data;
        }
      }

      if (!imageData) {
        throw new Error('No image data generated from Gemini');
      }

      return {
        success: true,
        message: 'Digital invite image generated successfully',
        imageData: imageData,
        mimeType: 'image/png',
        description: generatedText,
        prompt: prompt // Include prompt for debugging/testing
      };
    } catch (error) {
      console.error('Error generating digital invite image:', error);
      throw new Error('Failed to generate digital invite image: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  }

  /**
   * 4. Generate Reminder Message - Complete implementation
   * Generate a reminder message based on the original event message
   * @param eventMessage - Original event message
   * @param eventData - Complete event form data
   * @returns Promise with generated reminder message
   */
  async generateReminderMessage(eventMessage: string, eventData: EventFormData): Promise<string> {
    try {
      // Generate prompt for reminder message
      const prompt = generateReminderMessagePrompt(eventMessage, eventData);

      return await this.generateText(prompt, {
        temperature: 0.7,
        maxOutputTokens: 500
      });

    } catch (error) {
      console.error('Error generating reminder message:', error);
      throw new Error('Failed to generate reminder message');
    }
  }
}

// Create a singleton instance
const geminiService = new GeminiService();

export default geminiService;
export { GeminiService };
