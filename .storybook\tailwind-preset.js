const path = require('path');

module.exports = {
  webpackFinal: async (config) => {
    // Find the CSS rule
    const cssRule = config.module.rules.find(
      (rule) => rule.test && rule.test.toString().includes('.css')
    );

    // If we found the CSS rule, modify it properly rather than adding a new one
    if (cssRule) {
      // Find and fix css-loader options
      const loaders = cssRule.use || [];
      
      cssRule.use = loaders.map(loader => {
        // If this is the css-loader, ensure it has the right options
        if (typeof loader === 'object' && (loader.loader && loader.loader.includes('css-loader'))) {
          return {
            ...loader,
            options: {
              ...loader.options,
              url: true,  // Set explicitly to match schema
              import: true, // Set explicitly to match schema
              modules: loader.options?.modules || false,
              importLoaders: loader.options?.importLoaders || 1,
            }
          };
        }
        
        // If this is a string loader reference for css-loader, expand it to object form
        if (typeof loader === 'string' && loader.includes('css-loader')) {
          return {
            loader,
            options: {
              url: true,
              import: true,
              modules: false,
              importLoaders: 1
            }
          };
        }
        
        // Return other loaders unchanged
        return loader;
      });
      
      // Make sure postcss-loader is included with proper configuration
      const hasPostCSSLoader = cssRule.use.some(loader => 
        (typeof loader === 'string' && loader.includes('postcss-loader')) ||
        (loader.loader && loader.loader.includes('postcss-loader'))
      );
      
      if (!hasPostCSSLoader) {
        cssRule.use.push({
          loader: 'postcss-loader',
          options: {
            postcssOptions: {
              plugins: [
                require('@tailwindcss/postcss'),
                require('autoprefixer'),
              ],
            },
          },
        });
      }
    } else {
      // If no CSS rule found, add a new one with proper configuration
      config.module.rules.push({
        test: /\.css$/,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              url: true,
              import: true,
              importLoaders: 1,
            },
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  require('@tailwindcss/postcss'),
                  require('autoprefixer'),
                ],
              },
            },
          },
        ],
        include: path.resolve(__dirname, '../'),
      });
    }

    return config;
  },
};