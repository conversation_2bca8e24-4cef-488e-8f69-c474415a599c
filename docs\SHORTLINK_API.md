# Shortlink Service API Documentation

Base URL: `https://l.iamcoming.io`

The Shortlink Service provides a lightweight URL shortening service with analytics tracking capabilities. It features JWT authentication for secure management operations and anonymous access for link redirection.

## Authentication

All administrative endpoints require JWT authentication.

**Authentication Header Format:**

```
Authorization: Bearer YOUR_JWT_TOKEN
```

To generate a JWT token, you can use the provided script:

```bash
node scripts/generate-jwt.js
```

## API Endpoints

### Health Check

Check if the service is running properly.

- **URL**: `/health`
- **Method**: `GET`
- **Auth Required**: No
- **Success Response**:

  - **Code**: `200 OK`
  - **Content**:

    ```json
    {
      "status": "ok",
      "timestamp": "2025-06-03T10:15:30.123Z"
    }
    ```

### Redirect to Destination

Redirects from a short URL to its destination URL and records analytics.

- **URL**: `/:shortCode`
- **Method**: `GET`
- **Auth Required**: No
- **URL Parameters**:
  - `shortCode`: The short code for the URL
- **Success Response**:
  - **Code**: `302 Found`
  - **Headers**: `Location: [destination URL]`
- **Error Responses**:
  - **Code**: `404 Not Found`
    - **Content**: `Not found`
  - **Code**: `500 Internal Server Error`
    - **Content**: `Error processing redirect`

### List All Short Links

Retrieve a list of all short links in the system.

- **URL**: `/list`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:

  - **Code**: `200 OK`
  - **Content**:

    ```json
    [
      {
        "shortCode": "example",
        "redirectUrl": "https://example.com"
      },
      {
        "shortCode": "blog",
        "redirectUrl": "https://blog.example.com/featured-article"
      }
    ]
    ```

- **Error Response**:
  - **Code**: `500 Internal Server Error`
    - **Content**: `Error fetching links`
  - **Code**: `401 Unauthorized`
    - **Content**: `Unauthorized`

### Create a Short Link

Create a new short link in the system.

- **URL**: `/`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content-Type**: `application/json`
- **Request Body**:

  ```json
  {
    "shortCode": "example",
    "redirectUrl": "https://example.com"
  }
  ```

- **Success Response**:
  - **Code**: `201 Created`
  - **Content**: `Created short link for example`
- **Error Responses**:
  - **Code**: `400 Bad Request`
    - **Content**: `shortCode and redirectUrl required`
    - **Content**: `Short code is reserved`
  - **Code**: `409 Conflict`
    - **Content**: `Short code already exists`
  - **Code**: `500 Internal Server Error`
    - **Content**: `Error creating short link`
  - **Code**: `401 Unauthorized`
    - **Content**: `Unauthorized`

### Delete a Short Link

Delete an existing short link.

- **URL**: `/:shortCode`
- **Method**: `DELETE`
- **Auth Required**: Yes
- **URL Parameters**:
  - `shortCode`: The short code of the link to delete
- **Success Response**:
  - **Code**: `200 OK`
  - **Content**: `Deleted example`
- **Error Responses**:
  - **Code**: `404 Not Found`
    - **Content**: `Short code not found`
  - **Code**: `500 Internal Server Error`
    - **Content**: `Error deleting short link`
  - **Code**: `401 Unauthorized`
    - **Content**: `Unauthorized`

### Get Analytics for All Links

Retrieve analytics data for all links.

- **URL**: `/analytics`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:

  - **Code**: `200 OK`
  - **Content**:

    ```json
    [
      {
        "shortCode": "example",
        "analytics": {
          "hits": 42,
          "locations": {
            "San Francisco, California, US": 15,
            "New York, New York, US": 10,
            "London, England, GB": 17
          },
          "devices": {
            "desktop": 25,
            "mobile": 15,
            "tablet": 2
          },
          "os": {
            "Windows": 12,
            "macOS": 13,
            "iOS": 10,
            "Android": 7
          },
          "logs": [
            {
              "timestamp": "2025-06-03T10:15:30.123Z",
              "ua": "Mozilla/5.0...",
              "location": "San Francisco, California, US",
              "os": "macOS"
            }
            // Additional logs...
          ]
        }
      }
      // Additional links...
    ]
    ```

- **Error Responses**:
  - **Code**: `500 Internal Server Error`
    - **Content**: `Error fetching analytics`
  - **Code**: `401 Unauthorized`
    - **Content**: `Unauthorized`

### Get Detailed Analytics for a Specific Link

Retrieve detailed analytics for a specific short link with daily and weekly grouping.

- **URL**: `/analytics/:shortCode`
- **Method**: `GET`
- **Auth Required**: Yes
- **URL Parameters**:
  - `shortCode`: The short code to get analytics for
- **Success Response**:

  - **Code**: `200 OK`
  - **Content**:

    ```json
    {
      "shortCode": "example",
      "summary": {
        "hits": 42,
        "locations": {
          "San Francisco, California, US": 15,
          "New York, New York, US": 10,
          "London, England, GB": 17
        },
        "devices": {
          "desktop": 25,
          "mobile": 15,
          "tablet": 2
        },
        "os": {
          "Windows": 12,
          "macOS": 13,
          "iOS": 10,
          "Android": 7
        },
        "logs": [
          {
            "timestamp": "2025-06-03T10:15:30.123Z",
            "ua": "Mozilla/5.0...",
            "location": "San Francisco, California, US",
            "os": "macOS"
          }
          // Additional logs...
        ]
      },
      "grouped": {
        "daily": {
          "2025-06-03": 12,
          "2025-06-02": 15,
          "2025-06-01": 15
        },
        "weekly": {
          "2025-06-01": 42
        }
      }
    }
    ```

- **Error Responses**:
  - **Code**: `404 Not Found`
    - **Content**: `No analytics found`
  - **Code**: `500 Internal Server Error`
    - **Content**: `Error fetching analytics`
  - **Code**: `401 Unauthorized`
    - **Content**: `Unauthorized`

## Reserved Short Codes

The following short codes are reserved and cannot be used:

- `list`
- `health`
- `admin`
- `analytics`

## Implementation Notes

1. Analytics are collected automatically when short links are accessed
2. JWT tokens should be kept secure and rotated regularly
3. The service tracks:
   - Total hits
   - Geographic location data
   - Device types (desktop, mobile, tablet)
   - Operating systems
   - Access timestamps
4. Analytics logs store the last 100 hits for each short link

## Example Usage

### Creating a Short Link

```bash
curl -X POST https://l.iamcoming.io/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"shortCode": "example", "redirectUrl": "https://example.com"}'
```

### Accessing Analytics

```bash
curl -X GET https://l.iamcoming.io/analytics/example \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Using a Short Link

Simply visit in browser:

```
https://l.iamcoming.io/example
```

Or using cURL:

```bash
curl -L https://l.iamcoming.io/example
```

The Shortlink Service is now fully functional and ready for development, testing, and deployment! 🎉
