import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { saveContactGroups, extractContactGroupsFromEvent } from '@/lib/saved-contact-groups';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { organizationId } = req.query;
    const { eventId, groupsToSave, preference } = req.body;

    if (!organizationId || !eventId) {
      return res.status(400).json({ error: 'Organization ID and Event ID are required' });
    }

    const db = Database.getInstance();

    // Verify user session and organization membership
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is a member of the organization
    const organization = await db.getOrganizationById(organizationId as string);
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const isMember = organization.members?.some(member => member.userId === session.user.id);
    if (!isMember) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get the event to verify it exists and belongs to the organization
    const event = await db.readData('events', eventId);
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    if (event.ownerAccountId !== organizationId) {
      return res.status(403).json({ error: 'Event does not belong to this organization' });
    }

    // Extract contact groups from the event
    const allContactGroups = await extractContactGroupsFromEvent(eventId);
    
    if (allContactGroups.length === 0) {
      return res.status(400).json({ error: 'No contact groups found for this event' });
    }

    let savedGroupIds: string[] = [];

    // Handle the user's choice
    if (preference === 'save') {
      // Determine which groups to save
      let groupsToSaveData = allContactGroups;
      
      if (groupsToSave && Array.isArray(groupsToSave) && groupsToSave.length > 0) {
        // Filter to only save selected groups
        groupsToSaveData = allContactGroups.filter(group => 
          groupsToSave.includes(group.name)
        );
      }

      if (groupsToSaveData.length > 0) {
        // Save the contact groups
        savedGroupIds = await saveContactGroups(
          organizationId as string,
          eventId,
          groupsToSaveData
        );
      }

      // Mark organization as having been asked (one-time email)
      await db.updateOrganizationContactGroupSettings(organizationId as string, {
        hasBeenAsked: true
      });

      return res.status(200).json({
        success: true,
        message: `Successfully saved ${savedGroupIds.length} contact group(s)`,
        savedGroups: savedGroupIds.length,
        groupIds: savedGroupIds
      });

    } else {
      return res.status(400).json({ error: 'Invalid preference value' });
    }

  } catch (error) {
    console.error('Error handling contact group save request:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
