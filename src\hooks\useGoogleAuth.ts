// filepath: /home/<USER>/kitchen-sink/iamcoming/universe/src/hooks/useGoogleAuth.ts
import { useState } from 'react';
import { signIn, useSession } from "next-auth/react";
import { useToast } from "@/components/ui/use-toast";

export function useGoogleAuth() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // get redirect URL from url params
  const urlParams = new URLSearchParams(window.location.search);
  const redirectUrl = urlParams.get('redirectUrl') || '/events';

  /**
   * Initiates Google sign in
   * @param callbackUrl URL to redirect after successful sign in
   */
  const initiateGoogleSignIn = async (callbackUrl = redirectUrl) => {
    console.log("Initiating Google sign-in with callbackUrl:", callbackUrl);
    return signIn("google", { callbackUrl });
  };

  /**
   * Links a Google account to the current user
   * This is used when a user is already signed in with email and wants to link their Google account
   */
  const linkGoogleAccount = async () => {
    if (!session?.user?.id) {
      toast({
        title: "Not signed in",
        description: "You must be signed in to link a Google account",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Store the current user ID in sessionStorage before redirecting
      if (typeof window !== 'undefined') {
        // Use session storage to store the linking intent and user ID
        sessionStorage.setItem('googleLinkUserId', session.user.id);
        sessionStorage.setItem('googleLinkIntent', 'true');
      }

      // Use the standard Google sign-in but with a callback to the account page
      await signIn("google", {
        redirect: true,
        callbackUrl: '/account'
      });

      // Code below won't execute immediately as user will be redirected to Google
    } catch (error) {
      setIsLoading(false);
      toast({
        title: "Error linking Google account",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  /**
   * Unlinks a Google account from the current user
   */
  const unlinkGoogleAccount = async () => {
    if (!session?.user?.id) {
      toast({
        title: "Not signed in",
        description: "You must be signed in to unlink a Google account",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/unlink-google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: session.user.id }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to unlink Google account');
      }

      toast({
        title: "Google account unlinked",
        description: "Your Google account has been successfully unlinked",
      });

      // Force a session refresh
      window.location.reload();
    } catch (error) {
      toast({
        title: "Error unlinking Google account",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    initiateGoogleSignIn,
    linkGoogleAccount,
    unlinkGoogleAccount,
    isLoading
  };
}