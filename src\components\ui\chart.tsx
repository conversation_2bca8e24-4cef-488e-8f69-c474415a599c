"use client"

import * as Re<PERSON> from "react"
import { 
  <PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON> as <PERSON><PERSON>rtSector,
} from "recharts"
import { cn } from "@/lib/utils"

export interface ChartConfig {
  [key: string]: {
    label: string
    color?: string
    icon?: React.ComponentType<{ className?: string }>
    theme?: {
      light: string
      dark: string
    }
  }
}

interface ChartContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  config?: ChartConfig
  children: React.ReactElement
}

export function ChartContainer({
  config,
  className,
  children,
  ...props
}: ChartContainerProps) {
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Create CSS variables for each color in the config
  React.useEffect(() => {
    if (!config || !mounted) return

    const root = document.documentElement
    Object.entries(config).forEach(([key, value]) => {
      if (value.color) {
        root.style.setProperty(`--color-${key}`, value.color)
      }
    })

    return () => {
      if (!config) return
      Object.keys(config).forEach((key) => {
        root.style.removeProperty(`--color-${key}`)
      })
    }
  }, [config, mounted])

  return (
    <div className={cn("w-full", className)} {...props}>
      <ResponsiveContainer width="100%" height="100%">
        {children}
      </ResponsiveContainer>
    </div>
  )
}

interface ChartTooltipProps extends React.ComponentPropsWithoutRef<typeof RechartTooltip> {
  className?: string
}

export function ChartTooltip({
  className,
  content,
  ...props
}: ChartTooltipProps) {
  return (
    <RechartTooltip
      content={content}
      cursor={false}
      wrapperStyle={{ outline: "none" }}
      {...props}
    />
  )
}

interface ChartTooltipContentProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string
  active?: boolean
  payload?: Array<{
    name: string
    value: number
    payload: {
      name: string
      value: number
      fill?: string
      dataKey?: string
      [key: string]: any
    }
  }>
  label?: string
  labelKey?: string
  nameKey?: string
  valueKey?: string
  indicator?: "dot" | "line" | "dashed"
  hideLabel?: boolean
  hideIndicator?: boolean
}

export function ChartTooltipContent({
  className,
  active,
  payload,
  label,
  labelKey,
  nameKey,
  valueKey,
  indicator = "dot",
  hideLabel = false,
  hideIndicator = false,
  ...props
}: ChartTooltipContentProps) {
  if (!active || !payload?.length) {
    return null
  }

  return (
    <div
      className={cn(
        "rounded-lg border bg-background p-2 shadow-md",
        className
      )}
      {...props}
    >
      {!hideLabel && (
        <div className="mb-1 text-sm font-medium">
          {labelKey ? payload[0]?.payload[labelKey] : label}
        </div>
      )}
      <div className="flex flex-col gap-0.5">
        {payload.map((item, index) => {
          const dataKey = item.payload.dataKey
          const name = nameKey
            ? item.payload[nameKey]
            : dataKey
            ? dataKey.toString()
            : item.name
          const value = valueKey ? item.payload[valueKey] : item.value
          const fill = item.payload.fill || "#888"

          return (
            <div key={index} className="flex items-center gap-2">
              {!hideIndicator && (
                <>
                  {indicator === "dot" && (
                    <div
                      className="size-2 rounded-full"
                      style={{ backgroundColor: fill }}
                    />
                  )}
                  {indicator === "line" && (
                    <div
                      className="h-0.5 w-4"
                      style={{ backgroundColor: fill }}
                    />
                  )}
                  {indicator === "dashed" && (
                    <div
                      className="h-0.5 w-4 border-t-2"
                      style={{ borderColor: fill }}
                    />
                  )}
                </>
              )}
              <span className="text-xs font-medium">{name}</span>
              <span className="ml-auto text-xs font-medium">{value}</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}

interface ChartLegendProps extends React.ComponentPropsWithoutRef<typeof RechartLegend> {
  className?: string
}

export function ChartLegend({
  className,
  ...props
}: ChartLegendProps) {
  return (
    <RechartLegend
      className={cn("text-xs", className)}
      verticalAlign="middle"
      align="right"
      {...props}
    />
  )
}

interface ChartLegendContentProps extends React.HTMLAttributes<HTMLUListElement> {
  className?: string
  payload?: Array<{
    value: string
    type: string
    id: string
    color: string
    [key: string]: any
  }>
  nameKey?: string
}

export function ChartLegendContent({
  className,
  payload,
  nameKey,
  ...props
}: ChartLegendContentProps) {
  if (!payload?.length) {
    return null
  }

  return (
    <ul
      className={cn("flex flex-col gap-2", className)}
      {...props}
    >
      {payload.map((entry, index) => {
        const name = nameKey ? entry.payload?.[nameKey] : entry.value
        return (
          <li key={`item-${index}`} className="flex items-center gap-2">
            <div
              className="size-2 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-xs font-medium">{name}</span>
          </li>
        )
      })}
    </ul>
  )
}

interface PieChartProps extends React.ComponentPropsWithoutRef<typeof RechartPieChart> {
  className?: string
  data: Array<{
    name: string
    value: number
    [key: string]: any
  }>
  innerRadius?: number
  outerRadius?: number
  paddingAngle?: number
  dataKey?: string
  nameKey?: string
  valueKey?: string
  colors?: string[]
  children?: React.ReactNode
  renderLabel?: boolean
  renderActiveShape?: boolean
  activeIndex?: number
  onMouseEnter?: (data: any, index: number) => void
  centerText?: {
    title: string
    value: string | number
  }
}

export function PieChart({
  className,
  data,
  innerRadius = 0,
  outerRadius = 80,
  paddingAngle = 0,
  dataKey = "value",
  nameKey = "name",
  valueKey = "value",
  colors = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"],
  children,
  renderLabel = false,
  renderActiveShape = false,
  activeIndex = 0,
  onMouseEnter,
  centerText,
  ...props
}: PieChartProps) {
  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
    index,
  }: any) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5
    const x = cx + radius * Math.cos(-midAngle * (Math.PI / 180))
    const y = cy + radius * Math.sin(-midAngle * (Math.PI / 180))

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        className="text-xs"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    )
  }

  const renderActiveShapeComponent = (props: any) => {
    const {
      cx,
      cy,
      innerRadius,
      outerRadius,
      startAngle,
      endAngle,
      fill,
    } = props

    return (
      <g>
        <RechartSector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 5}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
      </g>
    )
  }

  return (
    <RechartPieChart className={cn("", className)} {...props}>
      <RechartPie
        data={data}
        dataKey={dataKey}
        nameKey={nameKey}
        valueKey={valueKey}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        paddingAngle={paddingAngle}
        label={renderLabel ? renderCustomizedLabel : undefined}
        activeIndex={renderActiveShape ? activeIndex : undefined}
        activeShape={renderActiveShape ? renderActiveShapeComponent : undefined}
        onMouseEnter={onMouseEnter}
      >
        {data.map((entry, index) => (
          <RechartCell
            key={`cell-${index}`}
            fill={entry.fill || colors[index % colors.length]}
          />
        ))}
        {centerText && (
          <text
            x="50%"
            y="50%"
            textAnchor="middle"
            dominantBaseline="middle"
            className="fill-current text-center"
          >
            <tspan x="50%" dy="-0.5em" className="text-lg font-bold">
              {centerText.value}
            </tspan>
            <tspan x="50%" dy="1.5em" className="text-xs text-muted-foreground">
              {centerText.title}
            </tspan>
          </text>
        )}
      </RechartPie>
      {children}
    </RechartPieChart>
  )
}
