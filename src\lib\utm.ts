import { debugLog } from './logger';

/**
 * UTM Parameter interface
 */
export interface UTMParams {
  utm_source?: string;    // Where traffic comes from (e.g., facebook, whatsapp, qr)
  utm_medium?: string;    // How it was shared (e.g., post, dm, ad, print)
  utm_campaign?: string;  // Campaign/event name (e.g., beta_launch, johns_wedding)
  utm_term?: string;      // Optional, for ad keywords
  utm_content?: string;   // Optional, for A/B variants
}

/**
 * Add UTM parameters to a URL
 * @param url Base URL to add parameters to
 * @param params UTM parameters to add
 * @returns URL with UTM parameters
 */
export function addUtmParams(url: string, params: UTMParams): string {
  if (!url) return url;
  
  try {
    const urlObj = new URL(url);
    
    // Add each UTM parameter if it exists
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        urlObj.searchParams.set(key, value);
      }
    });
    
    return urlObj.toString();
  } catch (error) {
    debugLog('Error adding UTM parameters to URL', { url, params, error });
    return url; // Return original URL if there's an error
  }
}

/**
 * Extract UTM parameters from a URL or search string
 * @param urlOrSearch URL or search string to extract parameters from
 * @returns Object containing UTM parameters
 */
export function extractUtmParams(urlOrSearch: string): UTMParams {
  const params: UTMParams = {};
  
  try {
    let searchParams: URLSearchParams;
    
    if (urlOrSearch.startsWith('http')) {
      // If it's a full URL, parse it
      const urlObj = new URL(urlOrSearch);
      searchParams = urlObj.searchParams;
    } else if (urlOrSearch.startsWith('?')) {
      // If it's just a search string, parse it directly
      searchParams = new URLSearchParams(urlOrSearch);
    } else {
      // If it's something else, try to parse it as a search string
      searchParams = new URLSearchParams(`?${urlOrSearch}`);
    }
    
    // Extract UTM parameters
    if (searchParams.has('utm_source')) params.utm_source = searchParams.get('utm_source') || undefined;
    if (searchParams.has('utm_medium')) params.utm_medium = searchParams.get('utm_medium') || undefined;
    if (searchParams.has('utm_campaign')) params.utm_campaign = searchParams.get('utm_campaign') || undefined;
    if (searchParams.has('utm_term')) params.utm_term = searchParams.get('utm_term') || undefined;
    if (searchParams.has('utm_content')) params.utm_content = searchParams.get('utm_content') || undefined;
    
    return params;
  } catch (error) {
    debugLog('Error extracting UTM parameters', { urlOrSearch, error });
    return {};
  }
}

/**
 * Generate UTM parameters for different sharing methods
 * @param source Source of the traffic (e.g., facebook, whatsapp)
 * @param medium Medium of sharing (e.g., post, dm)
 * @param campaign Campaign name (defaults to 'general')
 * @param additionalParams Additional UTM parameters
 * @returns Complete UTM parameters object
 */
export function generateUtmParams(
  source: string,
  medium: string,
  campaign: string = 'general',
  additionalParams: Partial<UTMParams> = {}
): UTMParams {
  return {
    utm_source: source,
    utm_medium: medium,
    utm_campaign: campaign,
    ...additionalParams
  };
}

/**
 * Generate UTM parameters for QR codes
 * @param eventId Event ID for campaign parameter
 * @param content Optional content identifier
 * @returns UTM parameters for QR code
 */
export function generateQrUtmParams(eventId: string, content?: string): UTMParams {
  return {
    utm_source: 'qr',
    utm_medium: 'print',
    utm_campaign: `event_${eventId}`,
    ...(content ? { utm_content: content } : {})
  };
}

/**
 * Generate UTM parameters for email sharing
 * @param eventId Event ID for campaign parameter
 * @param content Optional content identifier
 * @returns UTM parameters for email sharing
 */
export function generateEmailUtmParams(eventId: string, content?: string): UTMParams {
  return {
    utm_source: 'email',
    utm_medium: 'invite',
    utm_campaign: `event_${eventId}`,
    ...(content ? { utm_content: content } : {})
  };
}

/**
 * Generate UTM parameters for social sharing
 * @param platform Social platform (e.g., facebook, whatsapp)
 * @param eventId Event ID for campaign parameter
 * @param content Optional content identifier
 * @returns UTM parameters for social sharing
 */
export function generateSocialUtmParams(platform: string, eventId: string, content?: string): UTMParams {
  return {
    utm_source: platform.toLowerCase(),
    utm_medium: 'social',
    utm_campaign: `event_${eventId}`,
    ...(content ? { utm_content: content } : {})
  };
}
