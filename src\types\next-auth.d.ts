import "next-auth"
import { JWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name: string | null
      email: string
      image: string | null
      isProfileComplete: boolean
      hasGoogleLinked?: boolean
      googleEmail?: string | null
      isAdmin?: boolean
      hasAiAccess?: boolean
      organization?: {
        id: string
        name: string
        type: 'individual' | 'partner'
      }
    }
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    sub: string
    googleId?: string
    hasGoogleLinked?: boolean
    isAdmin?: boolean
    hasAiAccess?: boolean
  }
}