import { useCallback } from 'react';
import { handleDropdownToModalTransition } from '@/lib/utils';

/**
 * Custom hook for managing focus when transitioning from dropdowns to modals
 * This prevents aria-hidden conflicts and improves accessibility
 */
export function useDropdownToModal() {
  const transitionToModal = useCallback((callback: () => void, delay?: number) => {
    handleDropdownToModalTransition(callback, delay);
  }, []);

  return { transitionToModal };
}
