"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog"
import { Mail, Loader2, Check, MessageSquare } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { EventInvite } from "@/types"

interface SendInviteEmailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  importedInvites: EventInvite[]
  eventId: string
  importType: 'bulk' | 'groups' // to differentiate between CSV upload and saved groups
}

export function SendInviteEmailsDialog({
  open,
  onOpenChange,
  importedInvites,
  eventId,
  importType
}: SendInviteEmailsDialogProps) {  const [sending, setSending] = useState(false)
  const [emailsSent, setEmailsSent] = useState(0)
  const { toast } = useToast()

  // Filter invites that have email addresses
  const invitesWithEmail = importedInvites.filter(invite => 
    invite.email && invite.email.trim() !== ''
  )

  const handleSendEmails = async () => {
    if (invitesWithEmail.length === 0) {
      toast({
        title: "No email addresses",
        description: "None of the imported contacts have email addresses.",
        variant: "destructive"
      })
      return
    }

    setSending(true)
    setEmailsSent(0)

    try {
      let successCount = 0
      
      // Send emails in batches to avoid overwhelming the server
      const batchSize = 5
      for (let i = 0; i < invitesWithEmail.length; i += batchSize) {
        const batch = invitesWithEmail.slice(i, i + batchSize)
        
        const batchPromises = batch.map(async (invite) => {
          try {
            const response = await fetch(`/api/event/${eventId}/invites/${invite.ID}/send-email`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
            })

            if (response.ok) {
              successCount++
              setEmailsSent(successCount)
              return true
            } else {
              console.error(`Failed to send email to ${invite.email}`)
              return false
            }
          } catch (error) {
            console.error(`Error sending email to ${invite.email}:`, error)
            return false
          }
        })

        await Promise.all(batchPromises)
        
        // Small delay between batches
        if (i + batchSize < invitesWithEmail.length) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      toast({
        title: "Invitations sent!",
        description: `Successfully sent ${successCount} invitation email${successCount !== 1 ? 's' : ''}.`,
      })

      // Close dialog after successful sending
      setTimeout(() => {
        onOpenChange(false)
      }, 1500)

    } catch (error) {
      console.error('Error sending invitation emails:', error)
      toast({
        title: "Error",
        description: "Failed to send some invitation emails. Please try again.",
        variant: "destructive"
      })
    } finally {
      setSending(false)
    }
  }

  const handleSkip = () => {
    onOpenChange(false)
  }

  const importTypeText = importType === 'bulk' ? 'CSV upload' : 'saved contact groups'

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0 w-full max-w-md sm:max-w-md">
        <Card className="border-0 shadow-none w-full">
          <CardContent className="pt-6 pb-4 px-6">
            <div className="flex flex-col items-center text-center">
              {/* Success icon */}
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Check className="h-8 w-8 text-green-500" />
              </div>

              {/* Success message */}
              <h2 className="text-2xl font-semibold mb-2">Contacts Imported Successfully</h2>
              <p className="text-muted-foreground mb-6">
                {importedInvites.length} contact{importedInvites.length !== 1 ? 's' : ''} imported from {importTypeText}<br />
                Send invitations to your guests
              </p>

              {/* Availability message */}
              {invitesWithEmail.length !== importedInvites.length && (
                <div className="w-full py-1 px-4 bg-yellow-50 border border-amber-300 text-center rounded-md text-sm mb-3">
                  {importedInvites.length - invitesWithEmail.length} contact{importedInvites.length - invitesWithEmail.length !== 1 ? 's' : ''} without email will be skipped
                </div>
              )}

              {/* Send invitation buttons */}
              <Button
                variant="outline"
                onClick={handleSendEmails}
                className="flex items-center justify-center w-full mb-3"
                disabled={invitesWithEmail.length === 0 || sending}
              >
                {sending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending... ({emailsSent}/{invitesWithEmail.length})
                  </>
                ) : (
                  <>
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">Send invitation over email</span>
                    <span className="sm:hidden">Email Invite</span>
                  </>
                )}
              </Button>

              {/* No email availability message */}
              {invitesWithEmail.length === 0 && (
                <div className="w-full py-1 px-4 bg-yellow-50 border border-yellow-100 text-center rounded-md text-sm mb-3">
                  Email addresses are not available
                </div>
              )}

              <Button
                variant="outline"
                className="flex items-center justify-center w-full mb-6"
                disabled={true}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Send invitation as a text</span>
                <span className="sm:hidden">Text Invite</span>
                <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">Coming Soon</span>
              </Button>

              {/* Done button */}
              <Button
                variant='primary-button'
                onClick={handleSkip}
                className="w-full"
                disabled={sending}
              >
                Done
              </Button>
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}
