import { InvitePageSize, Orientation } from "@/types";
import { useState, useEffect } from "react";

interface InvitePreviewProps {
  eventId: string;
  size?: InvitePageSize;
  orientation?: Orientation;
  qrxp?: string;
  qryp?: string;
  format?: string;
  key?: number; // Added key prop to force re-render
  name?: string; // Add name prop for the preview
  bgColor?: string; // Background color for QR label
  qrColor?: string; // QR code color
  labelOrientation?: Orientation; // Orientation for the label
  showBranding?: boolean; // Show branding on the label
  fullSize?: boolean; // Show image at full size
}

export function InvitePreview({
  eventId,
  size = 'A5',
  orientation = 'portrait',
  qrxp = '50',
  qryp = '80',
  format = 'jpg',
  name,
  bgColor = '#FFFFFF',
  qrColor = '#000000',
  labelOrientation = 'portrait',
  showBranding = true, // Default to true for branding
  fullSize = false, // Default to false for backward compatibility
}: InvitePreviewProps) {
  const [loading, setLoading] = useState(true);
  const [url, setUrl] = useState(() => {
    const params = new URLSearchParams();
    params.append('eventId', eventId);
    params.append('format', format);
    params.append('qrxp', qrxp);
    params.append('qryp', qryp);
    params.append('size', size);
    params.append('orientation', orientation);
    if (name) {
      params.append('name', name);
    }
    params.append('bg-qr', bgColor);
    params.append('color-qr', qrColor);
    params.append('label-orientation', labelOrientation);
    if (showBranding) {
      params.append('show-branding', showBranding.toString());
    }
    params.append('t', Date.now().toString()); // Add timestamp initially
    return `/api/media/invite?${params.toString()}`;
  });

  useEffect(() => {
    const params = new URLSearchParams();
    params.append('eventId', eventId);
    params.append('format', format);
    params.append('qrxp', qrxp);
    params.append('qryp', qryp);
    params.append('size', size);
    params.append('orientation', orientation);
    if (name) {
      params.append('name', name);
    }
    params.append('bg-qr', bgColor);
    params.append('color-qr', qrColor);
    params.append('label-orientation', labelOrientation);
    if (showBranding) {
      params.append('show-branding', showBranding.toString());
    }
    params.append('t', Date.now().toString()); // Update timestamp only when props change
    setUrl(`/api/media/invite?${params.toString()}`);
  }, [eventId, format, qrxp, qryp, size, orientation, name, bgColor, qrColor, labelOrientation, showBranding]);

  useEffect(() => {
    setLoading(true);
  }, [url]);

  return (
    <div className="relative flex items-center justify-center">
      {loading && (
        <div className="absolute w-full inset-0 flex items-center justify-center bg-transparent bg-opacity-50">
          <div className="w-12 h-12 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin" />
        </div>
      )}
      <img
        src={url}
        alt="Invite Preview"
        className={`shadow-xl border-1 ${loading ? 'opacity-30' : 'opacity-100'} ${
          fullSize ? 'w-full h-auto max-w-none' : 'm-4'
        }`}
        onLoad={() => setLoading(false)}
        onError={() => setLoading(false)}
      />
    </div>
  );
}