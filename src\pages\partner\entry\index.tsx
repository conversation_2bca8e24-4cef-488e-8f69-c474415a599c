'use client'

import { useState, useEffect, useRef } from "react"
import { PartnerLayout } from "@/components/layouts/PartnerLayout"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/router"
import { Check, Clock, QrCode, Search, User, X, Camera, RefreshCw } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useSession } from "next-auth/react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import dynamic from "next/dynamic"

// Import QR Scanner dynamically to avoid SSR issues
const QrScanner = dynamic(() => import('react-qr-scanner'), {
  ssr: false,
})
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Mock data for events
const mockEvents = [
  {
    id: "event-1",
    name: "Summer Wedding",
    date: new Date(2023, 11, 15),
    venue: "Grand Ballroom",
    location: "Main Hall",
    totalGuests: 120,
    checkedIn: 85
  },
  {
    id: "event-2",
    name: "Corporate Gala",
    date: new Date(2023, 11, 20),
    venue: "Garden Pavilion",
    location: "Garden Area",
    totalGuests: 80,
    checkedIn: 0
  }
];

// Mock data for guest entries
const mockGuestEntries = [
  {
    id: "entry-1",
    eventId: "event-1",
    guestName: "John Smith",
    email: "<EMAIL>",
    checkInTime: new Date(2023, 11, 15, 18, 30),
    status: "checked_in"
  },
  {
    id: "entry-2",
    eventId: "event-1",
    guestName: "Sarah Johnson",
    email: "<EMAIL>",
    checkInTime: new Date(2023, 11, 15, 18, 45),
    status: "checked_in"
  },
  {
    id: "entry-3",
    eventId: "event-1",
    guestName: "Michael Brown",
    email: "<EMAIL>",
    checkInTime: null,
    status: "pending"
  }
];

export default function GuestEntryControl() {
  const router = useRouter()
  const { data: session } = useSession()
  const [selectedEvent, setSelectedEvent] = useState<string>("")
  const [searchTerm, setSearchTerm] = useState("")
  const [isScannerActive, setIsScannerActive] = useState(false)
  const [scanResult, setScanResult] = useState<any>(null)
  const [guestEntries, setGuestEntries] = useState(mockGuestEntries)
  const [isGuestDetailsOpen, setIsGuestDetailsOpen] = useState(false)
  const [selectedGuest, setSelectedGuest] = useState<any>(null)
  const videoRef = useRef<HTMLVideoElement>(null)

  // Filter guest entries based on selected event and search term
  const filteredEntries = guestEntries.filter(entry =>
    (!selectedEvent || entry.eventId === selectedEvent) &&
    (entry.guestName.toLowerCase().includes(searchTerm.toLowerCase()) ||
     entry.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Get event details
  const getEventDetails = (eventId: string) => {
    return mockEvents.find(event => event.id === eventId);
  };

  // Handle QR code scan
  const handleScan = (data: any) => {
    if (data) {
      console.log("QR Code Scanned:", data);
      setScanResult(data);
      setIsScannerActive(false);

      // Extract guest information from QR code data
      // In a real app, this would parse the actual QR code format
      // For now, we'll create a guest based on the data or use mock data

      let guestName = "Unknown Guest";
      let guestEmail = "";

      if (data.name) {
        guestName = data.name;
      } else if (data.guestName) {
        guestName = data.guestName;
      }

      if (data.email) {
        guestEmail = data.email;
      } else if (data.guestEmail) {
        guestEmail = data.guestEmail;
      }

      // If we couldn't extract meaningful data, use mock data
      if (guestName === "Unknown Guest" && !guestEmail) {
        guestName = "Alex Thompson";
        guestEmail = "<EMAIL>";
      }

      // Check if this guest already exists in our list
      const existingGuest = guestEntries.find(entry =>
        entry.eventId === selectedEvent &&
        (entry.email === guestEmail || entry.guestName === guestName)
      );

      if (existingGuest) {
        setSelectedGuest(existingGuest);
      } else {
        // Create a new guest entry
        const newGuest = {
          id: `entry-${Date.now()}`,
          eventId: selectedEvent,
          guestName: guestName,
          email: guestEmail,
          checkInTime: null,
          status: "pending"
        };
        setSelectedGuest(newGuest);
      }

      setIsGuestDetailsOpen(true);
    }
  };

  // Handle manual check-in
  const handleManualCheckIn = (guestId: string) => {
    const guest = guestEntries.find(entry => entry.id === guestId);
    if (guest) {
      setSelectedGuest(guest);
      setIsGuestDetailsOpen(true);
    }
  };

  // Handle check-in confirmation
  const handleConfirmCheckIn = () => {
    if (!selectedGuest) return;

    // Update guest status
    const updatedEntries = guestEntries.map(entry => {
      if (entry.id === selectedGuest.id) {
        return {
          ...entry,
          checkInTime: new Date(),
          status: "checked_in"
        };
      }
      return entry;
    });

    // If this is a new guest from QR scan, add them to the list
    if (!guestEntries.find(entry => entry.id === selectedGuest.id)) {
      updatedEntries.push({
        ...selectedGuest,
        checkInTime: new Date(),
        status: "checked_in"
      });
    }

    setGuestEntries(updatedEntries);
    setIsGuestDetailsOpen(false);
    setScanResult(null);
  };

  // Format date and time
  const formatDateTime = (date: Date | null) => {
    if (!date) return "Not checked in";
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "checked_in":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Checked In</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Pending</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Handle camera permissions and errors
  useEffect(() => {
    if (isScannerActive) {
      // Request camera permissions when scanner is activated
      navigator.mediaDevices.getUserMedia({ video: true })
        .catch(error => {
          console.error("Camera access error:", error);
          alert("Unable to access camera. Please check your camera permissions.");
          setIsScannerActive(false);
        });
    }
  }, [isScannerActive]);

  return (
    <PartnerLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Guest Entry Control</h1>
            <p className="text-muted-foreground">Manage guest check-ins and event access</p>
          </div>
          <Button
            variant={isScannerActive ? "destructive" : "primary-button"}
            onClick={() => setIsScannerActive(!isScannerActive)}
            disabled={!selectedEvent}
          >
            {isScannerActive ? (
              <>
                <X className="mr-2 h-4 w-4" />
                Stop Scanner
              </>
            ) : (
              <>
                <QrCode className="mr-2 h-4 w-4" />
                Scan QR Code
              </>
            )}
          </Button>
        </div>

        {/* Event Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Select Event</CardTitle>
            <CardDescription>
              Choose the event you want to manage guest entry for
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="event-select">Event</Label>
                <Select
                  value={selectedEvent}
                  onValueChange={setSelectedEvent}
                >
                  <SelectTrigger id="event-select">
                    <SelectValue placeholder="Select an event" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockEvents.map((event) => (
                      <SelectItem key={event.id} value={event.id}>
                        {event.name} - {event.date.toLocaleDateString()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedEvent && (
                <div className="space-y-2">
                  <Label>Event Details</Label>
                  <Card className="p-4">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="text-muted-foreground">Venue:</p>
                        <p className="font-medium">{getEventDetails(selectedEvent)?.venue}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Location:</p>
                        <p className="font-medium">{getEventDetails(selectedEvent)?.location}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Total Guests:</p>
                        <p className="font-medium">{getEventDetails(selectedEvent)?.totalGuests}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Checked In:</p>
                        <p className="font-medium">{getEventDetails(selectedEvent)?.checkedIn} / {getEventDetails(selectedEvent)?.totalGuests}</p>
                      </div>
                    </div>
                  </Card>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* QR Code Scanner */}
        {isScannerActive && (
          <Card>
            <CardHeader>
              <CardTitle>QR Code Scanner</CardTitle>
              <CardDescription>
                Scan a guest&apos;s QR code to check them in
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center">
              <div className="relative w-full max-w-md mb-4">
                <div className="aspect-video bg-gray-900 rounded-lg overflow-hidden">
                  <QrScanner
                    delay={300}
                    onError={(err: any) => {
                      console.error("QR Scanner Error:", err);
                    }}
                    onScan={(data: any) => {
                      if (data) {
                        // Parse the QR code data
                        try {
                          const qrData = JSON.parse(data.text);
                          handleScan(qrData);
                        } catch (e) {
                          // If not JSON, try to use the raw text
                          handleScan({ rawData: data.text });
                        }
                      }
                    }}
                    style={{ width: '100%', height: '100%' }}
                    constraints={{
                      video: {
                        facingMode: "environment", // Use the back camera on mobile devices
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                      }
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                    <div className="w-48 h-48 border-2 border-white/50 rounded-lg"></div>
                  </div>
                </div>
                <div className="bg-black/50 text-white text-center py-2 rounded-b-lg">
                  <p className="text-sm">Position the QR code within the frame</p>
                </div>
              </div>
              <div className="flex items-center justify-center gap-4">
                <Button variant="outline" onClick={() => setIsScannerActive(false)}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button variant="outline" onClick={() => setIsScannerActive(true)}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Reset Scanner
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Guest List */}
        {selectedEvent && !isScannerActive && (
          <Card>
            <CardHeader>
              <CardTitle>Guest Check-In List</CardTitle>
              <CardDescription>
                View and manage guest check-ins for this event
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search guests by name or email..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <Tabs defaultValue="all" className="w-full">
                <TabsList className="grid w-full grid-cols-3 mb-4">
                  <TabsTrigger value="all">All Guests</TabsTrigger>
                  <TabsTrigger value="checked_in">Checked In</TabsTrigger>
                  <TabsTrigger value="pending">Pending</TabsTrigger>
                </TabsList>

                <TabsContent value="all">
                  <GuestTable
                    entries={filteredEntries}
                    formatDateTime={formatDateTime}
                    getStatusBadge={getStatusBadge}
                    onCheckIn={handleManualCheckIn}
                  />
                </TabsContent>

                <TabsContent value="checked_in">
                  <GuestTable
                    entries={filteredEntries.filter(entry => entry.status === "checked_in")}
                    formatDateTime={formatDateTime}
                    getStatusBadge={getStatusBadge}
                    onCheckIn={handleManualCheckIn}
                  />
                </TabsContent>

                <TabsContent value="pending">
                  <GuestTable
                    entries={filteredEntries.filter(entry => entry.status === "pending")}
                    formatDateTime={formatDateTime}
                    getStatusBadge={getStatusBadge}
                    onCheckIn={handleManualCheckIn}
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        )}

        {/* Guest Details Dialog */}
        <Dialog open={isGuestDetailsOpen} onOpenChange={setIsGuestDetailsOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Guest Check-In</DialogTitle>
              <DialogDescription>
                Confirm guest details and check-in status
              </DialogDescription>
            </DialogHeader>

            {selectedGuest && (
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarFallback>{selectedGuest.guestName.split(' ').map((n: string) => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-xl font-semibold">{selectedGuest.guestName}</h3>
                    <p className="text-muted-foreground">{selectedGuest.email}</p>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Event</p>
                      <p className="font-medium">{getEventDetails(selectedGuest.eventId)?.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Status</p>
                      <div className="mt-1">
                        {getStatusBadge(selectedGuest.status)}
                      </div>
                    </div>
                    {selectedGuest.checkInTime && (
                      <div className="col-span-2">
                        <p className="text-sm text-muted-foreground">Check-In Time</p>
                        <p className="font-medium">{formatDateTime(selectedGuest.checkInTime)}</p>
                      </div>
                    )}
                  </div>
                </div>

                {selectedGuest.status === "pending" ? (
                  <Alert className="bg-green-50 border-green-200">
                    <Check className="h-4 w-4 text-green-600" />
                    <AlertTitle className="text-green-800">Ready to Check In</AlertTitle>
                    <AlertDescription className="text-green-700">
                      This guest is ready to be checked in to the event.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert className="bg-blue-50 border-blue-200">
                    <Check className="h-4 w-4 text-blue-600" />
                    <AlertTitle className="text-blue-800">Already Checked In</AlertTitle>
                    <AlertDescription className="text-blue-700">
                      This guest has already been checked in at {formatDateTime(selectedGuest.checkInTime)}.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsGuestDetailsOpen(false)}>
                Cancel
              </Button>
              {selectedGuest && selectedGuest.status === "pending" && (
                <Button onClick={handleConfirmCheckIn}>
                  <Check className="mr-2 h-4 w-4" />
                  Confirm Check-In
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </PartnerLayout>
  )
}

// Guest Table Component
function GuestTable({
  entries,
  formatDateTime,
  getStatusBadge,
  onCheckIn
}: {
  entries: any[],
  formatDateTime: (date: Date | null) => string,
  getStatusBadge: (status: string) => React.ReactNode,
  onCheckIn: (id: string) => void
}) {
  return (
    entries.length === 0 ? (
      <div className="text-center py-8">
        <User className="h-12 w-12 text-gray-300 mx-auto mb-4" />
        <p className="text-muted-foreground">No guests found.</p>
      </div>
    ) : (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Guest</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Check-In Time</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {entries.map((entry) => (
            <TableRow key={entry.id}>
              <TableCell className="font-medium">{entry.guestName}</TableCell>
              <TableCell>{entry.email}</TableCell>
              <TableCell>{formatDateTime(entry.checkInTime)}</TableCell>
              <TableCell>{getStatusBadge(entry.status)}</TableCell>
              <TableCell className="text-right">
                {entry.status === "pending" ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onCheckIn(entry.id)}
                  >
                    <Check className="mr-2 h-3.5 w-3.5" />
                    Check In
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onCheckIn(entry.id)}
                  >
                    View
                  </Button>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    )
  );
}
