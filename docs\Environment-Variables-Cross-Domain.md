# Environment Variables for Cross-Domain Authentication

The cross-domain authentication system requires the following environment variables:

## Required Variables

### `AUTH_SECRET`

- **Required**: Yes
- **Description**: Secret key used to sign JWT tokens for cross-domain authentication
- **Example**: `your-secret-key-here`
- **Note**: This should be a strong, random string. Use the same secret across all domains that need to verify tokens.

### `NODE_ENV`

- **Required**: Yes
- **Description**: Environment mode (affects cookie domains and security settings)
- **Values**: `development` | `production`
- **Note**: In production, cookies will be set for `.iamcoming.io` domain

## Optional Variables

### `IAC_DEBUG`

- **Required**: No
- **Description**: Enable debug logging for authentication
- **Values**: `true` | `false`
- **Default**: `false`

## Domain Configuration

The system is configured to support the following domains:

### Production

- Main domain: `iamcoming.io`
- Partner domain: `partner.iamcoming.io`
- Cookie domain: `.iamcoming.io`

### Development

- Main domain: `localhost:3000`
- Partner domain: `partner.iamcoming.io` (for testing)
- Local domain: `localhost:3000`
- Cookie domain: `localhost`

## Security Notes

1. **AUTH_SECRET**: Must be kept secure and should be the same across all domains that need to verify tokens
2. **CORS**: Only allows specific origins based on the domain configuration
3. **Token Expiry**: Session tokens expire after 1 hour by default
4. **Allowed Audiences**: Tokens specify allowed audiences to prevent misuse

## Testing

To test the configuration, ensure:

1. `AUTH_SECRET` is set in your environment
2. `NODE_ENV` is set appropriately
3. All NextAuth configuration is properly set up
4. The application is running on the expected ports/domains
