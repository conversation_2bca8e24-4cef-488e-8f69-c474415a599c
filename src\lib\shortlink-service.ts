import { SL_AnalyticsResponse, SL_AnalyticsStats, SL_GroupedAnalytics, SL_ShortLinkResponse } from "@/types/shortlinks-api";

/**
 * ShortlinkService - A client for interacting with the external shortlink API at l.iamcoming.io
 * Uses the native fetch API and gets JWT tokens from the server
 */
export class ShortlinkService {
  private baseUrl: string;
  private jwtToken: string | null = null;

  constructor(baseUrl = 'https://l.iamcoming.io') {
    this.baseUrl = baseUrl;
  }

  /**
   * Get JWT token from server endpoint
   */
  private async getJWTToken(): Promise<string> {
    if (this.jwtToken) {
      return this.jwtToken;
    }

    try {
      const response = await fetch('/api/shortlink/auth');
      if (!response.ok) {
        throw new Error(`Failed to get JWT token: ${response.status}`);
      }

      const data = await response.json();
      this.jwtToken = data.token;
      return this.jwtToken!; // We know it's not null here
    } catch (error) {
      console.error('Error getting JWT token:', error);
      throw new Error('Failed to authenticate with shortlink service');
    }
  }

  /**
   * Get the authorization headers for API requests
   */
  private async getAuthHeaders(): Promise<HeadersInit> {
    const token = await this.getJWTToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Handle API response and errors
   */
  private async handleResponse(response: Response) {
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error (${response.status}): ${errorText}`);
    }

    // Return JSON if the content type is JSON, otherwise return text
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    }
    return response.text();
  }

  /**
   * Create a new short link
   * @param shortCode - The desired short code
   * @param redirectUrl - The URL to redirect to
   * @returns The created short link data
   */
  async createShortLink(shortCode: string, redirectUrl: string): Promise<SL_ShortLinkResponse> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify({ shortCode, redirectUrl })
      });
      console.log(`Creating short link: ${shortCode} → ${redirectUrl}`, {
        baseUrl: this.baseUrl,
        method: 'POST',
        headers,
        body: JSON.stringify({ shortCode, redirectUrl })
      });


      return this.handleResponse(response);
    } catch (error) {
      console.error('Error creating short link:', error);
      throw error;
    }
  }

  /**
   * List all short links in the system
   * @returns Array of short link objects
   */
  async listShortLinks(): Promise<SL_ShortLinkResponse[]> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/list`, {
        method: 'GET',
        headers
      });

      return this.handleResponse(response);
    } catch (error) {
      console.error('Error listing short links:', error);
      throw error;
    }
  }

  /**
   * Delete a short link
   * @param shortCode - The short code to delete
   * @returns Confirmation message
   */
  async deleteShortLink(shortCode: string) {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/${shortCode}`, {
        method: 'DELETE',
        headers
      });

      return this.handleResponse(response);
    } catch (error) {
      console.error('Error deleting short link:', error);
      throw error;
    }
  }

  /**
   * Get analytics for a specific short link
   * @param shortCode - The short code to get analytics for
   * @returns Analytics data for the short link
   */
  async getShortLinkAnalytics(shortCode: string): Promise<SL_GroupedAnalytics> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/analytics/${shortCode}`, {
        method: 'GET',
        headers
      });

      if (!response.ok && response.status === 404) {
        // Return an empty analytics object if the short link doesn't exist yet
        return {
          shortCode: shortCode,
          summary: {
            hits: 0,
            locations: {},
            devices: {
              desktop: 0,
              mobile: 0,
              tablet: 0
            },
            os: {},
            logs: []
          },
          grouped: {
            daily: {},
            weekly: {}
          }
        };
      }

      return this.handleResponse(response);
    } catch (error) {
      console.error('Error getting analytics for short link:', error);
      throw error;
    }
  }

  /**
   * Get analytics for all short links
   * @returns Analytics data for all short links
   */
  async getAllAnalytics() {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/analytics`, {
        method: 'GET',
        headers
      });

      return this.handleResponse(response);
    } catch (error) {
      console.error('Error getting analytics for all short links:', error);
      throw error;
    }
  }

  /**
   * Get the full URL for a short link
   * @param shortCode - The short code
   * @returns The full URL
   */
  getFullUrl(shortCode: string): string {
    return `${this.baseUrl}/${shortCode}`;
  }

  /**
   * Update a short link by deleting the old one and creating a new one
   * @param shortCode - The short code to update
   * @param redirectUrl - The new URL to redirect to
   * @returns The updated short link data
   */
  async updateShortLink(shortCode: string, redirectUrl: string) {
    try {
      // Delete the existing shortlink
      await this.deleteShortLink(shortCode);

      // Create the new shortlink with the same short code
      return await this.createShortLink(shortCode, redirectUrl);
    } catch (error) {
      console.error('Error updating short link:', error);
      throw error;
    }
  }
}

// Create a singleton instance with default configuration
export const shortlinkService = new ShortlinkService();

export default shortlinkService;
