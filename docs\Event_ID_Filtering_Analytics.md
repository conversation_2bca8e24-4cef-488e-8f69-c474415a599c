# Event and Invite ID Filtering in Google Analytics

## Overview

The enhanced URL grouping implementation now extracts event IDs and invite IDs from your URLs and passes them as custom parameters to Google Analytics. For RSVP URLs following the pattern `/event/[eventId]/rsvp/[inviteId]`, both the event ID and invite ID are extracted. This allows you to filter and query analytics data by specific events and invites while still benefiting from grouped URL patterns.

## Custom Parameters for Filtering

### Available Custom Parameters

| Parameter            | Contains        | Example Usage                       |
| -------------------- | --------------- | ----------------------------------- |
| `custom_parameter_1` | Event ID        | Filter all data for event "abc123"  |
| `custom_parameter_2` | Invite ID       | Filter all data for invite "inv456" |
| `custom_parameter_3` | User ID         | Filter all data for user "user123"  |
| `custom_parameter_4` | Organization ID | Filter all data for org "org456"    |

### Additional Context Parameters

| Parameter       | Contains                  | Example Values                                                                                                                 |
| --------------- | ------------------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| `event_context` | Page context within event | `invites_management`, `rsvp_flow`, `event_settings`, `event_analytics`, `guest_management`, `event_messaging`, `event_details` |
| `page_type`     | General page category     | `event`, `rsvp`, `user`, `organization`, `general`                                                                             |

## Google Analytics Setup

### Step 1: Configure Custom Parameters (GA4)

In Google Analytics 4:

1. Go to **Configure > Custom Definitions > Custom Dimensions**
2. Create these custom dimensions:

| Dimension Name  | Parameter Name       | Scope | Description                           |
| --------------- | -------------------- | ----- | ------------------------------------- |
| Event ID        | `custom_parameter_1` | Event | Event identifier for filtering        |
| Invite ID       | `custom_parameter_2` | Event | Invite identifier for filtering       |
| User ID         | `custom_parameter_3` | Event | User identifier for filtering         |
| Organization ID | `custom_parameter_4` | Event | Organization identifier for filtering |
| Event Context   | `event_context`      | Event | Context within event pages            |
| Page Type       | `page_type`          | Event | General page category                 |

### Step 2: Test Custom Parameters

Use Google Analytics DebugView to verify parameters are being sent:

1. Enable Debug Mode: Add `?gtm_debug=1` to your URL
2. Navigate to **Admin > DebugView** in GA4
3. Check that events include your custom parameters

## Filtering Examples

### 1. Filter by Specific Event

**Use Case**: See all analytics data for a specific event

**Filter Setup**:

- Dimension: Any (Page views, Events, etc.)
- Filter: `Event ID` (custom_parameter_1) equals `"your-event-id"`

**Example Queries**:

```text
Event ID = "wedding-abc123"
Event ID = "birthday-xyz789"
```

### 2. Filter by Event and Invite

**Use Case**: Analyze specific invite performance within an event

**Filter Setup**:

- Primary Filter: `Event ID` equals `"your-event-id"`
- Secondary Filter: `Invite ID` equals `"your-invite-id"`

**Example Query**:

```text
Event ID = "wedding-abc123" AND Invite ID = "inv-456"
```

### 3. Filter by Event Context

**Use Case**: Compare different sections of event management

**Filter Setup**:

- Filter: `Event Context` equals specific context
- Group by: `Page Group` or `Page Title`

**Example Contexts**:

```text
event_context = "invites_management"  // All invite-related pages
event_context = "rsvp_flow"          // All RSVP pages
event_context = "event_settings"     // Settings pages
event_context = "guest_management"   // Guest management pages
```

### 4. Multi-Event Analysis

**Use Case**: Compare performance across multiple events

**Filter Setup**:

- Dimension: `Event ID`
- Secondary Dimension: `Page Group`
- Filter: `Page Type` equals `"event"`

## Example Reports

### 1. Event Performance Dashboard

**Metrics**: Page views, Users, Session duration
**Dimensions**:

- Primary: `Event ID`
- Secondary: `Page Group`

**Insights**: Which events get the most engagement and which pages within events are most popular.

### 2. Invite Management Analysis

**Filter**: `Event Context` = `"invites_management"`
**Dimensions**:

- Primary: `Event ID`
- Secondary: `Invite ID`

**Insights**: How hosts are managing invites, which events have the most invite activity.

### 3. RSVP Conversion Funnel

**Setup**:

1. **Step 1**: `Page Group` = `"/rsvp/*"`
2. **Step 2**: `Page Group` = `"/rsvp/*/success"`
3. **Filter by**: Specific `Event ID` if needed

**Insights**: RSVP completion rates overall or for specific events.

### 4. Event Journey Analysis

**Exploration Type**: Path Exploration
**Starting Point**: `Page Group` = `"/event/*"`
**Dimensions**: `Event ID`, `Page Group`

**Insights**: How users navigate through event management pages.

## Code Examples

### Track Specific Event Actions

```typescript
import { trackEventAction } from '@/components/GoogleAnalytics';

// Track when someone views event details
trackEventAction('view_event_details', 'wedding-abc123', {
  event_type: 'wedding',
  guest_count: 150,
});

// Track when someone manages invites
trackEventAction('manage_invites', 'birthday-xyz789', {
  invite_count: 25,
  action_type: 'bulk_send',
});
```

### Track Invite-Specific Actions

```typescript
import { trackInviteAction } from '@/components/GoogleAnalytics';

// Track when someone edits an invite
trackInviteAction('edit_invite', 'wedding-abc123', 'inv-456', {
  edit_type: 'message_customization',
});

// Track when someone sends an invite
trackInviteAction('send_invite', 'birthday-xyz789', 'inv-789', {
  send_method: 'whatsapp',
  recipient_type: 'family',
});
```

### Track RSVP Actions

```typescript
import { trackRSVPAction } from '@/components/GoogleAnalytics';

// Track RSVP submission for event-based RSVP (/event/[eventId]/rsvp/[inviteId])
trackRSVPAction('submit_rsvp', 'wedding-abc123', 'inv-456', {
  response: 'attending',
  guest_count: 2,
  dietary_restrictions: true,
});

// Track RSVP status change
trackRSVPAction('change_rsvp', 'birthday-xyz789', 'inv-789', {
  old_response: 'attending',
  new_response: 'declined',
});
```

## Advanced Filtering Scenarios

### 1. Compare Event Types

**Query**: Group events by type and compare performance

```text
Dimension: Event ID
Filter: Page Type = "event"
Secondary Dimension: Event Context
```

### 2. Identify Problem Events

**Query**: Find events with high bounce rates on invite pages

```text
Filter: Event Context = "invites_management"
Metric: Bounce Rate
Sort: Bounce Rate (Descending)
```

### 3. Track User Behavior by Event

**Query**: See how users interact with different events

```text
Primary Dimension: Event ID
Secondary Dimension: Page Group
Metrics: Page Views, Time on Page, Users
```

### 4. Monitor Real-time Event Activity

**Query**: Real-time monitoring of specific event

```text
Real-time Report
Filter: Event ID = "your-current-event-id"
Dimensions: Page Group, Event Context
```

## Benefits

1. **Granular Analysis**: Filter analytics by specific events while maintaining grouped reporting
2. **Event Comparison**: Compare performance across different events
3. **User Journey Tracking**: Follow user paths within specific events
4. **Problem Identification**: Quickly identify issues with specific events or invites
5. **A/B Testing**: Test different approaches on specific events
6. **Real-time Monitoring**: Monitor live events in real-time

## Best Practices

1. **Consistent Naming**: Use consistent event ID patterns (e.g., `event-type-id`)
2. **Regular Cleanup**: Archive old event data to keep reports manageable
3. **Custom Alerts**: Set up alerts for unusual activity on specific events
4. **Dashboard Creation**: Create event-specific dashboards for hosts
5. **Performance Monitoring**: Monitor high-value events more closely

---

**Note**: This enhanced filtering capability works alongside the existing URL grouping, giving you both high-level patterns and granular event-specific insights.
