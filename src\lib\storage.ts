import { FireBaseAdmin } from '@/lib/firebase';

interface GetFileUrlResult {
  exists: boolean;
  url?: string;
  message?: string;
}

/**
 * Gets the URL for a file in Firebase Storage
 * @param storagePath - The path to the file in storage (e.g., 'event/123/invite.png')
 * @returns Promise resolving to an object with exists status and URL if available
 */
export async function getFileUrl(storagePath: string): Promise<GetFileUrlResult> {
  try {
    // Initialize Firebase Admin
    const admin = FireBaseAdmin.getInstance();
    if (!admin) {
      throw new Error('Failed to initialize Firebase Admin');
    }

    // Get a reference to Storage with explicit bucket name
    const bucket = admin.storage().bucket('iamcoming-universe.firebasestorage.app');
    
    const fileRef = bucket.file(storagePath);
    
    try {
      // Check if the file exists
      const [exists] = await fileRef.exists();
      
      if (!exists) {
        console.log(`File does not exist at path: ${storagePath}`);
        return { 
          exists: false, 
          message: 'File not found' 
        };
      }

      console.log(`File exists at path: ${storagePath}, generating URL`);
      
      // Generate a public URL that doesn't expire
      const publicUrl = `https://storage.googleapis.com/${bucket.name}/${storagePath}`;
      
      return { 
        exists: true,
        url: publicUrl
      };
    } catch (fileError) {
      console.error('Error checking if file exists:', fileError);
      
      // Try to generate a signed URL as fallback
      try {
        const [url] = await fileRef.getSignedUrl({
          action: 'read',
          expires: Date.now() + 1000 * 60 * 60 * 24 * 365, // 1 year
        });
        
        console.log('Generated signed URL as fallback');
        return { 
          exists: true,
          url 
        };
      } catch (signedUrlError) {
        console.error('Error generating signed URL:', signedUrlError);
        throw signedUrlError;
      }
    }
  } catch (error) {
    console.error('Error getting file URL:', error);
    throw error;
  }
}

/**
 * Gets the URL for an event's invite image
 * @param eventId - The ID of the event
 * @param imageType - The type of image ('invitation-card' or 'digital-invite')
 * @returns Promise resolving to an object with exists status and URL if available
 */
export async function getEventInviteImageUrl(eventId: string, imageType: 'invitation-card' | 'digital-invite' = 'invitation-card'): Promise<GetFileUrlResult> {
  // Try different extensions since compression might change the format
  // Priority order: WebP (best compression), JPEG, PNG
  const extensions = ['webp', 'jpg', 'jpeg', 'png'];
  
  for (const ext of extensions) {
    const storagePath = `event/${eventId}/${imageType}.${ext}`;
    console.log(`Looking for event invite image at: ${storagePath}`);
    
    try {
      const result = await getFileUrl(storagePath);
      
      if (result.exists) {
        console.log(`Found image at: ${storagePath}`);
        return result;
      }
    } catch (error) {
      console.warn(`Error checking ${storagePath}:`, error);
      // Continue to next extension
    }
  }
    // No image found with any extension
  return {
    exists: false,
    url: undefined,
    message: `No ${imageType} image found for this event`
  };
}

/**
 * Gets the URL for an event's image with fallback logic
 * For digital invite: first try digital-invite.png, then fallback to invitation-card.png
 * For invitation card: only use invitation-card.png
 * @param eventId - The ID of the event
 * @param preferredType - The preferred image type
 * @returns Promise resolving to an object with exists status and URL if available
 */
export async function getEventImageWithFallback(eventId: string, preferredType: 'invitation-card' | 'digital-invite'): Promise<GetFileUrlResult & { imageType?: string }> {
  try {
    // First try the preferred type
    const preferredResult = await getEventInviteImageUrl(eventId, preferredType);
    
    if (preferredResult.exists) {
      return { ...preferredResult, imageType: preferredType };
    }
    
    // If digital invite doesn't exist, fallback to invitation card
    if (preferredType === 'digital-invite') {
      console.log('Digital invite image not found, falling back to invitation card image');
      const fallbackResult = await getEventInviteImageUrl(eventId, 'invitation-card');
      
      if (fallbackResult.exists) {
        return { ...fallbackResult, imageType: 'invitation-card' };
      }
    }
    
    // No image found
    return { 
      exists: false, 
      message: `No ${preferredType} image found for this event` 
    };
  } catch (error) {
    console.error(`Error getting image with fallback for event ${eventId}:`, error);
    throw error;
  }
}
