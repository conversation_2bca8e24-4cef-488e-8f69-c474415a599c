import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { shortlinkService } from '@/lib/shortlink-service';
import { UTMParams } from '@/lib/utm';
import { useToast } from '@/components/ui/use-toast';

interface ShortlinkFormProps {
  shortCode?: string;
  initialData?: {
    shortCode: string;
    redirectUrl: string;
  };
  onSuccess: () => void;
  isEditing?: boolean;
}

// Reserved short codes that cannot be used
const RESERVED_CODES = ['list', 'health', 'admin', 'analytics', 'api', 'www', 'mail', 'ftp', 'localhost', 'root'];

export function ShortlinkForm({ shortCode, initialData, onSuccess, isEditing = false }: ShortlinkFormProps) {
  const [formData, setFormData] = useState({
    shortCode: initialData?.shortCode || '',
    baseUrl: 'https://iamcoming.io',
    path: '',
    utm_source: '',
    utm_medium: '',
    utm_campaign: '',
    utm_term: '',
    utm_content: '',
  });
  const [loading, setLoading] = useState(false);
  const [shortCodeError, setShortCodeError] = useState('');
  const [previewUrl, setPreviewUrl] = useState('');
  const { toast } = useToast();

  // Update preview URL when form data changes
  useEffect(() => {
    updatePreviewUrl();
  }, [formData]);

  // Initialize form with existing data if editing
  useEffect(() => {
    if (initialData) {
      // Parse the redirect URL to extract base URL, path, and UTM parameters
      try {
        const url = new URL(initialData.redirectUrl);
        const baseUrl = `${url.protocol}//${url.hostname}${url.port ? ':' + url.port : ''}`;
        const path = url.pathname + url.search.replace(/[?&]utm_[^=]*=[^&]*/g, '').replace(/^[?&]/, '').replace(/[?&]$/, '');

        setFormData({
          shortCode: initialData.shortCode,
          baseUrl,
          path: path.startsWith('/') ? path.substring(1) : path,
          utm_source: url.searchParams.get('utm_source') || '',
          utm_medium: url.searchParams.get('utm_medium') || '',
          utm_campaign: url.searchParams.get('utm_campaign') || '',
          utm_term: url.searchParams.get('utm_term') || '',
          utm_content: url.searchParams.get('utm_content') || '',
        });
      } catch (error) {
        // If URL parsing fails, just use the redirect URL as is
        setFormData(prev => ({
          ...prev,
          shortCode: initialData.shortCode,
          baseUrl: initialData.redirectUrl,
          path: '',
        }));
      }
    }
  }, [initialData]);

  const updatePreviewUrl = () => {
    let url = formData.baseUrl;

    // Add path if provided
    if (formData.path) {
      url = url.endsWith('/') ? url.slice(0, -1) : url;
      const path = formData.path.startsWith('/') ? formData.path : '/' + formData.path;
      url += path;
    }

    // Add UTM parameters
    const utmParams: UTMParams = {};
    if (formData.utm_source) utmParams.utm_source = formData.utm_source;
    if (formData.utm_medium) utmParams.utm_medium = formData.utm_medium;
    if (formData.utm_campaign) utmParams.utm_campaign = formData.utm_campaign;
    if (formData.utm_term) utmParams.utm_term = formData.utm_term;
    if (formData.utm_content) utmParams.utm_content = formData.utm_content;

    // Add UTM parameters to URL
    if (Object.keys(utmParams).length > 0) {
      const urlObj = new URL(url);
      Object.entries(utmParams).forEach(([key, value]) => {
        if (value) urlObj.searchParams.set(key, value);
      });
      url = urlObj.toString();
    }

    setPreviewUrl(url);
  };

  const validateShortCode = (code: string) => {
    if (!code) {
      setShortCodeError('');
      return true;
    }

    if (code.length < 2) {
      setShortCodeError('Short code must be at least 2 characters long');
      return false;
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(code)) {
      setShortCodeError('Short code can only contain letters, numbers, hyphens, and underscores');
      return false;
    }

    if (RESERVED_CODES.includes(code.toLowerCase())) {
      setShortCodeError('This short code is reserved and cannot be used');
      return false;
    }

    setShortCodeError('');
    return true;
  };

  const handleShortCodeChange = (value: string) => {
    setFormData(prev => ({ ...prev, shortCode: value }));
    validateShortCode(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.shortCode && !validateShortCode(formData.shortCode)) {
      return;
    }

    if (!previewUrl) {
      toast({
        title: "Error",
        description: "Please provide a valid destination URL.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      if (isEditing && initialData?.shortCode) {
        // Update existing shortlink
        await shortlinkService.updateShortLink(initialData.shortCode, previewUrl);
      } else {
        // Generate a random short code if not provided
        const finalShortCode = formData.shortCode || generateRandomShortCode();

        await shortlinkService.createShortLink(finalShortCode, previewUrl);
      }

      onSuccess();
    } catch (error: any) {
      console.error(`Failed to ${isEditing ? 'update' : 'create'} shortlink:`, error);
      toast({
        title: "Error",
        description: error.message || `Failed to ${isEditing ? 'update' : 'create'} shortlink. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const generateRandomShortCode = () => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex flex-col xl:flex-row min-w-full items-stretch justify gap-4">
        {/* Basic Information */}
        <Card className="flex-1">
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Configure the destination URL and optional short code.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="shortCode">
                Short Code (optional)
              </Label>
              <Input
                id="shortCode"
                value={formData.shortCode}
                onChange={(e) => handleShortCodeChange(e.target.value)}
                placeholder="e.g., summer2024 (leave empty for random)"
                disabled={isEditing}
              />
              {shortCodeError && (
                <p className="text-sm text-red-600">{shortCodeError}</p>
              )}
              <p className="text-xs text-muted-foreground">
                If left empty, a random short code will be generated.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="baseUrl">Base URL</Label>
              <Input
                id="baseUrl"
                value={formData.baseUrl}
                onChange={(e) => setFormData(prev => ({ ...prev, baseUrl: e.target.value }))}
                placeholder="https://iamcoming.io"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="path">Path (optional)</Label>
              <Input
                id="path"
                value={formData.path}
                onChange={(e) => setFormData(prev => ({ ...prev, path: e.target.value }))}
                placeholder="e.g., events/special-offer"
              />
              <p className="text-xs text-muted-foreground">
                Additional path to append to the base URL.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* UTM Parameters */}
        <Card>
          <CardHeader>
            <CardTitle>UTM Parameters</CardTitle>
            <CardDescription>
              Add tracking parameters for analytics and campaign tracking.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="utm_source">UTM Source</Label>
                <Input
                  id="utm_source"
                  value={formData.utm_source}
                  onChange={(e) => setFormData(prev => ({ ...prev, utm_source: e.target.value }))}
                  placeholder="e.g., newsletter, facebook, google"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="utm_medium">UTM Medium</Label>
                <Input
                  id="utm_medium"
                  value={formData.utm_medium}
                  onChange={(e) => setFormData(prev => ({ ...prev, utm_medium: e.target.value }))}
                  placeholder="e.g., email, social, cpc"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="utm_campaign">UTM Campaign</Label>
                <Input
                  id="utm_campaign"
                  value={formData.utm_campaign}
                  onChange={(e) => setFormData(prev => ({ ...prev, utm_campaign: e.target.value }))}
                  placeholder="e.g., spring_sale, launch_2024"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="utm_term">UTM Term</Label>
                <Input
                  id="utm_term"
                  value={formData.utm_term}
                  onChange={(e) => setFormData(prev => ({ ...prev, utm_term: e.target.value }))}
                  placeholder="e.g., running+shoes, discount"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="utm_content">UTM Content</Label>
              <Input
                id="utm_content"
                value={formData.utm_content}
                onChange={(e) => setFormData(prev => ({ ...prev, utm_content: e.target.value }))}
                placeholder="e.g., logo_link, text_link"
              />
            </div>
          </CardContent>
        </Card>
      </div>
      {/* Preview */}
      {previewUrl && (
        <Card>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
            <CardDescription>
              This is how your final destination URL will look.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="p-3 bg-muted rounded-md">
              <code className="text-sm break-all">{previewUrl}</code>
            </div>
          </CardContent>
        </Card>
      )}


      {/* Separator */}
      <Separator />

      {/* Submit Button */}
      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={loading || !!shortCodeError}>
          {loading ? 'Creating...' : isEditing ? 'Update Shortlink' : 'Create Shortlink'}
        </Button>
      </div>
    </form>
  );
}
