/**
 * Strict TypeScript types for IAC Session API Response
 * 
 * This file defines the exact structure of the Session API 200 response.
 * It follows the clean separation of concerns approach where:
 * - Organizations contain only partner-level RBAC
 * - Permissions contain only system/admin-level RBAC
 * - No redundant fields or derived data
 */

/**
 * Base permission actions that can be performed on resources
 */
export type Permission = 'view' | 'create' | 'edit' | 'delete';

/**
 * Organization types in the IAC system
 */
export type OrganizationType = 'partner';

/**
 * System-level admin roles
 */
export type AdminRole =
  | 'admin:super-admin'
  | 'admin:user-manager'
  | 'admin:content-moderator'
  | 'admin:analytics-viewer';

/**
 * Partner organization roles
 */
export type PartnerRole =
  | 'partner:venue-owner'
  | 'partner:venue-manager'
  | 'partner:staff'
  | 'partner:viewer';

/**
 * Admin system resources
 */
export type AdminResource =
  | 'admin:dashboard'
  | 'admin:users'
  | 'admin:organizations'
  | 'admin:content'
  | 'admin:analytics'
  | 'admin:system'
  | 'admin:settings';

/**
 * Partner organization resources
 */
export type PartnerResource =
  | 'partner:dashboard'
  | 'partner:venues'
  | 'partner:venue'
  | 'partner:venue:events'
  | 'partner:venue:settings'
  | 'partner:team'
  | 'partner:billing'
  | 'partner:customers'
  | 'partner:settings';

/**
 * Role-permission mapping for admin roles
 */
export type AdminRolePermissions = {
  [K in AdminRole]?: {
    [R in AdminResource]?: Permission[];
  };
};

/**
 * Role-permission mapping for partner roles
 */
export type PartnerRolePermissions = {
  [K in PartnerRole]?: {
    [R in PartnerResource]?: Permission[];
  };
};

/**
 * User profile information (flexible object for additional metadata)
 */
export interface UserProfile {
  department?: string;
  title?: string;
  phone?: string;
  [key: string]: any; // Allow additional profile fields
}

/**
 * User object in session response
 */
export interface SessionUser {
  /** Unique user identifier */
  id: string;
  /** User's full display name */
  name: string;
  /** User's email address */
  email: string;
  /** Optional avatar URL */
  avatar?: string;
  /** Optional additional profile data */
  profile?: UserProfile;
}

/**
 * RBAC configuration for an organization
 * Contains only the role-permission mappings for roles the user has in this organization
 */
export interface OrganizationRBACConfig {
  /** 
   * Role-permission mappings for this organization
   * Only includes roles that the user actually has
   */
  rolePermissions: PartnerRolePermissions;
}

/**
 * Organization object in session response
 * Only includes partner organizations (admin is not an organization)
 */
export interface SessionOrganization {
  /** Organization identifier */
  id: string;
  /** Organization display name */
  name: string;
  /** Type of organization (only 'partner' for real organizations) */
  type: OrganizationType;
  /** RBAC configuration specific to this organization */
  rbacConfig: OrganizationRBACConfig;
}

/**
 * System-level permissions (admin privileges)
 * These are user-level permissions, not organization-specific
 */
export interface SystemPermissions {
  /** 
   * Admin role-permission mappings for system-level access
   * Only includes admin roles that the user actually has
   */
  rolePermissions: AdminRolePermissions;
}

/**
 * Complete Session API 200 Response
 * This is the exact structure returned by GET /session
 */
export interface SessionAPIResponse {
  /** Current logged-in user information */
  user: SessionUser;

  /** List of partner organizations user has access to */
  organizations: SessionOrganization[];

  /** System-level admin permissions (empty object if user has no admin access) */
  permissions: SystemPermissions;

  /** API access token for subsequent requests */
  token: string;
}

/**
 * Utility type to extract user roles from an organization
 */
export type UserOrganizationRoles<T extends SessionOrganization> =
  keyof T['rbacConfig']['rolePermissions'];

/**
 * Utility type to extract user admin roles from system permissions
 */
export type UserAdminRoles<T extends SystemPermissions> =
  keyof T['rolePermissions'];

/**
 * Type guard to check if user has any admin permissions
 */
export function hasAdminPermissions(permissions: SystemPermissions): boolean {
  return Object.keys(permissions.rolePermissions).length > 0;
}

/**
 * Type guard to check if user has access to any organizations
 */
export function hasOrganizationAccess(organizations: SessionOrganization[]): boolean {
  return organizations.length > 0;
}

/**
 * Helper type for permission checking functions
 */
export interface PermissionChecker {
  /** Check if user has a specific permission on a resource in an organization */
  hasOrganizationPermission(
    organization: SessionOrganization,
    resource: PartnerResource,
    permission: Permission
  ): boolean;

  /** Check if user has a specific admin permission on a system resource */
  hasAdminPermission(
    permissions: SystemPermissions,
    resource: AdminResource,
    permission: Permission
  ): boolean;

  /** Get all roles user has in an organization */
  getOrganizationRoles(organization: SessionOrganization): PartnerRole[];

  /** Get all admin roles user has */
  getAdminRoles(permissions: SystemPermissions): AdminRole[];
}

/**
 * Example usage and type validation
 */
export const exampleSessionResponse: SessionAPIResponse = {
  user: {
    id: 'user_12345',
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'https://cdn.example.com/avatars/john-doe.jpg',
    profile: {
      department: 'Engineering',
      title: 'Senior Developer',
      phone: '******-0123'
    }
  },
  organizations: [
    {
      id: 'org_partner_456',
      name: 'Acme Events',
      type: 'partner',
      rbacConfig: {
        rolePermissions: {
          'partner:venue-manager': {
            'partner:dashboard': ['view'],
            'partner:venues': ['view'],
            'partner:venue': ['view', 'edit'],
            'partner:venue:events': ['view', 'create', 'edit', 'delete']
          }
        }
      }
    }
  ],
  permissions: {
    rolePermissions: {
      'admin:super-admin': {
        'admin:dashboard': ['view'],
        'admin:users': ['view', 'create', 'edit', 'delete'],
        'admin:organizations': ['view', 'create', 'edit', 'delete']
      }
    }
  },
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
};
