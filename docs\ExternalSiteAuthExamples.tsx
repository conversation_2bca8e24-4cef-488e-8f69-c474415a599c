/**
 * Example implementation for external sites (e.g., partner.iamcoming.io)
 * This shows how to properly redirect users to IAC for authentication
 */

// For React applications
import React from 'react';

// Example 1: Simple redirect button
export function SimpleAuthRedirect() {
  const handleLogin = () => {
    // Redirect to IAC sign-in with site parameter
    const redirectUrl = encodeURIComponent(window.location.pathname + window.location.search);
    const iacSignInUrl = `https://app.iamcoming.io/auth/signin?site=partner&redirectUrl=${redirectUrl}`;

    window.location.href = iacSignInUrl;
  };

  return (
    <button onClick={handleLogin}>
      Sign in with IAC
    </button>
  );
}

// Example 2: Component with authentication check
export function AuthenticatedComponent() {
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    // Check authentication status by calling IAC session endpoint
    fetch('https://app.iamcoming.io/api/id/session', {
      credentials: 'include', // Important: include cookies
    })
      .then(response => {
        if (response.ok) {
          setIsAuthenticated(true);
        } else {
          setIsAuthenticated(false);
        }
      })
      .catch(() => {
        setIsAuthenticated(false);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const handleLogin = () => {
    const currentPath = window.location.pathname + window.location.search;
    const redirectUrl = encodeURIComponent(currentPath);
    const iacSignInUrl = `https://app.iamcoming.io/auth/signin?site=partner&redirectUrl=${redirectUrl}`;

    window.location.href = iacSignInUrl;
  };

  if (loading) {
    return <div>Checking authentication...</div>;
  }

  if (!isAuthenticated) {
    return (
      <div>
        <p>Please sign in to continue</p>
        <button onClick={handleLogin}>
          Sign in with IAC
        </button>
      </div>
    );
  }

  return (
    <div>
      <h1>Welcome! You are authenticated.</h1>
      {/* Your authenticated content here */}
    </div>
  );
}

// Example 3: Hook for authentication management
export function useIACAuth() {
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [user, setUser] = React.useState(null);

  const checkAuth = React.useCallback(async () => {
    try {
      const response = await fetch('https://app.iamcoming.io/api/id/session', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setIsAuthenticated(true);
        setUser(data.user);
      } else {
        setIsAuthenticated(false);
        setUser(null);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const redirectToAuth = React.useCallback((returnUrl?: string) => {
    const currentPath = returnUrl || (window.location.pathname + window.location.search);
    const redirectUrl = encodeURIComponent(currentPath);
    const iacSignInUrl = `https://app.iamcoming.io/auth/signin?site=partner&redirectUrl=${redirectUrl}`;

    window.location.href = iacSignInUrl;
  }, []);

  const logout = React.useCallback(async () => {
    try {
      // Call IAC signout endpoint (handles CORS properly)
      const response = await fetch('https://app.iamcoming.io/api/id/signout', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Signout failed');
      }
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      // Refresh auth state
      await checkAuth();
    }
  }, [checkAuth]);

  return {
    isAuthenticated,
    loading,
    user,
    redirectToAuth,
    logout,
    checkAuth,
  };
}

// Example 4: Vanilla JavaScript implementation
export const VanillaJSExample = {
  // Check if user is authenticated
  async checkAuth() {
    try {
      const response = await fetch('https://app.iamcoming.io/api/id/session', {
        credentials: 'include',
      });
      return response.ok;
    } catch {
      return false;
    }
  },

  // Redirect to IAC for authentication
  redirectToAuth(returnUrl?: string) {
    const currentPath = returnUrl || (window.location.pathname + window.location.search);
    const redirectUrl = encodeURIComponent(currentPath);
    const iacSignInUrl = `https://app.iamcoming.io/auth/signin?site=partner&redirectUrl=${redirectUrl}`;

    window.location.href = iacSignInUrl;
  },

  // Initialize authentication on page load
  async init() {
    const isAuthenticated = await this.checkAuth();

    if (!isAuthenticated) {
      // Show login UI or redirect to auth
      console.log('User not authenticated');
      return false;
    }

    console.log('User is authenticated');
    return true;
  }
};

// Example usage in HTML:
/*
<!DOCTYPE html>
<html>
<head>
    <title>Partner Site</title>
</head>
<body>
    <div id="auth-content" style="display: none;">
        <h1>Welcome to Partner Site</h1>
        <p>You are authenticated!</p>
    </div>
    
    <div id="login-content">
        <h1>Please Sign In</h1>
        <button onclick="login()">Sign in with IAC</button>
    </div>
    
    <script>
        async function login() {
            const currentPath = window.location.pathname + window.location.search;
            const redirectUrl = encodeURIComponent(currentPath);
            const iacSignInUrl = `https://app.iamcoming.io/auth/signin?site=partner&redirectUrl=${redirectUrl}`;
            window.location.href = iacSignInUrl;
        }
        
        async function checkAuth() {
            try {
                const response = await fetch('https://app.iamcoming.io/api/id/session', {
                    credentials: 'include',
                });
                
                if (response.ok) {
                    document.getElementById('login-content').style.display = 'none';
                    document.getElementById('auth-content').style.display = 'block';
                } else {
                    document.getElementById('login-content').style.display = 'block';
                    document.getElementById('auth-content').style.display = 'none';
                }
            } catch (error) {
                console.error('Auth check failed:', error);
            }
        }
        
        // Check auth on page load
        checkAuth();
    </script>
</body>
</html>
*/
