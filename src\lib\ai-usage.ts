import { Database } from '@/lib/database';
import { Event, Organization } from '@/types';

/**
 * Default AI generation limit per event
 */
export const DEFAULT_AI_LIMIT = 10;

/**
 * Special values for AI_GEN_LIMIT override
 */
export const AI_LIMIT_UNLIMITED = -1;
export const AI_LIMIT_DISABLED = 0;

/**
 * Prefix for temporary event IDs during creation
 */
export const TEMP_EVENT_PREFIX = 'temp_event_';

/**
 * Result of AI limit check
 */
export interface AILimitCheckResult {
  canUseAI: boolean;
  currentUsage: number;
  limit: number;
  message?: string;
}

/**
 * Temporary event interface for tracking during creation
 */
export interface TempEvent {
  id: string;
  ownerAccountId: string;
  organizationId?: string;
  aiUsageCount: number;
  isTemporary: true;
  createdAt: string;
  eventName: string;
  eventDate: Date;
  ownerEmail: string;
  managers: string[];
}

/**
 * Generate a temporary event ID for tracking during creation
 */
export function generateTempEventId(userId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${TEMP_EVENT_PREFIX}${userId}_${timestamp}_${random}`;
}

/**
 * Check if an event ID is temporary
 */
export function isTempEventId(eventId: string): boolean {
  return eventId.startsWith(TEMP_EVENT_PREFIX);
}

/**
 * Create a temporary event tracking entry for AI usage during creation
 */
export async function createTempEventTracking(tempEventId: string, userId: string, organizationId?: string): Promise<void> {
  const db = Database.getInstance();
  
  const tempEvent: TempEvent = {
    id: tempEventId,
    ownerAccountId: userId,
    organizationId: organizationId,
    aiUsageCount: 0,
    isTemporary: true,
    createdAt: new Date().toISOString(),
    // Minimal required fields for tracking
    eventName: 'Temporary Event (Creating)',
    eventDate: new Date(),
    ownerEmail: '', // Will be filled when real event is created
    managers: []
  };
  // Store as a document in temp_events collection
  await db.addData('temp_events', tempEvent);
}

/**
 * Get temporary event data
 */
export async function getTempEvent(tempEventId: string): Promise<TempEvent | null> {
  const db = Database.getInstance();
  try {
    const tempEvent = await db.readData('temp_events', tempEventId) as TempEvent;
    return tempEvent || null;
  } catch (error) {
    return null;
  }
}

/**
 * Transfer AI usage from temporary event to real event
 */
export async function transferTempEventUsage(tempEventId: string, realEventId: string): Promise<void> {
  if (!isTempEventId(tempEventId)) {
    return; // Not a temp event, nothing to transfer
  }

  const db = Database.getInstance();
  
  // Get temp event data
  const tempEvent = await getTempEvent(tempEventId);
  if (!tempEvent) {
    console.warn(`Temp event ${tempEventId} not found for transfer`);
    return;
  }
  // Get real event and update its usage
  const realEvent = await db.readData('events', realEventId) as Event;
  if (realEvent && tempEvent.aiUsageCount) {
    realEvent.aiUsageCount = (realEvent.aiUsageCount || 0) + tempEvent.aiUsageCount;
    await db.updateData('events', realEventId, realEvent);
  }

  // Clean up temp event
  await db.deleteData('temp_events', tempEventId);
}

/**
 * Clean up expired temporary events (older than 24 hours)
 */
export async function cleanupExpiredTempEvents(): Promise<void> {
  const db = Database.getInstance();
  const twentyFourHoursAgo = Date.now() - (24 * 60 * 60 * 1000);
  
  try {
    // This would need to be implemented based on your database structure
    // For now, just log that cleanup would happen
    console.log('Cleaning up expired temp events older than 24 hours');
  } catch (error) {
    console.error('Error cleaning up expired temp events:', error);
  }
}

/**
 * Get the effective AI limit for an event (including temp events)
 * Checks event-level overrides first, then account-level overrides, then default
 */
export async function getEffectiveAILimit(eventId: string): Promise<number> {
  const db = Database.getInstance();
  
  let event: Event | TempEvent | null = null;
  let organizationId: string | undefined;

  // Check if it's a temporary event
  if (isTempEventId(eventId)) {
    event = await getTempEvent(eventId);
    organizationId = event?.organizationId;
  } else {
    // Get the regular event
    event = await db.readData('events', eventId) as Event;
    organizationId = event?.organizationId;
  }

  if (!event) {
    throw new Error('Event not found');
  }

  // Check event-level override first (only for real events)
  if ('overrides' in event && event.overrides?.AI_GEN_LIMIT !== undefined) {
    return event.overrides.AI_GEN_LIMIT;
  }

  // Check account-level override
  if (organizationId) {
    const organization = await db.getOrganizationById(organizationId) as Organization | null;
    if (organization?.overrides?.AI_GEN_LIMIT !== undefined) {
      return organization.overrides.AI_GEN_LIMIT;
    }
  }

  // Return default limit
  return DEFAULT_AI_LIMIT;
}

/**
 * Check if AI usage is allowed for an event (including temp events)
 */
export async function checkAIUsageLimit(eventId: string): Promise<AILimitCheckResult> {
  const db = Database.getInstance();
  
  let event: Event | TempEvent | null = null;

  // Check if it's a temporary event
  if (isTempEventId(eventId)) {
    event = await getTempEvent(eventId);
  } else {
    // Get the regular event
    event = await db.readData('events', eventId) as Event;
  }

  if (!event) {
    throw new Error('Event not found');
  }

  const currentUsage = event.aiUsageCount || 0;
  const limit = await getEffectiveAILimit(eventId);

  // Check special cases
  if (limit === AI_LIMIT_DISABLED) {
    return {
      canUseAI: false,
      currentUsage,
      limit,
      message: 'AI generation is disabled for this event'
    };
  }

  if (limit === AI_LIMIT_UNLIMITED) {
    return {
      canUseAI: true,
      currentUsage,
      limit,
      message: 'Unlimited AI usage'
    };
  }

  // Check if limit is reached
  if (currentUsage >= limit) {
    return {
      canUseAI: false,
      currentUsage,
      limit,
      message: `AI usage limit reached (${currentUsage}/${limit})`
    };
  }

  return {
    canUseAI: true,
    currentUsage,
    limit,
    message: `AI usage available (${currentUsage}/${limit})`
  };
}

/**
 * Increment AI usage for an event (including temp events)
 */
export async function incrementAIUsage(eventId: string): Promise<number> {
  const db = Database.getInstance();
  
  let event: Event | TempEvent | null = null;
  let isTemp = false;

  // Check if it's a temporary event
  if (isTempEventId(eventId)) {
    event = await getTempEvent(eventId);
    isTemp = true;
  } else {
    // Get the regular event
    event = await db.readData('events', eventId) as Event;
  }

  if (!event) {
    throw new Error('Event not found');
  }
  // Increment usage
  const newUsage = (event.aiUsageCount || 0) + 1;
  event.aiUsageCount = newUsage;

  // Save back to database
  if (isTemp) {
    await db.updateData('temp_events', eventId, { aiUsageCount: newUsage });
  } else {
    await db.updateData('events', eventId, { aiUsageCount: newUsage });
  }

  return newUsage;
}

/**
 * Set AI usage count for an event (for admin/testing purposes)
 */
export async function setAIUsage(eventId: string, count: number): Promise<void> {
  const db = Database.getInstance();
  
  let event: Event | TempEvent | null = null;
  let isTemp = false;

  // Check if it's a temporary event
  if (isTempEventId(eventId)) {
    event = await getTempEvent(eventId);
    isTemp = true;
  } else {
    // Get the regular event
    event = await db.readData('events', eventId) as Event;
  }

  if (!event) {
    throw new Error('Event not found');
  }
  event.aiUsageCount = Math.max(0, count);

  // Save back to database
  if (isTemp) {
    await db.updateData('temp_events', eventId, { aiUsageCount: count });
  } else {
    await db.updateData('events', eventId, { aiUsageCount: count });
  }
}

/**
 * Reset AI usage count for an event
 */
export async function resetAIUsage(eventId: string): Promise<void> {
  await setAIUsage(eventId, 0);
}
