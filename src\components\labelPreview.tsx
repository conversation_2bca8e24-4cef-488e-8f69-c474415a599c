import { cn } from "@/lib/utils";

interface LabelOptions {
  name?: string;
  qrContent: string;
  orientation?: 'portrait' | 'landscape';
  colorQr?: string;
  bgQr?: string;
};

export function LabelPreview(options: LabelOptions) {
  const { name = 'Sample Name', qrContent, orientation = 'portrait', colorQr, bgQr } = options;

  const width = (orientation === 'portrait' ? 235 : 353) * 0.5;
  const height = (orientation === 'portrait' ? 353 : 235) * 0.5;


  const params = new URLSearchParams();
  Object.entries(options).forEach(([key, value]) => {
    if (value !== undefined) {
      const formattedKey = key.replace(/([A-Z])/g, (match) => `-${match.toLowerCase()}`);
      params.append(formattedKey, String(value));
    }
  });
  const url = `/api/media/label?${params.toString()}`;

  return (<img
    src={url}
    alt="Label Preview"
    className={cn("shadow-xl m-4 border-1")}
    style={{ width }}
  />)
}