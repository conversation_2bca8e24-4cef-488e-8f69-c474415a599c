import { APIResponse } from '@/types';
import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth";

/**
 * @api {POST} /event/:eventId/invites/bulk-update-group Bulk update invite groups
 * @apiName Bulk Update Invite Groups
 * @apiGroup Invites
 * @apiDescription Updates the group assignment for multiple invites at once
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId } = req.query;

  if (!eventId) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  // Get the session to check authentication
  const session = await getServerSession(req, res, authConfig);
  if (!session?.user) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Check if the user is the owner of the event
    const event = await Database.getInstance().readData('events', eventId as string);
    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }

    // Check if the user is the owner of the event
    const isOwner = (event.ownerEmail === session.user.email) ||
                    (event.ownerAccountId === session.user.id);

    if (!isOwner) {
      return res.status(403).json({
        error: "Permission denied",
        message: "Only the event owner can update invites"
      });
    }

    const updateResponse = await POST(req.body.inviteIds, req.body.groupName);
    res.status(updateResponse.code).json(updateResponse.data);
  } catch (error) {
    console.error('Error in bulk update group handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Updates the group assignment for multiple invites by their IDs
 * 
 * @param inviteIds Array of invite IDs to update
 * @param groupName The new group name to assign
 * @returns APIResponse containing the update status and details
 */
async function POST(inviteIds: string[], groupName: string): Promise<APIResponse> {
  if (!inviteIds || !Array.isArray(inviteIds) || inviteIds.length === 0) {
    return {
      data: { error: "No invite IDs provided" },
      code: 400,
      headers: {
        'Content-Type': 'application/json',
      }
    };
  }

  if (typeof groupName !== 'string') {
    return {
      data: { error: "Group name must be a string" },
      code: 400,
      headers: {
        'Content-Type': 'application/json',
      }
    };
  }

  const updatedInvites: Array<{ id: string; name: string; oldGroup?: string; newGroup: string }> = [];
  const failedUpdates: Array<{ id: string; error: string }> = [];
  const trimmedGroupName = groupName.trim();

  // Process each invite update
  for (const inviteId of inviteIds) {
    try {
      // First get the invite details before updating
      const invite = await Database.getInstance().readData('invites', Database.normalizeId(inviteId));

      if (!invite) {
        failedUpdates.push({ id: inviteId, error: 'Invite not found' });
        continue;
      }

      const name = invite?.name || 'Unknown';
      const oldGroup = invite?.group || undefined;
      
      // Update the invite with the new group
      const updatedInvite = {
        ...invite,
        group: trimmedGroupName,
        updatedAt: new Date().toISOString()
      };

      await Database.getInstance().addData('invites', updatedInvite);

      updatedInvites.push({
        id: inviteId,
        name,
        oldGroup,
        newGroup: trimmedGroupName || 'Ungrouped'
      });
      
      log('Invite group updated [' + inviteId + ']', {
        oldGroup: oldGroup || 'Ungrouped',
        newGroup: trimmedGroupName || 'Ungrouped'
      });
    } catch (error) {
      console.error(`Error updating invite ${inviteId}:`, error);
      failedUpdates.push({ 
        id: inviteId, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }

  const totalRequested = inviteIds.length;
  const totalUpdated = updatedInvites.length;
  const totalFailed = failedUpdates.length;

  return {
    data: {
      success: totalFailed === 0,
      message: totalFailed === 0
        ? `Successfully moved ${totalUpdated} invite${totalUpdated !== 1 ? 's' : ''} to "${trimmedGroupName || 'Ungrouped'}"`
        : `Moved ${totalUpdated} of ${totalRequested} invites to "${trimmedGroupName || 'Ungrouped'}". ${totalFailed} failed.`,
      updatedInvites,
      failedUpdates,
      updatedCount: totalUpdated,
      groupName: trimmedGroupName || 'Ungrouped',
      summary: {
        totalRequested,
        totalUpdated,
        totalFailed
      }
    },
    code: totalFailed === 0 ? 200 : 207, // 207 = Multi-Status for partial success
    headers: {
      'Content-Type': 'application/json',
    }
  };
}
