import { Database } from './database';
import { Event, EventInvite, InviteStatus } from '@/types';
import { sendEventArchiveEmail } from './mailer';
import JSZip from 'jszip';
import { FireBaseAdmin } from './firebase';

export interface EventArchiveData {
  ID?: string;
  eventId: string;
  eventName: string;
  ownerAccountId: string;
  ownerEmail: string;
  archivedAt: string;
  archiveUrl: string;
  expiresAt: string; // 14 days after archive creation
  storagePath: string; // Storage path for easier deletion
}

export interface EventArchiveContent {
  event: Event;
  invites: EventInvite[];
  metadata: {
    archivedAt: string;
    totalInvites: number;
    acceptedCount: number;
    declinedCount: number;
    pendingCount: number;
  };
}

/**
 * Creates an archive for an event that's past 90 days
 * @param eventId The event ID to archive
 * @param organizationId The organization ID owning the event
 */
export async function archiveEvent(eventId: string, organizationId: string): Promise<void> {
  try {
    const db = Database.getInstance();
    
    // Get event data
    const event = await db.readData('events', eventId) as Event;
    if (!event) {
      throw new Error('Event not found');
    }

    // Check if event is eligible for archiving (90+ days old)
    const eventDate = new Date(event.eventDate);
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
    
    if (eventDate >= ninetyDaysAgo) {
      throw new Error('Event is not yet eligible for archiving');
    }

    // Get all invites for this event
    const invites = await db.ListData('invites', {
      field: 'eventId',
      operator: '==',
      value: eventId
    }) as EventInvite[];

    // Create archive content
    const archiveContent = await createEventArchive(event, invites);
    
    // Generate timestamp once for both upload and database record
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const storagePath = `archives/event_archive_${eventId}_${timestamp}.zip`;
    
    // Upload archive to storage with specific path
    const archiveUrl = await uploadEventArchive(eventId, archiveContent, storagePath);
    
    // Calculate expiry date (14 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 14);
    
    // Create archive record
    const archiveData: EventArchiveData = {
      ID: eventId, // Use eventId as the archive ID for easy reference
      eventId,
      eventName: event.eventName,
      ownerAccountId: organizationId,
      ownerEmail: event.ownerEmail || '',
      archivedAt: new Date().toISOString(),
      archiveUrl,
      expiresAt: expiresAt.toISOString(),
      storagePath
    };
    
    // Store archive record in database
    await db.addData('event_archives', archiveData);
    
    // Send archive notification email to event owner BEFORE deletion
    if (event.ownerEmail) {
      await sendEventArchiveEmail(event, archiveUrl, expiresAt);
      console.log(`Archive notification sent to: ${event.ownerEmail}`);
    }
    
    // 🚨 NOW DELETE THE EVENT AND ALL RELATED DATA FROM DATABASE 🚨
    console.log(`Starting deletion of event ${eventId} and all related data...`);
    
    // Delete all invites for this event
    for (const invite of invites) {
      if (invite.ID) {
        try {
          await db.deleteData('invites', invite.ID);
          console.log(`Deleted invite: ${invite.ID} (${invite.name})`);
        } catch (error) {
          console.warn(`Failed to delete invite ${invite.ID}:`, error);
        }
      }
    }
    
    // Finally, delete the event itself
    await db.deleteData('events', eventId);
    console.log(`Deleted event: ${eventId}`);
    
    console.log(`✅ Event ${eventId} and all related data has been successfully archived and deleted from database`);
    
  } catch (error) {
    console.error('Error archiving event:', error);
    throw error;
  }
}

/**
 * Creates an archive ZIP file containing all event data
 * @param event The event to archive
 * @param invites The invites associated with the event
 * @returns Archive content as a buffer
 */
async function createEventArchive(event: Event, invites: EventInvite[]): Promise<Buffer> {
  const zip = new JSZip();
  
  // Calculate invite statistics
  const acceptedCount = invites.filter(invite => invite.status === 'accepted').length;
  const declinedCount = invites.filter(invite => invite.status === 'declined').length;
  const pendingCount = invites.filter(invite => invite.status === 'invited').length;
  
  // Create event.json
  const eventData: EventArchiveContent = {
    event,
    invites,
    metadata: {
      archivedAt: new Date().toISOString(),
      totalInvites: invites.length,
      acceptedCount,
      declinedCount,
      pendingCount
    }
  };
  
  zip.file('event.json', JSON.stringify(eventData, null, 2));
  
  // Create invites folder with individual invitation files
  const invitesFolder = zip.folder('invites');
  if (invitesFolder) {
    for (const invite of invites) {
      const fileName = `invitation_${invite.ID || invite.name.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
      invitesFolder.file(fileName, JSON.stringify(invite, null, 2));
    }
  }
  
  // Create images folder and download event images
  const imagesFolder = zip.folder('images');
  if (imagesFolder) {
    try {
      // Try to get digital image
      const digitalImageBuffer = await downloadEventImage(event.ID!, 'digital-invite');
      if (digitalImageBuffer) {
        imagesFolder.file('digital-image.jpg', digitalImageBuffer);
      }
    } catch (error) {
      console.warn('Could not download digital image:', error);
    }
    
    try {
      // Try to get printable invite
      const printableImageBuffer = await downloadEventImage(event.ID!, 'invitation-card');
      if (printableImageBuffer) {
        imagesFolder.file('printable-invite.jpg', printableImageBuffer);
      }
    } catch (error) {
      console.warn('Could not download printable invite:', error);
    }
  }
  
  // Create labels folder and generate QR code labels
  const labelsFolder = zip.folder('labels');
  if (labelsFolder && event.ID) {
    try {
      // Generate QR code labels for all invites
      const labelBuffers = await generateQRLabelsForArchive(event.ID, invites);
      
      if (labelBuffers.length > 0) {
        // Add each label to the archive
        for (let i = 0; i < labelBuffers.length; i++) {
          const invite = invites[i];
          const labelBuffer = labelBuffers[i];
          
          if (labelBuffer && invite.ID) {
            const filename = `label_${invite.name.replace(/[^a-zA-Z0-9]/g, '_')}_${invite.ID}.png`;
            labelsFolder.file(filename, labelBuffer);
          }
        }
        
        // Add a summary file
        labelsFolder.file('README.txt', `This folder contains ${labelBuffers.filter((b: Buffer | null) => b !== null).length} QR code labels generated for your event invitations. Each label contains a QR code that links directly to the RSVP page for that specific guest.`);
      } else {
        labelsFolder.file('README.txt', 'No QR code labels were generated for this event. This may be because no invites had labels created or there was an error during label generation.');
      }
    } catch (error) {
      console.warn('Error generating labels for archive:', error);
      labelsFolder.file('README.txt', 'Labels could not be generated for archival. Please contact support if you need the original labels.');
    }
  }
  
  // Generate the ZIP file
  return await zip.generateAsync({ type: 'nodebuffer', compression: 'DEFLATE' });
}

/**
 * Downloads an event image from storage
 * @param eventId The event ID
 * @param imageType The type of image to download
 * @returns Image buffer or null if not found
 */
async function downloadEventImage(eventId: string, imageType: 'digital-invite' | 'invitation-card'): Promise<Buffer | null> {
  try {
    const { getEventInviteImageUrl } = await import('./storage');
    const result = await getEventInviteImageUrl(eventId, imageType);
    
    if (!result.exists || !result.url) {
      return null;
    }
    
    // Fetch the image
    const response = await fetch(result.url);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }
    
    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
    
  } catch (error) {
    console.warn(`Failed to download event image ${imageType} for event ${eventId}:`, error);
    return null;
  }
}

/**
 * Uploads the event archive to storage
 * @param eventId The event ID
 * @param archiveBuffer The archive content
 * @param storagePath The storage path to use for the file
 * @returns The URL to download the archive
 */
async function uploadEventArchive(eventId: string, archiveBuffer: Buffer, storagePath: string): Promise<string> {
  try {
    // Initialize Firebase Admin
    const admin = FireBaseAdmin.getInstance();
    if (!admin) {
      throw new Error('Failed to initialize Firebase Admin');
    }

    // Get a reference to Storage
    const bucket = admin.storage().bucket('iamcoming-universe.firebasestorage.app');
    
    const fileUpload = bucket.file(storagePath);
    
    await fileUpload.save(archiveBuffer, {
      metadata: {
        contentType: 'application/zip',
        metadata: {
          eventId,
          archivedAt: new Date().toISOString(),
          fileType: 'event_archive'
        }
      }
    });
    
    // Generate a signed URL valid for 14 days
    const [url] = await fileUpload.getSignedUrl({
      action: 'read',
      expires: Date.now() + (14 * 24 * 60 * 60 * 1000), // 14 days
    });
    
    return url;
    
  } catch (error) {
    console.error('Error uploading event archive:', error);
    throw error;
  }
}

/**
 * Deletes old archived events that are past their retention period (104+ days)
 * @param eventId The event ID to delete from archives
 */
export async function deleteOldArchivedEvents(eventId: string): Promise<void> {
  try {
    const db = Database.getInstance();
    
    // Get archive record
    const archive = await db.readData('event_archives', eventId) as EventArchiveData;
    if (!archive) {
      console.log(`No archive found for event ${eventId}`);
      return;
    }
    
    // Check if archive has expired (14 days past creation)
    const expiresAt = new Date(archive.expiresAt);
    const now = new Date();
    
    if (now < expiresAt) {
      console.log(`Archive for event ${eventId} has not yet expired`);
      return;
    }
    
    // Delete archive file from storage using stored path
    try {
      if (archive.storagePath) {
        await deleteArchiveFileByPath(archive.storagePath);
        console.log(`Deleted archive file: ${archive.storagePath}`);
      } else {
        // Fallback to URL-based deletion for older archives
        await deleteArchiveFile(archive.archiveUrl);
        console.log(`Deleted archive file via URL: ${archive.archiveUrl}`);
      }
    } catch (error) {
      console.warn('Failed to delete archive file from storage:', error);
      
      // Additional fallback: try to find the file by eventId pattern
      try {
        await deleteArchiveFileByEventId(eventId);
        console.log(`Deleted archive file using eventId pattern search: ${eventId}`);
      } catch (fallbackError) {
        console.error('All deletion methods failed:', fallbackError);
      }
      // Continue with database cleanup even if file deletion fails
    }
    
    // Delete archive record
    await db.deleteData('event_archives', eventId);
    console.log(`Deleted archive record: ${eventId}`);
    
    console.log(`✅ Old archive for event ${eventId} has been permanently deleted`);
    
  } catch (error) {
    console.error('Error deleting old archived event:', error);
    throw error;
  }
}

/**
 * Processes all expired archives for deletion (bulk cleanup)
 * This is the main function for Day 104 cleanup
 */
export async function processExpiredArchives(): Promise<{
  deletedCount: number;
  errorCount: number;
  results: Array<{eventId: string; status: 'deleted' | 'error'; error?: string}>;
}> {
  try {
    const db = Database.getInstance();
    const archivesToDelete = await db.getArchivesToDelete();
    
    console.log(`Found ${archivesToDelete.length} expired archives to delete`);
    
    let deletedCount = 0;
    let errorCount = 0;
    const results: Array<{eventId: string; status: 'deleted' | 'error'; error?: string}> = [];
    
    for (const archive of archivesToDelete) {
      try {
        await deleteOldArchivedEvents(archive.eventId || archive.id);
        deletedCount++;
        results.push({
          eventId: archive.eventId || archive.id,
          status: 'deleted'
        });
        
      } catch (error) {
        errorCount++;
        results.push({
          eventId: archive.eventId || archive.id,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        console.error(`Error deleting archive ${archive.eventId}:`, error);
      }
    }
    
    console.log(`✅ Bulk cleanup completed: ${deletedCount} deleted, ${errorCount} errors`);
    
    return { deletedCount, errorCount, results };
    
  } catch (error) {
    console.error('Error in bulk archive cleanup:', error);
    throw error;
  }
}

/**
 * Deletes an archive file from storage using its storage path
 * @param storagePath The storage path of the archive file
 */
async function deleteArchiveFileByPath(storagePath: string): Promise<void> {
  try {
    // Initialize Firebase Admin
    const admin = FireBaseAdmin.getInstance();
    if (!admin) {
      throw new Error('Failed to initialize Firebase Admin');
    }

    const bucket = admin.storage().bucket('iamcoming-universe.firebasestorage.app');
    const fileRef = bucket.file(storagePath);
    
    await fileRef.delete();
    console.log(`Archive file ${storagePath} deleted from storage`);
    
  } catch (error) {
    console.error('Error deleting archive file by path:', error);
    throw error;
  }
}

/**
 * Deletes an archive file from storage using its URL
 * @param archiveUrl The URL of the archive file
 */
async function deleteArchiveFile(archiveUrl: string): Promise<void> {
  try {
    // Extract the file path from the URL
    const urlParts = archiveUrl.split('/');
    const pathIndex = urlParts.findIndex(part => part === 'archives');
    
    if (pathIndex === -1) {
      throw new Error('Invalid archive URL format');
    }
    
    // Reconstruct the storage path
    const fileName = urlParts[pathIndex + 1].split('?')[0]; // Remove query parameters
    const storagePath = `archives/${fileName}`;
    
    // Initialize Firebase Admin
    const admin = FireBaseAdmin.getInstance();
    if (!admin) {
      throw new Error('Failed to initialize Firebase Admin');
    }

    const bucket = admin.storage().bucket('iamcoming-universe.firebasestorage.app');
    const fileRef = bucket.file(storagePath);
    
    await fileRef.delete();
    console.log(`Archive file ${storagePath} deleted from storage`);
    
  } catch (error) {
    console.error('Error deleting archive file:', error);
    throw error;
  }
}

/**
 * Deletes an archive file from storage by searching for it using eventId pattern
 * This is a fallback method when the stored path doesn't match the actual file
 * @param eventId The event ID to search for
 */
async function deleteArchiveFileByEventId(eventId: string): Promise<void> {
  try {
    // Initialize Firebase Admin
    const admin = FireBaseAdmin.getInstance();
    if (!admin) {
      throw new Error('Failed to initialize Firebase Admin');
    }

    const bucket = admin.storage().bucket('iamcoming-universe.firebasestorage.app');
    
    // List all files in the archives folder that match the eventId pattern
    const [files] = await bucket.getFiles({
      prefix: 'archives/',
      delimiter: '/'
    });
    
    // Find files that match the event archive pattern for this eventId
    const matchingFiles = files.filter(file => 
      file.name.includes(`event_archive_${eventId}_`) && 
      file.name.endsWith('.zip')
    );
    
    if (matchingFiles.length === 0) {
      throw new Error(`No archive files found for event ${eventId}`);
    }
    
    // Delete all matching files (there should only be one, but just in case)
    for (const file of matchingFiles) {
      await file.delete();
      console.log(`Deleted archive file by pattern search: ${file.name}`);
    }
    
  } catch (error) {
    console.error('Error deleting archive file by eventId:', error);
    throw error;
  }
}

/**
 * Gets the status of an event archive
 * @param eventId The event ID
 * @returns Archive status information
 */
export async function getEventArchiveStatus(eventId: string): Promise<EventArchiveData | null> {
  try {
    const db = Database.getInstance();
    const archive = await db.readData('event_archives', eventId) as EventArchiveData;
    return archive || null;
  } catch (error) {
    console.error('Error getting event archive status:', error);
    return null;
  }
}

/**
 * Generates QR code labels for all invites to include in archive
 * @param eventId The event ID
 * @param invites Array of invites to generate labels for
 * @returns Array of label buffers (null if generation failed for specific invite)
 */
async function generateQRLabelsForArchive(eventId: string, invites: EventInvite[]): Promise<(Buffer | null)[]> {
  const { RenderLabel } = await import('./media/label');
  const { generateQrRsvpLink } = await import('./utils');
  
  const labelPromises = invites.map(async (invite) => {
    try {
      if (!invite.ID) {
        console.warn(`Skipping label generation for invite without ID: ${invite.name}`);
        return null;
      }
      
      // Generate RSVP link for QR code
      const qrContent = generateQrRsvpLink({ eventId: invite.eventId, ID: invite.ID });
      
      // Use default label settings
      const labelOptions = {
        orientation: 'portrait' as const,
        theme: {
          name: 'default',
          qr: '#000000',
          background: '#FFFFFF',
        },
        showBranding: true
      };
      
      // Sanitize the name to prevent any issues
      const sanitizedName = (invite.name || 'Guest').trim();
      
      // Generate the label
      const labelBuffer = await RenderLabel(sanitizedName, qrContent, labelOptions);
      
      return labelBuffer.fileContents;
      
    } catch (error) {
      console.warn(`Failed to generate label for ${invite.name}:`, error);
      return null;
    }
  });
  
  return await Promise.all(labelPromises);
}
