import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const db = Database.getInstance();

    // Get organization ID from the query parameters
    const { organizationId } = req.query;

    if (!organizationId || Array.isArray(organizationId)) {
      return res.status(400).json({ error: 'Invalid organization ID' });
    }

    // Only accept POST method for toggling partner status
    if (req.method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get the organization to toggle partner status
    const organization = await db.getOrganizationById(organizationId);

    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    // Security check: prevent users from modifying their own organization type if it's individual
    if (organization.id === session.user.id && organization.type === 'individual') {
      return res.status(403).json({
        error: 'Security restriction: Cannot change your own organization type from individual'
      });
    }

    // Toggle the partner status
    const newType = organization.type === 'partner' ? 'individual' : 'partner';

    // Update the organization in the database
    await db.updateOrganization(organizationId, {
      type: newType,
      lastUpdatedOn: new Date().toISOString()
    });

    // Get the updated organization
    const updatedOrganization = await db.getOrganizationById(organizationId);

    // Return success response
    return res.status(200).json({
      success: true,
      message: `Organization partner status updated to ${newType}`,
      organization: updatedOrganization
    });
  } catch (error) {
    console.error('Error toggling organization partner status:', error);
    return res.status(500).json({ error: 'Failed to toggle organization partner status' });
  }
}
