# Use the official Node.js image as the base image
FROM node:22-alpine AS build

# Set environment variable to enable legacy peer dependencies
ENV NPM_CONFIG_LEGACY_PEER_DEPS=true

# Set the working directory
WORKDIR /app

# Copy package files first for better caching
COPY package.json pnpm-lock.yaml ./

RUN npm install -g pnpm

# Install dependencies with caching
# Only reinstall dependencies if package.json or pnpm-lock.yaml has changed
RUN --mount=type=cache,target=/root/.pnpm-store \
    pnpm install --frozen-lockfile

# Copy the scripts directory first and make it executable
COPY scripts ./scripts
RUN chmod +x ./scripts/*.sh

# Copy the rest of the application code
COPY . .

# Configure Next.js to preserve raw request bodies for webhooks
ENV NEXT_PRESERVE_WEBHOOK_BODY=true

RUN ls -la
RUN ls -la ./scripts
# Build the Next.js application
RUN pnpm run build

# Use a multi-stage build to create a smaller production image
FROM node:22-alpine AS production

# Install font packages for sans-serif fonts
RUN apk add --no-cache fontconfig ttf-dejavu

# Set environment variable to enable legacy peer dependencies
ENV NPM_CONFIG_LEGACY_PEER_DEPS=true
RUN npm install -g pnpm

# Set the working directory
WORKDIR /app
# Copy the built application from the build stage
COPY --from=build /app ./

# Expose the port the app runs on
EXPOSE 3000
# Configure Next.js to preserve raw request bodies for webhooks
ENV NEXT_PRESERVE_WEBHOOK_BODY=true

# Start the application
CMD ["pnpm", "run", "serve"]