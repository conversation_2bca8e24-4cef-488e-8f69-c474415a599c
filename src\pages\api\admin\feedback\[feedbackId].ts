import { NextApiRequest, NextApiResponse } from 'next';
import { getToken } from 'next-auth/jwt';
import { Database } from '@/lib/database';
import { GenerateID } from '@/lib/ID';
import { z } from 'zod';
import { debugLog } from '@/lib/logger';

// Define validation schemas
const commentSchema = z.object({
  comment: z.string().max(1024, 'Comment cannot exceed 1024 characters').optional(),
  status: z.enum(['new', 'in-triage', 'in-progress', 'resolved', 'rejected']).optional()
});

/**
 * API endpoint to handle operations on a specific feedback item
 * 
 * @param req - NextApiRequest object
 * @param res - NextApiResponse object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify admin access
    const token = await getToken({ req, secret: process.env.AUTH_SECRET });
    console.log('Admin feedback detail request received', token);

    if (!token || !token.sub) {
      return res.status(403).json({ error: 'Unauthorized: Login required' });
    }

    // Check if user is admin directly from the token
    if (!token.isAdmin) {
      console.log('Access denied - not an admin user', { userId: token.sub });
      return res.status(403).json({ error: 'Unauthorized: Admin access required' });
    }

    console.log('Admin access granted for user', { userId: token.sub });

    // Get feedback ID from request
    const { feedbackId } = req.query;

    if (!feedbackId || typeof feedbackId !== 'string') {
      return res.status(400).json({ error: 'Invalid feedback ID' });
    }

    // Handle different request methods
    switch (req.method) {
      case 'GET':
        return getFeedback(feedbackId, res);
      case 'POST':
        return addComment(feedbackId, req, res, token);
      case 'PUT':
        return updateFeedbackStatus(feedbackId, req, res, token);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Error in admin feedback handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get a specific feedback item with its comments
 */
async function getFeedback(feedbackId: string, res: NextApiResponse) {
  try {
    const db = Database.getInstance();

    // Get feedback document
    const feedbackDoc = await db.readData('feedback', feedbackId);

    if (!feedbackDoc) {
      return res.status(404).json({ error: 'Feedback not found' });
    }

    // Get comments for this feedback
    const commentsSnapshot = await db
      .query('feedback_comments')
      .where('feedbackId', '==', feedbackId)
      .orderBy('createdAt', 'desc')
      .get();

    const comments = commentsSnapshot.docs.map(doc => doc.data());

    // Return feedback with comments
    return res.status(200).json({
      success: true,
      feedback: {
        ...feedbackDoc,
        comments
      }
    });
  } catch (error) {
    console.error('Error fetching feedback:', error);
    return res.status(500).json({ error: 'Failed to fetch feedback' });
  }
}

/**
 * Add a comment to a feedback item
 */
async function addComment(
  feedbackId: string,
  req: NextApiRequest,
  res: NextApiResponse,
  token: any
) {
  try {
    const validationResult = commentSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.error.errors
      });
    }

    const { comment, status } = validationResult.data;

    // Require at least a comment or status change
    if (!comment && !status) {
      return res.status(400).json({
        error: 'Either comment text or status change is required'
      });
    }

    const db = Database.getInstance();

    // Verify the feedback exists
    const feedbackDoc = await db.readData('feedback', feedbackId);
    if (!feedbackDoc) {
      return res.status(404).json({ error: 'Feedback not found' });
    }

    const currentStatus = feedbackDoc.status;
    const commentId = GenerateID('C');
    const now = new Date().toISOString();

    // Create comment entry
    const commentData = {
      id: commentId,
      feedbackId,
      userId: token.sub,
      userName: token.name || 'Admin',
      userEmail: token.email,
      createdAt: now,
      comment: comment || '',
      statusChange: status ? {
        from: currentStatus,
        to: status
      } : null
    };

    // Add comment to database
    await db.addData('feedback_comments', commentData);

    // If there's a status change, update the feedback document
    if (status && status !== currentStatus) {
      await db.updateData('feedback', feedbackId, {
        status,
        lastUpdated: now
      });
    }

    debugLog('Added comment/status to feedback', {
      feedbackId,
      commentId,
      hadComment: !!comment,
      statusChange: status ? `${currentStatus} → ${status}` : null
    });

    return res.status(201).json({
      success: true,
      message: status ? 'Comment added and status updated' : 'Comment added',
      comment: commentData,
      status: status || currentStatus
    });
  } catch (error) {
    console.error('Error adding comment:', error);
    return res.status(500).json({ error: 'Failed to add comment' });
  }
}

/**
 * Update feedback status without necessarily adding a comment
 */
async function updateFeedbackStatus(
  feedbackId: string,
  req: NextApiRequest,
  res: NextApiResponse,
  token: any
) {
  try {
    // Validate status
    const { status, comment } = req.body;

    if (!status || !['new', 'in-triage', 'in-progress', 'resolved', 'rejected'].includes(status)) {
      return res.status(400).json({ error: 'Invalid status value' });
    }

    const db = Database.getInstance();

    // Verify the feedback exists and get current status
    const feedbackDoc = await db.readData('feedback', feedbackId);
    if (!feedbackDoc) {
      return res.status(404).json({ error: 'Feedback not found' });
    }

    const currentStatus = feedbackDoc.status;

    // Don't update if status hasn't changed
    if (currentStatus === status) {
      return res.status(200).json({
        success: true,
        message: 'Status unchanged',
        status
      });
    }

    const now = new Date().toISOString();

    // Update feedback status
    await db.updateData('feedback', feedbackId, {
      status,
      lastUpdated: now
    });

    // Create a comment for the status change
    const commentId = GenerateID('C');
    const commentData = {
      id: commentId,
      feedbackId,
      userId: token.sub,
      userName: token.name || 'Admin',
      userEmail: token.email,
      createdAt: now,
      comment: comment || '',
      statusChange: {
        from: currentStatus,
        to: status
      }
    };

    // Record the status change as a comment
    await db.addData('feedback_comments', commentData);

    debugLog('Updated feedback status', {
      feedbackId,
      statusChange: `${currentStatus} → ${status}`,
      commentAdded: !!comment
    });

    return res.status(200).json({
      success: true,
      message: 'Status updated',
      status,
      comment: commentData
    });
  } catch (error) {
    console.error('Error updating feedback status:', error);
    return res.status(500).json({ error: 'Failed to update status' });
  }
}
