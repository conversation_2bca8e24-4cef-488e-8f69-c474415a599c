# Authentication & RBAC Types - IAC Universe v1

This document catalogues the comprehensive role-based access control (RBAC) system types used in IAC Universe v1. This system supports multi-organization, role-based permissions with venue-specific access controls.

## Core Permission Types

### Permission
**File**: `src/types/session-api.ts`
**Usage**: Base permission actions across the system

```typescript
type Permission = 'view' | 'create' | 'edit' | 'delete';
```

### Portal
**File**: `src/lib/auth/RBAC/permissions.ts`
**Usage**: System portals for role assignment

```typescript
type Portal = 'partner' | 'admin';
```

## Role Hierarchy Types

### AdminRole
**File**: `src/types/session-api.ts`
**Usage**: System-level administrative roles

```typescript
type AdminRole =
  | 'admin:super-admin'        // Full system access
  | 'admin:user-manager'       // User and organization management
  | 'admin:content-moderator'  // Content moderation capabilities
  | 'admin:analytics-viewer';  // Analytics and reporting access
```

### PartnerRole
**File**: `src/types/session-api.ts`
**Usage**: Partner organization roles with venue management

```typescript
type PartnerRole =
  | 'partner:venue-owner'    // Full venue ownership and management
  | 'partner:venue-manager'  // Venue management and event creation
  | 'partner:staff'          // Guest check-in and basic operations
  | 'partner:viewer';        // Read-only access to venue data
```

## Resource Types

### AdminResource
**File**: `src/types/session-api.ts`
**Usage**: System-level resources for admin access

```typescript
type AdminResource =
  | 'admin:dashboard'      // System dashboard access
  | 'admin:users'          // User management
  | 'admin:organizations'  // Organization management
  | 'admin:content'        // Content moderation
  | 'admin:analytics'      // System analytics
  | 'admin:system'         // System configuration
  | 'admin:settings';      // Global settings
```

### PartnerResource
**File**: `src/types/session-api.ts`
**Usage**: Partner organization resources

```typescript
type PartnerResource =
  | 'partner:dashboard'        // Partner dashboard
  | 'partner:venues'           // Venue listing and management
  | 'partner:venue'            // Individual venue access
  | 'partner:venue:events'     // Event management within venues
  | 'partner:venue:settings'   // Venue configuration
  | 'partner:team'             // Team member management
  | 'partner:billing'          // Billing and invoicing
  | 'partner:customers'        // Customer relationship management
  | 'partner:settings';        // Partner organization settings
```

## Role-Permission Mapping Types

### AdminRolePermissions
**File**: `src/types/session-api.ts`
**Usage**: Configuration mapping admin roles to resource permissions

```typescript
type AdminRolePermissions = {
  [K in AdminRole]?: {
    [R in AdminResource]?: Permission[];
  };
};
```

**Example Configuration**:
```typescript
const adminPermissions: AdminRolePermissions = {
  'admin:super-admin': {
    'admin:dashboard': ['view'],
    'admin:users': ['view', 'create', 'edit', 'delete'],
    'admin:organizations': ['view', 'create', 'edit', 'delete'],
    'admin:content': ['view', 'create', 'edit', 'delete'],
    'admin:analytics': ['view'],
    'admin:system': ['view', 'edit'],
    'admin:settings': ['view', 'edit']
  },
  'admin:user-manager': {
    'admin:dashboard': ['view'],
    'admin:users': ['view', 'create', 'edit'],
    'admin:organizations': ['view', 'edit']
  }
};
```

### PartnerRolePermissions
**File**: `src/types/session-api.ts`
**Usage**: Configuration mapping partner roles to resource permissions

```typescript
type PartnerRolePermissions = {
  [K in PartnerRole]?: {
    [R in PartnerResource]?: Permission[];
  };
};
```

**Example Configuration**:
```typescript
const partnerPermissions: PartnerRolePermissions = {
  'partner:venue-owner': {
    'partner:dashboard': ['view'],
    'partner:venues': ['view', 'create', 'edit', 'delete'],
    'partner:venue': ['view', 'edit', 'delete'],
    'partner:venue:events': ['view', 'create', 'edit', 'delete'],
    'partner:venue:settings': ['view', 'edit'],
    'partner:team': ['view', 'create', 'edit', 'delete'],
    'partner:billing': ['view'],
    'partner:customers': ['view', 'edit'],
    'partner:settings': ['view', 'edit']
  },
  'partner:venue-manager': {
    'partner:dashboard': ['view'],
    'partner:venues': ['view'],
    'partner:venue': ['view', 'edit'],
    'partner:venue:events': ['view', 'create', 'edit', 'delete'],
    'partner:customers': ['view', 'edit']
  },
  'partner:staff': {
    'partner:dashboard': ['view'],
    'partner:venues': ['view'],
    'partner:venue': ['view'],
    'partner:venue:events': ['view']
  }
};
```

## Session Management Types

### SessionUser
**File**: `src/types/session-api.ts`
**Usage**: User information in session response

```typescript
interface SessionUser {
  /** Unique user identifier */
  id: string;
  /** User's full display name */
  name: string;
  /** User's email address */
  email: string;
  /** Optional avatar URL */
  avatar?: string;
  /** Optional additional profile data */
  profile?: UserProfile;
}
```

### UserProfile (Session Context)
**File**: `src/types/session-api.ts`
**Usage**: Extended user profile for session management

```typescript
interface UserProfile {
  department?: string;
  title?: string;
  phone?: string;
  [key: string]: any; // Allow additional profile fields
}
```

### SessionOrganization
**File**: `src/types/session-api.ts**
**Usage**: Organization data in session with RBAC configuration

```typescript
interface SessionOrganization {
  /** Organization identifier */
  id: string;
  /** Organization display name */
  name: string;
  /** Type of organization (only 'partner' for real organizations) */
  type: OrganizationType;
  /** RBAC configuration specific to this organization */
  rbacConfig: OrganizationRBACConfig;
}
```

### OrganizationRBACConfig
**File**: `src/types/session-api.ts`
**Usage**: RBAC configuration for an organization

```typescript
interface OrganizationRBACConfig {
  /** 
   * Role-permission mappings for this organization
   * Only includes roles that the user actually has
   */
  rolePermissions: PartnerRolePermissions;
}
```

### SystemPermissions
**File**: `src/types/session-api.ts`
**Usage**: System-level admin permissions

```typescript
interface SystemPermissions {
  /** 
   * Admin role-permission mappings for system-level access
   * Only includes admin roles that the user actually has
   */
  rolePermissions: AdminRolePermissions;
}
```

### SessionAPIResponse
**File**: `src/types/session-api.ts`
**Usage**: Complete session API response structure

```typescript
interface SessionAPIResponse {
  /** Current logged-in user information */
  user: SessionUser;
  /** List of partner organizations user has access to */
  organizations: SessionOrganization[];
  /** System-level admin permissions (empty object if user has no admin access) */
  permissions: SystemPermissions;
  /** API access token for subsequent requests */
  token: string;
}
```

## Multi-Organization RBAC Types

### OrganizationScopedRole
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Role assignment with organization context

```typescript
interface OrganizationScopedRole {
  id: string;                        // Document ID
  userId: string;                    // User this role is assigned to
  organizationId: string;            // Organization this role applies to
  role: Role;                        // The actual role (e.g., 'partner:manager')
  assignedBy: string;                // User ID who assigned this role
  assignedAt: string;                // When the role was assigned (ISO string)
  expiresAt?: string;                // Optional expiration date (ISO string)
  isActive: boolean;                 // Whether the role is currently active
  context?: {
    venueIds?: string[];             // Specific venues user can access
    limitations?: string[];          // Any role limitations or restrictions
    metadata?: Record<string, any>;  // Additional context data
  };
}
```

### UserOrganizationRoles
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: User's complete role structure across organizations

```typescript
interface UserOrganizationRoles {
  userId: string;
  roles: OrganizationScopedRole[];
  currentOrganizationId?: string;    // Active organization context
  lastUpdated: string;               // ISO string
}
```

### OrganizationPermissionContext
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Context for organization-specific permission checks

```typescript
interface OrganizationPermissionContext {
  organizationId: string;            // Required: which organization
  venueId?: string;                  // Optional: specific venue context
  userId?: string;                   // Optional: for user-specific checks
}
```

### OrganizationWithRBAC
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization details with RBAC configuration

```typescript
interface OrganizationWithRBAC {
  id: string;
  name: string;
  type: 'partner' | 'admin';
  rbacConfig: any;                   // Organization-specific RBAC configuration
  canAccess: boolean;
  isActive: boolean;
}
```

## Permission Management Types

### UserRole
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Individual role assignment

```typescript
interface UserRole {
  id: string;
  userId: string;
  role: string;
  portal: 'partner' | 'admin';
  context?: PermissionContext;
  assignedAt: Date;
  assignedBy: string;
  expiresAt?: Date;
  isActive: boolean;
}
```

### PermissionContext
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Context for venue-specific operations

```typescript
interface PermissionContext {
  venueId?: string;
  organizationId?: string;
  userId?: string;
}
```

### PermissionCheckResult
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Result of permission verification

```typescript
interface PermissionCheckResult {
  granted: boolean;
  role: string;
  resource: string;
  permission: string;
  context?: PermissionContext;
  reason?: string;
}
```

### PermissionMiddlewareContext
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Context for permission validation middleware

```typescript
interface PermissionMiddlewareContext {
  user: {
    id: string;
    roles: UserRole[];
  };
  resource: string;
  permission: string;
  context?: PermissionContext;
}
```

## Role Management & Assignment Types

### RoleAssignmentRequest
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Request structure for role assignment

```typescript
interface RoleAssignmentRequest {
  userId: string;
  role: string;
  portal: 'partner' | 'admin';
  context?: PermissionContext;
  expiresAt?: Date;
  assignedBy: string;
}
```

### AssignOrganizationRoleRequest
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization-specific role assignment

```typescript
interface AssignOrganizationRoleRequest {
  userId: string;
  organizationId: string;
  role: Role;
  venueRestrictions?: string[];
  expiresAt?: Date;
  metadata?: Record<string, any>;
}
```

### SwitchOrganizationRequest
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization context switching

```typescript
interface SwitchOrganizationRequest {
  organizationId: string;
}
```

## Audit & Logging Types

### PermissionAuditLog
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Audit trail for permission changes

```typescript
interface PermissionAuditLog {
  id: string;
  action: 'grant' | 'revoke' | 'check';
  userId: string;
  targetUserId?: string;
  role: string;
  resource: string;
  permission: string;
  granted: boolean;
  context?: PermissionContext;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
}
```

### OrganizationRoleAuditLog
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization role change audit trail

```typescript
interface OrganizationRoleAuditLog {
  id: string;
  action: 'assign' | 'revoke' | 'modify' | 'expire';
  userId: string;
  organizationId: string;
  role: Role;
  performedBy: string;
  performedAt: string;
  previousRole?: Role;
  newRole?: Role;
  reason?: string;
  metadata?: Record<string, any>;
}
```

## Configuration & Mapping Types

### ResourceActionMap
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: API endpoint to resource/permission mapping

```typescript
interface ResourceActionMap {
  [endpoint: string]: {
    resource: string;
    permission: string;
    context?: (req: any) => PermissionContext;
  };
}
```

**Example Usage**:
```typescript
const apiEndpointMap: ResourceActionMap = {
  'GET /api/venues': {
    resource: 'partner:venues',
    permission: 'view'
  },
  'POST /api/venues': {
    resource: 'partner:venues',
    permission: 'create'
  },
  'PUT /api/venues/:venueId': {
    resource: 'partner:venue',
    permission: 'edit',
    context: (req) => ({ venueId: req.params.venueId })
  }
};
```

### RoleHierarchy
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Role inheritance definition

```typescript
interface RoleHierarchy {
  role: string;
  inheritsFrom?: string[];
  portal: 'partner' | 'admin';
}
```

### PermissionInheritance
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Permission inheritance between resources

```typescript
interface PermissionInheritance {
  parentResource: string;
  childResource: string;
  inheritedPermissions: string[];
}
```

## Database Document Types

### UserOrganizationRoleDocument
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Storage**: Firestore `userOrganizationRoles` collection

```typescript
interface UserOrganizationRoleDocument {
  userId: string;
  organizationId: string;
  role: string;
  assignedBy: string;
  assignedAt: string;
  expiresAt?: string;
  isActive: boolean;
  venueRestrictions?: string[];      // Array of venue IDs
  permissionsMetadata?: Record<string, any>; // Additional context
}
```

### UserActiveOrganizationDocument
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Storage**: Firestore `userActiveOrganization` collection

```typescript
interface UserActiveOrganizationDocument {
  userId: string;
  organizationId: string;
  updatedAt: string;
}
```

### OrganizationDocument
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Storage**: Firestore `organizations` collection

```typescript
interface OrganizationDocument {
  id: string;
  name: string;
  type: 'partner' | 'admin';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  settings?: Record<string, any>;
}
```

## Utility Function Interfaces

### PermissionChecker
**File**: `src/types/session-api.ts`
**Usage**: Helper interface for permission checking

```typescript
interface PermissionChecker {
  /** Check if user has a specific permission on a resource in an organization */
  hasOrganizationPermission(
    organization: SessionOrganization,
    resource: PartnerResource,
    permission: Permission
  ): boolean;

  /** Check if user has a specific admin permission on a system resource */
  hasAdminPermission(
    permissions: SystemPermissions,
    resource: AdminResource,
    permission: Permission
  ): boolean;

  /** Get all roles user has in an organization */
  getOrganizationRoles(organization: SessionOrganization): PartnerRole[];

  /** Get all admin roles user has */
  getAdminRoles(permissions: SystemPermissions): AdminRole[];
}
```

### OrganizationRBACFunctions
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization permission checking functions

```typescript
interface OrganizationRBACFunctions {
  hasOrganizationPermission(
    userRoles: OrganizationScopedRole[],
    resource: string,
    permission: Permission,
    context: OrganizationPermissionContext
  ): boolean;

  getUserRolesInOrganization(
    userRoles: OrganizationScopedRole[],
    organizationId: string
  ): OrganizationScopedRole[];

  canManageUserInOrganization(
    adminRole: OrganizationScopedRole,
    targetUserId: string,
    organizationId: string
  ): boolean;

  getPermissionsInOrganization(
    userRoles: OrganizationScopedRole[],
    organizationId: string
  ): Record<string, Permission[]>;
}
```

## Type Utility Functions

### Type Guards
```typescript
// Type guard to check if user has any admin permissions
function hasAdminPermissions(permissions: SystemPermissions): boolean {
  return Object.keys(permissions.rolePermissions).length > 0;
}

// Type guard to check if user has access to any organizations
function hasOrganizationAccess(organizations: SessionOrganization[]): boolean {
  return organizations.length > 0;
}
```

### Utility Types
```typescript
// Utility type to extract user roles from an organization
type UserOrganizationRoles<T extends SessionOrganization> =
  keyof T['rbacConfig']['rolePermissions'];

// Utility type to extract user admin roles from system permissions
type UserAdminRoles<T extends SystemPermissions> =
  keyof T['rolePermissions'];

// Type aliases for organization-specific roles
type PartnerOrganizationRole = OrganizationScopedRole & {
  role: `partner:${string}`;
};

type AdminOrganizationRole = OrganizationScopedRole & {
  role: `admin:${string}`;
};
```

## Constants

### Database Collections
```typescript
const DATABASE_COLLECTIONS = {
  USER_ORGANIZATION_ROLES: 'userOrganizationRoles',
  USER_ACTIVE_ORGANIZATION: 'userActiveOrganization',
  ORGANIZATIONS: 'organizations',
  ORGANIZATION_ROLE_AUDIT: 'organizationRoleAudit',
} as const;
```

## Usage Examples

### Permission Checking
```typescript
// Check organization permission
const canEditVenue = hasOrganizationPermission(
  userRoles,
  'partner:venue',
  'edit',
  { organizationId: 'org_123', venueId: 'venue_456' }
);

// Check admin permission
const canManageUsers = hasAdminPermission(
  systemPermissions,
  'admin:users',
  'edit'
);
```

### Role Assignment
```typescript
// Assign partner role with venue restrictions
const roleRequest: AssignOrganizationRoleRequest = {
  userId: 'user_123',
  organizationId: 'org_456',
  role: 'partner:venue-manager',
  venueRestrictions: ['venue_001', 'venue_002'],
  metadata: {
    assignmentReason: 'New venue manager training complete'
  }
};
```

### Session Management
```typescript
// Process session response
const session: SessionAPIResponse = await getSession();
const userOrganizations = session.organizations;
const hasAdminAccess = hasAdminPermissions(session.permissions);
const canAccessPartner = hasOrganizationAccess(session.organizations);
```

This comprehensive RBAC system provides fine-grained access control with support for multi-organization, venue-specific permissions while maintaining full audit trails and type safety throughout the application.