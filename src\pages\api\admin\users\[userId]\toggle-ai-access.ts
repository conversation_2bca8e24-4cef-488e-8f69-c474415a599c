import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated and is an admin
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the current user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    // Get database instance
    const db = Database.getInstance();

    // Get user ID from the query parameters
    const { userId } = req.query;

    if (!userId || Array.isArray(userId)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // Only accept POST method for toggling AI access
    if (req.method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get the user to toggle AI access
    const user = await db.getUserById(userId);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get the requested AI access status from the request body
    const { hasAiAccess } = req.body;

    if (typeof hasAiAccess !== 'boolean') {
      return res.status(400).json({ error: 'hasAiAccess must be a boolean value' });
    }

    // Update the user's AI access status
    await db.setUserAiAccessStatus(userId, hasAiAccess);

    // Return success response
    return res.status(200).json({
      success: true,
      message: `User AI access ${hasAiAccess ? 'enabled' : 'disabled'} successfully`,
      user: {
        ...user,
        hasAiAccess
      }
    });
  } catch (error) {
    console.error('Error toggling user AI access:', error);
    return res.status(500).json({ error: 'Failed to update AI access status' });
  }
}
