import { useEffect } from 'react';
import { signOut } from 'next-auth/react';
import { useRouter } from 'next/router';

export default function SignOut() {
  const router = useRouter(); useEffect(() => {
    const handleSignOut = async () => {
      try {
        // Step 1: Call our custom signout API to clear cross-domain cookies
        await fetch('/api/id/signout', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        // Step 2: Use NextAuth's signOut function (this handles the API call properly)
        await signOut({
          redirect: false, // We'll handle redirect manually
        });

        // Step 3: Clear all client-side storage and cookies
        if (typeof window !== 'undefined') {
          localStorage.clear();
          sessionStorage.clear();

          // Clear all possible cookies with different domains and paths
          const cookiesToClear = [
            'next-auth.session-token',
            '__Secure-next-auth.session-token',
            'next-auth.csrf-token',
            '__Host-next-auth.csrf-token',
            'cross-domain-session',
            'next-auth.callback-url',
            'next-auth.state'
          ];

          const domains = ['', '.iamcoming.io', '.localhost', 'localhost'];

          cookiesToClear.forEach(cookieName => {
            domains.forEach(domain => {
              const domainPart = domain ? `; domain=${domain}` : '';
              document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/${domainPart}`;
              document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/auth${domainPart}`;
              document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/api${domainPart}`;
              document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/api/auth${domainPart}`;
            });
          });

          // Step 4: Force a hard redirect to completely clear any cached state
          window.location.href = '/';
        }
      } catch (error) {
        console.error('Sign out error:', error);
        // Force redirect even if logout fails
        if (typeof window !== 'undefined') {
          window.location.href = '/';
        } else {
          router.push('/');
        }
      }
    };

    handleSignOut();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="flex flex-col items-center">
        {/* Simple spinner loader */}
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mb-3"></div>
        <p className="text-gray-700">Logging out...</p>
      </div>
    </div>
  );
}
