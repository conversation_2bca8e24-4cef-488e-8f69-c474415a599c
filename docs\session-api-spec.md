# Session API Specification for IAC Authentication System

## Overview

This document defines the Session API specification required to integrate with the IAC (I Am Coming) authentication system. This API enables frontend applications to authenticate users, manage sessions, and implement role-based access control (RBAC).

**Target Audience**: Backend developers implementing authentication services for IAC frontend applications.

## System Architecture Context

The IAC system uses a React-based frontend with a centralized authentication provider (`IACAuthProvider`) that communicates with backend session APIs. The authentication flow is:

1. **Frontend**: React app with `IACAuthProvider` component
2. **Session API**: Backend service (your implementation)
3. **Authentication**: Cookie-based session management
4. **Authorization**: Role-based access control (RBAC)

## API Requirements

### Base Configuration

- **Default Endpoint**: `https://id.iamcoming.io/api` (configurable)
- **Session Endpoint**: `{baseEndpoint}/session`
- **Method**: `GET`
- **Authentication**: Cookie-based sessions

### CORS Configuration

Since frontend apps may be hosted on different domains, ensure your API supports:

```javascript
// Example CORS headers for cross-domain authentication
Access-Control-Allow-Origin: https://partner.iamcoming.io
Access-Control-Allow-Credentials: true
Access-Control-Allow-Headers: Content-Type, Authorization
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
```

**Note**: For secure cross-domain authentication, the system uses HTTP-only cookies:

- Regular session cookies for same-domain requests
- Cross-domain session cookies (HTTP-only) for `*.iamcoming.io` subdomains
- External clients automatically receive cookies when users authenticate through the main domain

## API Specification

### GET /session

Retrieves the current authenticated user's session data, including user information, roles, permissions, and API token.

#### Request Format

```http
GET {baseEndpoint}/session
Content-Type: application/json
Cookie: session=<your-session-cookie-name>=<session-value>
```

**For Cross-Domain Authentication:**

```http
GET {baseEndpoint}/session
Content-Type: application/json
Cookie: cross-domain-session=<jwt-session-token>
```

**Important**:

- The frontend will send requests with `credentials: 'include'` to ensure cookies are included
- External domains automatically receive cross-domain session cookies when users authenticate
- No manual token handling required - cookies are sent automatically by the browser

#### Success Response (200 OK)

```json
{
  "user": {
    "id": "user_12345",
    "name": "John Doe",
    "email": "<EMAIL>",
    "avatar": "https://cdn.example.com/avatars/john-doe.jpg",
    "profile": {
      "department": "Engineering",
      "title": "Senior Developer",
      "phone": "******-0123"
    }
  },
  "organizations": [
    {
      "id": "org_partner_456",
      "name": "Acme Events",
      "type": "partner",
      "rbacConfig": {
        "rolePermissions": {
          "partner:venue-manager": {
            "partner:dashboard": ["view"],
            "partner:venues": ["view"],
            "partner:venue": ["view", "edit"],
            "partner:venue:events": ["view", "create", "edit", "delete"]
          }
        }
      }
    },
    {
      "id": "org_venue_789",
      "name": "Downtown Convention Center",
      "type": "partner",
      "rbacConfig": {
        "rolePermissions": {
          "partner:venue-owner": {
            "partner:dashboard": ["view"],
            "partner:venues": ["view", "create", "edit", "delete"],
            "partner:venue": ["view", "create", "edit", "delete"],
            "partner:venue:events": ["view", "create", "edit", "delete"],
            "partner:billing": ["view", "edit"],
            "partner:team": ["view", "create", "edit", "delete"]
          }
        }
      }
    }
  ],
  "permissions": {
    "rolePermissions": {
      "admin:super-admin": {
        "admin:dashboard": ["view"],
        "admin:users": ["view", "create", "edit", "delete"],
        "admin:organizations": ["view", "create", "edit", "delete"],
        "admin:content": ["view", "create", "edit", "delete"],
        "admin:analytics": ["view"],
        "admin:system": ["view", "create", "edit", "delete"],
        "admin:settings": ["view", "edit"]
      }
    }
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwiaWF0IjoxNjE2MjM5MDIyfQ.signature"
}
```

#### Error Responses

##### Unauthenticated (401 Unauthorized)

```json
{
  "error": "Unauthorized",
  "message": "No valid session found",
  "code": "SESSION_INVALID"
}
```

##### Forbidden (403 Forbidden)

```json
{
  "error": "Forbidden",
  "message": "Session expired",
  "code": "SESSION_EXPIRED"
}
```

##### Server Error (500 Internal Server Error)

```json
{
  "error": "Internal Server Error",
  "message": "Failed to retrieve session data",
  "code": "SESSION_FETCH_ERROR"
}
```

## Session API Response Structure

The IAC Session API provides a clean, logical structure with four main components:

1. **`user`** - Current logged-in user information
2. **`organizations`** - Partner organizations user has access to (with organization-specific RBAC)
3. **`permissions`** - System-level admin permissions (user-level privileges)
4. **`token`** - API access token for subsequent requests

### Design Principles

The API separates organization-level permissions from system-level permissions:

- **Organizations**: Real business entities (partners, venues) with role-based access
- **Permissions**: System-level admin privileges for platform management

This separation ensures that:

- Organization permissions are scoped to specific business contexts
- Admin permissions apply globally across the platform
- Each type of permission is managed independently

### TypeScript Type Definitions

```typescript
// Complete Session API Response Type
interface SessionAPIResponse {
  user: SessionUser;
  organizations: SessionOrganization[];
  permissions: SystemPermissions;
  token: string;
}

// User information
interface SessionUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  profile?: UserProfile;
}

// Organization with RBAC (partner organizations only)
interface SessionOrganization {
  id: string;
  name: string;
  type: 'partner';
  rbacConfig: {
    rolePermissions: PartnerRolePermissions;
  };
}

// System-level admin permissions
interface SystemPermissions {
  rolePermissions: AdminRolePermissions;
}

// Permission types
type Permission = 'view' | 'create' | 'edit' | 'delete';

// Partner roles and resources
type PartnerRole =
  | 'partner:venue-owner'
  | 'partner:venue-manager'
  | 'partner:staff';
type PartnerResource =
  | 'partner:dashboard'
  | 'partner:venues'
  | 'partner:venue'
  | 'partner:venue:events';

// Admin roles and resources
type AdminRole =
  | 'admin:super-admin'
  | 'admin:user-manager'
  | 'admin:content-moderator';
type AdminResource = 'admin:dashboard' | 'admin:users' | 'admin:organizations';

// Role-permission mappings
type PartnerRolePermissions = {
  [K in PartnerRole]?: {
    [R in PartnerResource]?: Permission[];
  };
};

type AdminRolePermissions = {
  [K in AdminRole]?: {
    [R in AdminResource]?: Permission[];
  };
};
```

### How to Use the Session API Structure

#### Check Organization Permissions

```javascript
// Get user roles in an organization
const userRoles = Object.keys(organization.rbacConfig.rolePermissions);

// Check specific permission
function hasOrgPermission(org, resource, permission) {
  const roles = Object.keys(org.rbacConfig.rolePermissions || {});
  return roles.some((role) =>
    org.rbacConfig.rolePermissions[role]?.[resource]?.includes(permission)
  );
}
```

#### Check Admin Permissions

```javascript
// Get admin roles
const adminRoles = Object.keys(sessionData.permissions.rolePermissions);

// Check admin permission
function hasAdminPermission(permissions, resource, permission) {
  const roles = Object.keys(permissions.rolePermissions || {});
  return roles.some((role) =>
    permissions.rolePermissions[role]?.[resource]?.includes(permission)
  );
}
```

### User Object

The user object in the session response contains basic user information:

```typescript
interface SessionUser {
  id: string; // Unique user identifier (required)
  name: string; // User's full display name (required)
  email: string; // User's email address (required)
  avatar?: string; // Optional avatar URL
  profile?: UserProfile; // Optional additional profile data
}

interface UserProfile {
  department?: string;
  title?: string;
  phone?: string;
  [key: string]: unknown; // Additional flexible profile fields
}
```

**Field Requirements:**

- `id`: Must be unique and stable across sessions
- `name`: Used for display purposes in UI
- `email`: Used for user identification and communication
- `avatar`: Should be a publicly accessible URL if provided
- `profile`: Flexible object for additional user metadata (department, title, etc.)

### RBAC (Role-Based Access Control)

The IAC session API uses a modular RBAC system with clear separation between organization-level and system-level permissions.

**RBAC Design Principles:**

- **Portal Separation**: Clear separation between Partner and Admin functionality
- **Type Safety**: Fully typed TypeScript interfaces for all roles and resources
- **Organization Scoping**: Organization permissions are context-specific
- **System Permissions**: Admin permissions apply globally across the platform
- **Resource Hierarchy**: Support for nested resources (e.g., `partner:venue:events`)
- **Standard Permissions**: Consistent CRUD operations (`view`, `create`, `edit`, `delete`)

### API Token

- **Format**: JWT (recommended) or secure random string
- **Purpose**: Used for subsequent API requests requiring authentication
- **Lifetime**: Should match or be shorter than session lifetime
- **Usage**: Included in `Authorization: Bearer <token>` headers

## Implementation Guidelines

### Session Management

#### Cookie Configuration

```javascript
// Example cookie settings (adjust for your framework)
{
  name: 'session',
  httpOnly: true,                    // Prevent XSS attacks
  secure: true,                      // HTTPS only in production
  sameSite: 'lax',                   // CSRF protection
  maxAge: 86400000,                  // 24 hours (in milliseconds)
  domain: '.iamcoming.io',           // Allow subdomain access
  path: '/'
}

// Cross-domain session cookie (for external authentication)
{
  name: 'cross-domain-session',
  httpOnly: true,                    // Prevent XSS attacks
  secure: true,                      // HTTPS only in production
  sameSite: 'none',                  // Required for cross-site cookies
  maxAge: 3600000,                   // 1 hour (shorter for security)
  domain: '.iamcoming.io',           // Allow subdomain access
  path: '/'
}
```

#### Session Storage Options

1. **Database Sessions** (Recommended for production)

   - Store session data in Redis/PostgreSQL/MongoDB
   - Better security and scalability
   - Support for session invalidation

2. **JWT Sessions** (Simpler implementation)
   - Stateless session management
   - Include user data and expiration in token
   - Harder to invalidate before expiration

#### Cross-Domain Authentication Support

For secure cross-domain authentication between `*.iamcoming.io` subdomains:

1. **HTTP-Only Cross-Domain Cookies** (Primary Method)

   - Cross-domain session cookie (`cross-domain-session`) is automatically set when user authenticates
   - Cookie domain is set to `.iamcoming.io` to work across all subdomains
   - Browsers automatically include the cookie in requests to any `*.iamcoming.io` domain
   - No manual token handling required by external clients

2. **Authentication Flow for External Clients**

   ```text
   1. User visits external site (e.g., partner.iamcoming.io)
   2. External site calls main domain's session API: https://beta.iamcoming.io/api/id/session
   3. If user not authenticated → 401 response → redirect to login
   4. User logs in at main domain (beta.iamcoming.io)
   5. Cross-domain cookie is set for .iamcoming.io domain
   6. User redirected back to external site
   7. External site calls session API again → cookie automatically sent → 200 success
   ```

3. **Implementation for External Clients**

   External sites (like `partner.iamcoming.io`) should:

   - Make CORS requests to `https://beta.iamcoming.io/api/id/session`
   - Include `credentials: 'include'` in fetch requests
   - Handle 401 responses by redirecting to login
   - No need to pass `site=partner` parameters or handle tokens manually

### RBAC Implementation

#### Permission Checking Functions

Since the API now separates organization and system permissions, you'll need different functions for each:

```javascript
// Check permissions within a partner organization
function hasOrganizationPermission(organization, resource, permission) {
  const roles = Object.keys(organization.rbacConfig.rolePermissions || {});
  return roles.some((role) => {
    const rolePermissions = organization.rbacConfig.rolePermissions[role];
    return rolePermissions?.[resource]?.includes(permission);
  });
}

// Check system-level admin permissions
function hasAdminPermission(permissions, resource, permission) {
  const roles = Object.keys(permissions.rolePermissions || {});
  return roles.some((role) => {
    const rolePermissions = permissions.rolePermissions[role];
    return rolePermissions?.[resource]?.includes(permission);
  });
}

// Usage examples:
const sessionData = await getSession();

// Check organization permission
const canEditVenue = hasOrganizationPermission(
  sessionData.organizations[0],
  'partner:venue',
  'edit'
);

// Check admin permission
const canManageUsers = hasAdminPermission(
  sessionData.permissions,
  'admin:users',
  'edit'
);
```

#### Utility Functions for Role Management

```javascript
// Get all roles user has in an organization
function getOrganizationRoles(organization) {
  return Object.keys(organization.rbacConfig.rolePermissions || {});
}

// Get all admin roles user has
function getAdminRoles(permissions) {
  return Object.keys(permissions.rolePermissions || {});
}

// Check if user has any admin access
function isAdmin(permissions) {
  return Object.keys(permissions.rolePermissions || {}).length > 0;
}

// Get all organizations user can access
function getAccessibleOrganizations(organizations) {
  return organizations.filter(
    (org) => Object.keys(org.rbacConfig.rolePermissions || {}).length > 0
  );
}
```

#### RBAC Structure in Session Response

The session API returns RBAC data in two separate contexts:

1. **Organization-level permissions** (in `organizations[].rbacConfig.rolePermissions`)
2. **System-level admin permissions** (in `permissions.rolePermissions`)

**Organization RBAC Example:**

```json
{
  "organizations": [
    {
      "id": "org_partner_456",
      "name": "Acme Events",
      "type": "partner",
      "rbacConfig": {
        "rolePermissions": {
          "partner:venue-manager": {
            "partner:dashboard": ["view"],
            "partner:venues": ["view"],
            "partner:venue": ["view", "edit"],
            "partner:venue:events": ["view", "create", "edit", "delete"]
          }
        }
      }
    }
  ]
}
```

**System-level RBAC Example:**

```json
{
  "permissions": {
    "rolePermissions": {
      "admin:super-admin": {
        "admin:dashboard": ["view"],
        "admin:users": ["view", "create", "edit", "delete"],
        "admin:organizations": ["view", "create", "edit", "delete"]
      }
    }
  }
}
```

**Key Design Principles:**

- **Separation of Concerns**: Organization permissions are scoped to specific business entities
- **System Permissions**: Admin permissions apply globally across the platform
- **Role Naming**: Uses portal prefixes (`partner:`, `admin:`) for clear separation
- **Resource Hierarchy**: Supports nested resources (e.g., `partner:venue:events`)
- **Standard Permissions**: Consistent CRUD operations (`view`, `create`, `edit`, `delete`)

### API Token Generation

#### JWT Example (Node.js)

```javascript
const jwt = require('jsonwebtoken');

// For API tokens (returned in session response)
function generateApiToken(user, sessionId) {
  const payload = {
    sub: user.id, // Subject (user ID)
    email: user.email,
    sessionId: sessionId,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24 hours
    iss: 'iamcoming.io', // Issuer
    aud: ['partner.iamcoming.io', 'localhost:3000'], // Allowed audiences
  };

  return jwt.sign(payload, process.env.JWT_SECRET, { algorithm: 'HS256' });
}

// For cross-domain session cookies (shorter expiry for security)
function generateCrossDomainSessionToken(user) {
  const payload = {
    userId: user.id,
    email: user.email,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    iss: 'iamcoming.io',
    aud: ['partner.iamcoming.io', 'localhost:3000'],
  };

  return jwt.sign(payload, process.env.AUTH_SECRET, { algorithm: 'HS256' });
}
```

#### Secure Random Token Example

```javascript
const crypto = require('crypto');

function generateApiToken() {
  return crypto.randomBytes(32).toString('hex');
}
```

## Security Requirements

### Essential Security Measures

1. **HTTPS Only**: Never transmit sessions over HTTP in production
2. **Secure Cookies**: Use `httpOnly`, `secure`, and `sameSite` flags
3. **CSRF Protection**: Implement CSRF tokens for state-changing operations
4. **Session Expiration**: Implement reasonable session timeouts
5. **Session Invalidation**: Provide logout endpoints that invalidate sessions
6. **Input Validation**: Validate all inputs and sanitize output
7. **Rate Limiting**: Protect against brute force and DoS attacks

### Additional Security Considerations

1. **Token Security**

   - Generate cryptographically secure tokens
   - Implement token rotation for long-lived sessions
   - Never log tokens in plaintext

2. **Permission Validation**

   - Always validate permissions server-side
   - Don't trust client-side permission checks
   - Log permission violations for security monitoring

3. **Session Management**
   - Implement concurrent session limits if needed
   - Provide session management UI for users
   - Monitor for suspicious session activity

## Testing Requirements

### Required Test Cases

#### 1. Valid Session Tests

```bash
# Test with valid session cookie (same-domain)
curl -X GET "https://your-api.com/session" \
  -H "Cookie: session=valid-session-id" \
  -H "Content-Type: application/json"

# Test with cross-domain session cookie (external domains)
curl -X GET "https://your-api.com/session" \
  -H "Cookie: cross-domain-session=valid-jwt-token" \
  -H "Content-Type: application/json"

# Expected: 200 OK with complete user data
```

#### 2. Invalid Session Tests

```bash
# Test with no session cookie
curl -X GET "https://your-api.com/session"
# Expected: 401 Unauthorized

# Test with invalid session cookie
curl -X GET "https://your-api.com/session" \
  -H "Cookie: session=invalid-session-id"
# Expected: 401 Unauthorized

# Test with expired session cookie
curl -X GET "https://your-api.com/session" \
  -H "Cookie: session=expired-session-id"
# Expected: 401 Unauthorized
```

#### 3. Data Validation Tests

- Verify all required fields are present in response
- Validate data types match specification
- Ensure RBAC structure is correctly formatted
- Confirm API token is valid and usable

#### 4. CORS Tests

```bash
# Test preflight request
curl -X OPTIONS "https://your-api.com/session" \
  -H "Origin: https://partner.iamcoming.io" \
  -H "Access-Control-Request-Method: GET"
# Expected: Proper CORS headers
```

### Integration Testing

1. **Frontend Integration**: Test with actual frontend applications
2. **Load Testing**: Verify performance under expected load
3. **Security Testing**: Penetration testing for session vulnerabilities
4. **Cross-Browser Testing**: Ensure cookie behavior works across browsers

## Example Implementation (Node.js/Express)

```javascript
const express = require('express');
const cookieParser = require('cookie-parser');
const cors = require('cors');

const app = express();

// Middleware
app.use(
  cors({
    origin: ['https://partner.iamcoming.io', 'https://admin.iamcoming.io'],
    credentials: true,
  })
);
app.use(cookieParser());

// Session endpoint
app.get('/session', async (req, res) => {
  try {
    let sessionId = null;
    let userId = null;

    // Check for cross-domain session cookie first (for external domains)
    if (req.cookies['cross-domain-session']) {
      const tokenData = verifySessionToken(req.cookies['cross-domain-session']);
      if (!tokenData) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid cross-domain session token',
          code: 'TOKEN_INVALID',
        });
      }
      userId = tokenData.userId;
    }
    // Check for regular session cookie (same-domain)
    else if (req.cookies.session) {
      sessionId = req.cookies.session;
      const session = await validateSession(sessionId);
      if (!session || session.expired) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid or expired session',
          code: 'SESSION_INVALID',
        });
      }
      userId = session.userId;
    }
    // No valid authentication found
    else {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'No session cookie found',
        code: 'NO_SESSION',
      });
    }

    // Get user data
    const user = await getUserById(userId);

    if (!user) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found',
        code: 'USER_NOT_FOUND',
      });
    }

    // Generate API token
    const apiToken = generateApiToken(
      user,
      sessionId || 'cross-domain-session'
    );

    // Return session data (matching the clean session API structure)
    res.json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        profile: user.profile,
      },
      organizations: user.organizations || [], // Organization-specific RBAC
      permissions: user.permissions || { rolePermissions: {} }, // System-level admin permissions
      token: apiToken,
    });
  } catch (error) {
    console.error('Session endpoint error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve session data',
      code: 'SESSION_FETCH_ERROR',
    });
  }
});

// Helper functions (implement based on your system)
async function validateSession(sessionId) {
  // Your session validation logic
}

async function getUserById(userId) {
  // Your user retrieval logic
  // Note: Include organizations and permissions data for the user
}

function generateApiToken(user, sessionId) {
  // Your token generation logic
}

function verifySessionToken(token) {
  try {
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.AUTH_SECRET);

    // Check if token is expired
    if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return {
      userId: decoded.userId,
      email: decoded.email,
    };
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}
```

## External Client Implementation Guide

This section provides specific guidance for external clients (like `partner.iamcoming.io`, `admin.iamcoming.io`, etc.) on how to implement authentication.

### For External Domain Developers

If you're building an application on a subdomain of `iamcoming.io`, follow these steps:

#### 1. Setup CORS Requests

```javascript
// Example: Making authenticated requests from partner.iamcoming.io
const response = await fetch('https://beta.iamcoming.io/api/id/session', {
  method: 'GET',
  credentials: 'include', // CRITICAL: This includes cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

if (response.status === 401) {
  // User not authenticated, redirect to login
  window.location.href = 'https://beta.iamcoming.io/auth/signin?site=partner';
} else if (response.ok) {
  const sessionData = await response.json();
  // User is authenticated, use sessionData.user and sessionData.rbac
}
```

#### 2. Handle Authentication States

```javascript
// Example authentication check function
async function checkAuth() {
  try {
    const response = await fetch('https://beta.iamcoming.io/api/id/session', {
      credentials: 'include',
    });

    if (response.ok) {
      const { user, rbac } = await response.json();
      return { authenticated: true, user, rbac };
    } else {
      return { authenticated: false };
    }
  } catch (error) {
    console.error('Auth check failed:', error);
    return { authenticated: false };
  }
}

// Use in your app
const authState = await checkAuth();
if (!authState.authenticated) {
  // Redirect to login with return URL
  const returnUrl = encodeURIComponent(window.location.href);
  window.location.href = `https://beta.iamcoming.io/auth/signin?site=partner&returnUrl=${returnUrl}`;
}
```

#### 3. React Hook Example

```javascript
// Custom hook for external React apps
import { useState, useEffect } from 'react';

export function useIACAuth() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [rbac, setRbac] = useState(null);

  useEffect(() => {
    async function loadSession() {
      try {
        const response = await fetch(
          'https://beta.iamcoming.io/api/id/session',
          {
            credentials: 'include',
          }
        );

        if (response.ok) {
          const data = await response.json();
          setUser(data.user);
          setRbac(data.rbac);
        } else {
          setUser(null);
          setRbac(null);
        }
      } catch (error) {
        console.error('Session load failed:', error);
        setUser(null);
        setRbac(null);
      } finally {
        setLoading(false);
      }
    }

    loadSession();
  }, []);

  const login = () => {
    const currentUrl = encodeURIComponent(window.location.href);
    window.location.href = `https://beta.iamcoming.io/auth/signin?site=partner&returnUrl=${currentUrl}`;
  };

  const logout = async () => {
    try {
      await fetch('https://beta.iamcoming.io/auth/sign-out', {
        method: 'POST',
        credentials: 'include',
      });
      setUser(null);
      setRbac(null);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return { user, loading, rbac, login, logout };
}
```

#### 4. Important Notes for External Clients

1. **No Token Handling Required**: Cookies are automatically managed by the browser
2. **No Site Parameters**: External clients don't need to pass `site=partner` in API calls
3. **CORS Configuration**: Ensure your domain is whitelisted in the main API's CORS settings
4. **Cookie Domain**: Cookies are automatically shared across `*.iamcoming.io` subdomains
5. **Login Redirects**: Use `site=partner` only when redirecting to login, not for API calls

#### 5. CORS Requirements for Backend

If you're setting up CORS on the main API server, ensure external domains are included:

```javascript
// CORS configuration for main API
app.use(
  cors({
    origin: [
      'https://partner.iamcoming.io',
      'https://admin.iamcoming.io',
      'https://app.iamcoming.io',
      'http://localhost:3000', // for development
    ],
    credentials: true, // CRITICAL: Required for cookies
  })
);
```

## Environment Variables

Ensure these environment variables are configured:

```bash
# Required for JWT token signing and verification
AUTH_SECRET=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# Required for proper cookie domain settings
NODE_ENV=production  # or development

# Optional for debugging
IAC_DEBUG=true
```

**Important**: `AUTH_SECRET` must be the same across all services that need to verify cross-domain session tokens.

## Deployment Checklist

### Pre-Production

- [ ] Implement all required endpoints according to specification
- [ ] Configure secure cookie settings for production
- [ ] Set up HTTPS with valid SSL certificates
- [ ] Configure CORS for production domains
- [ ] Implement proper error handling and logging
- [ ] Set up session storage (Redis/Database)
- [ ] Configure rate limiting and security middleware
- [ ] Test all authentication flows end-to-end

### Production

- [ ] Monitor session endpoint performance and errors
- [ ] Set up alerts for authentication failures
- [ ] Implement session cleanup for expired sessions
- [ ] Monitor for security vulnerabilities
- [ ] Regular security audits and penetration testing
- [ ] Document session management procedures for operations team

## Support and Contact

For technical questions about this specification:

1. **Review this document** for implementation details
2. **Test with curl** or Postman to verify your implementation
3. **Validate responses** match the exact JSON structure specified
4. **Check security requirements** are properly implemented

### Troubleshooting Common Issues

For detailed troubleshooting assistance including:

- Common error responses and their causes
- Cookie extraction and session management
- Testing methodologies and debugging steps
- Development vs production configuration differences

See the comprehensive troubleshooting guide in the UI package documentation.

### Testing the Live API

Test the session endpoint against the production system:

```bash
# Test with regular session cookie (same-domain)
curl -v -X GET https://beta.iamcoming.io/api/id/session \
  -H "Cookie: session=your-session-cookie-here"

# Test with cross-domain session cookie (external domains)
curl -v -X GET https://beta.iamcoming.io/api/id/session \
  -H "Cookie: cross-domain-session=your-jwt-token-here"
```

**Common Issues:**

- **401 Unauthorized**: Missing or invalid session cookie
- **CORS errors**: Check origin headers and credentials configuration
- **Cookie problems**: Ensure proper cookie domain and path settings
- **Token expiration**: Cross-domain cookies expire after 1 hour for security

## Version Information

**Current Version**: 3.0.0 (July 2025)

This specification defines a clean, logical session API structure featuring:

- **Clean Structure**: Separated organization permissions from system-level admin permissions
- **Four Core Fields**: `user`, `organizations`, `permissions`, `token`
- **TypeScript Types**: Strict type definitions for all data structures
- **RBAC Support**: Role-based access control with portal separation
- **Cross-Domain Auth**: Secure cookie-based authentication across subdomains

---

**Document Version**: 3.0.0  
**Last Updated**: July 2, 2025  
**Target System**: IAC Authentication System  
**Audience**: Backend developers implementing session APIs
