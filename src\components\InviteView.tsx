/* eslint-disable @next/next/no-img-element */
import React from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Clock, MapPin, X, Trash, Globe } from "lucide-react"
import { Event, EventInvite } from "@/types";
import { FormatDate, FormatTime } from "@/lib/dayjs";
import { Format24to12 } from "@/lib/time";
import { generateStorageImageUrl } from "@/lib/utils";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface Router {
  back: () => void;
}

interface InviteViewProps {
  event: Event;
  invite: EventInvite;
  setRsvpDialogOpen: (open: boolean) => void;
  addToCalendar: () => void;
}

export default function InviteView({ event, invite, setRsvpDialogOpen, addToCalendar }: InviteViewProps) {
  // Use the generateStorageImageUrl function to create a URL to the event image
  // If there's no custom image, fall back to default placeholder
  const backgroundImagePath = `event/${event.ID}/invite.png`;
  const defaultBackgroundImage = "/backgrounds/events/" + event.ID + "/placeholder.svg";
  
  // Use state to track if the image exists
  const [imageExists, setImageExists] = React.useState(false);
  const [backgroundImage, setBackgroundImage] = React.useState(defaultBackgroundImage);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  
  // Get the event timezone
  const eventTimezone = event.timezone || "Australia/Melbourne";

  // Check if RSVP deadline has passed
  const isRsvpClosed = React.useMemo(() => {
    if (!event.rsvpDueDate) return false;

    const now = new Date();
    const dueDate = new Date(event.rsvpDueDate);

    return now > dueDate;
  }, [event.rsvpDueDate]);
  // Try to fetch the image on component mount
  React.useEffect(() => {
    // Try to fetch the image URL from storage API
    fetch(`/api/storage/getUrl?eventId=${event.ID}`)
      .then(response => response.json())
      .then(data => {
        if (data.exists && data.url) {
          // Add a cache-busting parameter to ensure fresh image
          const separator = data.url.includes('?') ? '&' : '?';
          const imageUrlWithCache = data.url + separator + 'cache=' + new Date().getTime();
          setImageExists(true);
          setBackgroundImage(imageUrlWithCache);
        }
      })
      .catch(error => {
        console.error("Error fetching event image:", error);
      });
  }, [event.ID]);

  return (
    <div className="relative h-screen bg-cover bg-center" >
      <div className="fixed inset-0 bg-black bg-cover" style={{ backgroundImage: `url(${backgroundImage})` }} />
      <div className="absolute inset-0 flex flex-col justify-between ">
        <div className="bg-white bg-opacity-50 p-4 rounded-lg shadow text-foreground space-y-6 max-w-xl mx-auto my-6 text-center">
          <h3 className="text-lg font-semibold">Hey, {invite.name}</h3>
          <p>{event.host} has invited you to</p>
          <h1 className="text-3xl md:text-4xl font-bold">{event.eventName}</h1>
          <p className="text-lg opacity-90 whitespace-pre-wrap">{event.message}</p>
          <div className="space-y-3 text-slate-50 bg-black bg-opacity-50 p-4 rounded-lg">
            <div className="flex items-center justify-center">
              <Calendar className="h-5 w-5 mr-2" />
              <span>{FormatDate(event.eventDate, eventTimezone)}</span>
            </div>
            <div className="flex items-center justify-center">
              <Clock className="h-5 w-5 mr-2" />
              <span>
                {Format24to12(event.start)} - {event.end ? Format24to12(event.end) : "TBD"}
              </span>
            </div>
            <div className="flex items-center justify-center">
              <MapPin className="h-5 w-5 mr-2" />
              <span>{event.location}</span>
            </div>
            <div className="flex items-center justify-center text-xs mt-1">
              <Globe className="h-4 w-4 mr-1" />
              <span>Timezone: {eventTimezone}</span>
            </div>
          </div>

            {invite.response && (
          <div className="space-y-3 bg-black bg-opacity-30 p-4 rounded-lg">
            {invite.status === 'accepted' ? (
              <div className="text-green-500">
                <p>You have RSVPed {invite.response?.adults || invite.adults} adults and {invite.response?.children || invite.children} children on {FormatDate(new Date(invite.response.timestamp), eventTimezone)} at {FormatTime(invite.response.timestamp, eventTimezone)}.</p>
              </div>
            ) : (
              <div className="text-red-500">
                <p>You have declined the invite on {FormatDate(new Date(invite.response.timestamp), eventTimezone)} at {FormatTime(invite.response.timestamp, eventTimezone)}.</p>
              </div>
            )}
          </div>
            )}

          {event.rsvpDueDate && isRsvpClosed && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-800">
              <p className="font-medium">RSVP Deadline Passed</p>
              <p className="text-sm">The deadline for responding to this invitation was {new Date(event.rsvpDueDate).toLocaleDateString()} at {new Date(event.rsvpDueDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} {event.timezone}</p>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
            <Button
              variant="outline"
              onClick={addToCalendar}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Add to Calendar
            </Button>
            <Button
              variant={"default"}
              onClick={() => setRsvpDialogOpen(true)}
              disabled={isRsvpClosed}
              title={isRsvpClosed ? "RSVP deadline has passed" : ""}
            >
              {invite.response ? "Edit RSVP" : "RSVP Now"}
              {isRsvpClosed && " (Closed)"}
            </Button>
          </div>
        </div>
        <div className="mt-8 p-8 text-center border-t border-slate-200">
          <img src="/iac-logo-standard.svg" alt="I am Coming" className="h-8 mx-auto" />
        </div>
      </div>
    </div>
  )
}
