"use client"

import { use, useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/router"
import { use<PERSON><PERSON>, <PERSON> } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Minus, Users } from "lucide-react"
import { useInvite, useInvites } from "@/hooks/useInvites"
import { EventInvite, EventInviteListItem } from "@/types"
import { Form } from "@/components/ui/form"
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout"
import { debugLog } from "@/lib/logger"
import { Header } from "@/components/Header"
import { useEvent } from "@/hooks/useEvent"
import { Select, SelectContent, SelectI<PERSON>, SelectTrigger, SelectValue } from "@/components/ui/select"

const inviteSchema = z.object({
  ID: z.string().optional(),
  name: z.string().nonempty("Invite Name is required"),
  eventId: z.string(),
  email: z.string().refine((val) => val === "" || z.string().email().safeParse(val).success, {
    message: "Invalid email address",
  }).optional(),  phone: z.string().refine((val) => val === "" || /^\+?[1-9]\d{1,14}$|^\d{3}-\d{3}-\d{4}$/.test(val), {
    message: "Please enter a valid phone number (e.g., +1234567890 or ************)",
  }).optional(),
  group: z.string().optional(),
  status: z.enum(["invited", "accepted", "declined"]),
  adults: z.union([z.string(), z.number()]).transform(val => Number(val)).pipe(z.number().min(0, "Adult count cannot be negative")),
  children: z.union([z.string(), z.number()]).transform(val => Number(val)).pipe(z.number().min(0, "Children count cannot be negative")),
  message: z.array(z.object({
    id: z.string(),
    content: z.string(),
    sender: z.enum(["guest", "host"]),
    timestamp: z.date(),
  })),
})

export default function EditInvitePage() {
  const router = useRouter();
  const [eventId, setEventId] = useState<string>('');
  const [inviteId, setInviteId] = useState<string>('');
  const { invite, loading, error, updateInvite } = useInvite(eventId, inviteId);
  const { event } = useEvent(eventId);
  const { invites } = useInvites(eventId);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [groups, setGroups] = useState<string[]>([]);
  const [showNewGroupInput, setShowNewGroupInput] = useState(false);
  const [newGroup, setNewGroup] = useState('');

  useEffect(() => {
    if (router.isReady) {
      setEventId(router.query.eventId as string);
      setInviteId(router.query.inviteId as string);
    }
  }, [router.isReady, router.query]);

  // Extract unique groups from invites
  useEffect(() => {
    if (invites && invites.length > 0) {
      const uniqueGroups = invites
        .map((invite: EventInviteListItem) => invite.group)
        .filter((group): group is string =>
          group !== undefined &&
          group !== null &&
          group.trim() !== ''
        );

      // Remove duplicates
      const uniqueGroupsSet = [...new Set(uniqueGroups)];

      // Make sure the current invite's group is included in the list
      if (invite?.group && !uniqueGroupsSet.includes(invite.group)) {
        uniqueGroupsSet.push(invite.group);
      }

      setGroups(uniqueGroupsSet as string[]);
      console.log('Available groups:', uniqueGroupsSet);
      console.log('Current invite group:', invite?.group);
    }
  }, [invites, invite?.group]);

  const form = useForm<EventInvite>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      name: invite?.name || '',
      eventId: eventId || '',
      email: invite?.email || '',
      phone: invite?.phone || '',
      group: invite?.group || '',
      adults: invite?.adults || 1,
      children: invite?.children || 0,
      status: invite?.status || 'invited',
      message: invite?.message || []
    }
  });

  useEffect(() => {
    if (invite) {
      form.reset({
        name: invite.name,
        email: invite.email,
        phone: invite.phone,
        group: invite.group || '',
        adults: invite.adults,
        children: invite.children,
        status: invite.status,
        message: invite.message || [],
        eventId: invite.eventId
      });

      // Explicitly set the group value to ensure it's updated
      if (invite.group) {
        setTimeout(() => {
          form.setValue('group', invite.group || '');
        }, 100);
      }
    } else if (eventId) {
      // Set eventId for new invites
      form.setValue('eventId', eventId);
    }
  }, [invite, form, eventId]);

  const handleAddNewGroup = () => {
    if (newGroup.trim()) {
      setGroups([...groups, newGroup.trim()]);
      form.setValue('group', newGroup.trim());
      setNewGroup('');
      setShowNewGroupInput(false);
    }
  };

  const onSubmit = async (data: EventInvite) => {
    if (!eventId) return;
    setIsSubmitting(true);
    try {
      const submitData = {
        ...data,
        eventId // Ensure eventId is included
      };

      if (inviteId === 'new') {
        // Create new invite
        const response = await fetch(`/api/event/${eventId}/invites`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(submitData),
        });

        if (!response.ok) {
          throw new Error('Failed to create invite');
        }

        const newInvite = await response.json();
        // Redirect to success page instead of directly to invite details
        router.push(`/event/${eventId}/invites/${newInvite.ID}/success`);
      } else {
        // Update existing invite
        await updateInvite(inviteId, submitData);
        // For updates, redirect back to the invite details page
        router.push(`/event/${eventId}/invites/${inviteId}`);
      }
    } catch (error) {
      console.error('Error saving invite:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">Error loading invite: {error.message}</div>
      </div>
    );
  }

  if (!invite) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-muted-foreground">Invite not found</div>
      </div>
    );
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col  bg-gray-50">
        {/* Use the shared Header component with breadcrumbs */}
        <Header 
          title={inviteId === 'new' ? 'New Invite' : 'Edit Invite'}
          breadcrumbs={[
            { label: 'Events', href: '/events' },
            { label: event?.eventName || 'Event Details', href: `/event/${eventId}` },
            { label: 'Invites', href: `/event/${eventId}/invites` },
            ...(inviteId !== 'new' ? [{ label: invite?.name || 'Invite Details', href: `/event/${eventId}/invites/${inviteId}` }] : [])
          ]}
        />

        {/* Content */}
        <div className="flex-1 p-4 pb-20">
          <div className="container mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Invite Details</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">                  <div className="space-y-2">
                    <Label htmlFor="title">Invite Title</Label>
                    <Controller
                      name="name"
                      control={form.control}
                      render={({ field }) => (
                        <div className="space-y-1">
                          <Input
                            id="title"
                            {...field}
                            placeholder="Eg: John Doe, Sharma Family"
                            required
                          />
                          {form.formState.errors.name && (
                            <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
                          )}
                        </div>
                      )}
                    />
                    <p className="text-xs text-muted-foreground">This will appear on the invite your guests receive.</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="group">Select Group Label for invite (Optional)</Label>
                    {!showNewGroupInput ? (
                      <Controller
                        name="group"
                        control={form.control}
                        render={({ field }) => (
                          <Select
                            value={field.value || ''}
                            defaultValue={invite?.group || ''}
                            onValueChange={(value) => {
                              if (value === "create-new") {
                                setShowNewGroupInput(true);
                              } else {
                                field.onChange(value);
                              }
                            }}
                          >
                            <SelectTrigger id="group" className="w-full">
                              <SelectValue placeholder="Select group">
                                {field.value || invite?.group || "Select group"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {groups.length > 0 ? (
                                <>
                                  {groups.map((group) => (
                                    <SelectItem key={group} value={group}>
                                      {group}
                                    </SelectItem>
                                  ))}
                                  <SelectItem value="create-new">
                                    <span className="flex items-center">
                                      <Plus className="mr-2 h-4 w-4" />
                                      Create new group
                                    </span>
                                  </SelectItem>
                                </>
                              ) : (
                                <SelectItem value="create-new">
                                  <span className="flex items-center">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Create new group
                                  </span>
                                </SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Input
                          value={newGroup}
                          onChange={(e) => setNewGroup(e.target.value)}
                          placeholder="Enter new group name"
                          className="flex-1"
                        />
                        <Button
                          type="button"
                          onClick={handleAddNewGroup}
                          disabled={!newGroup.trim()}
                        >
                          Add
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setShowNewGroupInput(false);
                            setNewGroup('');
                          }}
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">Add a label to organize your invites.</p>
                  </div>

                  <div className="space-y-2">
                    <Label>Number of Adults</Label>
                    <div className="flex items-center space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        className="rounded-full"
                        onClick={() => form.setValue("adults", Math.max(0, form.getValues("adults") - 1))}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <Controller
                        name="adults"
                        control={form.control}
                        render={({ field }) => (
                          <Input
                            type="number"
                            {...field}
                            className="w-20 text-center"
                            min="0"
                          />
                        )}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        className="rounded-full"
                        onClick={() => form.setValue("adults", form.getValues("adults") + 1)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Number of Children</Label>
                    <div className="flex items-center space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        className="rounded-full"
                        onClick={() => form.setValue("children", Math.max(0, form.getValues("children") - 1))}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <Controller
                        name="children"
                        control={form.control}
                        render={({ field }) => (
                          <Input
                            type="number"
                            {...field}
                            className="w-20 text-center"
                            min="0"
                          />
                        )}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        className="rounded-full"
                        onClick={() => form.setValue("children", form.getValues("children") + 1)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="mt-11 mb-9">
                    <CardTitle>Email&apos;s needed to RSVP. If we have guest&apos;s phone too, we&apos;ll send reminders to both.</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">No reminders will be sent for declined events.</p>
                  </div>                  <div className="space-y-2">
                    <Label htmlFor="email">Email (Optional)</Label>
                    <p className="text-xs text-muted-foreground">As a host, adding an email address is optional, but for a guest, it is required to send RSVP responses.</p>
                    <Controller
                      name="email"
                      control={form.control}
                      render={({ field }) => (
                        <div className="space-y-1">
                          <Input
                            id="email"
                            type="email"
                            {...field}
                            placeholder="Enter email address"
                          />
                          {form.formState.errors.email && (
                            <p className="text-sm text-red-500">{form.formState.errors.email.message}</p>
                          )}
                        </div>
                      )}
                    />
                  </div><div className="space-y-2">
                    <Label htmlFor="phone">Phone (Optional)</Label>
                    <Controller
                      name="phone"
                      control={form.control}
                      render={({ field }) => (
                        <div className="space-y-1">
                          <Input
                            id="phone"
                            type="tel"
                            {...field}
                            placeholder="Enter phone number"
                          />
                          {form.formState.errors.phone && (
                            <p className="text-sm text-red-500">{form.formState.errors.phone.message}</p>
                          )}
                        </div>
                      )}
                    />
                  </div>                  <Button
                    variant="primary-button"
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {inviteId === 'new' ? 'Create & Continue' : 'Save Changes'}
                  </Button>
                  <p className="text-xs text-center text-muted-foreground mt-2">
                    {inviteId === 'new'
                      ? 'This will create the invite. You\'ll be able to send it next.'
                      : 'This will update the invite details and return to the invite page.'}
                  </p>
                </form>
              </Form>
            </CardContent>
          </Card>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}