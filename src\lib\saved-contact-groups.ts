import { SavedContactGroup, ContactGroupData, EventInvite } from "@/types";
import { Database } from "./database";
import { GenerateID } from "./ID";

/**
 * Save contact groups from an event to the saved_contact_groups collection
 */
export async function saveContactGroups(
  organizationId: string,
  eventId: string,
  groups: ContactGroupData[]
): Promise<string[]> {
  const db = Database.getInstance();
  const savedGroupIds: string[] = [];
  const now = new Date().toISOString();

  for (const group of groups) {
    const groupId = GenerateID('SCG');
    const savedGroup: SavedContactGroup = {
      id: groupId,
      organizationId,
      name: group.name,
      contacts: group.contacts,
      createdAt: now,
      updatedAt: now,
      createdFromEventId: eventId
    };

    await db.addData('saved_contact_groups', savedGroup);
    savedGroupIds.push(groupId);
  }

  return savedGroupIds;
}

/**
 * Get all saved contact groups for an organization
 */
export async function getSavedContactGroups(
  organizationId: string
): Promise<SavedContactGroup[]> {
  const db = Database.getInstance();

  // Query without orderBy to avoid index requirement
  const snapshot = await db.query('saved_contact_groups')
    .where('organizationId', '==', organizationId)
    .get();

  // Sort in memory by createdAt descending
  const groups = snapshot.docs.map(doc => doc.data() as SavedContactGroup);
  return groups.sort((a, b) => {
    const aDate = new Date(a.createdAt).getTime();
    const bDate = new Date(b.createdAt).getTime();
    return bDate - aDate; // Descending order (newest first)
  });
}

/**
 * Update a saved contact group
 */
export async function updateSavedContactGroup(
  groupId: string,
  updates: Partial<SavedContactGroup>
): Promise<void> {
  const db = Database.getInstance();
  
  const updateData = {
    ...updates,
    updatedAt: new Date().toISOString()
  };

  await db.updateData('saved_contact_groups', groupId, updateData);
}

/**
 * Hard delete a saved contact group
 */
export async function deleteSavedContactGroup(groupId: string): Promise<void> {
  const db = Database.getInstance();
  
  // Perform a hard delete instead of soft delete
  await db.deleteData('saved_contact_groups', groupId);
}



/**
 * Extract contact groups from event invites
 */
export async function extractContactGroupsFromEvent(eventId: string): Promise<ContactGroupData[]> {
  const db = Database.getInstance();
  
  // Get all invites for the event
  const snapshot = await db.query('invites')
    .where('eventId', '==', eventId)
    .get();

  const invites = snapshot.docs.map(doc => doc.data() as EventInvite);
  
  // Group invites by their group field
  const groupMap = new Map<string, EventInvite[]>();
  
  invites.forEach(invite => {
    const groupName = invite.group || 'Ungrouped';
    if (!groupMap.has(groupName)) {
      groupMap.set(groupName, []);
    }
    groupMap.get(groupName)!.push(invite);
  });

  // Convert to ContactGroupData format
  const contactGroups: ContactGroupData[] = [];
  
  groupMap.forEach((groupInvites, groupName) => {
    // Skip ungrouped contacts
    if (groupName === 'Ungrouped') return;
    
    const contacts = groupInvites
      .filter(invite => invite.email || invite.phone) // Only include contacts with email or phone
      .map(invite => ({
        email: invite.email || '',
        name: invite.name,
        phone: invite.phone || ''
      }))
      .filter(contact => contact.email || contact.phone); // Remove empty contacts

    if (contacts.length > 0) {
      contactGroups.push({
        name: groupName,
        contacts
      });
    }
  });

  return contactGroups;
}

/**
 * Check if an organization has contact groups that can be saved
 */
export async function hasContactGroupsToSave(eventId: string): Promise<boolean> {
  const groups = await extractContactGroupsFromEvent(eventId);
  return groups.length > 0;
}

/**
 * Get a saved contact group by ID
 */
export async function getSavedContactGroupById(groupId: string): Promise<SavedContactGroup | null> {
  const db = Database.getInstance();
  const group = await db.readData('saved_contact_groups', groupId);
  return group as SavedContactGroup | null;
}
