import { useState, useEffect } from 'react';
import { EventManager } from '@/types';
import { z } from 'zod';

const emailSchema = z.string().email("Please enter a valid email address");

interface UseEventManagersReturn {
  managers: EventManager[];
  loading: boolean;
  error: string | null;
  lookupUserByEmail: (email: string) => Promise<EventManager | null>;
  mapUserIdsToManagers: (userIds: string[]) => Promise<EventManager[]>;
}

export function useEventManagers(userIdsOrEmails: string[]): UseEventManagersReturn {
  const [managers, setManagers] = useState<EventManager[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to check if a string is an email
  const isEmail = (value: string): boolean => {
    try {
      emailSchema.parse(value);
      return true;
    } catch {
      return false;
    }
  };

  // Function to look up a user by email and convert to EventManager
  const lookupUserByEmail = async (email: string): Promise<EventManager | null> => {
    try {
      const response = await fetch('/api/users/lookup-by-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });

      if (!response.ok) {
        throw new Error('Failed to look up user');
      }

      const data = await response.json();

      if (!data.user) {
        return {
          email,
          role: 'invited',
        };
      }

      return {
        userId: data.user.id,
        name: data.user.name,
        email: data.user.email,
        image: data.user.image,
        role: 'manager'
      };
    } catch (error) {
      console.error("Error looking up user by email:", error);
      return null;
    }
  };

  // Function to look up multiple users by IDs
  const mapUserIdsToManagers = async (userIds: string[]): Promise<EventManager[]> => {
    if (!userIds.length) return [];

    try {
      const response = await fetch('/api/users/lookup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userIds })
      });

      if (!response.ok) {
        throw new Error('Failed to look up users');
      }

      const data = await response.json();

      return data.users.map((user: any) => ({
        userId: user.id,
        name: user.name,
        email: user.email,
        image: user.image,
        role: 'manager'
      }));
    } catch (error) {
      console.error("Error looking up users:", error);
      return [];
    }
  };

  // Effect to process the input array and fetch user details
  useEffect(() => {
    async function processManagers() {
      setLoading(true);
      setError(null);

      try {
        // Split into emails and user IDs
        const emails = userIdsOrEmails.filter(id => isEmail(id));
        const userIds = userIdsOrEmails.filter(id => !isEmail(id));

        // Process both in parallel
        const [emailUsers, idUsers] = await Promise.all([
          // Map emails to invited users or look them up
          Promise.all(emails.map(lookupUserByEmail)),
          mapUserIdsToManagers(userIds)
        ]);

        // Combine and filter out nulls
        const allManagers = [...emailUsers, ...idUsers].filter((manager): manager is EventManager => manager !== null);
        setManagers(allManagers);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch manager information');
      } finally {
        setLoading(false);
      }
    }

    processManagers();
  }, [userIdsOrEmails]);

  return {
    managers,
    loading,
    error,
    lookupUserByEmail,
    mapUserIdsToManagers
  };
}
