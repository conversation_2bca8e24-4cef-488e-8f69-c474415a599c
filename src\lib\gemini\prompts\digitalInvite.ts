import { EventFormData } from '../../../types'; // Assuming this path is correct for your project

/**
 * Detect event type based on event name and description
 */
function detectEventType(eventName: string, description?: string): string {
  const text = `${eventName} ${description || ''}`.toLowerCase();

  if (text.includes('birthday') || text.includes('bday')) return 'birthday';
  if (text.includes('wedding') || text.includes('marriage')) return 'wedding';
  if (text.includes('baby shower') || text.includes('babyshower')) return 'baby shower';
  if (text.includes('graduation') || text.includes('grad')) return 'graduation';
  if (text.includes('anniversary')) return 'anniversary';
  if (text.includes('housewarming') || text.includes('house warming')) return 'housewarming';
  if (text.includes('farewell') || text.includes('goodbye')) return 'farewell';
  if (text.includes('dinner') || text.includes('lunch')) return 'dinner party';
  if (text.includes('conference') || text.includes('meeting') || text.includes('business')) return 'business';
  if (text.includes('party') || text.includes('celebration')) return 'party';
  if (text.includes('holiday') || text.includes('christmas') || text.includes('thanksgiving')) return 'holiday';
  if (text.includes('workshop') || text.includes('training') || text.includes('seminar')) return 'workshop';

  return 'general celebration';
}

/**
 * Get theme-specific design guidelines based on event type
 */
function getThemeSpecificGuidelines(eventType: string): string {
  switch (eventType) {
    case 'birthday':
      return `Vibrant, joyful colors (bright blues, pinks, yellows, or rainbow themes). Fun, festive atmosphere with bokeh lights or sparkles. Soft, dreamy backgrounds with celebration motifs.`;

    case 'wedding':
      return `Elegant, romantic color palette (soft pastels, whites, golds, or blush tones). Sophisticated and timeless aesthetic. Dreamy, ethereal backgrounds with floral or nature elements.`;

    case 'baby shower':
      return `Soft, gentle color palette (pastels, baby blues, pinks, or neutral tones). Warm, nurturing atmosphere. Sweet, innocent aesthetic with soft textures.`;

    case 'graduation':
      return `Academic, achievement-focused colors (navy, gold, burgundy, or school colors). Sophisticated, accomplished mood. Inspiring, success-oriented backgrounds.`;

    case 'business':
      return `Professional color palette (navy, grey, black, white, or corporate colors). Clean, modern aesthetic with geometric patterns or cityscapes. Sophisticated, authoritative mood. Professional backgrounds with architectural or tech elements.`;

    case 'dinner party':
      return `Warm, inviting colors (deep reds, golds, oranges, or earth tones). Cozy, intimate atmosphere. Sophisticated dining backgrounds with warm lighting.`;

    case 'holiday':
      return `Seasonal color palette appropriate to the holiday. Festive, celebratory mood. Traditional or modern holiday aesthetics.`;

    default:
      return `Colors matching the overall mood. General celebratory or event-appropriate elements. Atmosphere reflecting the event's purpose and tone. Professional yet engaging background suitable for any celebration.`;
  }
}

/**
 * Get event-specific decorations based on event type
 */
function getEventDecorations(eventType: string): string {
  switch (eventType) {
    case 'birthday':
      return 'colorful balloons, streamers, confetti, party hats, and ribbons';
    case 'wedding':
      return 'elegant floral arrangements, sparkling lights, white roses, and romantic candles';
    case 'baby shower':
      return 'cute baby toys, soft clouds, pastel balloons, and gentle ribbons';
    case 'graduation':
      return 'graduation caps, diplomas, celebratory banners, and academic books';
    case 'anniversary':
      return 'romantic flowers, elegant candles, golden accents, and love hearts';
    case 'housewarming':
      return 'house keys, potted plants, welcome signs, and cozy home elements';
    case 'farewell':
      return 'farewell banners, memory photos, warm lighting, and nostalgic elements';
    case 'dinner party':
      return 'elegant table settings, wine glasses, candles, and gourmet food elements';
    case 'business':
      return 'professional elements, modern geometric patterns, corporate colors, and sleek designs';
    case 'party':
      return 'festive decorations, colorful lights, celebration banners, and party accessories';
    case 'holiday':
      return 'seasonal decorations, holiday lights, festive ornaments, and traditional elements';
    case 'workshop':
      return 'learning materials, notebooks, presentation boards, and educational tools';
    default:
      return 'general celebratory decorations, elegant accents, and festive elements';
  }
}

/**
 * Get main focal object for the event type
 */
function getMainFocalObject(eventType: string): string {
  switch (eventType) {
    case 'birthday':
      return 'a beautiful birthday cake adorned with glowing candles';
    case 'wedding':
      return 'an elegant two-tiered wedding cake with delicate flowers';
    case 'baby shower':
      return 'a cute teddy bear surrounded by baby items and soft toys';
    case 'graduation':
      return 'a stack of books with a rolled diploma and graduation cap on top';
    case 'anniversary':
      return 'an elegant bouquet of roses with anniversary rings or champagne glasses';
    case 'housewarming':
      return 'stylish house keys with a small potted plant and welcome mat';
    case 'farewell':
      return 'a memory book or photo frame surrounded by farewell cards';
    case 'dinner party':
      return 'an elegantly set dining table with fine wine and gourmet dishes';
    case 'business':
      return 'professional presentation materials or modern office elements';
    case 'party':
      return 'a festive centerpiece with party favors and celebration items';
    case 'holiday':
      return 'traditional holiday centerpiece appropriate to the season';
    case 'workshop':
      return 'educational materials, notebooks, and learning tools arranged professionally';
    default:
      return 'an elegant centerpiece that represents celebration and joy';
  }
}

/**
 * Generate prompt for thematic background image
 * Creates beautiful background images based on event theme that work with text overlays
 */
export function generateDigitalInviteImagePrompt(eventData: EventFormData): string {
  const eventType = detectEventType(eventData.eventName, eventData.message);
  const decorations = getEventDecorations(eventType);
  const mainObject = getMainFocalObject(eventType);
  const guidelines = getThemeSpecificGuidelines(eventType);

  return `Generate a professional, high-quality, **animated** background image for a digital invitation.
  
---
  
## Event Details
* **Event Type:** ${eventType}
* **Context:** "${eventData.message || eventData.eventName}"
  
---
  
## Image Composition & Style
* **Theme:** Create a visually stunning ${eventType} themed scene.
* **Key Elements:** Feature ${decorations}.
* **Focal Point:** Include ${mainObject} as a prominent element, but ensure it's not dead center to allow for text overlay.
* **Style:** Artistic illustration or premium photography style, with a **dynamic and animated** feel.
* **Color Scheme:** Utilize a color palette appropriate for a ${eventType} event, as follows: ${guidelines}.
* **Atmosphere:** Evoke a mood that perfectly reflects a ${eventType} celebration.
  
---
  
## Design Requirements
* **NO HUMANS:** The image must not contain any human figures or faces.
* **NO TEXT OR WORDS:** Absolutely no text, numbers, or words embedded within the image itself.
* **Text Overlay Space:** Ensure ample, clear space in the center-top or central areas for an event title and details to be overlaid later.
* **Contrast:** Provide sufficient contrast areas for both light and dark text overlay.
* **Aspect Ratio:** Optimized for digital display, aiming for 16:9 or 4:3.
* **Professional Quality:** High-resolution and visually appealing.
  
---
  
The goal is to create an elegant and engaging background that immediately conveys the ${eventType} theme, designed to serve as a perfect canvas for text-based event information.`;
}