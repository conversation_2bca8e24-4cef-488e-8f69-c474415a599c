// lib/api/authenticated.ts
import type { NextApiRequest, NextApiResponse, NextApiHandler } from 'next';
import NextAuth from 'next-auth';
import {authConfig} from '../../auth'; // adjust this path to your auth config

export function withAuth(handler: NextApiHandler): NextApiHandler {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const { auth } = NextAuth(authConfig);
    const session = await auth(req, res);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Attach session to request for use in handler
    (req as any).session = session;
    return handler(req, res);
  };
}
