import { getRemoteConfig, fetchAndActivate, getValue, getBoolean } from 'firebase/remote-config';
import { getFirebaseApp } from './firebaseClient';
import { debugLog, log } from './logger';

// Default values for feature flags (used if remote config fails)
const DEFAULT_CONFIG = {
  beta_mode: true
};

// Type for feature flags
export interface FeatureFlags {
  beta_mode: boolean;
}

class RemoteConfig {
  private static instance: RemoteConfig;
  private remoteConfig: any = null;
  private configValues: FeatureFlags = DEFAULT_CONFIG;
  private initialized = false;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): RemoteConfig {
    if (!RemoteConfig.instance) {
      RemoteConfig.instance = new RemoteConfig();
    }
    return RemoteConfig.instance;
  }

  public async initialize(): Promise<void> {
    if (this.initialized) return;
    
    if (typeof window === 'undefined') {
      // Server-side - use default values
      this.configValues = DEFAULT_CONFIG;
      this.initialized = true;
      return;
    }

    try {
      // Get the Firebase app
      const app = getFirebaseApp();
      if (!app) {
        throw new Error('Firebase must be initialized before using Remote Config');
      }

      // Initialize Remote Config
      this.remoteConfig = getRemoteConfig(app);

      log('Initializing Remote Config', { app, remoteConfig: this.remoteConfig });
      
      // Set minimum fetch interval for development/production
      const fetchTimeoutMillis = process.env.NODE_ENV === 'development' ? 0 : 3600000; // 1 hour in production
      this.remoteConfig.settings.minimumFetchIntervalMillis = fetchTimeoutMillis;
      
      // Set default values
      this.remoteConfig.defaultConfig = DEFAULT_CONFIG;

      // Fetch and activate
      await fetchAndActivate(this.remoteConfig);
      
      // Map remote config values to our config object
      this.configValues = {
        beta_mode: getBoolean(this.remoteConfig, 'beta_mode')
      };

      debugLog('Remote Config initialized', this.configValues);
      this.initialized = true;
    } catch (error) {
      debugLog('Remote Config initialization failed, using defaults', { error });
      this.configValues = DEFAULT_CONFIG;
      this.initialized = true;
    }
  }

  public getFeatureFlags(): FeatureFlags {
    if (!this.initialized) {
      debugLog('Warning: Remote Config not initialized, using default values');
    }
    return this.configValues;
  }

  public isBetaMode(): boolean {
    return this.configValues.beta_mode;
  }
}

export default RemoteConfig;