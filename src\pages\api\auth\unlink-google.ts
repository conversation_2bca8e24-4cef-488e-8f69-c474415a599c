// filepath: /home/<USER>/kitchen-sink/iamcoming/universe/src/pages/api/auth/unlink-google.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { getToken } from 'next-auth/jwt';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get the current user's token
    const token = await getToken({ req, secret: process.env.AUTH_SECRET });
    if (!token) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the user ID from the request body
    const { userId } = req.body;

    // Verify that the authenticated user matches the requested userId
    if (token.sub !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Get database instance
    const db = Database.getInstance();

    // Unlink the Google account
    await db.unlinkGoogleAccount(userId);

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error unlinking Google account:', error);
    return res.status(500).json({ error: 'Failed to unlink Google account' });
  }
}