# Helper Types - IAC Universe v1

This document catalogues helper types that facilitate application interaction but are not stored in the database. These include API response structures, form schemas, validation results, and pagination metadata.

## API Response Types

### APIResponse
**File**: `src/types/index.ts`
**Usage**: Standard API response wrapper

```typescript
interface APIResponse {
  /**
   * HTTP status code
   */
  code: number;
  /**
   * HTTP headers
   */
  headers: Record<string, string>;
  /**
   * Response data
   */
  data: any;
}
```

### SessionAPIResponse
**File**: `src/types/session-api.ts`
**Usage**: Complete session API response structure

```typescript
interface SessionAPIResponse {
  /** Current logged-in user information */
  user: SessionUser;
  /** List of partner organizations user has access to */
  organizations: SessionOrganization[];
  /** System-level admin permissions (empty object if user has no admin access) */
  permissions: SystemPermissions;
  /** API access token for subsequent requests */
  token: string;
}
```

### FeedbackListResponse
**File**: `src/types/feedback.ts`
**Usage**: Paginated feedback list API response

```typescript
interface FeedbackListResponse {
  success: boolean;
  feedback: Feedback[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}
```

### FeedbackDetailResponse
**File**: `src/types/feedback.ts`
**Usage**: Individual feedback detail API response

```typescript
interface FeedbackDetailResponse {
  success: boolean;
  feedback: Feedback & {
    comments: FeedbackComment[];
  };
}
```

### SL_ShortLinkResponse
**File**: `src/types/shortlinks-api.ts`
**Usage**: Shortlink creation response

```typescript
interface SL_ShortLinkResponse {
  shortCode: string;
  redirectUrl: string;
}
```

### SL_AnalyticsResponse
**File**: `src/types/shortlinks-api.ts`
**Usage**: Shortlink analytics response

```typescript
interface SL_AnalyticsResponse {
  shortCode: string;
  analytics: SL_AnalyticsStats;
}
```

### SL_GroupedAnalytics
**File**: `src/types/shortlinks-api.ts`
**Usage**: Grouped shortlink analytics

```typescript
interface SL_GroupedAnalytics {
  shortCode: string;
  summary: SL_AnalyticsStats;
  grouped: {
    daily: Record<string, number>;
    weekly: Record<string, number>;
  };
}
```

## Form & Validation Types

### FeedbackFormData
**File**: `src/types/feedback.ts`
**Usage**: Feedback form submission

```typescript
interface FeedbackFormData {
  related: string;
  experience: string;
  feedbackType: string;
  details: string;
  email: string;
}
```

### AuthVerificationRequest
**File**: `src/types/index.ts`
**Usage**: Email verification flow

```typescript
interface AuthVerificationRequest {
  identifier: string;
  url: string;
  expires: Date;
  provider: EmailProvider;
  token: string;
}
```

### SL_ShortLinkRequest
**File**: `src/types/shortlinks-api.ts`
**Usage**: Shortlink creation request

```typescript
interface SL_ShortLinkRequest {
  shortCode: string;
  redirectUrl: string;
}
```

### PostEventContactGroupEmailData
**File**: `src/types/index.ts`
**Usage**: Post-event email template data

```typescript
interface PostEventContactGroupEmailData {
  organizationName: string;
  eventName: string;
  eventDate: string;
  contactGroups: Array<{
    name: string;
    contactCount: number;
  }>;
  saveGroupsUrl: string;
}
```

### PostEventThankYouEmailData
**File**: `src/types/index.ts`
**Usage**: Thank you email template data

```typescript
interface PostEventThankYouEmailData {
  guestName: string;
  eventName: string;
  eventDate: string;
  hostName: string;
}
```

## Permission & RBAC Helper Types

### PermissionCheckResult
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Permission check outcome

```typescript
interface PermissionCheckResult {
  granted: boolean;
  role: string;
  resource: string;
  permission: string;
  context?: PermissionContext;
  reason?: string;
}
```

### RoleAssignmentRequest
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Role assignment request

```typescript
interface RoleAssignmentRequest {
  userId: string;
  role: string;
  portal: 'partner' | 'admin';
  context?: PermissionContext;
  expiresAt?: Date;
  assignedBy: string;
}
```

### AssignOrganizationRoleRequest
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization role assignment

```typescript
interface AssignOrganizationRoleRequest {
  userId: string;
  organizationId: string;
  role: Role;
  venueRestrictions?: string[];
  expiresAt?: Date;
  metadata?: Record<string, any>;
}
```

### SwitchOrganizationRequest
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization context switching

```typescript
interface SwitchOrganizationRequest {
  organizationId: string;
}
```

### PermissionMiddlewareContext
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Middleware permission validation

```typescript
interface PermissionMiddlewareContext {
  user: {
    id: string;
    roles: UserRole[];
  };
  resource: string;
  permission: string;
  context?: PermissionContext;
}
```

## Session & User Management Types

### SessionUser
**File**: `src/types/session-api.ts`
**Usage**: User object in session response

```typescript
interface SessionUser {
  /** Unique user identifier */
  id: string;
  /** User's full display name */
  name: string;
  /** User's email address */
  email: string;
  /** Optional avatar URL */
  avatar?: string;
  /** Optional additional profile data */
  profile?: UserProfile;
}
```

### SessionOrganization
**File**: `src/types/session-api.ts`
**Usage**: Organization object in session response

```typescript
interface SessionOrganization {
  /** Organization identifier */
  id: string;
  /** Organization display name */
  name: string;
  /** Type of organization (only 'partner' for real organizations) */
  type: OrganizationType;
  /** RBAC configuration specific to this organization */
  rbacConfig: OrganizationRBACConfig;
}
```

### SystemPermissions
**File**: `src/types/session-api.ts`
**Usage**: System-level admin permissions

```typescript
interface SystemPermissions {
  /** 
   * Admin role-permission mappings for system-level access
   * Only includes admin roles that the user actually has
   */
  rolePermissions: AdminRolePermissions;
}
```

### OrganizationAwareSessionResponse
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Enhanced session with organization context

```typescript
interface OrganizationAwareSessionResponse {
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    profile?: Record<string, unknown>;
  };
  organizations: OrganizationWithRBAC[];
  currentOrganization?: OrganizationWithRBAC;
  roles?: Role[]; // @deprecated
  accountId?: string; // @deprecated
  token: string;
  rbac: any;
}
```

## Error Handling Types

### Error
**File**: `src/types/index.ts`
**Usage**: Basic error response

```typescript
interface Error {
  message: string;
  code: number;
}
```

### ValidationError (Implied)
**Usage**: Form and API validation errors

```typescript
interface ValidationError {
  field: string;
  message: string;
  code?: string;
}
```

## Resource & Action Mapping Types

### ResourceActionMap
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: API endpoint to resource/permission mapping

```typescript
interface ResourceActionMap {
  [endpoint: string]: {
    resource: string;
    permission: string;
    context?: (req: any) => PermissionContext;
  };
}
```

### RoleHierarchy
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Role inheritance definition

```typescript
interface RoleHierarchy {
  role: string;
  inheritsFrom?: string[];
  portal: 'partner' | 'admin';
}
```

### PermissionInheritance
**File**: `src/lib/auth/RBAC/types.ts`
**Usage**: Permission inheritance rules

```typescript
interface PermissionInheritance {
  parentResource: string;
  childResource: string;
  inheritedPermissions: string[];
}
```

## Configuration & Role Mapping Types

### AdminRolePermissions
**File**: `src/types/session-api.ts`
**Usage**: Admin role-permission configuration

```typescript
type AdminRolePermissions = {
  [K in AdminRole]?: {
    [R in AdminResource]?: Permission[];
  };
};
```

### PartnerRolePermissions
**File**: `src/types/session-api.ts`
**Usage**: Partner role-permission configuration

```typescript
type PartnerRolePermissions = {
  [K in PartnerRole]?: {
    [R in PartnerResource]?: Permission[];
  };
};
```

### OrganizationRBACConfig
**File**: `src/types/session-api.ts`
**Usage**: Organization RBAC configuration

```typescript
interface OrganizationRBACConfig {
  /** 
   * Role-permission mappings for this organization
   * Only includes roles that the user actually has
   */
  rolePermissions: PartnerRolePermissions;
}
```

### OrganizationWithRBAC
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization details with RBAC configuration

```typescript
interface OrganizationWithRBAC {
  id: string;
  name: string;
  type: 'partner' | 'admin';
  rbacConfig: any;
  canAccess: boolean;
  isActive: boolean;
}
```

## Utility Helper Interfaces

### PermissionChecker
**File**: `src/types/session-api.ts`
**Usage**: Helper interface for permission checking

```typescript
interface PermissionChecker {
  /** Check if user has a specific permission on a resource in an organization */
  hasOrganizationPermission(
    organization: SessionOrganization,
    resource: PartnerResource,
    permission: Permission
  ): boolean;

  /** Check if user has a specific admin permission on a system resource */
  hasAdminPermission(
    permissions: SystemPermissions,
    resource: AdminResource,
    permission: Permission
  ): boolean;

  /** Get all roles user has in an organization */
  getOrganizationRoles(organization: SessionOrganization): PartnerRole[];

  /** Get all admin roles user has */
  getAdminRoles(permissions: SystemPermissions): AdminRole[];
}
```

### OrganizationRBACFunctions
**File**: `src/lib/auth/RBAC/organization-types.ts`
**Usage**: Organization permission checking functions

```typescript
interface OrganizationRBACFunctions {
  hasOrganizationPermission(
    userRoles: OrganizationScopedRole[],
    resource: string,
    permission: Permission,
    context: OrganizationPermissionContext
  ): boolean;

  getUserRolesInOrganization(
    userRoles: OrganizationScopedRole[],
    organizationId: string
  ): OrganizationScopedRole[];

  canManageUserInOrganization(
    adminRole: OrganizationScopedRole,
    targetUserId: string,
    organizationId: string
  ): boolean;

  getPermissionsInOrganization(
    userRoles: OrganizationScopedRole[],
    organizationId: string
  ): Record<string, Permission[]>;
}
```

## AI & Service Helper Types

### AILimitCheckResult (from lib/ai-usage.ts)
**Usage**: AI usage limit checking

```typescript
interface AILimitCheckResult {
  allowed: boolean;
  currentUsage: number;
  limit: number;
  isUnlimited: boolean;
  reason?: string;
}
```

### TempEvent (from lib/ai-usage.ts)
**Usage**: Temporary event for AI tracking during creation

```typescript
interface TempEvent {
  ownerAccountId: string;
  organizationId: string;
  aiUsageCount?: number;
  overrides?: {
    AI_GEN_LIMIT?: number;
    [key: string]: any;
  };
}
```

### EventArchiveData (from lib/eventArchive.ts)
**Usage**: Event archive metadata

```typescript
interface EventArchiveData {
  eventId: string;
  eventName: string;
  archiveDate: string;
  metadata: {
    totalInvites: number;
    acceptedInvites: number;
    declinedInvites: number;
    totalGuests: number;
  };
}
```

### EventArchiveContent (from lib/eventArchive.ts)
**Usage**: Complete archive content structure

```typescript
interface EventArchiveContent {
  event: Event;
  invites: EventInvite[];
  statistics: RSVPReport;
  metadata: EventArchiveData;
}
```

## Type Utility Functions

Many helper types include associated utility functions:

```typescript
// Type guards for session API
function hasAdminPermissions(permissions: SystemPermissions): boolean
function hasOrganizationAccess(organizations: SessionOrganization[]): boolean

// Utility types for extracting roles
type UserOrganizationRoles<T extends SessionOrganization> = keyof T['rbacConfig']['rolePermissions'];
type UserAdminRoles<T extends SystemPermissions> = keyof T['rolePermissions'];

// Feedback status formatting
function formatFeedbackStatus(status: FeedbackStatus): string
```

## Request/Response Patterns

### Standard API Response Pattern
```typescript
interface StandardAPIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: string;
    details?: any;
  };
  metadata?: {
    pagination?: PaginationMeta;
    permissions?: Permission[];
    timestamp: string;
  };
}
```

### Pagination Pattern
```typescript
interface PaginationMeta {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
  page?: number;
  totalPages?: number;
}
```

## Migration Considerations

When migrating helper types to the new universe/packages/types system:

1. **API Consistency**: Maintain standard response patterns across services
2. **Permission Helpers**: Preserve complex permission checking interfaces
3. **Form Validation**: Enhance form types with better validation patterns
4. **Error Handling**: Standardize error response structures
5. **Session Management**: Improve session types with better type safety
6. **Request/Response**: Create generic patterns for API interactions
7. **Utility Functions**: Migrate utility interfaces as shared functionality
8. **Type Guards**: Preserve runtime type checking capabilities

## Usage Patterns

### API Layer
Helper types are primarily used in:
- API route handlers for request/response typing
- Client-side API calls for type safety
- Middleware for authentication and authorization
- Form handling and validation

### Business Logic
Helper types facilitate:
- Permission checking and role validation
- Data transformation between layers
- Configuration object validation
- Service integration contracts

### UI Layer
Helper types support:
- Form state management
- Component prop validation
- Event handling
- State management integration