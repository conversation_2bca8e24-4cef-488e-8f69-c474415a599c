"use client"

import { useState } from "react"
import { useRouter } from "next/router"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar, MapPin, Plus, User, MessageSquare, AlertCircle, Mail } from "lucide-react"
import dayjs from "dayjs"
import { useSession, signOut } from "next-auth/react"
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout"
import { useEvents } from "@/hooks/useEvents"
import { useEventLimits } from "@/hooks/useEventLimits"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/Header"
import { EventListItem } from "@/types"
import { <PERSON><PERSON>, AlertDes<PERSON>, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON>, Too<PERSON>ip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

function determineEventStatus(event: EventListItem): "upcoming" | "past" | "draft" | "ongoing" {
  if (!event.date) return "draft";

  const now = dayjs().tz(event.timezone || 'Australia/Melbourne');
  const eventDate = dayjs(event.date).tz(event.timezone || 'Australia/Melbourne');
  const eventStart = event.start ? dayjs(`${eventDate.format('YYYY-MM-DD')}T${event.start}`, 'YYYY-MM-DDTHH:mm').tz(event.timezone || 'Australia/Melbourne') : null;
  const eventEnd = event.end ? dayjs(`${eventDate.format('YYYY-MM-DD')}T${event.end}`, 'YYYY-MM-DDTHH:mm').tz(event.timezone || 'Australia/Melbourne') : null;

  // Check if event is ongoing
  if (eventStart && eventEnd) {
    if (now.isBetween(eventStart, eventEnd, null, '[]')) { // '[]' means inclusive
      return "ongoing";
    }
  } else if (eventDate.format('YYYY-MM-DD') === now.format('YYYY-MM-DD')) {
    return "ongoing";
  }

  if (eventDate.isBefore(now, 'day')) {
    return "past";
  }

  return "upcoming";
}

function EventsDashboard() {
  const router = useRouter()
  const { data: session } = useSession()
  const { events, loading, error } = useEvents()
  const eventLimits = useEventLimits()

  const navigateToEventDetails = (eventId: string) => {
    router.push(`/event/${eventId}`)
  }

  const navigateToCreateEvent = () => {
    // If the user has reached their event limits, don't navigate to create event page
    if (!eventLimits.canCreateEvent) {
      return;
    }
    router.push("/event/new")
  }

  const navigateToAccountManagement = () => {
    router.push("/account")
  }

  const handleSignOut = async () => {
    try {
      await signOut({
        redirect: false,
        callbackUrl: "/auth/signin"
      })
      router.push("/auth/signin")
    } catch (error) {
      console.error("Error signing out:", error)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
  }

  // Determine event status based on date and time
  const ongoingEvents = events.filter((event) => determineEventStatus(event) === "ongoing")
  const upcomingEvents = events.filter((event) => determineEventStatus(event) === "upcoming")
  const pastEvents = events.filter((event) => determineEventStatus(event) === "past")
  const draftEvents = events.filter((event) => determineEventStatus(event) === "draft")

  // Count user-owned (non-managed) events
  const ownedUpcomingEvents = upcomingEvents.filter(event => !event.isManager)
  const ownedTotalEvents = events.filter(event => !event.isManager)

  return (
    <ProtectedLayout>
      <div className="flex flex-col  bg-gray-50">
        {/* Use the shared Header component with only user profile */}
        <Header buttons={[]} />

        {/* Main Content */}
        <main className="flex-1 container mx-auto p-4 md:p-6">
          {/* Welcome Section */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={session?.user?.image || undefined} alt={session?.user?.name || "User"} />
                <AvatarFallback>{getInitials(session?.user?.name || "User")}</AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-2xl font-bold">Welcome, {(session?.user?.name || "User").split(" ")[0]}</h2>
                <p className="text-muted-foreground">{session?.user?.email}</p>
              </div>
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <Button
                      variant="primary-button"
                      onClick={navigateToCreateEvent}
                      disabled={!eventLimits.canCreateEvent}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Create New Event
                    </Button>
                  </div>
                </TooltipTrigger>
                {!eventLimits.canCreateEvent && (
                  <TooltipContent>
                    <p>
                      {eventLimits.hasReachedUpcomingLimit && eventLimits.hasReachedTotalLimit
                        ? `You've reached both your upcoming (${eventLimits.limits.upcomingEvents}) and total (${eventLimits.limits.totalEvents}) event limits.`
                        : eventLimits.hasReachedUpcomingLimit
                          ? `You've reached your limit of ${eventLimits.limits.upcomingEvents} upcoming events.`
                          : `You've reached your limit of ${eventLimits.limits.totalEvents} total events.`}
                    </p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Event Limits Warning */}
          {!eventLimits.canCreateEvent && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Event Limit Reached</AlertTitle>
              <AlertDescription>
                {eventLimits.hasReachedUpcomingLimit && eventLimits.hasReachedTotalLimit ? (
                  <>
                    You&apos;ve reached both your limit of {eventLimits.limits.upcomingEvents} upcoming events and {eventLimits.limits.totalEvents} total events. To create a new event, you&apos;ll need to delete an existing one.
                  </>
                ) : eventLimits.hasReachedUpcomingLimit ? (
                  <>
                    You&apos;ve reached your limit of {eventLimits.limits.upcomingEvents} upcoming events.
                    To create a new event, you&apos;ll need to delete an existing upcoming event.
                  </>
                ) : (
                  <>
                    You&apos;ve reached your limit of {eventLimits.limits.totalEvents} total events.
                    To create a new event, you&apos;ll need to delete an existing event.
                  </>
                )}
              </AlertDescription>
            </Alert>
          )}          {/* Events Section */}          {loading ? (
            <Card className="text-center p-8">
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                <CardTitle>Loading your events...</CardTitle>
                <CardDescription>
                  Please wait while we fetch your events
                </CardDescription>
              </div>
            </Card>) : error ? (
            <Card className="text-center p-12">
              <div className="flex flex-col items-center justify-center space-y-6">
                <div className="bg-red-50 p-6 rounded-full">
                  <AlertCircle className="h-16 w-16 text-red-500" />
                </div>
                <div className="space-y-2">
                  <CardTitle className="text-2xl text-red-800">Unable to Load Events</CardTitle>
                  <CardDescription className="text-base text-red-600 max-w-md">
                    We encountered an error while fetching your events. Please check your internet connection and try again.
                  </CardDescription>
                  {error.message && (
                    <p className="text-sm text-red-500 mt-2 font-mono bg-red-50 p-2 rounded">
                      {error.message}
                    </p>
                  )}
                </div>
                <Button onClick={() => window.location.reload()} className="mt-4 px-6 py-3">
                  Try Again
                </Button>
              </div>
            </Card>          ) : events.length === 0 ? (
            <Card className="text-center p-8">
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="bg-gray-100 p-4 rounded-full">
                  <Calendar className="h-12 w-12 text-gray-400" />
                </div>
                <CardTitle>No Events Yet</CardTitle>
                <CardDescription>
                  You haven&apos;t created any events yet. Get started by creating your first event.
                </CardDescription>
                <Button variant="primary-button" onClick={navigateToCreateEvent} className="mt-2" >
                  <Plus className="mr-2 h-4 w-4" />
                  Create New Event
                </Button>
              </div>
            </Card>
          ) : (
            <Tabs defaultValue="upcoming" className="w-full">
              <TabsList className="grid w-full grid-cols-3 mb-4">
                <TabsTrigger value="ongoing">Ongoing ({ongoingEvents.length})</TabsTrigger>
                <TabsTrigger value="upcoming">Upcoming ({upcomingEvents.length})</TabsTrigger>
                <TabsTrigger value="past">Past ({pastEvents.length})</TabsTrigger>
                {/* <TabsTrigger value="drafts">Drafts ({draftEvents.length})</TabsTrigger> */}
              </TabsList>              <TabsContent value="ongoing">
                {ongoingEvents.length === 0 ? (
                  <Card className="text-center p-8">
                    <div className="flex flex-col items-center justify-center space-y-4">
                      <div className="bg-orange-50 p-4 rounded-full">
                        <Calendar className="h-12 w-12 text-orange-500" />
                      </div>
                      <CardTitle className="text-lg">No Ongoing Events</CardTitle>
                      <CardDescription>
                        You don&apos;t have any events happening right now.
                      </CardDescription>
                    </div>
                  </Card>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {ongoingEvents.map((event) => renderEventCard(event, navigateToEventDetails, router))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="upcoming">
                {upcomingEvents.length === 0 ? (
                  <Card className="text-center p-8">
                    <div className="flex flex-col items-center justify-center space-y-4">
                      <div className="bg-blue-50 p-4 rounded-full">
                        <Calendar className="h-12 w-12 text-blue-500" />
                      </div>
                      <CardTitle className="text-lg">No Upcoming Events</CardTitle>
                      <CardDescription>
                        You don&apos;t have any upcoming events. Create a new event to get started.
                      </CardDescription>
                      <Button 
                        variant="primary-button" 
                        onClick={navigateToCreateEvent} 
                        className="mt-4"
                        disabled={!eventLimits.canCreateEvent}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Create New Event
                      </Button>
                    </div>
                  </Card>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {upcomingEvents.map((event) => renderEventCard(event, navigateToEventDetails, router))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="past">
                {pastEvents.length === 0 ? (
                  <Card className="text-center p-8">
                    <div className="flex flex-col items-center justify-center space-y-4">
                      <div className="bg-gray-50 p-4 rounded-full">
                        <Calendar className="h-12 w-12 text-muted-foreground" />
                      </div>
                      <CardTitle className="text-lg">No Past Events</CardTitle>
                      <CardDescription>
                        You don&apos;t have any past events yet. Create your first event to get started.
                      </CardDescription>
                      <Button 
                        variant="primary-button" 
                        onClick={navigateToCreateEvent} 
                        className="mt-4"
                        disabled={!eventLimits.canCreateEvent}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Create New Event
                      </Button>
                    </div>
                  </Card>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {pastEvents.map((event) => renderEventCard(event, navigateToEventDetails, router))}
                  </div>
                )}
              </TabsContent>

              {/* <TabsContent value="drafts">
                {draftEvents.length === 0 ? (
                  <Card className="text-center p-6">
                    <CardDescription>
                      You don&apos;t have any draft events. Create a new event to get started.
                    </CardDescription>
                    <Button variant="primary-button" onClick={navigateToCreateEvent} className="mt-4">
                      <Plus className="mr-2 h-4 w-4" />
                      Create New Event
                    </Button>
                  </Card>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {draftEvents.map((event) => renderEventCard(event, navigateToEventDetails, router))}
                  </div>
                )}
              </TabsContent> */}
            </Tabs>
          )}
        </main>
      </div>
    </ProtectedLayout>
  )
}

function renderEventCard(event: EventListItem, navigateToEventDetails: (id: string) => void, router: any) {
  return (
    <Card
      key={event.id}
      className="cursor-pointer hover:shadow-md transition-shadow flex flex-col h-full"
      onClick={() => navigateToEventDetails(event.id)}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            <CardTitle className="flex-1">{event.name}</CardTitle>
            {event.isManager && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                Managed
              </Badge>
            )}
          </div>
          {event.paymentStatus === 'paid' && event.plan && (
            <Badge className="bg-purple-500 hover:bg-purple-600 ml-2">
              {event.plan === 'host_plus' ? 'Host+' : event.plan === 'host_pro' ? 'Host Pro' : 'Free'}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="pb-2 flex-grow">
        <div className="space-y-2 text-sm">
          <div className="flex items-start gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground mt-0.5" />
            <span>
              {dayjs(event.date).format("dddd, MMMM D, YYYY")}
              {event.start && event.end && (
                <span className="ml-1">
                  ({event.start} - {event.end})
                </span>
              )}
            </span>
          </div>
          <div className="flex items-start gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
            <span>{event.location}</span>
          </div>
          <div className="flex items-start gap-2">
            <User className="h-4 w-4 text-muted-foreground mt-0.5" />
            <span>Hosted by {event.host}</span>
          </div>          {event.message && (
            <div className="flex items-start gap-2">
              <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
              <span className="line-clamp-2">{event.message}</span>
            </div>
          )}
        </div>
        </CardContent>
      <CardFooter className="mt-auto">
        <div className="flex gap-2 w-full">
          <Button
            variant="outline"
            className="flex-1"
            onClick={(e) => {
              e.stopPropagation()
              navigateToEventDetails(event.id)
            }}
          >
            View Details
          </Button>          
          {determineEventStatus(event) === "past" && (
            <Button
              variant="outline"
              className="flex-1"
              onClick={(e) => {
                e.stopPropagation()
                router.push(`/event/${event.id}/send-thank-you-emails`)
              }}
            >
              <Mail className="h-4 w-4 mr-2" />
              Thank You Emails
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}

export default EventsDashboard