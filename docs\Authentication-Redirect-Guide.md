# Authentication Redirect Usage Guide

This guide explains how to properly handle authentication redirects with site parameters in the IAC system.

## Overview

The IAC authentication system supports redirecting users to external sites after sign-in using secure HTTP-only cookies. This guide covers both the implementation details and usage patterns.

## How It Works

1. **User visits external site** (e.g., `partner.iamcoming.io`)
2. **External site redirects** to IAC sign-in with `site` parameter
3. **User signs in** on IAC
4. **IAC redirects** to secure callback with HTTP-only cookie
5. **User is redirected** back to external site with authentication

## URL Patterns

### Sign-in URLs with Site Parameter

```bash
# Partner site redirect
https://app.iamcoming.io/auth/signin?site=partner

# Local development redirect
https://app.iamcoming.io/auth/signin?site=local

# With custom redirect URL
https://app.iamcoming.io/auth/signin?site=partner&redirectUrl=/dashboard
```

### Secure Callback URLs

```bash
# Generated automatically after sign-in
https://app.iamcoming.io/api/auth/callback?site=partner
https://app.iamcoming.io/api/auth/callback?site=local
```

## Implementation Examples

### For External Sites (Client-Side)

```typescript
import { useExternalSiteAuth } from '@/hooks/useAuthRedirect';

function PartnerSiteComponent() {
  const { isAuthenticated, redirectToAuth } = useExternalSiteAuth('partner');

  const handleLogin = () => {
    // Redirect to IAC for authentication, then back to /dashboard
    redirectToAuth('/dashboard');
  };

  if (!isAuthenticated) {
    return <button onClick={handleLogin}>Sign in with IAC</button>;
  }

  return <div>Welcome! You are authenticated.</div>;
}
```

### For IAC App (Internal Usage)

```typescript
import { useAuthRedirect } from '@/hooks/useAuthRedirect';

function InternalComponent() {
  const { signInWithSite, signInWithRedirect, navigateToSignIn } =
    useAuthRedirect();

  const handlePartnerRedirect = () => {
    // Sign in and redirect to partner site
    signInWithSite('partner', '/dashboard');
  };

  const handleCustomRedirect = () => {
    // Sign in and redirect to custom URL
    signInWithRedirect('/my-custom-page');
  };

  const handleNavigateToSignIn = () => {
    // Just navigate to sign-in page with parameters
    navigateToSignIn({
      site: 'partner',
      redirectUrl: '/dashboard',
    });
  };

  return (
    <div>
      <button onClick={handlePartnerRedirect}>Go to Partner</button>
      <button onClick={handleCustomRedirect}>Custom Redirect</button>
      <button onClick={handleNavigateToSignIn}>Navigate to Sign-in</button>
    </div>
  );
}
```

### Direct URL Construction

```typescript
import { generateSignInUrl, generateCallbackUrl } from '@/lib/auth/redirect';

// Generate sign-in URL
const signInUrl = generateSignInUrl({
  site: 'partner',
  redirectUrl: '/dashboard',
});
// Result: /auth/signin?site=partner&redirectUrl=%2Fdashboard

// Generate callback URL
const callbackUrl = generateCallbackUrl('partner');
// Result: /api/auth/callback?site=partner
```

## Server-Side Implementation

### Custom API Route for External Redirects

```typescript
// pages/api/redirect-to-partner.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { generateSignInUrl } from '@/lib/auth/redirect';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { redirectUrl } = req.query;

  const signInUrl = generateSignInUrl({
    site: 'partner',
    redirectUrl: redirectUrl as string,
  });

  res.redirect(302, signInUrl);
}
```

### Middleware for Authentication Checks

```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { generateSignInUrl } from '@/lib/auth/redirect';

export function middleware(request: NextRequest) {
  // Check if user needs authentication
  const needsAuth = !request.cookies.get('next-auth.session-token');

  if (needsAuth) {
    const site = request.nextUrl.searchParams.get('site');
    const redirectUrl = generateSignInUrl({
      site: site as any,
      redirectUrl: request.nextUrl.pathname,
    });

    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }

  return NextResponse.next();
}
```

## Configuration

### Environment Variables

```bash
# Required for authentication
AUTH_SECRET=your-secret-here-minimum-64-characters
NEXTAUTH_URL=https://app.iamcoming.io
NEXTAUTH_SECRET=your-nextauth-secret

# Optional for debugging
IAC_DEBUG=true
```

### Allowed Sites Configuration

The allowed sites are configured in `/src/lib/auth/redirect.ts`:

```typescript
export const ALLOWED_SITES: Record<AllowedSite, string> = {
  partner: 'https://partner.iamcoming.io',
  local: 'http://localhost:3000',
};
```

## Security Considerations

### HTTP-Only Cookies

- Session tokens are stored in HTTP-only cookies
- Tokens are not exposed in URLs or accessible via JavaScript
- Cookies are set with appropriate domain and security flags

### CORS Configuration

```typescript
// Ensure CORS is properly configured in your API routes
res.setHeader('Access-Control-Allow-Origin', origin);
res.setHeader('Access-Control-Allow-Credentials', 'true');
```

### Domain Validation

- Only allowed sites can receive authentication cookies
- Sites are validated against the `ALLOWED_SITES` configuration
- Invalid sites are rejected with appropriate error messages

## Debugging

### Enable Debug Mode

```bash
# Set environment variable
IAC_DEBUG=true

# Or use the debug script
./scripts/enable-session-debug.sh
```

### Debug Output

When debug mode is enabled, you'll see detailed logs:

```log
🔍 Sign-in page - authenticated user detected
- Site parameter: partner
- Callback URL: /api/auth/callback?site=partner
Valid site parameter detected, redirecting to secure callback: partner
```

### Common Issues

1. **Site parameter not preserved**: Check the `redirect` callback in `auth.ts`
2. **Cookies not set**: Verify CORS and domain configuration
3. **Invalid site error**: Ensure the site is in `ALLOWED_SITES`
4. **Redirect loops**: Check for proper session detection logic

## Testing

### Manual Testing

```bash
# Test sign-in with site parameter
curl -X GET 'https://app.iamcoming.io/auth/signin?site=partner' \
  -H 'Accept: text/html' \
  --verbose

# Test secure callback
curl -X GET 'https://app.iamcoming.io/api/auth/callback?site=partner' \
  -H 'Cookie: next-auth.session-token=...' \
  --verbose
```

### Integration Testing

```typescript
// Example test case
describe('Authentication Redirect', () => {
  it('should redirect to partner site after sign-in', async () => {
    // Sign in user
    await signIn('email', { email: '<EMAIL>' });

    // Visit sign-in page with site parameter
    await page.goto('/auth/signin?site=partner');

    // Should redirect to partner site
    expect(page.url()).toContain('partner.iamcoming.io');
  });
});
```

## Best Practices

1. **Always validate site parameters** before using them
2. **Use the provided hooks and utilities** instead of manual implementation
3. **Store sensitive redirect info in session storage**, not URL parameters
4. **Handle authentication state properly** in your components
5. **Test the complete flow** from external site to IAC and back
6. **Monitor debug logs** during development to ensure proper flow

## Troubleshooting

### Common Solutions

1. **Clear browser cookies** if authentication seems stuck
2. **Check console logs** for debug information
3. **Verify environment variables** are properly set
4. **Ensure external sites use HTTPS** in production
5. **Test with different browsers** to rule out browser-specific issues
