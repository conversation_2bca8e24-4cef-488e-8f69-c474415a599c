# GitHub Copilot Instructions

## Project Overview

This is a **Next.js 15** event management application ("IAC - I Am Coming") built with TypeScript, Firebase/Firestore, NextAuth, and Tailwind CSS. The app handles event creation, invitation management, RSVP tracking, analytics, and shortlink generation.

## Core Commands

### Development
- `pnpm dev` - Start development server (localhost:3000)
- `pnpm build` - Build for production
- `pnpm serve` - Start production server
- `pnpm lint` - Run ESLint

### Testing
- `pnpm test:e2e` - Open Cypress for interactive E2E testing
- `pnpm test:e2e:ci` - Run Cypress headless for CI
- `pnpm test:e2e:dev` - Open Cypress with dev config
- `pnpm vitest run` - Run Storybook tests

### Documentation & UI
- `pnpm storybook` - Start Storybook dev server (localhost:6006)
- `pnpm build-storybook` - Build Storybook

### Utilities
- `pnpm generate-favicon` - Generate favicon assets
- `pnpm publish` - Deploy to production (runs ./scripts/push-image.sh)

## Architecture

### Tech Stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Authentication**: NextAuth with Firebase adapter
- **Database**: Firebase Firestore
- **Storage**: Firebase Storage
- **Email**: MailerSend, Nodemailer
- **Analytics**: Google Analytics, custom analytics tracking
- **Testing**: Cypress (E2E), Vitest (component), Storybook
- **Deployment**: Docker, AWS (via scripts)

### Key Directories
- `src/app/` - Next.js App Router pages and layouts
- `src/components/` - React components (shadcn/ui based)
- `src/lib/` - Business logic, utilities, database operations
- `src/pages/api/` - API routes for backend functionality
- `src/hooks/` - Custom React hooks
- `src/types/` - TypeScript type definitions
- `cypress/` - End-to-end tests using Cypress + Cucumber
- `scripts/` - Deployment and utility scripts
- `docs/` - Feature documentation and design docs

### Core Services (src/lib/)
- `firebase.ts` - Firebase Admin SDK setup
- `firebaseClient.ts` - Client-side Firebase
- `database.ts` - Firestore operations
- `auth/` - Authentication utilities
- `analytics.ts` - Event tracking and analytics
- `shortlink-service.ts` - URL shortening
- `mailer.ts` - Email sending
- `storage.ts` - File upload/storage
- `utm.ts` - UTM tracking
- `ai-middleware.ts` - AI integrations

### External Services
- Firebase (Auth, Firestore, Storage)
- MailerSend/Nodemailer (Email)
- Google Analytics
- AWS (Deployment)
- Stripe (Payments)
- Google Gemini AI

## Style Guidelines

### TypeScript
- Strict mode enabled
- Use type assertions sparingly
- Import aliases: `@/` maps to `src/`
- Prefer interfaces over types for object shapes

### Component Structure
- Use shadcn/ui components from `@/components/ui/`
- Functional components with TypeScript
- Props interfaces should be named `ComponentNameProps`
- Use React 19 features (no React.FC needed)

### Styling
- Tailwind CSS with custom design system
- CSS variables for theming (see tailwind.config.ts)
- Dark mode support via `[data-theme='dark']`
- Component variants using `class-variance-authority`

### Code Organization
- Keep API routes in `src/pages/api/`
- Business logic in `src/lib/` modules
- Custom hooks in `src/hooks/`
- Types in `src/types/`
- Use barrel exports for cleaner imports

### Error Handling
- Use try-catch blocks for async operations
- Log errors using `src/lib/logger.ts`
- Return structured error responses from API routes
- Handle Firebase errors gracefully

### Testing
- E2E tests use Cypress with Cucumber (BDD)
- Component tests via Storybook + Vitest
- Test files should be co-located or in `src/test/`

## Environment & Deployment

### Required Environment Variables
- Firebase: `FIREBASE_CLIENT_EMAIL`, `FIREBASE_PRIVATE_KEY`, `FIREBASE_PROJECT_ID`
- NextAuth: `NEXTAUTH_SECRET`, `NEXTAUTH_URL`
- Email: MailerSend API keys
- Analytics: Google Analytics keys

### Package Manager
- Use **pnpm** exclusively (not npm or yarn)
- Lock file: `pnpm-lock.yaml`
- Install dependencies: `pnpm install`

### Deployment
- Dockerized application (see Dockerfile)
- Deploy via `./scripts/deploy.sh`
- Production builds use `pnpm build`

## Agent Guidelines from AGENTS.md

- Use **pnpm** as package manager
- Environment variables in `.env.local` (dev) and `.env.production` (prod)
- API routes live in `src/pages/api/`
- Utilities and business logic in `src/lib/`
- End-to-end tests with Cypress + Cucumber
- Storybook for component development and testing
- JWT generation available via `scripts/generate-jwt.js`
- Additional documentation in `docs/` and `spec-docs/` folders

## Migration & Database Scripts

- User migration: `scripts/migrate-users-to-organizations.ts`
- JWT generation: `scripts/generate-jwt.js`
- Database operations via `src/lib/database.ts`
- See `docs/` folder for migration guides and API specifications
