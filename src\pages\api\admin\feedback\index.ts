import { NextApiRequest, NextApiResponse } from 'next';
import { getToken } from 'next-auth/jwt';
import { Database } from '@/lib/database';
import { debugLog } from '@/lib/logger';

/**
 * API endpoint to handle admin feedback operations - listing all feedback
 * 
 * @param req - NextApiRequest object
 * @param res - NextApiResponse object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify admin access
    const token = await getToken({ req, secret: process.env.AUTH_SECRET });
    console.log('Admin feedback request received', token);

    if (!token || !token.isAdmin) {
      return res.status(403).json({ error: 'Unauthorized: Admin access required' });
    }

    // Handle different request methods
    switch (req.method) {
      case 'GET':
        return getFeedbackList(req, res);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Error in admin feedback handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get list of feedback with pagination and filtering options
 */
async function getFeedbackList(req: NextApiRequest, res: NextApiResponse) {
  try {
    const db = Database.getInstance();

    // Parse query parameters
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;
    const status = req.query.status as string | undefined;
    const sortBy = (req.query.sortBy as string) || 'createdAt';
    const sortOrder = (req.query.sortOrder as string) || 'desc';

    // Build query
    let query = db.query('feedback');

    // Add status filter if provided
    if (status && status !== 'all') {
      query = query.where('status', '==', status);
    }

    // Add sorting
    query = query.orderBy(sortBy, sortOrder as 'asc' | 'desc');

    // Execute query with pagination
    const snapshot = await query.limit(limit).offset(offset).get();

    // Collect feedback items
    const feedbackItems = snapshot.docs.map(doc => doc.data());

    // Count total items for pagination
    const totalCount = await query.count().get().then(snapshot => snapshot.data().count);

    debugLog('Admin fetched feedback list', {
      count: feedbackItems.length,
      total: totalCount,
      filters: { status, sortBy, sortOrder }
    });

    return res.status(200).json({
      success: true,
      feedback: feedbackItems,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + feedbackItems.length < totalCount
      }
    });
  } catch (error) {
    console.error('Error fetching feedback list:', error);
    return res.status(500).json({ error: 'Failed to fetch feedback list' });
  }
}
