"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface ChartTooltipContentProps {
  active?: boolean;
  payload?: any[];
  label?: string;
  className?: string;
}

export function ChartTooltipContent({
  active,
  payload,
  label,
  className,
}: ChartTooltipContentProps) {
  if (!active || !payload?.length) {
    return null;
  }

  // Get only the first entry for a more compact tooltip
  const entry = payload[0];
  if (!entry) return null;

  const fill = entry.payload?.fill || entry.fill || "#888";
  const name = entry.name || entry.payload?.name || "Unknown";
  const value = entry.value || entry.payload?.value || 0;

  return (
    <div
      className={cn(
        "rounded-md border bg-background py-1 px-2 shadow-sm",
        className
      )}
    >
      <div className="flex items-center gap-2">
        <div
          className="size-2 rounded-full"
          style={{ backgroundColor: fill }}
        />
        <span className="text-xs font-medium">{name}:</span>
        <span className="text-xs font-medium">{value}</span>
      </div>
    </div>
  );
}
