import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { AdminLayout } from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { shortlinkService } from '@/lib/shortlink-service';
import { SL_GroupedAnalytics, SL_ShortLinkResponse } from '@/types/shortlinks-api';
import { extractUtmParams } from '@/lib/utm';
import { ArrowLeft, Edit, Trash2, ExternalLink, Copy, Eye, Calendar, Target, Monitor, Smartphone, Tablet, Globe, Clock, Activity, BarChart3, Tag } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';

export default function ShortlinkDetailPage() {
  const router = useRouter();
  const { shortCode } = router.query;
  const [shortlink, setShortlink] = useState<SL_ShortLinkResponse | null>(null);
  const [analytics, setAnalytics] = useState<SL_GroupedAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (shortCode && typeof shortCode === 'string') {
      loadShortlinkDetails();
      loadAnalytics();
    }
  }, [shortCode]);

  const loadShortlinkDetails = async () => {
    try {
      setLoading(true);
      // Since we don't have a direct API to get a single shortlink, 
      // we'll get all and filter
      const data = await shortlinkService.listShortLinks();
      const foundShortlink = data.find((link: SL_ShortLinkResponse) => link.shortCode === shortCode);

      if (foundShortlink) {
        setShortlink(foundShortlink);
      } else {
        toast({
          title: "Error",
          description: "Shortlink not found.",
          variant: "destructive",
        });
        router.push('/admin/links');
      }
    } catch (error) {
      console.error('Failed to load shortlink details:', error);
      toast({
        title: "Error",
        description: "Failed to load shortlink details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadAnalytics = async () => {
    if (!shortCode || typeof shortCode !== 'string') return;

    try {
      setAnalyticsLoading(true);
      const data = await shortlinkService.getShortLinkAnalytics(shortCode);
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to load analytics:', error);
      // Don't show error toast for analytics, as it might not be available
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!shortCode || typeof shortCode !== 'string') return;

    if (!window.confirm(`Are you sure you want to delete the shortlink "${shortCode}"?`)) {
      return;
    }

    try {
      await shortlinkService.deleteShortLink(shortCode);
      toast({
        title: "Success",
        description: "Shortlink deleted successfully!",
      });
      router.push('/admin/links');
    } catch (error) {
      console.error('Failed to delete shortlink:', error);
      toast({
        title: "Error",
        description: "Failed to delete shortlink. Please try again.",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = async () => {
    if (!shortCode || typeof shortCode !== 'string') return;

    const url = shortlinkService.getFullUrl(shortCode);
    try {
      await navigator.clipboard.writeText(url);
      toast({
        title: "Copied!",
        description: `Short URL copied to clipboard: ${url}`,
      });
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      toast({
        title: "Error",
        description: "Failed to copy URL to clipboard.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <AdminLayout pageTitle="Loading...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!shortlink) {
    return (
      <AdminLayout pageTitle="Shortlink Not Found">
        <div className="text-center py-8">
          <p className="text-muted-foreground">Shortlink not found.</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout pageTitle={`Shortlink: ${shortlink.shortCode}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">

            <div>
              <Link href="/admin/links" className='block'>
                <Button variant="link" size="sm">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Links
                </Button>
              </Link>
              <div className="mt-2">
                <h1 className="text-2xl font-bold tracking-tight flex items-center">
                  <code className="px-2 py-1 bg-muted rounded text-lg mr-2">
                    {shortlink.shortCode}
                  </code>
                </h1>
                <p className="text-muted-foreground">
                  Shortlink details and analytics
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button size={'sm'} variant="outline" onClick={copyToClipboard}>
              <Copy className="mr-2 h-4 w-4" />
              Copy URL
            </Button>
            <Link href={`/admin/links/${shortCode}/edit`}>
              <Button size={'sm'} variant="outline">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </Link>
            <Button
              size={'sm'}
              variant="destructive"
              onClick={handleDelete}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        {/* Shortlink Details */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Target className="mr-2 h-5 w-5" />
                Shortlink Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Short Code</label>
                <div className="flex items-center space-x-2 mt-1">
                  <code className="px-2 py-1 bg-muted rounded text-sm">
                    {shortlink.shortCode}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={copyToClipboard}
                    className="h-6 w-6 p-0"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Full URL</label>
                <div className="mt-1">
                  <a
                    href={shortlinkService.getFullUrl(shortlink.shortCode)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline flex items-center"
                  >
                    {shortlinkService.getFullUrl(shortlink.shortCode)}
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Destination URL</label>
                <div className="mt-1">
                  <a
                    href={shortlink.redirectUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline flex items-center break-all"
                  >
                    {shortlink.redirectUrl}
                    <ExternalLink className="ml-1 h-3 w-3 flex-shrink-0" />
                  </a>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Tag className="mr-2 h-5 w-5" />
                UTM Parameters
              </CardTitle>
              <CardDescription>
                Tracking parameters used in this shortlink
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {(() => {
                const utmParams = extractUtmParams(shortlink.redirectUrl);

                return (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="utm_source">UTM Source</Label>
                        <Input
                          id="utm_source"
                          value={utmParams.utm_source || ''}
                          placeholder="e.g., newsletter, facebook, google"
                          readOnly
                          className="bg-muted/50"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="utm_medium">UTM Medium</Label>
                        <Input
                          id="utm_medium"
                          value={utmParams.utm_medium || ''}
                          placeholder="e.g., email, social, cpc"
                          readOnly
                          className="bg-muted/50"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="utm_campaign">UTM Campaign</Label>
                        <Input
                          id="utm_campaign"
                          value={utmParams.utm_campaign || ''}
                          placeholder="e.g., spring_sale, launch_2024"
                          readOnly
                          className="bg-muted/50"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="utm_term">UTM Term</Label>
                        <Input
                          id="utm_term"
                          value={utmParams.utm_term || ''}
                          placeholder="e.g., running+shoes, discount"
                          readOnly
                          className="bg-muted/50"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="utm_content">UTM Content</Label>
                      <Input
                        id="utm_content"
                        value={utmParams.utm_content || ''}
                        placeholder="e.g., logo_link, text_link"
                        readOnly
                        className="bg-muted/50"
                      />
                    </div>

                    <div className="pt-2 mt-4 border-t">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">Total Clicks</span>
                        <Badge variant="secondary" className="text-sm px-3 py-1">
                          {analytics?.summary?.hits || 0} clicks
                        </Badge>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        </div>

        {/* Analytics Section */}
        {analyticsLoading ? (
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            </CardContent>
          </Card>
        ) : analytics ? (
          <div className="space-y-6">
            {/* Analytics Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="mr-2 h-5 w-5" />
                  Analytics Overview
                </CardTitle>
                <CardDescription>
                  Comprehensive performance metrics for this shortlink
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold">{analytics.summary?.hits || 0}</div>
                    <div className="text-sm text-muted-foreground">Total Clicks</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold">{analytics.summary?.locations ? Object.keys(analytics.summary.locations).length : 0}</div>
                    <div className="text-sm text-muted-foreground">Locations</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold">{analytics.summary?.devices ? Object.keys(analytics.summary.devices).length : 0}</div>
                    <div className="text-sm text-muted-foreground">Device Types</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold">{analytics.summary?.os ? Object.keys(analytics.summary.os).length : 0}</div>
                    <div className="text-sm text-muted-foreground">Operating Systems</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Device Breakdown */}
            {analytics.summary?.devices && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Monitor className="mr-2 h-5 w-5" />
                    Device Breakdown
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.summary.devices).map(([deviceType, count]) => {
                      const percentage = analytics.summary?.hits ? ((count / analytics.summary.hits) * 100).toFixed(1) : '0';
                      const getDeviceIcon = (deviceType: string) => {
                        const type = deviceType.toLowerCase();
                        if (type.includes('mobile') || type.includes('phone')) return <Smartphone className="h-4 w-4" />;
                        if (type.includes('tablet')) return <Tablet className="h-4 w-4" />;
                        return <Monitor className="h-4 w-4" />;
                      };

                      return (
                        <div key={deviceType} className="flex items-center justify-between p-3 border rounded">
                          <div className="flex items-center space-x-2">
                            {getDeviceIcon(deviceType)}
                            <span className="font-medium capitalize">{deviceType}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground">{percentage}%</span>
                            <Badge variant="outline">{count} clicks</Badge>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Top Locations */}
            {analytics.summary?.locations && Object.keys(analytics.summary.locations).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Globe className="mr-2 h-5 w-5" />
                    Top Locations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.summary.locations)
                      .sort(([, a], [, b]) => b - a)
                      .slice(0, 10)
                      .map(([location, count]) => {
                        const percentage = analytics.summary?.hits ? ((count / analytics.summary.hits) * 100).toFixed(1) : '0';
                        return (
                          <div key={location} className="flex items-center justify-between p-3 border rounded">
                            <div className="flex items-center space-x-2">
                              <Globe className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{location}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">{percentage}%</span>
                              <Badge variant="outline">{count} clicks</Badge>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Operating Systems */}
            {analytics.summary?.os && Object.keys(analytics.summary.os).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="mr-2 h-5 w-5" />
                    Operating Systems
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.summary.os)
                      .sort(([, a], [, b]) => b - a)
                      .map(([osName, count]) => {
                        const percentage = analytics.summary?.hits ? ((count / analytics.summary.hits) * 100).toFixed(1) : '0';
                        return (
                          <div key={osName} className="flex items-center justify-between p-3 border rounded">
                            <div className="flex items-center space-x-2">
                              <Activity className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{osName}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">{percentage}%</span>
                              <Badge variant="outline">{count} clicks</Badge>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Time-based Analytics */}
            {(analytics.grouped?.daily || analytics.grouped?.weekly) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="mr-2 h-5 w-5" />
                    Time-based Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Daily Analytics */}
                  {analytics.grouped?.daily && Object.keys(analytics.grouped.daily).length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3">Daily Clicks (Last 30 days)</h4>
                      <div className="space-y-2">
                        {Object.entries(analytics.grouped.daily)
                          .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                          .slice(0, 30)
                          .map(([date, clicks]) => (
                            <div key={date} className="flex items-center justify-between p-2 border rounded">
                              <span className="text-sm font-medium">
                                {new Date(date).toLocaleDateString()}
                              </span>
                              <Badge variant="outline">{clicks} clicks</Badge>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}

                  {/* Weekly Analytics */}
                  {analytics.grouped?.weekly && Object.keys(analytics.grouped.weekly).length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3">Weekly Clicks</h4>
                      <div className="space-y-2">
                        {Object.entries(analytics.grouped.weekly)
                          .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                          .map(([week, clicks]) => (
                            <div key={week} className="flex items-center justify-between p-2 border rounded">
                              <span className="text-sm font-medium">
                                Week of {new Date(week).toLocaleDateString()}
                              </span>
                              <Badge variant="outline">{clicks} clicks</Badge>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Recent Access Logs */}
            {analytics.summary?.logs && analytics.summary.logs.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="mr-2 h-5 w-5" />
                    Recent Access Logs
                  </CardTitle>
                  <CardDescription>
                    Latest {analytics.summary.logs.length} access records
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.summary.logs.slice(0, 20).map((log, index) => (
                      <div key={index} className="p-3 border rounded-lg space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm font-medium">
                              {log.timestamp ? new Date(log.timestamp).toLocaleString() : 'Unknown time'}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {log.location && (
                              <Badge variant="secondary" className="text-xs">
                                {log.location}
                              </Badge>
                            )}
                            {log.os && (
                              <Badge variant="outline" className="text-xs">
                                {log.os}
                              </Badge>
                            )}
                          </div>
                        </div>
                        {log.ua && (
                          <div className="text-xs text-muted-foreground break-all">
                            {log.ua}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
              <CardDescription>
                Analytics data is not available for this shortlink
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Analytics may not be available yet or there may be no clicks recorded for this shortlink.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
}
