import { Event, EventInvite, Recipient, PostEventContactGroupEmailData, PostEventThankYouEmailData } from "@/types";
import { SendVerificationRequestParams } from "next-auth/providers/email";
import { SESClient, SendEmailCommand, SendEmailCommandInput, SendRawEmailCommand } from "@aws-sdk/client-ses";
import { FormatDate, FormatTime } from "./dayjs";
import { Format24to12 } from "./time";
import { renderTemplate } from "./templateRenderer";
import { log } from "./logger";
import { generateCalendarInvite, generateRsvpLink } from "./utils";
import { UTMParams, addUtmParams, generateEmailUtmParams } from "./utm";
import { createTransport } from 'nodemailer';

const FROM_EMAIL = process.env.FROM_EMAIL || "<EMAIL>";

if (process.env.AWS_ACCESS_KEY_ID === undefined || process.env.AWS_SECRET_ACCESS_KEY === undefined || process.env.AWS_REGION === undefined) {
  throw new Error("AWS SES configuration is not set");
}

const sesClient = new SESClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

// Create a nodemailer transporter that uses AWS SES
export const transporter = createTransport({
  SES: { ses: sesClient, aws: { SendRawEmailCommand } },
});

export function createPersonalization(event: Event, invite?: EventInvite, emailType: 'invitation' | 'reminder' | 'confirmation' | 'welcome' | 'post_event_thank_you' = 'invitation') {
  // Get the event timezone
  const eventTimezone = event.timezone || "Australia/Melbourne";

  // Generate UTM parameters for email tracking
  let utmParams: UTMParams | undefined;
  let trackableInviteLink: string = '';

  if (invite && event.ID) {
    // Create UTM parameters based on email type
    utmParams = generateEmailUtmParams(
      event.ID,
      `email_${emailType}_${invite.ID}`
    );

    // Generate a trackable invite link with UTM parameters
    const baseInviteLink = `https://iamcoming.io/event/${event.ID}/rsvp/${invite.ID}`;
    trackableInviteLink = addUtmParams(baseInviteLink, utmParams);
  }

  // Create the base personalization object
  return {
    eventName: event.eventName,
    eventDate: FormatDate(event.eventDate, eventTimezone),
    eventTime: `${Format24to12(event.start)} - ${Format24to12(event.end) || "TBD"}`,
    eventLocation: event.location,
    eventTimezone: eventTimezone,
    eventDescription: event.message,
    guestName: invite?.name,
    eventLink: `https://iamcoming.io/event/${event.ID}`,
    // Use the trackable link with UTM parameters if available
    inviteLink: trackableInviteLink || `https://iamcoming.io/event/${event.ID}/rsvp/${invite?.ID}`,
    inviteResponse: invite?.status === 'accepted'
      ? `You have accepted the invitation with ${invite.response?.adults} Adults ${invite.response?.children} Children`
      : "You have declined the invitation",
    inviteMessage: invite?.response?.message || "",
    responseDate: invite?.response?.timestamp
      ? FormatDate(new Date(invite.response.timestamp), eventTimezone)
      : "",
    responseTime: invite?.response?.timestamp
      ? FormatTime(new Date(invite.response.timestamp), eventTimezone)
      : "",
    isAccepted: invite?.status === 'accepted',
    invite: invite, // Include the full invite object for more detailed templating
    // Include UTM parameters for potential use in templates
    utmParams: utmParams
  };
}

export async function sendEventWelcomeEmail(event: Event, recipient: Recipient) {
  if (!recipient.email) {
    console.log("Recipient email is not set, skipping email send");
    return
  }
  const personalization = createPersonalization(event, undefined, 'welcome');
  const textBody = await renderTemplate("eventWelcomeText.hbs", personalization);
  const htmlBody = await renderTemplate("eventWelcomeHtml.hbs", personalization);

  const params = {
    Source: `I am Coming<${FROM_EMAIL}>`,
    Destination: {
      ToAddresses: [recipient.email],
    },
    Message: {
      Subject: {
        Data: `Your event ${event.eventName} has been created`,
      },
      Body: {
        Text: {
          Data: textBody,
        },
        Html: {
          Data: htmlBody,
        },
      },
    },
  };

  const command = new SendEmailCommand(params);
  await sesClient.send(command);
}

export async function sendGuestResponseConfirmationEmail(event: Event, invite: EventInvite, recipient: Recipient) {
  const personalization = createPersonalization(event, invite, 'confirmation');

  // Generate templates for guest
  const guestTextBody = await renderTemplate("guestResponseConfirmationText.hbs", personalization);
  const guestHtmlBody = await renderTemplate("guestResponseConfirmationHtml.hbs", personalization);

  // Generate templates for host
  const hostTextBody = await renderTemplate("hostNotificationText.hbs", personalization);
  const hostHtmlBody = await renderTemplate("hostNotificationHtml.hbs", personalization);

  // Generate calendar invite if the guest accepted
  const calendarContent = invite.status === 'accepted' ? generateCalendarInvite(event, invite) : null;

  try {
    // Send email to the guest with calendar attachment if they accepted (only if recipient has email)
    if (recipient.email) {
      if (invite.status === 'accepted' && calendarContent) {
        // Use nodemailer to send email with calendar attachment
        await transporter.sendMail({
          from: `I am Coming<${FROM_EMAIL}>`,
          to: recipient.email,
          subject: `Your RSVP for ${event.eventName}`,
          text: guestTextBody,
          html: guestHtmlBody,
          attachments: [
            {
              filename: `${event.eventName.replace(/[^a-zA-Z0-9]/g, '_')}.ics`,
              content: calendarContent,
              contentType: 'text/calendar; charset=UTF-8; method=REQUEST'
            }
          ]
        });
        log(`RSVP confirmation email with calendar invite sent to ${recipient.email}`);
      } else {
        // For declined RSVPs, send regular email without calendar
        await transporter.sendMail({
          from: `I am Coming<${FROM_EMAIL}>`,
          to: recipient.email,
          subject: `Your RSVP for ${event.eventName}`,
          text: guestTextBody,
          html: guestHtmlBody
        });
        log(`RSVP confirmation email sent to ${recipient.email}`);
      }
    } else {
      log("Recipient email is not set, skipping email to recipient but will still notify event owner and managers");
    }

    // Send notification to event owner and managers - always do this even if recipient has no email
    const managerCCs = event.managers && Array.isArray(event.managers) && event.managers.length > 0
      ? event.managers
      : [];

    await transporter.sendMail({
      from: `${invite.name} via I am Coming<${FROM_EMAIL}>`,
      to: event.ownerEmail,
      cc: managerCCs,
      subject: `${invite.status.toUpperCase()} : ${invite.name} :: ${event.eventName}`,
      text: hostTextBody,
      html: hostHtmlBody
    });

    log(`RSVP notification sent to event owner ${event.ownerEmail} and ${managerCCs.length} managers`);
  } catch (error) {
    console.error('Error sending RSVP emails:', error);
    throw new Error('Failed to send RSVP emails');
  }
}

export async function sendInvitationEmail(event: Event, invite: EventInvite, recipient: Recipient) {
  if (!recipient.email) {
    log("Recipient email is not set, skipping invitation email");
    return;
  }

  const personalization = createPersonalization(event, invite, 'invitation');
  const textBody = await renderTemplate("invitationText.hbs", personalization);
  const htmlBody = await renderTemplate("invitationHtml.hbs", personalization);

  try {
    // Send email to the guest
    await transporter.sendMail({
      from: `I am Coming<${FROM_EMAIL}>`,
      to: recipient.email,
      subject: `You're invited to ${event.eventName}!`,
      text: textBody,
      html: htmlBody
    });

    log(`Invitation email sent to ${recipient.email} for event ${event.eventName}`);
  } catch (error) {
    console.error('Error sending invitation email:', error);
    throw new Error('Failed to send invitation email');
  }
}

export async function sendLoginEmail({ identifier, url, provider, expires, token, theme }: SendVerificationRequestParams) {
  log("Sending login email to", { identifier, url, provider, expires, token, theme });
  const personalization = {
    url,
  };
  const textBody = await renderTemplate("loginEmailText.hbs", personalization);
  const htmlBody = await renderTemplate("loginEmailHtml.hbs", personalization);

  const emailCommand: SendEmailCommandInput = {
    Source: `I am Coming<${FROM_EMAIL}>`,
    Destination: {
      ToAddresses: [identifier],
    },
    Message: {
      Subject: {
        Data: `Sign in to IamComing`,
      },
      Body: {
        Text: {
          Data: textBody,
        },
        Html: {
          Data: htmlBody,
        },
      },
    },
  };

  const command = new SendEmailCommand(emailCommand);
  await sesClient.send(command);
}

export async function sendUserWelcomeEmail(recipient: { name: string; email: string }) {
  if (!recipient.email) {
    log("Recipient email is not set, skipping welcome email");
    return;
  }

  const personalization = {
    userName: recipient.name || "there"
  };

  const textBody = await renderTemplate("userWelcomeText.hbs", personalization);
  const htmlBody = await renderTemplate("userWelcomeHtml.hbs", personalization); // Using the HTML template now

  try {
    // Send email to the user
    await transporter.sendMail({
      from: `I am Coming<${FROM_EMAIL}>`,
      to: recipient.email,
      subject: `Welcome to IAMCOMING.IO!`,
      text: textBody,
      html: htmlBody
    });

    log(`Welcome email sent to ${recipient.email}`);
    return true;
  } catch (error) {
    console.error('Error sending welcome email:', error);
    throw new Error('Failed to send welcome email');
  }
}

export async function sendPasswordResetEmail(email: string, resetUrl: string) {
  if (!email) {
    log("Recipient email is not set, skipping password reset email");
    return false;
  }

  const personalization = {
    resetUrl
  };

  try {
    const textBody = await renderTemplate("passwordResetText.hbs", personalization);
    const htmlBody = await renderTemplate("passwordResetHtml.hbs", personalization);

    // Send email to the user
    await transporter.sendMail({
      from: `I am Coming<${FROM_EMAIL}>`,
      to: email,
      subject: `Reset Your IAMCOMING.IO Password`,
      text: textBody,
      html: htmlBody
    });

    log(`Password reset email sent to ${email}`);
    return true;
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return false;
  }
}

export async function sendReminderEmail(event: Event, invite: EventInvite, message: string) {
  if (!invite.email) {
    log("Recipient email is not set, skipping reminder email");
    return false;
  }

  // Create personalization data with the reminder message (if provided)
  const personalization = {
    ...createPersonalization(event, invite, 'reminder'),
    reminderMessage: message || ""
  };

  // Generate email content
  const textBody = await renderTemplate("reminderText.hbs", personalization);
  const htmlBody = await renderTemplate("reminderHtml.hbs", personalization);

  try {
    // Send email to the guest
    await transporter.sendMail({
      from: `I am Coming<${FROM_EMAIL}>`,
      to: invite.email,
      subject: `Reminder: ${event.eventName}`,
      text: textBody,
      html: htmlBody
    });

    log(`Reminder email sent to ${invite.email} for event ${event.eventName}`);
    return true;
  } catch (error) {
    console.error(`Error sending reminder email to ${invite.email}:`, error);
    return false;
  }
}



/**
 * Send post-event contact group save email
 */
export async function sendPostEventContactGroupEmail(
  organizationId: string,
  eventId: string
): Promise<boolean> {
  try {
    const { Database } = await import('./database');
    const { extractContactGroupsFromEvent } = await import('./saved-contact-groups');

    const db = Database.getInstance();

    // Get event and organization details
    const event = await db.readData('events', eventId);
    const organization = await db.getOrganizationById(organizationId);

    if (!event || !organization) {
      console.error('Event or organization not found');
      return false;
    }

    // Get contact groups from the event
    const contactGroups = await extractContactGroupsFromEvent(eventId);

    if (contactGroups.length === 0) {
      console.log('No contact groups found for event, skipping email');
      return false;
    }

    // Create email data (no tokens needed - use normal authentication)
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://iamcoming.io';
    const emailData: PostEventContactGroupEmailData = {
      organizationName: organization.name,
      eventName: event.eventName,
      eventDate: FormatDate(event.eventDate, event.timezone || "Australia/Melbourne"),
      contactGroups: contactGroups.map(group => ({
        name: group.name,
        contactCount: group.contacts.length
      })),
      saveGroupsUrl: `${baseUrl}/organizations/${organizationId}/save-contact-groups/${eventId}`
    };

    // Generate email content
    const textBody = await renderTemplate("postEventContactGroupSaveText.hbs", emailData);
    const htmlBody = await renderTemplate("postEventContactGroupSaveHtml.hbs", emailData);

    // Send email to organization owner
    const ownerEmail = event.ownerEmail;
    if (!ownerEmail) {
      console.error('No owner email found for event');
      return false;
    }

    await transporter.sendMail({
      from: `I am Coming<${FROM_EMAIL}>`,
      to: ownerEmail,
      subject: `Save your contact groups from ${event.eventName} for future events?`,
      text: textBody,
      html: htmlBody
    });

    log(`Post-event contact group save email sent to ${ownerEmail} for event ${event.eventName}`);
    return true;
  } catch (error) {
    console.error('Error sending post-event contact group save email:', error);
    return false;
  }
}

/**
 * Send post-event thank you email to a guest
 */
export async function sendPostEventThankYouEmail(
  event: Event,
  invite: EventInvite
): Promise<boolean> {
  if (!invite.email) {
    log("Guest email is not set, skipping post-event thank you email");
    return false;
  }

  try {
    // Create email data
    const emailData: PostEventThankYouEmailData = {
      guestName: invite.name,
      eventName: event.eventName,
      eventDate: FormatDate(event.eventDate, event.timezone || "Australia/Melbourne"),
      hostName: event.host
    };

    // Generate email content
    const textBody = await renderTemplate("postEventThankYouText.hbs", emailData);
    const htmlBody = await renderTemplate("postEventThankYouHtml.hbs", emailData);

    // Send email to the guest
    await transporter.sendMail({
      from: `${event.host} via I am Coming<${FROM_EMAIL}>`,
      to: invite.email,
      subject: `Thanks for making ${event.eventName} special! 🎉`,
      text: textBody,
      html: htmlBody
    });

    log(`Post-event thank you email sent to ${invite.email} for event ${event.eventName}`);
    return true;
  } catch (error) {
    console.error(`Error sending post-event thank you email to ${invite.email}:`, error);
    return false;
  }
}

/**
 * Send event archive notification email to event owner
 * @param event The event that was archived
 * @param archiveUrl The download URL for the archive
 * @param expiresAt The expiration date of the archive
 */
export async function sendEventArchiveEmail(event: Event, archiveUrl: string, expiresAt: Date) {
  try {
    if (!event.ownerEmail) {
      throw new Error('Event owner email is missing');
    }

    const eventTimezone = event.timezone || "Australia/Melbourne";
    const formattedDate = FormatDate(event.eventDate, eventTimezone);
    const expiryDate = FormatDate(expiresAt.toISOString(), eventTimezone);
    const archivedDate = FormatDate(new Date().toISOString(), eventTimezone);

    // Create personalization data for templates
    const personalization = {
      eventName: event.eventName,
      eventDate: formattedDate,
      archivedDate: archivedDate,
      archiveUrl: archiveUrl,
      expiryDate: expiryDate,
      supportEmail: '<EMAIL>'
    };

    // Generate email content using templates
    const textBody = await renderTemplate("eventArchiveText.hbs", personalization);
    const htmlBody = await renderTemplate("eventArchiveHtml.hbs", personalization);

    const subject = `Archive Ready: Download Your ${event.eventName} Event Data`;

    await transporter.sendMail({
      from: `I am Coming <${FROM_EMAIL}>`,
      to: event.ownerEmail,
      subject: subject,
      text: textBody,
      html: htmlBody
    });

    log(`Event archive email sent to ${event.ownerEmail} for event ${event.eventName}`);
    return true;
  } catch (error) {
    console.error(`Error sending event archive email to ${event.ownerEmail}:`, error);
    return false;
  }
}