import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { ContactGroupData } from '@/types';
import {
  getSavedContactGroups,
  saveContactGroups
} from '@/lib/saved-contact-groups';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { organizationId } = req.query;

    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Verify user session and organization membership
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const db = Database.getInstance();
    const organization = await db.getOrganizationById(organizationId as string);
    
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const isMember = organization.members?.some(member => member.userId === session.user.id);
    if (!isMember) {
      return res.status(403).json({ error: 'Access denied' });
    }

    if (req.method === 'GET') {
      // Get all saved contact groups for the organization
      const savedGroups = await getSavedContactGroups(organizationId as string);
      
      return res.status(200).json({
        success: true,
        contactGroups: savedGroups
      });

    } else if (req.method === 'POST') {
      // Create a new saved contact group
      const { name, description, contacts, eventId } = req.body;

      if (!name || !contacts || !Array.isArray(contacts)) {
        return res.status(400).json({ 
          error: 'Name and contacts array are required' 
        });
      }

      // Validate contacts format
      const validContacts = contacts.filter(contact =>
        contact.email || contact.phone
      );

      // Allow empty groups when manually creating (no eventId)
      // But require contacts when saving from an event (has eventId)
      if (validContacts.length === 0 && eventId) {
        return res.status(400).json({
          error: 'At least one contact with email or phone is required when saving from an event'
        });
      }

      const contactGroupData: ContactGroupData = {
        name: name.trim(),
        contacts: validContacts.map(contact => ({
          email: contact.email || '',
          name: contact.name || '',
          phone: contact.phone || ''
        }))
      };

      // Add description if provided
      if (description) {
        (contactGroupData as any).description = description.trim();
      }

      const savedGroupIds = await saveContactGroups(
        organizationId as string,
        eventId || 'manual', // Use 'manual' if no eventId provided
        [contactGroupData]
      );
      
      // Get the newly created contact group to return it
      const db = Database.getInstance();
      const contactGroup = await db.readData('saved_contact_groups', savedGroupIds[0]);

      return res.status(201).json({
        success: true,
        message: 'Contact group created successfully',
        groupId: savedGroupIds[0],
        contactGroup // Return the full contact group object
      });

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Error handling contact groups request:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
