import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated and is an admin
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the current user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    // Handle GET request - fetch all users with pagination and search
    if (req.method === 'GET') {
      // Extract pagination and search parameters from query
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const searchQuery = req.query.search as string || '';
      const sortBy = req.query.sortBy as string || 'createdOn';
      const sortOrder = req.query.sortOrder as string || 'desc';

      // Get database instance
      const db = Database.getInstance();

      // Fetch users with pagination and search
      const { users, total } = await db.getAllUsers({
        page,
        limit,
        searchQuery,
        sortBy,
        sortOrder: sortOrder as 'asc' | 'desc'
      });

      // Return users list with pagination metadata
      return res.status(200).json({
        success: true,
        users,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      });
    }

    // If not GET, return method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error handling admin users request:', error);
    return res.status(500).json({ error: 'Failed to process admin users request' });
  }
}