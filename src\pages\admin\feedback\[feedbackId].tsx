import { useState, useEffect } from 'react';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { AdminLayout } from '@/components/layouts/AdminLayout';
import { withAdminAuth } from '@/lib/auth/admin';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { AlertCircle, ChevronLeft, User, Calendar, MessageSquare, Tag, MapPin, Star } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { useToast } from '@/components/ui/use-toast';
import Link from 'next/link';
import {
  Feedback,
  FeedbackComment,
  FeedbackStatus,
  FeedbackDetailResponse,
  feedbackStatusColors,
  formatFeedbackStatus
} from '@/types/feedback';

export default function AdminFeedbackDetail() {
  const router = useRouter();
  const { feedbackId } = router.query;
  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [comment, setComment] = useState('');
  const [newStatus, setNewStatus] = useState<FeedbackStatus>('new');
  const [submitting, setSubmitting] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const { toast } = useToast();

  // Fetch feedback data
  useEffect(() => {
    async function fetchFeedbackData() {
      if (!feedbackId) return;

      setLoading(true);
      try {
        const response = await fetch(`/api/admin/feedback/${feedbackId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch feedback');
        }

        const data = await response.json();
        setFeedback(data.feedback);
        setNewStatus(data.feedback.status); // Initialize the status selector
      } catch (err) {
        console.error('Error fetching feedback:', err);
        setError('Failed to load feedback data');
      } finally {
        setLoading(false);
      }
    }

    fetchFeedbackData();
  }, [feedbackId]);

  // Handle comment submission
  const handleCommentSubmit = async () => {
    if (!feedbackId) return;

    setSubmitting(true);
    try {
      const response = await fetch(`/api/admin/feedback/${feedbackId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          comment,
          status: newStatus !== feedback?.status ? newStatus : undefined,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit comment');
      }

      const data = await response.json();

      // Update the local feedback state with new comment and potential status change
      setFeedback(prev => {
        if (!prev) return null;

        const updatedFeedback = {
          ...prev,
          status: data.status || prev.status,
          comments: [...(prev.comments || []), data.comment],
        };

        return updatedFeedback;
      });

      // Clear comment field after submission
      setComment('');

      toast({
        title: 'Success',
        description: 'Your comment has been added',
      });
    } catch (err) {
      console.error('Error submitting comment:', err);
      toast({
        title: 'Error',
        description: 'Failed to add comment',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle status update
  const handleStatusUpdate = async (newStatus: FeedbackStatus) => {
    if (!feedbackId || !feedback || newStatus === feedback.status) return;

    setSubmitting(true);
    try {
      const response = await fetch(`/api/admin/feedback/${feedbackId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          comment: comment, // Include optional comment with status change
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update status');
      }

      const data = await response.json();

      // Update the local feedback state
      setFeedback(prev => {
        if (!prev) return null;

        const updatedFeedback = {
          ...prev,
          status: data.status,
          comments: [...(prev.comments || []), data.comment],
        };

        return updatedFeedback;
      });

      // Clear comment field after submission
      setComment('');

      // Close the dialog
      setDialogOpen(false);

      toast({
        title: 'Status Updated',
        description: `Feedback status changed to "${formatFeedbackStatus(newStatus)}"`,
      });
    } catch (err) {
      console.error('Error updating status:', err);
      toast({
        title: 'Error',
        description: 'Failed to update status',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'MMM d, yyyy h:mm a');
    } catch (e) {
      return dateString;
    }
  };

  if (loading) {
    return (
      <AdminLayout pageTitle="Feedback Details">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error || !feedback) {
    return (
      <AdminLayout pageTitle="Feedback Details">
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle size={48} className="text-destructive" />
          <p className="text-lg font-medium">{error || 'Feedback not found'}</p>
          <Link href="/admin/feedback" passHref>
            <Button variant="outline">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Feedback Inbox
            </Button>
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout pageTitle="Feedback Details">
      <div className="space-y-6">
        {/* Header with navigation and actions */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <Link href="/admin/feedback" passHref>
              <Button variant="outline" size="sm">
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back to Inbox
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">Feedback #{feedback.id}</h1>
          </div>

          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="default">Change Status</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Update Feedback Status</DialogTitle>
                <DialogDescription>
                  Change the status of this feedback and optionally add a comment.
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Current Status</label>
                  <div className="flex items-center">
                    <Badge className={feedbackStatusColors[feedback.status as FeedbackStatus]}>
                      {formatFeedbackStatus(feedback.status as FeedbackStatus)}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">New Status</label>
                  <Select
                    value={newStatus}
                    onValueChange={(value) => setNewStatus(value as FeedbackStatus)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a new status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new">New</SelectItem>
                      <SelectItem value="in-triage">In Triage</SelectItem>
                      <SelectItem value="in-progress">In Progress</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Comment (Optional)</label>
                  <Textarea
                    placeholder="Add a comment about this status change..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setNewStatus(feedback.status);
                    setComment('');
                    setDialogOpen(false);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => handleStatusUpdate(newStatus)}
                  disabled={newStatus === feedback.status || submitting}
                >
                  {submitting ? 'Updating...' : 'Update Status'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Feedback content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Feedback message */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <MessageSquare className="mr-2 h-5 w-5 text-muted-foreground" />
                    Feedback Message
                  </div>
                  <Badge className={feedbackStatusColors[feedback.status as FeedbackStatus]}>
                    {formatFeedbackStatus(feedback.status as FeedbackStatus)}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  Submitted on {formatDate(feedback.createdAt)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-lg whitespace-pre-wrap">
                  {feedback.message}
                </div>
              </CardContent>
            </Card>

            {/* Comments section */}
            <Card>
              <CardHeader>
                <CardTitle>Comments & Activity</CardTitle>
                <CardDescription>
                  {feedback.comments?.length || 0} comments
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {feedback.comments && feedback.comments.length > 0 ? (
                  <div className="space-y-4">
                    {feedback.comments.map((comment) => (
                      <div key={comment.id} className="bg-muted/30 p-4 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                              <User className="h-4 w-4 text-primary" />
                            </div>
                            <div className="ml-2">
                              <p className="text-sm font-medium">{comment.userName}</p>
                              <p className="text-xs text-muted-foreground">{comment.userEmail}</p>
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {formatDate(comment.createdAt)}
                          </p>
                        </div>

                        {comment.statusChange && (
                          <div className="mb-2 pb-2 border-b text-sm">
                            <span>Status changed from </span>
                            <Badge className={feedbackStatusColors[comment.statusChange.from as FeedbackStatus] || 'bg-gray-100'}>
                              {formatFeedbackStatus(comment.statusChange.from as FeedbackStatus)}
                            </Badge>
                            <span> to </span>
                            <Badge className={feedbackStatusColors[comment.statusChange.to as FeedbackStatus]}>
                              {formatFeedbackStatus(comment.statusChange.to as FeedbackStatus)}
                            </Badge>
                          </div>
                        )}

                        {comment.comment && (
                          <p className="whitespace-pre-wrap text-sm">{comment.comment}</p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-4">No comments yet</p>
                )}
              </CardContent>
              <Separator />
              <CardFooter className="p-4">
                <div className="w-full space-y-4">
                  <Textarea
                    placeholder="Add your comment..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="min-h-[100px]"
                  />
                  <div className="flex justify-end">
                    <Button
                      onClick={handleCommentSubmit}
                      disabled={!comment.trim() || submitting}
                    >
                      {submitting ? 'Submitting...' : 'Add Comment'}
                    </Button>
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>

          {/* Sidebar with metadata */}
          <div className="space-y-6">
            {/* Basic info */}
            <Card>
              <CardHeader>
                <CardTitle>Feedback Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium flex items-center">
                    <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                    Date Submitted
                  </p>
                  <p className="text-sm">{formatDate(feedback.createdAt)}</p>
                </div>

                {feedback.category && (
                  <div className="space-y-1">
                    <p className="text-sm font-medium flex items-center">
                      <Tag className="mr-2 h-4 w-4 text-muted-foreground" />
                      Category
                    </p>
                    <p className="text-sm">{feedback.category}</p>
                  </div>
                )}

                {feedback.rating && (
                  <div className="space-y-1">
                    <p className="text-sm font-medium flex items-center">
                      <Star className="mr-2 h-4 w-4 text-muted-foreground" />
                      Rating
                    </p>
                    <p className="text-sm">{feedback.rating} / 5</p>
                  </div>
                )}

                <div className="space-y-1">
                  <p className="text-sm font-medium flex items-center">
                    <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                    Page
                  </p>
                  <p className="text-sm">{feedback.page}</p>
                </div>

                <div className="space-y-1">
                  <p className="text-sm font-medium flex items-center">
                    <User className="mr-2 h-4 w-4 text-muted-foreground" />
                    Submitted By
                  </p>
                  <p className="text-sm">
                    {feedback.userEmail || 'Anonymous'}
                    {feedback.userId && (
                      <Link href={`/admin/users/${feedback.userId}`} passHref>
                        <Button variant="link" className="p-0 h-auto text-sm">
                          View User
                        </Button>
                      </Link>
                    )}
                  </p>
                </div>

                <div className="space-y-1">
                  <p className="text-sm font-medium">ReCAPTCHA Score</p>
                  <p className="text-sm">{feedback.recaptchaScore}</p>
                </div>
              </CardContent>
            </Card>

            {/* Session data */}
            {feedback.sessionData && (
              <Card>
                <CardHeader>
                  <CardTitle>Session Data</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {feedback.sessionData.deviceType && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Device</p>
                      <p className="text-sm">{feedback.sessionData.deviceType}</p>
                    </div>
                  )}

                  {feedback.sessionData.browser && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Browser</p>
                      <p className="text-sm">{feedback.sessionData.browser}</p>
                    </div>
                  )}

                  {feedback.sessionData.os && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Operating System</p>
                      <p className="text-sm">{feedback.sessionData.os}</p>
                    </div>
                  )}

                  {feedback.sessionData.referrer && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Referrer</p>
                      <p className="text-sm break-all">{feedback.sessionData.referrer}</p>
                    </div>
                  )}

                  {feedback.sessionData.timeOnPage && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Time on Page</p>
                      <p className="text-sm">
                        {Math.round(feedback.sessionData.timeOnPage / 1000)} seconds
                      </p>
                    </div>
                  )}

                  {feedback.sessionData.pagesVisited && feedback.sessionData.pagesVisited.length > 0 && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Pages Visited</p>
                      <div className="text-sm space-y-1">
                        {feedback.sessionData.pagesVisited.map((page: string, i: number) => (
                          <div key={i} className="bg-muted/30 px-2 py-1 rounded">
                            {page}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}

// Server-side protection for admin routes
export const getServerSideProps: GetServerSideProps = withAdminAuth();
