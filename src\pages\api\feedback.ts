// filepath: /Users/<USER>/kitchen-sink/iac/universe/src/pages/api/feedback.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { getToken } from 'next-auth/jwt';
import { Database } from '@/lib/database';
import { z } from 'zod';
import { GenerateID } from '@/lib/ID';
import { debugLog, log } from '@/lib/logger';

// Define reCAPTCHA response interface
interface RecaptchaResponse {
  success: boolean;
  score: number;
  action: string;
  challenge_ts: string;
  hostname: string;
  'error-codes'?: string[];
}

// Define validation schema for feedback request
const feedbackSchema = z.object({
  message: z.string().min(1, 'Feedback message is required'),
  rating: z.number().min(1).max(5).optional(),
  category: z.string().optional(),
  source: z.string().optional(),
  page: z.string().optional(),
  sessionData: z.record(z.any()).optional(), // Additional session data from feedback component
  recaptchaToken: z.string().min(1, 'reCAPTCHA token is required')
});

/**
 * Verify reCAPTCHA token with Google's verification API
 * @param token The reCAPTCHA token to verify
 * @returns The reCAPTCHA verification response
 */
async function verifyRecaptchaToken(token: string): Promise<RecaptchaResponse> {
  if (!token) {
    log('Empty reCAPTCHA token provided');
    return {
      success: false,
      score: 0,
      action: '',
      challenge_ts: '',
      hostname: '',
    };
  }

  try {
    const verifyUrl = `https://www.google.com/recaptcha/api/siteverify?secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${token}`;

    const response = await fetch(verifyUrl, { method: 'POST' });

    if (!response.ok) {
      const errorText = await response.text();
      log('reCAPTCHA API returned error', {
        status: response.status,
        statusText: response.statusText,
        errorText
      });
      throw new Error(`Failed to verify reCAPTCHA token: ${response.status} ${response.statusText}`);
    }

    const result = await response.json() as RecaptchaResponse;

    // Log the verification result
    log('reCAPTCHA verification result', {
      success: result.success,
      score: result.score,
      action: result.action,
      hostname: result.hostname,
      errorCodes: result['error-codes']
    });

    return result;
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    log('reCAPTCHA verification exception', { error: (error as Error).message });
    return {
      success: false,
      score: 0,
      action: '',
      challenge_ts: '',
      hostname: '',
      'error-codes': [(error as Error).message]
    };
  }
}

/**
 * API endpoint to handle user feedback submissions
 * 
 * @param req - NextApiRequest object
 * @param res - NextApiResponse object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Validate request body
    const validationResult = feedbackSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.error.errors
      });
    }

    // Extract feedback data
    const { message, rating, category, source, page, sessionData, recaptchaToken } = validationResult.data;

    // Verify reCAPTCHA token with a threshold of 0.6 (similar to contact form)
    const recaptchaVerified = await verifyRecaptchaToken(recaptchaToken);

    if (!recaptchaVerified.success || recaptchaVerified.score < 0.6) {
      log('reCAPTCHA verification failed or score too low', {
        success: recaptchaVerified.success,
        score: recaptchaVerified.score,
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
        userAgent: req.headers['user-agent']
      });
      return res.status(400).json({
        error: 'Security verification failed. Please try again.'
      });
    }

    // Get user information if authenticated
    let userId: string | null = null;
    let userEmail: string | null = null;

    const token = await getToken({ req, secret: process.env.AUTH_SECRET });
    if (token) {
      userId = token.sub as string;
      userEmail = token.email as string;
    }

    // Generate a unique ID for the feedback
    const feedbackId = GenerateID('F');

    // Prepare feedback data
    const feedbackData = {
      id: feedbackId,
      message,
      rating,
      category: category || 'general',
      source: source || 'website',
      page: page || 'unknown',
      userId,
      userEmail,
      sessionData: sessionData || {}, // Include session data from frontend
      createdAt: new Date().toISOString(),
      status: 'new',
      recaptchaScore: recaptchaVerified.score
    };

    // Get database instance
    const db = Database.getInstance();

    // Save feedback to Firestore
    await db.addData('feedback', feedbackData);

    debugLog('Feedback submitted:', feedbackData);

    // Return success response
    return res.status(201).json({
      success: true,
      message: 'Feedback received successfully',
      id: feedbackId
    });
  } catch (error) {
    console.error('Error in feedback handler:', error);
    return res.status(500).json({ error: 'Failed to save feedback' });
  }
}
