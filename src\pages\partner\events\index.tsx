'use client'

import { useState } from "react"
import { PartnerLayout } from "@/components/layouts/PartnerLayout"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/router"
import { Calendar, Plus, MapPin, User, Clock, Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useSession } from "next-auth/react"
import { useEvents } from "@/hooks/useEvents"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import dayjs from "dayjs"
import { EventListItem } from "@/types"

export default function PartnerEvents() {
  const router = useRouter()
  const { data: session } = useSession()
  const { events, loading, error } = useEvents()
  const [searchTerm, setSearchTerm] = useState("")
  
  // Filter events created by this partner
  const partnerEvents = events.filter(event => event.organizationId === session?.user?.organization?.id)
  
  // Filter events based on search term
  const filteredEvents = partnerEvents.filter(event => 
    event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.host.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  // Determine event status based on date and time
  const upcomingEvents = filteredEvents.filter((event) => new Date(event.date as Date) > new Date())
  const pastEvents = filteredEvents.filter((event) => new Date(event.date as Date) <= new Date())
  
  return (
    <PartnerLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Events Management</h1>
            <p className="text-muted-foreground">Manage events created for your customers</p>
          </div>
          <Button 
            variant="primary-button" 
            onClick={() => router.push('/partner/events/new')}
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Event
          </Button>
        </div>
        
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input 
            placeholder="Search events by name, location, or host..." 
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        {/* Events List */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <Card className="text-center p-8">
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="bg-red-100 p-4 rounded-full">
                <Calendar className="h-12 w-12 text-red-400" />
              </div>
              <CardTitle>Error Loading Events</CardTitle>
              <CardDescription>
                {error.message}
              </CardDescription>
              <Button onClick={() => window.location.reload()} className="mt-2">
                Try Again
              </Button>
            </div>
          </Card>
        ) : filteredEvents.length === 0 ? (
          <Card className="text-center p-8">
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="bg-gray-100 p-4 rounded-full">
                <Calendar className="h-12 w-12 text-gray-400" />
              </div>
              <CardTitle>No Events Found</CardTitle>
              <CardDescription>
                {searchTerm ? "No events match your search criteria." : "You haven't created any events yet."}
              </CardDescription>
              {!searchTerm && (
                <Button variant="primary-button" onClick={() => router.push('/partner/events/new')} className="mt-2">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Event
                </Button>
              )}
            </div>
          </Card>
        ) : (
          <Tabs defaultValue="upcoming" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="upcoming">Upcoming ({upcomingEvents.length})</TabsTrigger>
              <TabsTrigger value="past">Past ({pastEvents.length})</TabsTrigger>
            </TabsList>
            
            <TabsContent value="upcoming">
              {upcomingEvents.length === 0 ? (
                <Card className="text-center p-6">
                  <CardDescription>
                    You don&apos;t have any upcoming events.
                  </CardDescription>
                  <Button variant="primary-button" onClick={() => router.push('/partner/events/new')} className="mt-4">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Event
                  </Button>
                </Card>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {upcomingEvents.map((event) => renderEventCard(event, router))}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="past">
              {pastEvents.length === 0 ? (
                <Card className="text-center p-6">
                  <CardDescription>
                    You don&apos;t have any past events.
                  </CardDescription>
                </Card>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {pastEvents.map((event) => renderEventCard(event, router))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}
      </div>
    </PartnerLayout>
  )
}

function renderEventCard(event: EventListItem, router: any) {
  return (
    <Card
      key={event.id}
      className="cursor-pointer hover:shadow-md transition-shadow"
      onClick={() => router.push(`/event/${event.id}`)}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="flex-1">{event.name}</CardTitle>
          {event.plan && (
            <Badge className="bg-purple-500 hover:bg-purple-600 ml-2">
              {event.plan === 'host_plus' ? 'Host+' : event.plan === 'host_pro' ? 'Host Pro' : 'Free'}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-2 text-sm">
          <div className="flex items-start gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground mt-0.5" />
            <span>
              {dayjs(event.date).format("dddd, MMMM D, YYYY")}
              {event.start && event.end && (
                <span className="ml-1">
                  ({event.start} - {event.end})
                </span>
              )}
            </span>
          </div>
          <div className="flex items-start gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
            <span>{event.location}</span>
          </div>
          <div className="flex items-start gap-2">
            <User className="h-4 w-4 text-muted-foreground mt-0.5" />
            <span>Hosted by {event.host}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        <Button
          variant="outline"
          className="w-full"
          onClick={(e) => {
            e.stopPropagation()
            router.push(`/event/${event.id}`)
          }}
        >
          View Details
        </Button>
      </CardFooter>
    </Card>
  );
}
