# IAC BFF API

Routes Summary:

- `POST /api/auth/generate-token`: generate a token for a user

- `GET /api/events`: Returns a list of events for the user
- `GET /api/events/:eventId`: Returns a single event for the user
- `POST /api/events`: Creates a new event for the user
- `PUT /api/events/:eventId`: Updates an event for the user

- `GET /api/events/:eventId/invites`: Returns a list of invites for the event
- `POST /api/events/:eventId/invites`: Creates a new invite for the event
- `GET /api/events/:eventId/invites/:inviteId`: Returns a single invite for the event
- `PUT /api/events/:eventId/invites/:inviteId`: Updates an invite for the event
- `PUT /api/events/:eventId/invites/:inviteId/rsvp`: RSVP an invite for the event