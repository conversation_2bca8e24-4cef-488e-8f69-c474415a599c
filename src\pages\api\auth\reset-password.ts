import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { validatePassword } from '@/lib/auth/password';
import { z } from 'zod';
import { withAuth } from '@/lib/auth/api';
import { debugLog } from '@/lib/logger';

// Define validation schema for password reset request
const resetPasswordSchema = z.object({
  currentPassword: z.string(),
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
});

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get the user session (attached by withAuth middleware)
    const session = (req as any).session;
    if (!session || !session.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Validate request body
    const validationResult = resetPasswordSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: validationResult.error.errors 
      });
    }

    const { currentPassword, newPassword } = validationResult.data;

    // Validate new password strength
    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.success) {
      return res.status(400).json({ error: passwordValidation.error });
    }

    // Get database instance
    const db = Database.getInstance();

    // Get user profile
    const userProfile = await db.getUserProfile(session.user.id);
    if (!userProfile) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify current password
    const { verifyPassword } = await import('@/lib/auth/password');
    if (!userProfile.passwordHash || !(await verifyPassword(currentPassword, userProfile.passwordHash))) {
      return res.status(401).json({ error: 'Current password is incorrect' });
    }

    // Update password
    await db.updateUserPassword(session.user.id, newPassword);

    debugLog('Password updated successfully for user:', session.user.id);

    // Return success response
    return res.status(200).json({ 
      success: true, 
      message: 'Password updated successfully' 
    });
  } catch (error) {
    console.error('Error in reset password handler:', error);
    return res.status(500).json({ error: 'Failed to update password' });
  }
}

export default withAuth(handler);
