import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';

export function useSendInviteEmail() {
  const [sendingEmail, setSendingEmail] = useState(false);
  const { toast } = useToast();

  const sendEmail = async (eventId: string, inviteId: string, inviteEmail?: string) => {
    if (!inviteEmail) {
      toast({
        title: "No email available",
        description: "This invite doesn't have an email address.",
        variant: "destructive"
      });
      return false;
    }

    setSendingEmail(true);
    try {
      // Call the API endpoint to send the invitation email
      const response = await fetch(`/api/event/${eventId}/invites/${inviteId}/send-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send invitation email');
      }

      toast({
        title: "Email sent!",
        description: `Invitation sent to ${inviteEmail}`,
      });
      return true;
    } catch (error) {
      console.error('Error sending invitation email:', error);
      toast({
        title: "Error sending email",
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: "destructive"
      });
      return false;
    } finally {
      setSendingEmail(false);
    }
  };

  return {
    sendEmail,
    sendingEmail
  };
}
