// =============================================================================
// UNIFIED RBAC SYSTEM - IMPORTS FROM PORTAL FILES
// =============================================================================

// Import core permissions and types
export { permissions, portals, type Permission, type Portal } from './permissions';

// Import Partner Portal definitions
import {
  partnerRoles,
  partnerResources,
  partnerRolePermissions,
  hasPartnerPermission,
  getPartnerRolePermissions,
  type PartnerRole,
  type PartnerResource
} from './partner';

// Import Admin Portal definitions
import {
  adminRoles,
  adminResources,
  adminRolePermissions,
  hasAdminPermission,
  getAdminRolePermissions,
  type AdminRole,
  type AdminResource
} from './admin';

// Re-export portal-specific definitions
export {
  // Partner Portal
  partnerRoles,
  partnerResources,
  partnerRolePermissions,
  hasPartnerPermission,
  getPartnerRolePermissions,
  type PartnerRole,
  type PartnerResource,

  // Admin Portal
  adminRoles,
  adminResources,
  adminRolePermissions,
  hasAdminPermission,
  getAdminRolePermissions,
  type AdminRole,
  type AdminResource
};

// =============================================================================
// UNIFIED TYPES AND HELPER FUNCTIONS
// =============================================================================

import { permissions, portals, type Permission, type Portal } from './permissions';

// Unified role types
export type Role = PartnerRole | AdminRole;
export type Resource = PartnerResource | AdminResource;

/**
 * Universal permission checker that works for both portals
 */
export function hasPermission(
  role: Role,
  resource: string,
  permission: Permission,
  context?: { venueId?: string }
): boolean {
  // Determine portal from role
  if (role.startsWith('partner:')) {
    return hasPartnerPermission(role as PartnerRole, resource, permission, context?.venueId);
  } else if (role.startsWith('admin:')) {
    return hasAdminPermission(role as AdminRole, resource, permission);
  }

  return false;
}

/**
 * Get all permissions for a role (works for both portals)
 */
export function getRolePermissions(role: Role): Record<string, Permission[]> {
  if (role.startsWith('partner:')) {
    return getPartnerRolePermissions(role as PartnerRole);
  } else if (role.startsWith('admin:')) {
    return getAdminRolePermissions(role as AdminRole);
  }

  return {};
}

/**
 * Check if a role belongs to a specific portal
 */
export function isPortalRole(role: Role, portal: Portal): boolean {
  return role.startsWith(`${portal}:`);
}

/**
 * Get all roles for a specific portal
 */
export function getPortalRoles(portal: Portal): readonly string[] {
  if (portal === portals.PARTNER) {
    return Object.values(partnerRoles);
  } else if (portal === portals.ADMIN) {
    return Object.values(adminRoles);
  }

  return [];
}

/**
 * Get all resources for a specific portal
 */
export function getPortalResources(portal: Portal): readonly string[] {
  if (portal === portals.PARTNER) {
    return Object.values(partnerResources);
  } else if (portal === portals.ADMIN) {
    return Object.values(adminResources);
  }

  return [];
}

/**
 * Utility function to create venue-specific resource strings
 */
export function createVenueResource(baseResource: string, venueId: string): string {
  return baseResource.replace(':venue', `:venue:${venueId}`);
}

/**
 * Check if a user has multiple roles and aggregate their permissions
 */
export function hasMultiRolePermission(
  roles: Role[],
  resource: string,
  permission: Permission,
  context?: { venueId?: string }
): boolean {
  return roles.some(role => hasPermission(role, resource, permission, context));
}

/**
 * Get aggregated permissions for multiple roles
 */
export function getMultiRolePermissions(roles: Role[]): Record<string, Permission[]> {
  const aggregatedPermissions: Record<string, Set<Permission>> = {};

  roles.forEach(role => {
    const rolePermissions = getRolePermissions(role);
    Object.entries(rolePermissions).forEach(([resource, permissions]) => {
      if (!aggregatedPermissions[resource]) {
        aggregatedPermissions[resource] = new Set();
      }
      permissions.forEach(permission => {
        aggregatedPermissions[resource].add(permission);
      });
    });
  });

  // Convert Sets back to arrays
  const result: Record<string, Permission[]> = {};
  Object.entries(aggregatedPermissions).forEach(([resource, permissionSet]) => {
    result[resource] = Array.from(permissionSet);
  });

  return result;
}

// =============================================================================
// RBAC CONFIGURATION EXPORT
// =============================================================================

// Complete RBAC Configuration
export const rbacConfig = {
  permissions,
  portals,

  // Partner Portal
  partner: {
    roles: partnerRoles,
    resources: partnerResources,
    rolePermissions: partnerRolePermissions,
  },

  // Admin Portal
  admin: {
    roles: adminRoles,
    resources: adminResources,
    rolePermissions: adminRolePermissions,
  },
} as const;

// Default export
export default rbacConfig;
