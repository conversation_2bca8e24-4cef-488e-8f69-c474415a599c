import React, { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { DatePicker } from './DatePicker';

const meta = {
  title: 'Components/DatePicker',
  component: DatePicker,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    disabled: { control: 'boolean' },
    className: { control: 'text' },
  },
} satisfies Meta<typeof DatePicker>;

// This default export is required to properly index the stories
export default meta;
type Story = StoryObj<typeof DatePicker>;

// Using a render function because DatePicker needs state management
const Template = (args: any) => {
  const [date, setDate] = useState<Date | undefined>(args.value);
  return <DatePicker {...args} value={date} onChange={setDate} />;
};

export const Default: Story = {
  render: (args) => <Template {...args} />,
};

export const WithSelectedDate: Story = {
  render: (args) => <Template {...args} />,
  args: {
    value: new Date(),
  },
};

export const Disabled: Story = {
  render: (args) => <Template {...args} />,
  args: {
    disabled: true,
  },
};

export const CustomStyling: Story = {
  render: (args) => <Template {...args} />,
  args: {
    className: 'border-primary',
  },
};