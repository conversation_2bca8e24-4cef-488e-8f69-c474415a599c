import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { AdminLayout } from "@/components/layouts/AdminLayout";
import { withAdminAuth } from "@/lib/auth/admin";
import { GetServerSideProps } from "next";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { formatTimeAgo, FormatDate } from "@/lib/dayjs";
import {
  Building2,
  Calendar,
  ChevronLeft,
  Shield,
  Users,
  ExternalLink,
  UserPlus,
  Trash2,
  AlertCircle,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { Organization, UserProfile, Event } from "@/types";
import Link from "next/link";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

export default function OrganizationDetailsPage() {
  const router = useRouter();
  const { organizationId } = router.query;
  const { toast } = useToast();

  const [organization, setOrganization] = useState<Organization | null>(null);
  const [members, setMembers] = useState<UserProfile[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMembers, setLoadingMembers] = useState<boolean>(false);
  const [loadingEvents, setLoadingEvents] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Member management state
  const [showAddMemberDialog, setShowAddMemberDialog] = useState<boolean>(false);
  const [newMemberEmail, setNewMemberEmail] = useState<string>('');
  const [addingMember, setAddingMember] = useState<boolean>(false);
  const [removingMemberId, setRemovingMemberId] = useState<string | null>(null);

  // Fetch organization details
  useEffect(() => {
    if (!organizationId) return;

    const fetchOrganization = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/admin/organizations/${organizationId}`);

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        setOrganization(data.organization);
        setError(null);

        // Pre-fetch members when organization is loaded
        if (data.organization && data.organization.members && data.organization.members.length > 0) {
          fetchMembers();
        }
      } catch (err) {
        setError('Failed to load organization details');
      } finally {
        setLoading(false);
      }
    };

    fetchOrganization();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organizationId]);

  // Fetch organization members
  const fetchMembers = async () => {
    if (!organizationId) return;

    try {
      setLoadingMembers(true);
      const response = await fetch(`/api/admin/organizations/${organizationId}/members`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      if (data.members && Array.isArray(data.members)) {
        // Ensure we're getting all members, including placeholders for unknown users
        if (data.members.length === 0 && organization?.members && organization.members.length > 0) {
          // If API returned no members but organization has members, create placeholders
          const placeholders = organization.members.map(member => ({
            id: member.userId,
            email: 'Unknown user',
            name: 'Unknown user',
            isProfileComplete: false,
            isWelcomeEmailSent: false
          } as UserProfile));
          setMembers(placeholders);
        } else {
          setMembers(data.members);
        }
      } else {
        setMembers([]);
      }
    } catch (err) {
      // Create placeholder members if API call fails but organization has members
      if (organization?.members && organization.members.length > 0) {
        const placeholders = organization.members.map(member => ({
          id: member.userId,
          email: 'Unknown user',
          name: 'Unknown user',
          isProfileComplete: false,
          isWelcomeEmailSent: false
        } as UserProfile));
        setMembers(placeholders);
      } else {
        setMembers([]);
      }
    } finally {
      setLoadingMembers(false);
    }
  };

  // Fetch organization events
  const fetchEvents = async () => {
    if (!organizationId) return;

    try {
      setLoadingEvents(true);
      const response = await fetch(`/api/admin/organizations/${organizationId}/events`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      setEvents(data.events);
    } catch (err) {
      // Handle error silently
    } finally {
      setLoadingEvents(false);
    }
  };

  // Handle tab changes
  const handleTabChange = (value: string) => {
    if (value === 'members' && members.length === 0 && !loadingMembers) {
      fetchMembers();
    } else if (value === 'events' && events.length === 0 && !loadingEvents) {
      fetchEvents();
    }
  };

  // Toggle partner status
  const handleTogglePartnerStatus = async () => {
    if (!organization) return;

    try {
      const response = await fetch(`/api/admin/organizations/${organizationId}/toggle-partner`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      // Update the organization in state
      setOrganization({
        ...organization,
        type: data.organization.type
      });

      toast({
        title: "Success",
        description: `Organization is now ${data.organization.type === 'partner' ? 'a partner' : 'an individual organization'}.`,
        variant: "default",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to update partner status.",
        variant: "destructive",
      });
      console.error(err);
    }
  };

  // Add a new member to the organization
  const handleAddMember = async () => {
    if (!organization || !newMemberEmail.trim()) return;

    try {
      setAddingMember(true);

      const response = await fetch(`/api/admin/organizations/${organizationId}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: newMemberEmail.trim()
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      const data = await response.json();

      // Update the organization in state
      if (data.organization) {
        setOrganization(data.organization);
      }

      // Refresh the members list
      fetchMembers();

      // Reset the form
      setNewMemberEmail('');
      setShowAddMemberDialog(false);

      toast({
        title: "Success",
        description: data.message || "Member added successfully.",
        variant: "default",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to add member.",
        variant: "destructive",
      });
      console.error(err);
    } finally {
      setAddingMember(false);
    }
  };

  // Remove a member from the organization
  const handleRemoveMember = async (memberId: string) => {
    if (!organization) return;

    try {
      setRemovingMemberId(memberId);

      const response = await fetch(`/api/admin/organizations/${organizationId}/members/${memberId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      const data = await response.json();

      // Update the organization in state
      if (data.organization) {
        setOrganization(data.organization);
      }

      // Refresh the members list
      fetchMembers();

      toast({
        title: "Success",
        description: data.message || "Member removed successfully.",
        variant: "default",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to remove member.",
        variant: "destructive",
      });
      console.error(err);
    } finally {
      setRemovingMemberId(null);
    }
  };

  if (loading) {
    return (
      <AdminLayout pageTitle="Organization Details">
        <div className="flex items-center space-x-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Organization Details</h1>
        </div>

        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader className="pb-4">
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-40" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    );
  }

  if (error || !organization) {
    return (
      <AdminLayout pageTitle="Error">
        <div className="flex items-center space-x-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Error</h1>
        </div>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <p className="text-red-600 mb-4">{error || "Organization not found"}</p>
              <Button onClick={() => router.push('/admin/organizations')}>
                Return to Organizations
              </Button>
            </div>
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout pageTitle={organization.name || 'Organization Details'}>
      {/* Back button and title */}
      <div className="flex items-center space-x-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Organization Details</h1>

        {organization.type === 'partner' && (
          <Badge className="ml-2 bg-blue-100 text-blue-800 flex items-center gap-1">
            <Shield className="h-3 w-3" /> Partner
          </Badge>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6">
        {/* Organization Profile Card */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>{organization.name}</CardTitle>
                <CardDescription>
                  Created {formatTimeAgo(organization.createdOn)}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Building2 className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Organization Type:</span>
                </div>
                <Badge
                  variant="outline"
                  className={
                    organization.type === 'partner'
                      ? 'bg-blue-50 text-blue-700 border-blue-200'
                      : 'bg-gray-50 text-gray-700 border-gray-200'
                  }
                >
                  {organization.type === 'partner' ? 'Partner' : 'Individual'}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Created On:</span>
                </div>
                <span>{FormatDate(organization.createdOn)}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Members:</span>
                </div>
                <span>{organization.members?.length || 0}</span>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="partner-status" className="text-base">Partner Status</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable partner features for this organization
                  </p>
                </div>
                <Switch
                  id="partner-status"
                  checked={organization.type === 'partner'}
                  onCheckedChange={handleTogglePartnerStatus}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs for Members and Events */}
        <div className="bg-white rounded-lg shadow">
          <Tabs defaultValue="members" onValueChange={handleTabChange}>
            <div className="px-4 pt-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="members">Members</TabsTrigger>
                <TabsTrigger value="events">Events</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="members">
              <Card className="border-0 shadow-none">
                <CardContent className="p-4">
                  {loadingMembers ? (
                    <div className="space-y-4 py-4">
                      {Array.from({ length: 3 }).map((_, i) => (
                        <div key={i} className="flex items-center justify-between">
                          <Skeleton className="h-10 w-1/2" />
                          <Skeleton className="h-8 w-20" />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div>
                      {/* Add Member Button */}
                      <div className="flex justify-end mb-4">
                        <Dialog open={showAddMemberDialog} onOpenChange={setShowAddMemberDialog}>
                          <DialogTrigger asChild>
                            <Button size="sm" className="flex items-center gap-1">
                              <UserPlus className="h-4 w-4" />
                              Add Member
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Add Organization Member</DialogTitle>
                              <DialogDescription>
                                Enter the email address of the user you want to add to this organization.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="py-4">
                              <Label htmlFor="email" className="mb-2 block">Email Address</Label>
                              <Input
                                id="email"
                                type="email"
                                placeholder="<EMAIL>"
                                value={newMemberEmail}
                                onChange={(e) => setNewMemberEmail(e.target.value)}
                                className="w-full"
                              />
                            </div>
                            <DialogFooter>
                              <Button
                                variant="outline"
                                onClick={() => setShowAddMemberDialog(false)}
                                disabled={addingMember}
                              >
                                Cancel
                              </Button>
                              <Button
                                onClick={handleAddMember}
                                disabled={!newMemberEmail.trim() || addingMember}
                              >
                                {addingMember ? 'Adding...' : 'Add Member'}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>

                      {members.length === 0 && (!organization.members || organization.members.length === 0) ? (
                        <div className="text-center py-8 text-muted-foreground">
                          No members found for this organization.
                        </div>
                      ) : members.length === 0 && organization.members && organization.members.length > 0 ? (
                        <div className="text-center py-8 text-amber-600 flex flex-col items-center gap-2">
                          <AlertCircle className="h-8 w-8" />
                          <p>Members exist in the organization but could not be loaded properly.</p>
                          <p className="text-sm">Please refresh the page or try again later.</p>
                        </div>
                      ) : (
                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Role</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {members.map((member) => (
                                <TableRow key={member.id}>
                                  <TableCell className="font-medium">
                                    {member.name || (member.email === 'Unknown user' ?
                                      <span className="text-amber-600 flex items-center gap-1">
                                        <AlertCircle className="h-3.5 w-3.5" />
                                        Unknown User
                                      </span> :
                                      'Unnamed User')}
                                  </TableCell>
                                  <TableCell>
                                    {member.email === 'Unknown user' ?
                                      <span className="text-amber-600 flex items-center gap-1">
                                        <AlertCircle className="h-3.5 w-3.5" />
                                        User not found in database
                                      </span> :
                                      member.email}
                                  </TableCell>
                                  <TableCell>
                                    {organization.members?.find(m => m.userId === member.id)?.role || ('member' as 'owner' | 'member')}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <div className="flex items-center justify-end gap-2">
                                      <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={() => handleRemoveMember(member.id)}
                                        disabled={removingMemberId === member.id}
                                      >
                                        {removingMemberId === member.id ? (
                                          'Removing...'
                                        ) : (
                                          <>
                                            <Trash2 className="h-3.5 w-3.5 mr-1" />
                                            Remove
                                          </>
                                        )}
                                      </Button>
                                      <Link href={`/admin/users/${member.id}`} passHref>
                                        <Button variant="outline" size="sm">
                                          <ExternalLink className="h-3.5 w-3.5 mr-1" />
                                          View
                                        </Button>
                                      </Link>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="events">
              <Card className="border-0 shadow-none">
                <CardContent className="p-4">
                  {loadingEvents ? (
                    <div className="space-y-4 py-4">
                      {Array.from({ length: 3 }).map((_, i) => (
                        <div key={i} className="flex items-center justify-between">
                          <Skeleton className="h-10 w-1/2" />
                          <Skeleton className="h-8 w-20" />
                        </div>
                      ))}
                    </div>
                  ) : events.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No events found for this organization.
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Event Name</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Location</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {events.map((event) => (
                            <TableRow key={event.ID}>
                              <TableCell className="font-medium">{event.eventName}</TableCell>
                              <TableCell>{FormatDate(event.eventDate)}</TableCell>
                              <TableCell>{event.location}</TableCell>
                              <TableCell>
                                <Badge
                                  variant="outline"
                                  className={
                                    event.status === "active"
                                      ? "bg-green-50 text-green-700 border-green-200"
                                      : event.status === "pending_payment"
                                        ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                        : "bg-gray-50 text-gray-700 border-gray-200"
                                  }
                                >
                                  {event.status === "active"
                                    ? "Active"
                                    : event.status === "pending_payment"
                                      ? "Pending Payment"
                                      : event.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <Link href={`/event/${event.ID}`} passHref>
                                  <Button variant="outline" size="sm">
                                    <ExternalLink className="h-3.5 w-3.5 mr-1" />
                                    View
                                  </Button>
                                </Link>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </AdminLayout>
  );
}

// Server-side protection for admin routes
export const getServerSideProps: GetServerSideProps = withAdminAuth();
