import { useState, useEffect } from 'react';

export interface ActivityEvent {
  type: string;
  timestamp: string;
  userId: string | null;
  properties: Record<string, any>;
}

export function useInviteActivity(eventId?: string, inviteId?: string) {
  const [activities, setActivities] = useState<ActivityEvent[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!eventId || !inviteId) {
      setLoading(false);
      return;
    }

    const fetchActivity = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/event/${eventId}/invites/${inviteId}/activity`);
        
        if (!response.ok) {
          throw new Error(`Error fetching activity: ${response.statusText}`);
        }
        
        const data = await response.json();
        setActivities(data.activities || []);
        setError(null);
      } catch (err) {
        console.error('Error fetching invite activity:', err);
        setError(err instanceof Error ? err : new Error(String(err)));
        setActivities([]);
      } finally {
        setLoading(false);
      }
    };

    fetchActivity();
  }, [eventId, inviteId]);

  return { activities, loading, error };
}