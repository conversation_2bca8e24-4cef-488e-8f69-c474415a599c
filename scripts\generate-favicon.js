const sharp = require('sharp');
const path = require('path');
const { copyFileSync } = require('fs');

const svgPathSmall = path.join(__dirname, '../public/logo-small.svg');
const outputPath = path.join(__dirname, '../src/app/icon.ico');

sharp(svgPathSmall)
  .resize(48, 48, {
    fit: 'contain',
    background: { r: 255, g: 255, b: 255, alpha: 0 } // Transparent background
  })
  .toFile(outputPath, (err, info) => {
    if (err) {
      console.error('Error generating favicon:', err);
    } else {
      console.log('Favicon generated successfully!', info);
      // copyFileSync(outputPath, path.join(__dirname, '../public/favicon.ico'));
    }
  });