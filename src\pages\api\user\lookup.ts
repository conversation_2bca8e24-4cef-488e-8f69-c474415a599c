import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { UserProfile } from '@/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Authenticate the request
    const session = await getServerSession(req, res, authConfig);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Validate request body
    const { userIds } = req.body;
    if (!userIds || !Array.isArray(userIds)) {
      return res.status(400).json({ error: 'Invalid request - userIds array is required' });
    }

    // Get database instance
    const db = Database.getInstance();

    // Look up each user profile
    const users = await Promise.all(
      userIds.map(async (userId) => {
        const profile = await db.getUserProfile(userId);
        if (!profile) return null;
        
        // Return only the necessary information
        return {
          id: profile.id,
          email: profile.email,
          name: profile.name || undefined
        };
      })
    );

    // Filter out null values and return results
    return res.status(200).json({
      success: true,
      users: users.filter(Boolean)
    });
  } catch (error) {
    console.error('Error looking up users:', error);
    return res.status(500).json({ error: 'Failed to look up users' });
  }
}