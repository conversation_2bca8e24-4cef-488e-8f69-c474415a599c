# Shortlink Migration to External Service

This document provides step-by-step instructions for migrating from the internal shortlink system to the external shortlink service at `l.iamcoming.io`.

## What Changed

- All shortlink creation/management now uses the external API service at `l.iamcoming.io`
- The existing shortlink API routes have been updated to use the external service
- JWT authentication is used to securely communicate with the external service
- The campaign shortlink API has been updated to create shortlinks on the external service

## Setup Instructions

1. **Configure Environment Variables**

   Make sure both `.env.local` and `.env.production` have these variables:
   
   ```
   # Shortlink API Configuration
   SHORTLINK_JWT_SECRET=f7a4c9e2b5d1836gho7k5j3i8l2m9n0pq
   SHORTLINK_JWT_TOKEN=UsJILRwbOxoqXAxWzxtVl9_w4CzacoMQh5qq9p6l0gclkhuU-E4CkaAuoOPa_aTstKRvoYMl2jT6lPFyeRO3Ig
   ```

2. **Run the Migration Script**

   To migrate existing shortlinks to the external service:

   ```bash
   # Install dependencies if needed
   npm install dotenv

   # Run the migration script
   npx ts-node scripts/migrate-shortlinks.ts
   ```

   You can also run in dry-run mode to see what would be migrated:

   ```bash
   npx ts-node scripts/migrate-shortlinks.ts --dry-run
   ```

   For more detailed information:

   ```bash
   npx ts-node scripts/migrate-shortlinks.ts --dry-run --verbose
   ```

3. **Verify the Integration**

   Try creating a new shortlink through the campaign API to verify that it's working:

   ```bash
   curl -X POST 'http://localhost:3000/api/admin/campaigns/CAMPAIGN_ID/shortlinks' \
     -H 'Content-Type: application/json' \
     -H 'Authorization: Bearer YOUR_AUTH_TOKEN' \
     -d '{
       "title": "Test External Shortlink",
       "description": "Testing the external shortlink service",
       "originalUrl": "https://example.com/landing-page"
     }'
   ```

## Troubleshooting

If you encounter any issues with the shortlink service:

1. **Check JWT Token**: Ensure the JWT token is correctly set in your environment variables
2. **Network Access**: Make sure your server can reach `l.iamcoming.io`
3. **Error Handling**: Check the logs for specific error messages from the external service

## Additional Information

- The shortlink client is implemented in `src/lib/shortlink-service.ts`
- The campaign API endpoints have been updated to use the external service
- For backward compatibility, `/api/s/[shortCode]` will redirect to the external service
- All shortlink analytics are now handled by the external service

For detailed API documentation, see [EXTERNAL_SHORTLINK_SERVICE.md](./docs/EXTERNAL_SHORTLINK_SERVICE.md).
