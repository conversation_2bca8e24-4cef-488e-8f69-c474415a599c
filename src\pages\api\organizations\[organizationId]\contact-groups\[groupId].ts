import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import {
  getSavedContactGroupById,
  updateSavedContactGroup,
  deleteSavedContactGroup
} from '@/lib/saved-contact-groups';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { organizationId, groupId } = req.query;

    if (!organizationId || !groupId) {
      return res.status(400).json({ error: 'Organization ID and Group ID are required' });
    }

    // Verify user session and organization membership
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const db = Database.getInstance();
    const organization = await db.getOrganizationById(organizationId as string);
    
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const isMember = organization.members?.some(member => member.userId === session.user.id);
    if (!isMember) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get the contact group and verify it belongs to the organization
    const contactGroup = await getSavedContactGroupById(groupId as string);
    
    if (!contactGroup) {
      return res.status(404).json({ error: 'Contact group not found' });
    }

    if (contactGroup.organizationId !== organizationId) {
      return res.status(403).json({ error: 'Contact group does not belong to this organization' });
    }

    if (req.method === 'GET') {
      // Get individual contact group details
      return res.status(200).json({
        success: true,
        contactGroup
      });

    } else if (req.method === 'PUT') {
      // Update contact group
      const { name, description, contacts } = req.body;

      const updates: any = {};

      if (name !== undefined) {
        if (!name.trim()) {
          return res.status(400).json({ error: 'Name cannot be empty' });
        }
        updates.name = name.trim();
      }

      if (description !== undefined) {
        updates.description = description.trim();
      }

      if (contacts !== undefined) {
        if (!Array.isArray(contacts)) {
          return res.status(400).json({ error: 'Contacts must be an array' });
        }

        // Validate and clean contacts
        const validContacts = contacts
          .filter(contact => contact.email || contact.phone)
          .map(contact => ({
            email: contact.email || '',
            name: contact.name || '',
            phone: contact.phone || ''
          }));

        if (validContacts.length === 0) {
          return res.status(400).json({ 
            error: 'At least one contact with email or phone is required' 
          });
        }

        updates.contacts = validContacts;
      }

      if (Object.keys(updates).length === 0) {
        return res.status(400).json({ error: 'No valid updates provided' });
      }

      await updateSavedContactGroup(groupId as string, updates);

      return res.status(200).json({
        success: true,
        message: 'Contact group updated successfully'
      });

    } else if (req.method === 'DELETE') {
      // Soft delete contact group
      await deleteSavedContactGroup(groupId as string);

      return res.status(200).json({
        success: true,
        message: 'Contact group deleted successfully'
      });

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Error handling contact group request:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
