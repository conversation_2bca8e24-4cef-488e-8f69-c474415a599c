import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import analytics from '@/lib/analytics';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { eventId, inviteId } = req.query;
    const { isOwner, isManager } = req.body;

    if (!eventId || typeof eventId !== 'string' || !inviteId || typeof inviteId !== 'string') {
      return res.status(400).json({ error: 'Invalid event or invite ID' });
    }

    // Get user info from session
    const session = await getServerSession(req, res, authConfig);
    const userId = session?.user?.id || null;

    // Extract UTM parameters from the request
    const utmParams = analytics.extractUtmParamsFromRequest(req);

    // Track the invite link open with UTM parameters
    await analytics.trackInviteLinkOpen(
      eventId,
      inviteId,
      userId,
      isOwner || false,
      isManager || false,
      utmParams
    );

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error tracking invite open:', error);
    return res.status(500).json({ error: 'Failed to track invite open' });
  }
}