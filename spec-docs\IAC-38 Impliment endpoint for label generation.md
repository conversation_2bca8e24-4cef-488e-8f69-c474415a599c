# Label generation API

create a api route for `GET /api/media/label`. Api makes use of a render lib's `generateLabel` function to render(return) the image content(buffer for non svg format).

## Params

| Param | Type | Required | Description |
|-------|------|----------|-------------|
| name | String | Yes | Name to be displayed |
| qr-content | String | Yes | URL for the QR Code |
|orientation | Enum | No | Defaults to `portrait`. Possible values: `portrait` | `landscape`. |
| color-qr | String | No | Color for QR and text. CSS compatible Color code |
| bg-qr | String | No | CSS compatible Color code |
| format | Enum | Optional | Output format: svg, jpg, jpeg, png (default: svg) |
| download | Boolean | No | Defaults to `false`. pass a truthy value to force download the image. |
| filename | String | No | filename for the downloaded file | 

## Acceptance Criteria
 
 On request, renders a QR invite label with passed values.

 If `download` is truthy, then force download the image.

 And if `filename` is also set, then the downloaded file will have a filename set to `{filename}.{format}`. Otherwise, the querystring's md5 hash string in lowercase will be used as filename.

 Cache the event image in-memory for 10 minutes to avoid redundant downloads

 WHen format is not SVG, the image has to be 

 ## Label structure

 Label is a base portrait/landscape rounded rectangle with a padding of `5mm`. corner radius will be `5mm`. 

 Put a QR code (width `20mm`x`20mm`) first follwed by `3mm` gap and render `name` with `5mm` text height.

 Put the logo image (`20mm` wide) under the text (on right of QR for `landscape` orientation).

