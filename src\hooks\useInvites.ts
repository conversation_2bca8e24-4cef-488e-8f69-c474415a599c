
import { EventInvite, EventInviteListItem } from "@/types";
import { useEffect, useState } from "react";

export function useInvites(eventId?: string, sortByName = false) {
  const [invites, setInvites] = useState<EventInviteListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchInvites = async () => {
    setLoading(true);

    if (!eventId) return;
    try {
      const response = await fetch('/api/event/' + eventId + '/invites');
      const data = await response.json();

      if (!Array.isArray(data) && data.error) {
        setError(data.error);
        setInvites([]);
      } else {
        setInvites(data.invites.sort((a: EventInviteListItem, b: EventInviteListItem) => a.name.localeCompare(b.name)));
        setError(null);
      }
    } catch (error) {
      setError(error as Error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvites();
  }, [eventId]);

  const createInvite = async (eventId: string, label: string) => {
    setLoading(true);
    setError(null);

    try {
      const res = await fetch('/api/event/' + eventId + '/invites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          label,
        }),
      });
      const data = await res.json();

      setInvites([...invites, data].sort((a, b) => a.label.localeCompare(b.label)));
      setLoading(false);

      return data;
    } catch (error) {
      setError(error as Error);
    }

    setLoading(false);
  }

  return { invites, loading, error, createInvite, refetch: fetchInvites };
}

export function useInvite(eventId: string, inviteId?: string) {
  const [invite, setInvite] = useState<EventInvite>({
    ID: '',
    name: '',
    eventId: eventId,
    email: '',
    phone: '',
    status: 'invited',

    adults: 0,
    children: 0,

    message: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (inviteId === 'new') {
      setLoading(false);
      return;
    }
    if (!inviteId) return;
    const fetchInvite = async () => {
      try {
        const response = await fetch('/api/event/' + eventId + '/invites/' + inviteId);
        const data = await response.json();
        if (!Array.isArray(data) && data.error) {
          setError(data.error);
          setInvite({
            ID: '',
            name: '',
            eventId: eventId,
            email: '',
            phone: '',
            status: 'invited',

            adults: 0,
            children: 0,

            message: []
          });
        } else {
          if (data.message === undefined) {
            data.message = [];
          }
          setInvite(data);
          setError(null);
        }
      } catch (error) {
        setError(error as Error);
      } finally {
        setLoading(false);
      }
    };


    fetchInvite();
  }, [eventId, inviteId]);

  const updateInvite = async (inviteId: string, invite: EventInvite) => {
    setLoading(true);
    setError(null);

    if (inviteId === 'new') {
      setInvite({
        ID: '',
        name: '',
        eventId: eventId,
        email: '',
        phone: '',
        status: 'invited',

        adults: 0,
        children: 0,

        message: []
      });
    } else {
      try {
        const res = await fetch('/api/event/' + eventId + '/invites/' + inviteId, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(invite),
        });

        if (!res.ok) {
          throw new Error(`Failed to update invite: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();

        setInvite(data);
        setLoading(false);

        return data;
      } catch (error) {
        setError(error as Error);
      }
    }

    setLoading(false);
  }

  return { invite, loading, error, updateInvite };
}