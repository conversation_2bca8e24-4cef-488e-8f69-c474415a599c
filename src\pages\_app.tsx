import type { AppProps } from 'next/app'
import { Inter } from "next/font/google"
import { SessionProvider } from "next-auth/react"
import { Toaster } from "@/components/ui/toaster"
import { cn } from "@/lib/utils"
import "@/styles/globals.css"
import { useEffect } from 'react'
import { initFirebase } from '@/lib/firebaseClient'
import { debugLog } from '@/lib/logger'
import { DefaultSeo } from 'next-seo'
import { GoogleAnalytics } from '@/components/GoogleAnalytics'
import { GoogleTagManager } from '@next/third-parties/google'
import { FeedbackButton } from '@/components/FeedbackButton'
import { useRouter } from 'next/router'

const inter = Inter({ subsets: ["cyrillic"] })

export default function App({ Component, pageProps: { session, ...pageProps } }: AppProps) {
  const router = useRouter()
  
  // Initialize Firebase and Remote Config
  useEffect(() => {
    debugLog("Initializing Firebase and Remote Config");
    initFirebase();
  }, []);

  // Check if current route is a preview route
  const isPreviewRoute = router.asPath.includes('/rsvp/preview')

  return (
    <SessionProvider session={session}>
      <DefaultSeo
        additionalMetaTags={[
          {
            name: 'viewport',
            content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
          }
        ]}
      />
      <GoogleAnalytics />
      <GoogleTagManager gtmId="GTM-PWSPXLLH" />
      <main className={cn("bg-background font-sans antialiased", inter.className)}>
        <Component {...pageProps} />
        <Toaster />
        {!isPreviewRoute && <FeedbackButton />}
      </main>
    </SessionProvider>
  )
}