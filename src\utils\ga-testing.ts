/**
 * <PERSON><PERSON><PERSON> Console Testing Utilities for Google Analytics URL Grouping
 * 
 * Paste these functions into your browser console to test the URL grouping
 * functionality and verify Google Analytics events are being sent correctly.
 */

import { groupUrl, createPageTitle, extractUrlMetadata } from '../lib/url-grouping';
import '../types/analytics';

// Test URL grouping in browser console
window.testUrlGrouping = function () {
  console.log('🔍 Testing URL Grouping Functionality...\n');

  const testUrls = [
    '/event/test123',
    '/event/test123/invites',
    '/event/test123/invites/inv456/edit',
    '/rsvp/rsvp789',
    '/user/user123/profile'
  ];

  testUrls.forEach(url => {
    // These functions are now imported from the url-grouping module
    const grouped = groupUrl(url);
    const title = createPageTitle(grouped);
    console.log(`Original: ${url}`);
    console.log(`Grouped:  ${grouped}`);
    console.log(`Title:    ${title}`);
    console.log('---');
  });
};

// Test Google Analytics events
window.testGAEvents = function () {
  console.log('📊 Testing Google Analytics Events...\n');

  if (typeof window.gtag === 'function') {
    console.log('✅ Google Analytics gtag function is available');

    // Test a sample page view with grouped URL
    window.gtag('event', 'page_view', {
      page_title: 'Test Event Details',
      page_location: window.location.href,
      page_path: '/event/*',
      page_group: '/event/*',
      original_path: '/event/test123',
      event_id: 'test123'
    });

    console.log('✅ Test page_view event sent with grouped URL');

    // Test a custom event
    window.gtag('event', 'test_url_grouping', {
      event_category: 'Testing',
      event_label: 'URL Grouping Test',
      page_group: '/event/*',
      original_path: window.location.pathname
    });

    console.log('✅ Test custom event sent');

  } else {
    console.log('❌ Google Analytics gtag function not available');
    console.log('Make sure Google Analytics is properly loaded');
  }
};

// Test event and invite ID tracking
window.testEventTracking = function () {
  console.log('🎯 Testing Event/Invite ID Tracking...\n');

  if (typeof window.gtag === 'function') {
    // Test event-specific tracking
    window.gtag('event', 'test_event_tracking', {
      event_category: 'Testing',
      event_label: 'Event ID Tracking Test',
      event_id: 'test-event-123',
      custom_parameter_1: 'test-event-123',
      page_group: '/event/*',
      event_context: 'event_details'
    });
    console.log('✅ Event tracking test sent');

    // Test invite-specific tracking
    window.gtag('event', 'test_invite_tracking', {
      event_category: 'Testing',
      event_label: 'Invite ID Tracking Test',
      event_id: 'test-event-123',
      invite_id: 'test-invite-456',
      custom_parameter_1: 'test-event-123',
      custom_parameter_2: 'test-invite-456',
      page_group: '/event/*/invites/*',
      event_context: 'invite_details'
    });
    console.log('✅ Invite tracking test sent');

    // Test RSVP tracking (event-based only)
    window.gtag('event', 'test_rsvp_tracking', {
      event_category: 'Testing',
      event_label: 'RSVP Tracking Test',
      event_id: 'test-event-123',
      invite_id: 'test-invite-789',
      custom_parameter_1: 'test-event-123',
      custom_parameter_2: 'test-invite-789',
      page_group: '/event/*/rsvp/*',
      event_context: 'rsvp_flow'
    });
    console.log('✅ RSVP tracking test sent');

    console.log('\n📊 In Google Analytics, you can now filter by:');
    console.log('- Event ID: custom_parameter_1 = "test-event-123"');
    console.log('- Invite ID: custom_parameter_2 = "test-invite-456" or "test-invite-789"');
    console.log('- User ID: custom_parameter_3 (for user-related actions)');
    console.log('- Organization ID: custom_parameter_4 (for org-related actions)');

  } else {
    console.log('❌ Google Analytics gtag function not available');
  }
};

// Check current page grouping
window.checkCurrentPageGrouping = function () {
  console.log('📍 Current Page Grouping Analysis...\n');

  const currentUrl = window.location.pathname + window.location.search;
  console.log(`Current URL: ${currentUrl}`);

  if (typeof groupUrl === 'function') {
    const grouped = groupUrl(currentUrl);
    const title = createPageTitle ? createPageTitle(grouped) : 'N/A';

    console.log(`Grouped URL: ${grouped}`);
    console.log(`Page Title: ${title}`);

    if (typeof extractUrlMetadata === 'function') {
      const metadata = extractUrlMetadata(currentUrl, grouped);
      console.log('Metadata:', metadata);
    }
  } else {
    console.log('❌ URL grouping functions not available');
  }
};

// Monitor Google Analytics data layer
window.monitorDataLayer = function () {
  console.log('👀 Monitoring Google Analytics Data Layer...\n');

  if (window.dataLayer) {
    console.log('✅ Data Layer is available');
    console.log('Recent Data Layer entries:');
    console.log(window.dataLayer.slice(-5)); // Show last 5 entries

    // Set up monitoring for new events
    const originalPush = window.dataLayer.push;
    window.dataLayer.push = function (...args) {
      console.log('📈 New GA Event:', args);
      return originalPush.apply(window.dataLayer, args);
    };

    console.log('✅ Data Layer monitoring enabled');
  } else {
    console.log('❌ Data Layer not available');
  }
};

// Run all tests
window.runAllTests = function () {
  console.clear();
  console.log('🚀 Running All Google Analytics URL Grouping Tests...\n');

  window.testUrlGrouping();
  console.log('\n');
  window.testGAEvents();
  console.log('\n');
  window.testEventTracking();
  console.log('\n');
  window.checkCurrentPageGrouping();
  console.log('\n');
  window.monitorDataLayer();

  console.log('\n✅ All tests completed!');
  console.log('\nNext steps:');
  console.log('1. Check Google Analytics Real-time reports');
  console.log('2. Navigate to different pages and monitor events');
  console.log('3. Test filtering by custom parameters in GA4');
  console.log('4. Use runAllTests() to test again');
};

// Auto-run basic test on load
console.log('🔧 Google Analytics URL Grouping Test Utilities Loaded!');
console.log('\nAvailable functions:');
console.log('- testUrlGrouping()');
console.log('- testGAEvents()');
console.log('- testEventTracking() [NEW - Test event/invite ID filtering]');
console.log('- checkCurrentPageGrouping()');
console.log('- monitorDataLayer()');
console.log('- runAllTests()');
console.log('\nRun runAllTests() to test everything at once.');

export { };
