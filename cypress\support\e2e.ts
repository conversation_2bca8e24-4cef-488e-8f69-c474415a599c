// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Hide XHR requests from command log
const app = window.top as Window & { console: Console };
if (app) {
  app.console.log = () => {};
}

// Hide fetch requests from command log
const originalFetch = window.fetch;
window.fetch = function(...args: Parameters<typeof fetch>) {
  return originalFetch.apply(this, args).then(response => {
    return response;
  });
};

// Declare global types for TypeScript
declare global {
  namespace Cypress {
    interface Chainable {
      // Add custom commands here if needed
    }
  }
} 