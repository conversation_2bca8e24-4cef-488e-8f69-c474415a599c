import React from "react";
import { cn } from "@/lib/utils";

interface RibbonProps {
  text: string;
  backgroundColor?: string;
  textColor?: string;
  className?: string;
}

export function Ribbon({
  text,
  backgroundColor = "#f59e0b", // Default amber color
  textColor = "white",
  className,
}: RibbonProps) {
  return (
    <div 
      className={cn(
        "absolute top-0 right-0 overflow-hidden h-24 w-24 z-10",
        className
      )}
      aria-label={`${text} ribbon`}
    >
      <div 
        className="absolute transform rotate-45 text-center font-medium text-xs py-1 right-[-35px] top-[22px] w-[140px] shadow-md"
        style={{ 
          backgroundColor,
          color: textColor
        }}
      >
        {text}
      </div>
    </div>
  );
}