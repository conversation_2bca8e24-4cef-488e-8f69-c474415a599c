import React from 'react';
import type { Preview } from '@storybook/react';
// Import your Tailwind CSS directly from the project
import '../src/styles/globals.css';

export const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#111827' }, 
      ],
    },
    nextjs: {
      appDirectory: true,
    },
  },
  decorators: [
    (Story) => (
      <div className="p-8 m-2 bg-background text-foreground">
        <Story />
      </div>
    ),
  ],
};