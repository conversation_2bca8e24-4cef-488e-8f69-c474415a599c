# Payment Integration Instructions

This project uses <PERSON><PERSON> for handling payments for paid events.

## Features
- **Payment Options**:
  - Credit card payments via Stripe.
  - Bank transfers manually verified by IAC staff.

## Workflows
1. **Event Payment**:
   - Hosts select a paid plan during event creation.
   - Hosts are redirected to <PERSON><PERSON> for payment processing.
   - Payment status is updated in the platform.

2. **Invoice Management**:
   - Partners can view invoices for events they create.
   - Payment status is displayed for each invoice.

## Implementation Details
- Use Stripe's API for payment processing.
- Ensure secure handling of payment data.
- Log payment events for analytics and troubleshooting.
