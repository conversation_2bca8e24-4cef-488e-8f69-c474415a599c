import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated and is an admin
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the current user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    // Get database instance
    const db = Database.getInstance();

    // Get organization ID from the query parameters
    const { organizationId } = req.query;

    if (!organizationId || Array.isArray(organizationId)) {
      return res.status(400).json({ error: 'Invalid organization ID' });
    }

    // Handle GET request - fetch organization by ID
    if (req.method === 'GET') {
      const organization = await db.getOrganizationById(organizationId);

      if (!organization) {
        return res.status(404).json({ error: 'Organization not found' });
      }

      // Return organization data
      return res.status(200).json({
        success: true,
        organization,
      });
    }

    // If not GET, return method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error handling admin organization request:', error);
    return res.status(500).json({ error: 'Failed to process admin organization request' });
  }
}
