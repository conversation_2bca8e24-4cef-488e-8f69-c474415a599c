import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated and is an admin
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the current user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    // Handle GET request - get user count
    if (req.method === 'GET') {
      // Get database instance
      const db = Database.getInstance();

      // Fetch total user count
      const { total } = await db.getAllUsers({
        page: 1,
        limit: 1,
        searchQuery: ''
      });

      // Return user count
      return res.status(200).json({
        success: true,
        count: total
      });
    }

    // If not GET, return method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error handling admin user count request:', error);
    return res.status(500).json({ error: 'Failed to get user count' });
  }
}
