import { MinusIcon, PlusIcon } from "@radix-ui/react-icons";
import { Button } from "./button";
import { useCallback, useEffect, useState } from "react";

interface CounterProps {
  defaultValue?: number;
  onChange?: (value: number) => void;
  min?: number;
  max?: number;
}

export function Counter({ defaultValue = 0, onChange = () => { }, min = 0, max = Infinity }: CounterProps) {
  
  const [count, setCount] = useState(defaultValue);
  
  const changeCount = useCallback((value: number) => {
    if (value >= min && value <= max) {
      setCount(value);
      onChange(value)
    }
  }, [max, min, onChange])

  useEffect(() => {
    changeCount(count);
  }, [changeCount, count]);
  
  useEffect(() => {
    
    changeCount(defaultValue);
  }, [changeCount, defaultValue]);


  return (
    <div className="p-2 flex justify-around items-center gap-8">
      <Button type="button" size="icon" variant={"outline"} onClick={() => changeCount(count - 1)} >
        <MinusIcon />
      </Button>
      <div className="text-xl">{count}</div>
      <Button type="button" size="icon" variant={"outline"} onClick={() => changeCount(count + 1)} >
        <PlusIcon />
      </Button>
    </div>
  );
}