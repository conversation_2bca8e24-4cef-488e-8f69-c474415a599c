import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Calendar, Clock, MapPin, Edit, Send, Globe, UserPlus, Baby, Mail, Phone, Frown, ArrowLeft } from "lucide-react"
import { Event, EventInvite } from "@/types";
import { FormatDate } from "@/lib/dayjs";
import { Format24to12 } from "@/lib/time";

interface ConfirmationViewProps {
  event: Event;
  invite: EventInvite;
  setRsvpDialogOpen: (open: boolean) => void;
  addToCalendar: () => void;
  setView: React.Dispatch<React.SetStateAction<"confirmation" | "invite" | "chat">>;
}

export default function ConfirmationView({ event, invite, setRsvpDialogOpen, addToCalendar, setView }: ConfirmationViewProps) {
  // Get the event timezone
  const eventTimezone = event.timezone || "Australia/Melbourne";

  // Determine if the invite was declined
  const isDeclined = invite.status === 'declined';

  // Ensure response data is available even if it's initially undefined
  const responseData: typeof invite.response = invite.response || null;
  // Log data to help with debugging
  console.log("Invite data:", invite);
  console.log("Response data:", responseData);
  
  return (
    <div className="flex flex-col bg-gray-50 min-h-screen w-full">
      <div className="flex-1 p-4 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 pb-2 flex flex-col items-center text-center">
            {isDeclined ? (
              // Declined view
              <>
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                  <Frown className="w-6 h-6 text-red-500" />
                </div>
                <h2 className="text-2xl font-bold mb-3">Thank you for letting us know!</h2>
                <p className="text-muted-foreground mb-6">You have declined the invitation for &quot;{event.eventName}&quot;. We will miss you.</p>
              </>
            ) : (
              // Accepted view
              <>
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <svg
                    className="w-8 h-8 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold mb-2">Thank You, You are in!</h2>
                <p className="text-muted-foreground mb-6">Thanks for confirming your attendance for &quot;{event.eventName}&quot; we&apos;ll see you there.</p>
              </>
            )}

            <div className="w-full bg-gray-100 p-4 rounded-lg mb-6">
              <div className="flex items-center mb-2">
                <Calendar className="h-5 w-5 text-muted-foreground mr-2" />
                <span>{FormatDate(event.eventDate, eventTimezone)}</span>
              </div>
              <div className="flex items-center mb-2">
                <Clock className="h-5 w-5 text-muted-foreground mr-2" />
                <span>
                  {Format24to12(event.start)} - {event.end ? Format24to12(event.end) : "TBD"}
                </span>
              </div>
              <div className="flex items-center mb-2">
                <MapPin className="h-5 w-5 text-muted-foreground mr-2" />
                <span>{event.location}</span>
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Globe className="h-4 w-4 text-muted-foreground mr-2" />
                <span>Timezone: {eventTimezone}</span>
              </div>
            </div>

            {!isDeclined && (
              <Button variant="outline" className="w-full mb-4" onClick={() => addToCalendar()}>
                <Calendar className="h-4 w-4 mr-2" />
                Add to Google Calendar
              </Button>
            )}

            {!isDeclined && (
              <div className="text-left w-full">
                <p className="font-semibold ">Your RSVP Details:</p>
                {invite.response ? (
                  <div className="py-3 font-normal text-muted-foreground">
                    <p className="flex items-center gap-2 mb-1"><UserPlus className="h-4 w-4 text-muted-foreground" /> Adults: {responseData?.adults || 0}</p>
                    <p className="flex items-center gap-2 mb-1"><Baby className="h-4 w-4 text-muted-foreground" /> Children: {responseData?.children || 0}</p>
                    {invite.email && <p className="flex items-center gap-2 mb-1"><Mail className="h-4 w-4 text-muted-foreground" /> Email: {invite.email}</p>}
                    {invite.phone && <p className="flex items-center gap-2"><Phone className="h-4 w-4 text-muted-foreground" /> Phone: {invite.phone}</p>}
                  </div>
                ) : (
                  <div className="py-3">
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-2 w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3"></div>
                    {invite.email && <div className="h-4 bg-gray-200 rounded animate-pulse mb-2 w-4/5"></div>}
                    {invite.phone && <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>}
                  </div>
                )}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex flex-col space-y-2 pt-0">
            {isDeclined && (
              <p className="text-muted-foreground text-center mb-3">If you change your mind, you can RSVP again.</p>
            )}
            <Button variant="primary-button" className="w-full" onClick={() => {
              setRsvpDialogOpen(false);
              setView("invite");
            }}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Invite
            </Button>
            {/* <Button className="w-full" onClick={() => setView("chat")}>
              <Send className="h-4 w-4 mr-2" />
              Send Message
            </Button> */}
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
