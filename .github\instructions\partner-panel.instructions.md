# Partner Panel

I want to introduce a section for a new type of users in IAC, the Partner. The Partner is a venue or event booking center that will be able to create events for their clients (Hosts) and manage the guest list.

The Partner will charge their customers and we will bulk invoice them for the events they create on a monthly basis. Partners will have an option if selected by us to pay via card using the stripe integration we have or via bank transfer which IAC staff will manually verify and mark the invoice paid.

Note: The Organization interface has been updated to use 'partner' instead of 'company' as the organization type.

A partner can have many venues and each venue can have a multiple locations/areas for events. When a partner creates an event, they will select the venue and location/area for the event. The partner will also be able to set the number of invites for the event and the price per invite if their customer wants to increase the invites through IAC. The partner will also be able to set the event date and time.

Write workflows for the partner panel. Host will be a normal user of IAC with access to the event created by the partner. The host will be able to manage the event and guest list. The host will also be able to create invites for their guests and send them via email or SMS. The host will also be able to see the RSVPs just like a normal user of IAC.

## Epics and User Stories

### Event Setup & Configuration

- As a partner, I want to be able to create events for my customers (hosts) so that they can manage their guest list.
- As a partner, I want to be able to set the number of invites for the event so that my customers can manage their guest list.
- As a partner, I want to be able to set the event date and time so that my customers can manage their guest list.
- As a partner, I want to be able to set the venue and location/area for the event so that my customers can manage their guest list.
- As a partner, I want to be able to set the price per invite so that my customers can manage their guest list.

### Payment & Invoicing

- As a partner, I want to be able to see the invoice for the events I create so that I can manage my payments.
- As a partner, I want to be able to see the payment status for the events I create so that I can manage my payments.

### Venue & Time Slot Management

- As a partner, I want to be able to manage my venues and locations/areas so that I can manage my events.
- As a partner, I want to be able to manage time slots for the events so that I can manage my events.

### Guest Entry & Access Control

- As a partner, I want to be able to scan the QR code for the guests in their email so that I can manage the entry to the event.

## User Workflow

- Customer Walks into the event booking center.
- Confirms event date and time
- Partner Charges event fee ($35 for IAC)
- Partner then creates an event in IAC Partner Portal wit host details.
  - Host receives the email link to manage event. Host Uses exiting IAC account to accept management invite or creates a new account with IAC.
  - host is limited by number of invites set/agreed on the venue booking
  - Guest Creates invites
    - One By one
    - CSV Upload
    - Create from contacts (Native App)
  - RSVPs are confirmed. Guests revive Email confirmation and Entry code (QR) or Apple/Google Wallet Passes.
- On day of event, partner has Scanning setup on entry
- Guest show RSVP Confirmation QR/Apple/Google Pass
- Guest is granted access and Details updated for both Host and Partner.
- Partner can manage venues, locations and time slots.
