# Migration from Email-Based to Account-Based Event Ownership

## Overview

This document outlines the architectural changes made to transition from using email addresses as event owners to using account IDs. This change enhances security, improves reliability, and provides greater flexibility in how we manage event ownership and permissions.

## Motivation

Previously, the system identified event owners and managers by their email addresses. This approach had several limitations:

- **Security concerns**: Email addresses can change or be reassigned, potentially giving unauthorized users access to events
- **Reliability issues**: Email address comparison is prone to case sensitivity and formatting issues
- **Limited flexibility**: Harder to implement sophisticated permission systems based solely on email addresses
- **Identity management**: Email addresses don't uniquely identify users across authentication providers

By migrating to account-based ownership, we create a more robust foundation for our permission system that works reliably across different authentication methods.

## Changes Made

### 1. Data Model Updates

Updated the core data models to use account IDs for ownership:

- Modified `Event` interface:
  ```typescript
  export interface Event {
    // ...existing code...
    ownerAccountId: string;  // Account ID of the owner (previously ownerEmail)
    ownerEmail?: string;     // Keep for backward compatibility, will be deprecated
    managers: string[];      // Array of account IDs instead of email addresses
    // ...existing code...
  }
  ```

- Modified `EventListItem` interface with similar changes:
  ```typescript
  export interface EventListItem {
    // ...existing code...
    ownerAccountId: string;  // Account ID of the owner
    ownerEmail?: string;     // Keep for backward compatibility
    // ...existing code...
  }
  ```

### 2. API Endpoint Updates

Updated API endpoints to use account IDs for authentication and permission checks:

- Event API Handlers (`/api/event/[eventId]/index.ts`):
  - Modified to use `session.user.id` instead of `session.user.email` for authentication
  - Updated permission checks to validate against `ownerAccountId`
  - Added backward compatibility for existing events using `ownerEmail`

- Events List API (`/api/events/index.ts`):
  - Updated to fetch events by account ID
  - Added double-checking for events owned by email address for backward compatibility

- Added User Lookup APIs:
  - Created `/api/user/lookup.ts` to look up users by their account IDs
  - Created `/api/user/lookup-by-email.ts` to look up account IDs by email addresses

### 3. Component Updates

- `EventManagers` Component (`/components/EventManagers.tsx`):
  - Refactored to store and display managers by account ID instead of email
  - Added user lookup functionality to display friendly email addresses to users
  - Updated manager addition and removal to work with account IDs

- Event Management Screen (`/pages/event/[eventId]/index.tsx`):
  - Updated ownership checks to use account IDs
  - Modified permission logic for management operations

### 4. Event Limits Updates

- Event Limits Hook (`/hooks/useEventLimits.ts`):
  - Modified to work with account IDs instead of email addresses

- Event Limits API (`/api/events/check-limits.ts`):
  - Updated to check event limits based on account ID
  - Added backward compatibility to include events owned by email address

### 5. Database Updates

- Added new method to the Database class:
  - `ListAllData<T>`: To support fetching all events for migration

### 6. Migration Tool

Created a migration script (`/scripts/migrate-event-ownership.ts`) to update existing events:
- Identifies events using email-based ownership
- Looks up account IDs for the emails
- Updates the events with the correct account IDs
- Handles the migration of both event owners and managers

## Backward Compatibility

The changes maintain backward compatibility in several ways:

1. Event records keep the `ownerEmail` field alongside the new `ownerAccountId` field
2. API endpoints check both account ID and email when verifying permissions
3. The migration script preserves all existing data while adding the new ID-based references

## How to Run the Migration

To update existing events in the database:

```bash
# From the project root
npx ts-node src/scripts/migrate-event-ownership.ts
```

## Monitoring and Validation

After running the migration:

1. Monitor the application to ensure events are correctly associated with their owners and managers
2. Validate that permission checks continue to work correctly
3. Verify that event listing and filtering operations work as expected

## Future Considerations

This change lays the groundwork for:

1. More sophisticated permission models (roles, groups, etc.)
2. Better integration with various authentication providers
3. Support for account linking and merging
4. Eventually phasing out email-based references entirely

## Security Implications

This change improves security by:

- Relying on stable, system-generated IDs rather than mutable email addresses
- Making it harder to gain unauthorized access when a user's email changes
- Providing a foundation for more granular permission controls

## Implementation Notes

The migration maintains the current user experience while upgrading the underlying architecture. Users will continue to add managers by email address, but the system will store and check against account IDs behind the scenes.

## Contributors

- Development team (April 2025)