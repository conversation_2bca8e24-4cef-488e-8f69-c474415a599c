#!/bin/bash

BASE_PATH="$(dirname $(dirname $(realpath $0)))"

# Ensure the script exits if any command fails
set -e

# Define variables
IMAGE_NAME="iamcoming-app"
IMAGE_TAG="latest"
DOCKERFILE_PATH="./Dockerfile"
GITHUB_USERNAME="iamcoming-io"
GITHUB_REPO="universe"
PUBLISH=false
ENV=$1

# Parse arguments
for arg in "$@"
do
  if [ "$arg" == "--publish" ]; then
    PUBLISH=true
  fi
done

cleanup() {
  # Remove the build.env file
  rm -f build.env
}

# Run the build-env.sh script
${BASE_PATH}/scripts/build-env.sh ${ENV}

# Build the Docker image
DOCKER_BUILDKIT=1 docker build --no-cache --progress=plain -t ${IMAGE_NAME}:${IMAGE_TAG} -f ${DOCKERFILE_PATH} .

cleanup

# Check if the publish flag is set
if [ "$PUBLISH" = true ]; then
  # Tag the Docker image for GitHub Container Registry
  docker tag ${IMAGE_NAME}:${IMAGE_TAG} ghcr.io/${GITHUB_USERNAME}/${GITHUB_REPO}/${IMAGE_NAME}:${IMAGE_TAG}

  # Push the Docker image to GitHub Container Registry
  docker push ghcr.io/${GITHUB_USERNAME}/${GITHUB_REPO}/${IMAGE_NAME}:${IMAGE_TAG}

  # Print success message
  echo "Docker image ${IMAGE_NAME}:${IMAGE_TAG} built and pushed to GitHub Container Registry successfully."
else
  # Print success message
  echo "Docker image ${IMAGE_NAME}:${IMAGE_TAG} built successfully. Use --publish to push to GitHub Container Registry."
fi