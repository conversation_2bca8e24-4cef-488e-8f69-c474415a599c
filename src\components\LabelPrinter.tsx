/* eslint-disable @next/next/no-img-element */
import { EventInviteListItem, InvitePageSize, PrintSettings } from '@/types';
import React, { useState, useRef, useEffect } from 'react';
import { generateRsvpLink, generateStorageImageUrl } from "@/lib/utils";
import { generateQrUtmParams } from "@/lib/utm";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, Image as ImageIcon, Move, Printer, Save, Download } from "lucide-react";
import { useSession } from "next-auth/react";
import { useToast } from "@/components/ui/use-toast";
import { headers } from 'next/headers';
import { useEvent } from "@/hooks/useEvent";
import { off } from 'process';

// Add print styles to remove margins
const printStyles = `
  @media print {
    @page {
      margin: 0;
      size: auto;
    }
    body {
      margin: 0;
      padding: 0;
    }
    .page {
      margin: 0 !important;
      box-shadow: none !important;
    }
  }
`;

interface LabelPrinterProps {
  invites: EventInviteListItem[];
}

type Orientation = 'portrait' | 'landscape';

const LabelPrinter: React.FC<LabelPrinterProps> = ({ invites }) => {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [mode, setMode] = useState<'label' | 'invite'>('label');
  const [inviteImage, setInviteImage] = useState<string | null>(null);
  const [pageSize, setPageSize] = useState<InvitePageSize>('A4');
  const [orientation, setOrientation] = useState<Orientation>('portrait');
  const [labelPosition, setLabelPosition] = useState({ x: 50, y: 50 }); // percentage values
  // Removed labelFormat state and set format to 'png' for all label operations
  const [uploading, setUploading] = useState(false);
  const [saving, setSaving] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [eventId, setEventId] = useState<string | null>(null);
  const { event, saveEvent } = useEvent(eventId || undefined);
  const [labelFgColor, setLabelFgColor] = useState('#000000');
  const [labelBgColor, setLabelBgColor] = useState('#FFFFFF');

  // Extract eventId from first invite and try to load existing image
  useEffect(() => {
    if (!invites || invites.length === 0 || !invites[0].eventId) return;

    const id = invites[0].eventId;
    setEventId(id);
    setInviteImage(null); // Reset image state when invites change

    // Try to load invite image if it exists
    fetch(`/api/storage/getUrl?eventId=${id}`)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.exists && data.url) {
          // Add a cache-busting parameter to ensure fresh image
          const separator = data.url.includes('?') ? '&' : '?';
          const imageUrlWithCache = data.url + separator + 'cache=' + new Date().getTime();
          setInviteImage(imageUrlWithCache);
        } else {
          setInviteImage(null);
        }
      })
      .catch(() => {
        setInviteImage(null);
      });
  }, [invites]);

  // Load print settings from event when it's available
  useEffect(() => {
    if (event?.printSettings?.inviteSettings) {
      // Load from the nested inviteSettings object
      if (event.printSettings.inviteSettings.pageSize) {
        setPageSize(event.printSettings.inviteSettings.pageSize);
      }
      if (event.printSettings.inviteSettings.orientation) {
        setOrientation(event.printSettings.inviteSettings.orientation);
      }
      if (event.printSettings.inviteSettings.labelPosition) {
        setLabelPosition(event.printSettings.inviteSettings.labelPosition);
      }
      // Load color settings if available
      setLabelFgColor('#000000'); // Default to black
      setLabelBgColor('#FFFFFF'); // Default to white
    }
  }, [event]);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0] || !eventId) {
      return;
    }

    const file = e.target.files[0];
    if (file.type !== 'image/png' && file.type !== 'image/jpeg') {
      toast({
        title: "Invalid File Type",
        description: "Please upload a PNG or JPEG image",
        variant: "destructive"
      });
      return;
    }

    setUploading(true);
    
    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', file);
      formData.append('eventId', eventId);
      
      // Add user info from session if available
      if (session?.user?.email) {
        formData.append('uploadedBy', session.user.email);
      }
      
      // Upload using our API endpoint
      const response = await fetch('/api/storage/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'X-User-Email': session?.user?.email || 'anonymous'
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }
      
      const data = await response.json();
      if (!data.url) {
        throw new Error('No URL returned from upload API');
      }

      // Verify the image can be loaded before updating the UI
      const img = new Image();
      img.onload = () => {
        // Add timestamp to prevent caching, but check if URL already has query parameters
        const separator = data.url.includes('?') ? '&' : '?';
        setInviteImage(data.url + separator + 'cache=' + new Date().getTime());
        toast({
          title: "Upload Successful",
          description: "Your invite background has been saved",
        });
      };
      img.onerror = () => {
        setInviteImage(null);
        toast({
          title: "Warning",
          description: "Image uploaded but may not display correctly. Please try again.",
          variant: "destructive"
        });
      };
      img.src = data.url;
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Could not upload the image. Please try again.",
        variant: "destructive"
      });
      setInviteImage(null);
    } finally {
      setUploading(false);
      // Clear the file input so the same file can be selected again if needed
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const triggerFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  function escapeHtml(str:string) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;',
        ",": '&#x2F;',
    };
    type mk = keyof typeof map;
    return str.replace(/[&<>"']/g, function(m: string) { return map[m as mk]; });
}

  /**
   * Generate a label image URL using the new API
   */
  const generateLabelUrl = (invite: EventInviteListItem) => {
    const utmParams = generateQrUtmParams(invite.eventId, `print_label_${invite.ID}`);
    const rsvpLink = generateRsvpLink(invite, utmParams);

    // escape & in the name to avoid issues in the URL
    const escapedName = escapeHtml(invite.name);
    
    const params = new URLSearchParams({
      'name': escapedName,
      'qr-content': rsvpLink,
      'orientation': orientation,
      'color-qr': labelFgColor,
      'bg-qr': labelBgColor,
      'format': 'png' // Always use PNG format
    });
    
    return `/api/media/label?${params.toString()}`;
  };

  const handleDownloadLabel = (invite: EventInviteListItem) => {
    const utmParams = generateQrUtmParams(invite.eventId, `download_label_${invite.ID}`);
    const rsvpLink = generateRsvpLink(invite, utmParams);

    // escape special characters in the name to avoid issues in the URL
    const escapedName = encodeURIComponent(invite.name);
    
    const params = new URLSearchParams({
      'name': escapedName,
      'qr-content': rsvpLink,
      'orientation': orientation,
      'color-qr': labelFgColor,
      'bg-qr': labelBgColor,
      'format': 'png', // Always use PNG format
      'download': 'true',
      'filename': `Label-${invite.name.replace(/[^a-z0-9]/gi, '-').toLowerCase()}`
    });
    
    // Open in a new tab to trigger download
    window.open(`/api/media/label?${params.toString()}`, '_blank');
  };

  const renderLabelMode = () => (
    <div className="flex flex-wrap justify-around">
      {invites.map((invite, index) => {
        const labelUrl = generateLabelUrl(invite);

        return (
          <div
            className="border border-gray-200 m-2 p-2 print:p-0 print:border-[0.25mm] print:m-0 print:mb-1 box-border text-center break-inside-avoid relative"
            key={index}
          >
            <img 
              src={labelUrl}
              alt={`Label for ${invite.name}`}
              className="w-[30mm] h-auto"
            />
            <div className="flex justify-center mt-2 print:hidden">
              <Button
                variant="outline"
                size="sm"
                className="text-xs"
                onClick={() => handleDownloadLabel(invite)}
              >
                <Download className="h-3 w-3 mr-1" />
                Download
              </Button>
            </div>
          </div>
        );
      })}
    </div>
  );

  const getLabelStyle = () => {
    return {
      position: 'absolute' as const,
      top: `${labelPosition.y}%`,
      left: `${labelPosition.x}%`,
      transform: 'translate(-50%, -50%)',
      padding: '0px',
      backgroundColor: 'rgba(255, 255, 255, 1.0)',
      borderRadius: '4px',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      width: '38mm',
      height: `${55}mm`
    };
  };

  const getPageStyle = () => {
    const dimensions = {
      A4: { width: orientation === 'portrait' ? '210mm' : '297mm', height: orientation === 'portrait' ? '297mm' : '210mm' },
      A5: { width: orientation === 'portrait' ? '148mm' : '205mm', height: orientation === 'portrait' ? '205mm' : '148mm' },
    };

    const key = pageSize as keyof typeof dimensions;

    return {
      width: dimensions[key].width,
      height: dimensions[key].height,
      margin: '10mm auto', // This margin is for screen display
      backgroundColor: '#fff',
      boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
      position: 'relative' as const,
      overflow: 'hidden',
      pageBreakAfter: 'always' as const,
    };
  };

  const [imageError, setImageError] = useState(false);

  // Reset image error state when inviteImage changes
  useEffect(() => {
    setImageError(false);
  }, [inviteImage]);

  const handleImageError = () => {
    setImageError(true);

    toast({
      title: "Image Loading Error",
      description: "Could not load the invite background image. Please try uploading again.",
      variant: "destructive"
    });
  };

  const handleSavePrintSettings = async () => {
    if (!event || !eventId) {
      toast({
        title: "Error",
        description: "Could not save print settings. Event information is missing.",
        variant: "destructive"
      });
      return;
    }

    setSaving(true);

    try {
      // Create print settings object with nested inviteSettings
      const printSettings: PrintSettings = {
        inviteSettings: {
          pageSize,
          orientation,
          labelPosition: {
            x: labelPosition.x,
            y: labelPosition.y
          },
          labelScale: 1,
        }
      };

      // Update the event with the new print settings
      const updatedEvent = {
        ...event,
        printSettings
      };

      // Save the updated event
      await saveEvent(updatedEvent);

      toast({
        title: "Success",
        description: "Label settings have been saved.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save label settings. Please try again.",
        variant: "destructive"
      });
      console.error("Error saving print settings:", error);
    } finally {
      setSaving(false);
    }
  };

  const renderInviteMode = () => (
    <>
      {invites.map((invite, index) => {
        // Generate QR code link with UTM parameters for tracking
        const utmParams = generateQrUtmParams(invite.eventId, `print_invite_${invite.ID}`);
        const rsvpLink = generateRsvpLink(invite, utmParams);
        const labelUrl = generateLabelUrl(invite);

        return (
          <div key={index} style={getPageStyle()} className="page">
            {uploading ? (
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-100">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-rose-500 mb-2"></div>
                <p className="text-muted-foreground">Uploading image...</p>
              </div>
            ) : inviteImage && !imageError ? (
              <img
                src={inviteImage}
                alt="Invite Background"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                }}
                onError={handleImageError}
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <p className="text-gray-400">
                  {imageError
                    ? "Failed to load background image. Please try uploading again."
                    : "No invite background image"}
                </p>
              </div>
            )}
            <div style={getLabelStyle()}>
              <img src={labelUrl} alt={`Label for ${invite.name}`} className="w-full h-auto" />
            </div>
          </div>
        );
      })}
    </>
  );

  return (
    <div>
      <style>{printStyles}</style>
      <Card className="mb-4 print:hidden">
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Print and Customize</h2>
            <div className="flex items-center gap-2">
              {mode === 'invite' && (
                <Button
                  variant="outline"
                  onClick={triggerFileUpload}
                  disabled={uploading}
                >
                  {!uploading && <Upload className="ml-2 h-4 w-4" />}
                  {uploading ? 'Uploading...' : 'Upload Invite Image'}
                </Button>
              )}
              <Button
                variant="outline"
                onClick={() => window.print()}
                className="flex items-center"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print All
              </Button>
            </div>
          </div>

          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Button
                  variant={mode === 'label' ? "primary-button" : "outline"}
                  className="rounded-r-none"
                  onClick={() => setMode('label')}
                >
                  QR Label Printing
                </Button>
                <Button
                  variant={mode === 'invite' ? "primary-button" : "outline"}
                  className="rounded-l-none"
                  onClick={() => setMode('invite')}
                >
                  Invite printing
                </Button>
              </div>
            </div>

            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/png,image/jpeg"
              onChange={handleFileUpload}
              aria-label="Upload invite background image"
            />

            <div className="flex flex-wrap gap-4">
              <div className="space-y-1">
                <Label htmlFor="page-size">Page Size</Label>
                <Select value={pageSize} onValueChange={(value: InvitePageSize) => setPageSize(value)}>
                  <SelectTrigger id="page-size" className="w-[130px]">
                    <SelectValue placeholder="Page Size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="A4">A4</SelectItem>
                    <SelectItem value="A5">A5</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="orientation">Orientation</Label>
                <Select value={orientation} onValueChange={(value: Orientation) => setOrientation(value)}>
                  <SelectTrigger id="orientation" className="w-[130px]">
                    <SelectValue placeholder="Orientation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="portrait">Portrait</SelectItem>
                    <SelectItem value="landscape">Landscape</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Format selector removed - PNG is now the default format */}
            </div>

            <div className="flex flex-wrap gap-4">
              <div className="space-y-1">
                <Label htmlFor="label-fg-color">Text/QR Color</Label>
                <div className="flex gap-2">
                  <input 
                    type="color" 
                    id="label-fg-color" 
                    value={labelFgColor}
                    onChange={(e) => setLabelFgColor(e.target.value)}
                    className="w-10 h-10 rounded cursor-pointer"
                    aria-label="Color picker for Label text and QR code color"
                  />
                  <Input 
                    value={labelFgColor}
                    onChange={(e) => setLabelFgColor(e.target.value)}
                    className="w-[100px]"
                  />
                </div>
              </div>

              <div className="space-y-1">
                <Label htmlFor="label-bg-color">Background Color</Label>
                <div className="flex gap-2">
                  <input 
                    type="color" 
                    id="label-bg-color" 
                    value={labelBgColor}
                    onChange={(e) => setLabelBgColor(e.target.value)}
                    className="w-10 h-10 rounded cursor-pointer"
                    aria-label="Color picker for Label background color"
                  />
                  <Input 
                    value={labelBgColor}
                    onChange={(e) => setLabelBgColor(e.target.value)}
                    className="w-[100px]"
                  />
                </div>
              </div>
            </div>

            {mode === 'invite' && (
              <div className="space-y-2">
                <div className="flex items-center">
                  <Move className="mr-2 h-4 w-4" />
                  <Label>Label Position</Label>
                </div>
                <div className="flex flex-wrap gap-4">
                  <div className="space-y-1">
                    <Label htmlFor="position-x" className="text-sm">Horizontal (%)</Label>
                    <Input
                      id="position-x"
                      type="number"
                      min="0"
                      max="100"
                      value={labelPosition.x}
                      onChange={(e) => setLabelPosition({...labelPosition, x: Number(e.target.value)})}
                      className="w-[130px]"
                    />
                  </div>
                  <div>
                    <Label htmlFor="position-y" className="text-sm">Vertical (%)</Label>
                    <Input
                      id="position-y"
                      type="number"
                      min="0"
                      max="100"
                      value={labelPosition.y}
                      onChange={(e) => setLabelPosition({...labelPosition, y: Number(e.target.value)})}
                      className="w-[130px]"
                    />
                  </div>
                </div>
              </div>
            )}

            <Button
              variant="outline"
              className="mt-auto max-w-fit"
              onClick={handleSavePrintSettings}
              disabled={saving}
            >
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>
        </CardContent>
      </Card>
      {mode === 'label' ? renderLabelMode() : renderInviteMode()}
    </div>
  );
};

export default LabelPrinter;
