/**
 * URL Grouping Utility for Google Analytics
 * Converts dynamic URLs with IDs into grouped patterns for better analytics reporting
 */

/**
 * Configuration for URL patterns that should be grouped
 */
const URL_GROUPING_PATTERNS = [
  // Event-related routes
  { pattern: /^\/event\/[^\/]+$/, replacement: '/event/*' },
  { pattern: /^\/event\/[^\/]+\/invites$/, replacement: '/event/*/invites' },
  { pattern: /^\/event\/[^\/]+\/invites\/[^\/]+$/, replacement: '/event/*/invites/*' },
  { pattern: /^\/event\/[^\/]+\/invites\/[^\/]+\/edit$/, replacement: '/event/*/invites/*/edit' },
  { pattern: /^\/event\/[^\/]+\/rsvp\/[^\/]+$/, replacement: '/event/*/rsvp/*' },
  { pattern: /^\/event\/[^\/]+\/settings$/, replacement: '/event/*/settings' },
  { pattern: /^\/event\/[^\/]+\/analytics$/, replacement: '/event/*/analytics' },
  { pattern: /^\/event\/[^\/]+\/guests$/, replacement: '/event/*/guests' },
  { pattern: /^\/event\/[^\/]+\/messages$/, replacement: '/event/*/messages' },

  // User-related routes
  { pattern: /^\/user\/[^\/]+$/, replacement: '/user/*' },
  { pattern: /^\/user\/[^\/]+\/profile$/, replacement: '/user/*/profile' },
  { pattern: /^\/user\/[^\/]+\/events$/, replacement: '/user/*/events' },

  // Organization routes (if any)
  { pattern: /^\/org\/[^\/]+$/, replacement: '/org/*' },
  { pattern: /^\/org\/[^\/]+\/settings$/, replacement: '/org/*/settings' },
  { pattern: /^\/org\/[^\/]+\/members$/, replacement: '/org/*/members' },

  // Generic ID patterns (catch remaining dynamic routes)
  { pattern: /\/[a-zA-Z0-9-_]{20,}/g, replacement: '/*' }, // Long IDs (UUIDs, etc.)
  { pattern: /\/\d{4,}/g, replacement: '/*' }, // Numeric IDs with 4+ digits
]

/**
 * Groups a URL by replacing dynamic segments with wildcards
 * @param url The original URL to group
 * @returns The grouped URL pattern
 */
export function groupUrl(url: string): string {
  if (!url || typeof url !== 'string') {
    return url
  }

  // Remove query parameters and hash for pattern matching
  const urlPath = url.split('?')[0].split('#')[0]

  // Apply each grouping pattern
  for (const { pattern, replacement } of URL_GROUPING_PATTERNS) {
    if (pattern.test(urlPath)) {
      return urlPath.replace(pattern, replacement)
    }
  }

  // If no specific pattern matches, return the original path
  return urlPath
}

/**
 * Creates a custom page title based on the grouped URL
 * This helps with Google Analytics reporting by providing descriptive page names
 * @param groupedUrl The grouped URL pattern
 * @param originalUrl The original URL (for fallback)
 * @returns A descriptive page title
 */
export function createPageTitle(groupedUrl: string, originalUrl?: string): string {
  const titleMap: Record<string, string> = {
    '/': 'Home',
    '/about': 'About',
    '/contact': 'Contact',
    '/pricing': 'Pricing',
    '/login': 'Login',
    '/signup': 'Sign Up',
    '/dashboard': 'Dashboard',

    // Event pages
    '/event/*': 'Event Details',
    '/event/*/invites': 'Event Invites',
    '/event/*/invites/*': 'Invite Details',
    '/event/*/invites/*/edit': 'Edit Invite',
    '/event/*/rsvp/*': 'Event RSVP',
    '/event/*/settings': 'Event Settings',
    '/event/*/analytics': 'Event Analytics',
    '/event/*/guests': 'Event Guests',
    '/event/*/messages': 'Event Messages',

    // User pages
    '/user/*': 'User Profile',
    '/user/*/profile': 'User Profile Settings',
    '/user/*/events': 'User Events',

    // Organization pages
    '/org/*': 'Organization',
    '/org/*/settings': 'Organization Settings',
    '/org/*/members': 'Organization Members',
  }

  return titleMap[groupedUrl] || groupedUrl || originalUrl || 'Unknown Page'
}

/**
 * Extracts meaningful metadata from the original URL for custom dimensions
 * @param originalUrl The original URL with dynamic segments
 * @param groupedUrl The grouped URL pattern
 * @returns Metadata object for custom dimensions and custom parameters
 */
export function extractUrlMetadata(originalUrl: string, groupedUrl: string): Record<string, any> {
  const metadata: Record<string, any> = {
    page_group: groupedUrl,
    original_path: originalUrl
  }

  // Extract event ID from various event-related URLs
  if (groupedUrl.includes('/event/')) {
    const eventIdMatch = originalUrl.match(/\/event\/([^\/\?]+)/)
    if (eventIdMatch) {
      metadata.event_id = eventIdMatch[1]
      metadata.custom_parameter_1 = eventIdMatch[1] // For GA4 custom parameters

      // Add event context for better filtering
      if (groupedUrl.includes('/invites')) {
        metadata.event_context = 'invites_management'
      } else if (groupedUrl.includes('/rsvp')) {
        metadata.event_context = 'rsvp_flow'
      } else if (groupedUrl.includes('/settings')) {
        metadata.event_context = 'event_settings'
      } else if (groupedUrl.includes('/analytics')) {
        metadata.event_context = 'event_analytics'
      } else if (groupedUrl.includes('/guests')) {
        metadata.event_context = 'guest_management'
      } else if (groupedUrl.includes('/messages')) {
        metadata.event_context = 'event_messaging'
      } else {
        metadata.event_context = 'event_details'
      }
    }
  }

  // Extract invite ID from invite-related URLs
  if (groupedUrl.includes('/invites/') && !groupedUrl.endsWith('/invites')) {
    const inviteIdMatch = originalUrl.match(/\/invites\/([^\/\?]+)/)
    if (inviteIdMatch) {
      metadata.invite_id = inviteIdMatch[1]
      metadata.custom_parameter_2 = inviteIdMatch[1] // For GA4 custom parameters

      // Also extract event ID if present in invite URLs
      const eventIdFromInviteMatch = originalUrl.match(/\/event\/([^\/]+)\/invites/)
      if (eventIdFromInviteMatch) {
        metadata.event_id = eventIdFromInviteMatch[1]
        metadata.custom_parameter_1 = eventIdFromInviteMatch[1]
        metadata.event_context = 'invite_details'
      }
    }
  }

  // Extract invite ID from RSVP URLs that follow /event/[eventId]/rsvp/[inviteId] pattern
  if (groupedUrl.includes('/event/*/rsvp/*')) {
    const rsvpInviteIdMatch = originalUrl.match(/\/event\/[^\/]+\/rsvp\/([^\/\?]+)/)
    if (rsvpInviteIdMatch) {
      metadata.invite_id = rsvpInviteIdMatch[1]
      metadata.custom_parameter_2 = rsvpInviteIdMatch[1] // For GA4 custom parameters

      // Event ID should already be extracted above in the event section
      // But let's ensure it's captured for RSVP context
      const eventIdFromRsvpMatch = originalUrl.match(/\/event\/([^\/]+)\/rsvp/)
      if (eventIdFromRsvpMatch) {
        metadata.event_id = eventIdFromRsvpMatch[1]
        metadata.custom_parameter_1 = eventIdFromRsvpMatch[1]
        metadata.event_context = 'rsvp_flow'
      }
    }
  }

  // Extract user ID from user-related URLs
  if (groupedUrl.includes('/user/')) {
    const userIdMatch = originalUrl.match(/\/user\/([^\/\?]+)/)
    if (userIdMatch) {
      metadata.user_id = userIdMatch[1]
      metadata.custom_parameter_3 = userIdMatch[1] // For GA4 custom parameters
    }
  }

  // Extract organization ID from org-related URLs
  if (groupedUrl.includes('/org/')) {
    const orgIdMatch = originalUrl.match(/\/org\/([^\/\?]+)/)
    if (orgIdMatch) {
      metadata.org_id = orgIdMatch[1]
      metadata.custom_parameter_4 = orgIdMatch[1] // For GA4 custom parameters
    }
  }

  // Add page type for easier filtering
  if (groupedUrl.includes('/event/')) {
    metadata.page_type = 'event'
  } else if (groupedUrl.includes('/rsvp/')) {
    metadata.page_type = 'rsvp'
  } else if (groupedUrl.includes('/user/')) {
    metadata.page_type = 'user'
  } else if (groupedUrl.includes('/org/')) {
    metadata.page_type = 'organization'
  } else {
    metadata.page_type = 'general'
  }

  return metadata
}

/**
 * Debug function to test URL grouping patterns
 * @param testUrls Array of URLs to test
 */
export function debugUrlGrouping(testUrls: string[]): void {
  console.log('URL Grouping Debug:')
  testUrls.forEach(url => {
    const grouped = groupUrl(url)
    const title = createPageTitle(grouped, url)
    const metadata = extractUrlMetadata(url, grouped)
    console.log({
      original: url,
      grouped,
      title,
      metadata
    })
  })
}
