'use client';

import { useSearchParams } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useRouter } from 'next/router';
import { Footer } from '@/components/Footer';

export default function AuthError() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const error = searchParams?.get('error');

  const getErrorMessage = () => {
    switch (error) {
      case 'Verification':
        return 'The verification link is invalid or has expired. Please request a new one.';
      case 'Configuration':
        return 'There is a problem with the server configuration. Please try again later.';
      case 'AccessDenied':
        return 'You do not have permission to sign in.';
      case 'OAuthAccountNotLinked':
        return 'This email is already associated with an account using a different sign-in method. Please use your original sign-in method or contact support to link your accounts.';
      default:
        return 'An error occurred during authentication. Please try again.';
    }
  };

  // Additional action for OAuthAccountNotLinked error
  const handleTryAgain = () => {
    if (error === 'OAuthAccountNotLinked') {
      // Redirect to sign in page with email provider option
      router.push('/auth/signin?provider=email');
    } else {
      router.push('/auth/signin');
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-grow">
        <div className="container flex items-center justify-center py-12">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Authentication Error</CardTitle>
              <CardDescription>There was a problem signing you in</CardDescription>
            </CardHeader>
            <CardContent>
              <Alert variant="destructive">
                <AlertDescription>{getErrorMessage()}</AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button onClick={handleTryAgain}>
                Try Again
              </Button>
            </CardFooter>
          </Card>
        </div>
      </main>
      <Footer type="app" />
    </div>
  );
}