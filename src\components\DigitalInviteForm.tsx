import { MonitorIcon, UploadIcon, <PERSON><PERSON><PERSON>, Loader2 as LoaderIcon } from "lucide-react";
import { Button } from "./ui/button";
import { MobileIcon } from "@radix-ui/react-icons";
import { useState, useRef, useEffect } from "react";
import { useToast } from "./ui/use-toast";
import { useSession } from "next-auth/react";
import { useEvent } from "@/hooks/useEvent";

interface DigitalInviteFormProps {
  eventId: string;
}

export function DigitalInviteForm({ eventId }: DigitalInviteFormProps) {
  const { toast } = useToast();
  const { data: session } = useSession();
  const { event } = useEvent(eventId); // Get event data to access the message
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isMobile, setIsMobile] = useState(true); // Default to mobile view
  const [inviteImage, setInviteImage] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [generatingAI, setGeneratingAI] = useState(false);
  const [previewKey, setPreviewKey] = useState<number>(Date.now());
  const fileInputRef = useRef<HTMLInputElement>(null);

  function getDimensions(isMobile: boolean) {
    return {
      width: isMobile ? 'undefined' : '800px', // No width for mobile, fixed for desktop
      height: '600px',
    };
  }

  const [iframeDimensions, setIframeDimensions] = useState(getDimensions(true)); // Start with mobile dimensions
  // Check if there's an existing background image when component mounts
  useEffect(() => {
    if (eventId) {
      // For digital invite, use fallback logic: try digital-invite first, then invitation-card
      fetch(`/api/storage/getUrl?eventId=${eventId}&imageType=digital-invite`)
        .then(response => response.json())
        .then(data => {
          if (data.exists && data.url) {
            // Add a cache-busting parameter to ensure fresh image
            const separator = data.url.includes('?') ? '&' : '?';
            const imageUrlWithCache = data.url + separator + 'cache=' + new Date().getTime();
            setInviteImage(imageUrlWithCache);
          }
        })
        .catch(error => {
          console.error("Error fetching event image:", error);
        });
    }
  }, [eventId]);

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    if (!validTypes.includes(file.type)) {
      toast({
        title: "Invalid File Type",
        description: "Please upload a JPG or PNG image.",
        variant: "destructive"
      });
      return;
    }

    setUploading(true);

    try {      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', file);
      formData.append('eventId', eventId);
      formData.append('imageType', 'digital-invite'); // Specify this is for digital invite

      // Add user info from session if available
      if (session?.user?.email) {
        formData.append('uploadedBy', session.user.email);
      }

      // Upload using our API endpoint
      const response = await fetch('/api/storage/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'X-User-Email': session?.user?.email || 'anonymous'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }      const data = await response.json();
      if (!data.url) {
        throw new Error('No URL returned from upload API');
      }      // Verify the image can be loaded before updating the UI
      const img = new Image();
      img.onload = () => {
        // Add timestamp to prevent caching, but check if URL already has query parameters
        const separator = data.url.includes('?') ? '&' : '?';
        setInviteImage(data.url + separator + 't=' + Date.now());        // Update the preview key to force a re-render of the iframe
        setPreviewKey(Date.now());

        toast({
          title: "Upload Successful",
          description: "Your background image has been updated and will appear in the preview.",
        });
      };
      img.onerror = () => {
        // Retry after a short delay in case Firebase Storage needs time to process
        setTimeout(() => {
          const retryImg = new Image();
          retryImg.onload = () => {
            const separator = data.url.includes('?') ? '&' : '?';
            setInviteImage(data.url + separator + 't=' + Date.now());
            setPreviewKey(Date.now());
            
            toast({
              title: "Upload Successful",
              description: "Your background image has been updated and will appear in the preview.",
            });
          };
          retryImg.onerror = () => {
            setInviteImage(null);
            toast({
              title: "Warning",
              description: "Image uploaded but may not display correctly. Please refresh the page or try again.",
              variant: "destructive"
            });
          };
          retryImg.src = data.url;
        }, 2000); // Wait 2 seconds before retry
      };
      img.src = data.url;
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Could not upload the image. Please try again.",
        variant: "destructive"
      });
      setInviteImage(null);
    } finally {
      setUploading(false);
      // Clear the file input so the same file can be selected again if needed
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };
  // Trigger file input click
  const triggerFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Generate AI image based on event data
  const generateAIImage = async () => {
    if (!event) {
      toast({
        title: "Error",
        description: "Event data not loaded. Please refresh the page and try again.",
        variant: "destructive"
      });
      return;
    }

    setGeneratingAI(true);

    // Force recompilation
    try {
      const response = await fetch('/api/ai/generate-digital-invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventId: eventId, // Add the eventId from props
          eventName: event.eventName,
          eventDate: event.eventDate,
          startTime: event.start,
          endTime: event.end,
          location: event.location,
          timezone: event.timezone,
          host: event.host,
          message: event.message,
          messageStyle: 'personalised' // Default style, could be made configurable
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 429) {
          const usageInfo = data.currentUsage !== undefined && data.limit !== undefined
            ? ` (${data.currentUsage}/${data.limit} used)`
            : '';

          toast({
            title: "AI Usage Limit Exceeded",
            description: `AI usage limit exceeded${usageInfo}. Please try again later or contact support for more AI credits.`,
            variant: "destructive"
          });
          return; // Exit early instead of throwing
        }

        toast({
          title: "Generation Failed",
          description: data.error || 'Failed to generate AI image',
          variant: "destructive"
        });
        return; // Exit early instead of throwing
      }

      if (!data.result || !data.result.imageData) {
        toast({
          title: "Generation Failed",
          description: 'No image data received from AI generation',
          variant: "destructive"
        });
        return; // Exit early instead of throwing
      }

      // Convert base64 image data to blob and upload it
      const imageData = data.result.imageData;
      const mimeType = data.result.mimeType || 'image/png';
      
      // Create blob from base64 data
      const byteCharacters = atob(imageData);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: mimeType });

      // Create a file from the blob
      const file = new File([blob], `ai-generated-invite-${Date.now()}.png`, { type: mimeType });

      // Upload using the same logic as manual upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('eventId', eventId);
      formData.append('imageType', 'digital-invite');

      if (session?.user?.email) {
        formData.append('uploadedBy', session.user.email);
      }

      const uploadResponse = await fetch('/api/storage/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'X-User-Email': session?.user?.email || 'anonymous'
        }
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        toast({
          title: "Upload Failed",
          description: errorData.error || 'Upload failed',
          variant: "destructive"
        });
        return; // Exit early instead of throwing
      }

      const uploadData = await uploadResponse.json();
      if (!uploadData.url) {
        toast({
          title: "Upload Failed",
          description: 'No URL returned from upload API',
          variant: "destructive"
        });
        return; // Exit early instead of throwing
      }

      // Update the image and preview
      const separator = uploadData.url.includes('?') ? '&' : '?';
      setInviteImage(uploadData.url + separator + 't=' + Date.now());
      setPreviewKey(Date.now());

      toast({
        title: "AI Image Generated",
        description: "Your digital invite has been generated successfully!",
      });

    } catch (error) {
      console.error('Error generating AI image:', error);

      // Ensure we handle the error properly and don't let it bubble up
      const errorMessage = error instanceof Error ? error.message : "Could not generate the AI image. Please try again.";

      toast({
        title: "Generation Failed",
        description: errorMessage,
        variant: "destructive"
      });

      // Prevent the error from bubbling up to React's error boundary
      return;
    } finally {
      setGeneratingAI(false);
    }
  };

  return <div className="flex flex-col flex-1 items-stretch min-w-0 overflow-hidden">
    {/* Header section */}
    <div className="border-r p-4 sm:p-8">
      <div className="flex flex-col lg:flex-row min-w-0">
        {/* Header text */}
        <div className="flex-1 min-w-0">
          <h2 className="text-base font-semibold">Digital Invitation Preview</h2>
          <p className="text-xs text-muted-foreground">Review and customize your digital invite. Upload a cover image, edit content, and preview how it will appear to your guests.</p>
        </div>        {/* Buttons - positioned bottom on sm/md screens, right on lg+ screens */}
        <div className="flex gap-2 justify-start mt-4 lg:justify-end lg:mt-0 flex-wrap">
          {/* Dark mode button - commented out until dark mode is implemented
          <Button className="rounded-full" size={"icon"} variant={"outline"} onClick={() => {
            setIsDarkMode(!isDarkMode);
          }}>
            <MoonIcon className={isDarkMode ? "text-foreground" : "text-muted-foreground"} />
          </Button>
          */}
          <Button className="rounded-full hidden lg:flex" size={"icon"} variant={"outline"} onClick={() => {
            setIsMobile(false);
            setIframeDimensions(getDimensions(false));
          }}>
            <MonitorIcon className={!isMobile ? "text-foreground" : "text-muted-foreground"} />
          </Button>
          <Button className="rounded-full hidden lg:flex" size={"icon"} variant={"outline"} onClick={() => {
            setIsMobile(true);
            setIframeDimensions(getDimensions(true));
          }}>
            <MobileIcon className={isMobile ? "text-foreground" : "text-muted-foreground"} />
          </Button>
          <Button
            size={"sm"}
            variant={"outline"}
            onClick={triggerFileUpload}
            disabled={uploading || generatingAI}
            className={uploading ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-50"}
          >
            <UploadIcon className={uploading ? "text-gray-400 mr-2" : "text-gray-700 mr-2"} />
            <span className={uploading ? "text-gray-400 text-sm" : "text-gray-700 text-sm"}>
              {uploading ? "Uploading..." : (inviteImage ? "Change Background" : "Upload Background")}
            </span>
          </Button>
          
          {/* AI Generation Button */}
          {session?.user?.hasAiAccess && event && (
            <Button
              size={"sm"}
              variant={"primary-button"}
              onClick={() => {
                try {
                  generateAIImage();
                } catch (error) {
                  console.error('Error in generateAIImage click handler:', error);
                  toast({
                    title: "Generation Failed",
                    description: "An unexpected error occurred. Please try again.",
                    variant: "destructive"
                  });
                }
              }}
              disabled={uploading || generatingAI}
              className={generatingAI ? "opacity-50 cursor-not-allowed" : ""}
            >
              {generatingAI ? (
                <>
                  <LoaderIcon className="w-4 h-4 animate-spin mr-2" />
                  <span className="text-sm">Generating...</span>
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  <span className="text-sm">Generate with AI</span>
                </>
              )}
            </Button>
          )}
          {/* Hidden file input */}
          <input
            aria-label="Upload Background"
            type="file"
            ref={fileInputRef}
            onChange={handleFileUpload}
            accept="image/jpeg,image/png,image/jpg"
            className="hidden"          />        </div>
      </div>
    </div>
    {/* Preview section */}
    <div className="flex-1 justify-center items-center flex bg-gray-300 inset-shadow-sm p-2 sm:p-4 md:p-8 overflow-hidden min-w-0">
      {(uploading || generatingAI) ? (
        <div className="flex flex-col items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-rose-500 mb-2"></div>
          <p className="text-muted-foreground">
            {uploading ? "Uploading image..." : "Generating AI image..."}
          </p>
        </div>
      ) : (
        <div className={`${isMobile ? "w-full max-w-full min-w-0" : "flex-1"} flex justify-center overflow-hidden`}>
          <iframe
            key={previewKey} // Add key to force re-render when image changes
            title="Digital Invite Preview"
            src={`/event/${eventId}/rsvp/preview${isDarkMode ? '?theme=dark' : ''}`} // Use the actual eventId
            {...(iframeDimensions.width && { width: iframeDimensions.width })}
            height={iframeDimensions.height}
            className={`shadow ${isMobile ? "w-full max-w-full border-0" : ""}`}
            style={isMobile ? {
              maxWidth: '100%',
              width: '100%',
              border: 'none',
              display: 'block'
            } : {}}
          />
        </div>
      )}
    </div>
  </div>
}