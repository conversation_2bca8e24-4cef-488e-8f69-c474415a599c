import { useEffect, useState, useCallback } from 'react';
import { Organization } from '@/types';

interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export function useOrganizations() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  const fetchOrganizations = useCallback(async (query: string = searchQuery, page: number = pagination.page) => {
    setLoading(true);
    try {
      const url = new URL('/api/admin/organizations', window.location.origin);
      url.searchParams.append('page', page.toString());
      url.searchParams.append('limit', pagination.limit.toString());

      if (query) {
        url.searchParams.append('search', query);
      }

      const response = await fetch(url.toString());

      if (!response.ok) {
        throw new Error(`Error fetching organizations: ${response.statusText}`);
      }

      const data = await response.json();
      setOrganizations(data.organizations);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch organizations'));
    } finally {
      setLoading(false);
    }
  }, [searchQuery, pagination.page, pagination.limit]);

  useEffect(() => {
    fetchOrganizations();
  }, [fetchOrganizations]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    fetchOrganizations(query, 1); // Reset to first page on new search
  };

  const handlePageChange = (newPage: number) => {
    fetchOrganizations(searchQuery, newPage);
  };

  const getOrganizationById = (organizationId: string) => {
    return organizations.find(org => org.id === organizationId) || null;
  };

  const togglePartnerStatus = async (organizationId: string) => {
    try {
      const response = await fetch(`/api/admin/organizations/${organizationId}/toggle-partner`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error toggling partner status: ${response.statusText}`);
      }

      // Refresh the organizations list
      fetchOrganizations(searchQuery, pagination.page);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to toggle partner status'));
      return false;
    }
  };

  return {
    organizations,
    loading,
    error,
    searchQuery,
    pagination,
    fetchOrganizations,
    handleSearch,
    handlePageChange,
    getOrganizationById,
    togglePartnerStatus
  };
}
