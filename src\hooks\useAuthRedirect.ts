import { useRouter } from 'next/router';
import { useSession, signIn } from 'next-auth/react';
import { useCallback } from 'react';
import { isAllowedSite } from '@/lib/auth/config';
import {
  AllowedSite,
  generateSignInUrl,
  generateCallbackUrl,
  storeRedirectInfo,
  type SignInParams
} from '@/lib/auth/redirect';

/**
 * Hook for handling authentication redirects with site parameters
 */
export function useAuthRedirect() {
  const router = useRouter();
  const { data: session, status } = useSession();

  /**
   * Sign in with redirect to external site
   */
  const signInWithSite = useCallback(async (site: AllowedSite, redirectUrl?: string) => {
    if (!isAllowedSite(site)) {
      throw new Error(`Invalid site: ${site}`);
    }

    // Store redirect info in session storage
    storeRedirectInfo({ site, redirectUrl });

    // Generate callback URL that will handle the site redirect
    const callbackUrl = generateCallbackUrl(site);

    // Trigger NextAuth sign in with the callback URL
    await signIn(undefined, {
      callbackUrl,
      redirect: true
    });
  }, []);

  /**
   * Sign in with custom redirect URL
   */
  const signInWithRedirect = useCallback(async (redirectUrl: string) => {
    // Store redirect info in session storage
    storeRedirectInfo({ redirectUrl });

    // Trigger NextAuth sign in
    await signIn(undefined, {
      callbackUrl: `/auth/signin?redirectUrl=${encodeURIComponent(redirectUrl)}`,
      redirect: true
    });
  }, []);

  /**
   * Navigate to sign-in page with parameters
   */
  const navigateToSignIn = useCallback((params: SignInParams = {}) => {
    const signInUrl = generateSignInUrl(params);
    router.push(signInUrl);
  }, [router]);

  /**
   * Check if user is authenticated and handle redirect
   */
  const handleAuthenticatedRedirect = useCallback(() => {
    if (status !== 'authenticated' || !session) {
      return false;
    }

    // Check for site parameter in current URL
    const site = router.query.site as string;
    if (site && isAllowedSite(site)) {
      router.push(generateCallbackUrl(site));
      return true;
    }

    return false;
  }, [status, session, router]);

  return {
    // State
    isAuthenticated: status === 'authenticated',
    isLoading: status === 'loading',
    session,

    // Actions
    signInWithSite,
    signInWithRedirect,
    navigateToSignIn,
    handleAuthenticatedRedirect,

    // Utilities
    isAllowedSite,
  };
}

/**
 * Hook specifically for external sites that need to redirect users to IAC for authentication
 */
export function useExternalSiteAuth(site: AllowedSite) {
  const { signInWithSite, isAuthenticated } = useAuthRedirect();

  /**
   * Redirect to IAC for authentication
   */
  const redirectToAuth = useCallback((redirectUrl?: string) => {
    signInWithSite(site, redirectUrl);
  }, [site, signInWithSite]);

  return {
    isAuthenticated,
    redirectToAuth,
  };
}
