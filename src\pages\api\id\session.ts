import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { setIacCorsHeaders } from '@/lib/cors';
import { rbacConfig } from '@/lib/auth/RBAC';
import { getUserOrganizationsWithRBAC } from '@/lib/auth/RBAC/organization-utils';
import type { SessionAPIResponse, AdminRolePermissions, SessionOrganization } from '@/types/session-api';
import crypto from 'crypto';

function generateApiToken() {
  return crypto.randomBytes(32).toString('hex');
}

function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};
  cookieHeader.split(';').forEach(cookie => {
    const [name, ...rest] = cookie.trim().split('=');
    if (name && rest.length > 0) {
      cookies[name] = rest.join('=');
    }
  });
  return cookies;
}

function verifySessionToken(token: string, debug = false): { userId: string; email: string } | null {
  try {
    const secret = process.env.AUTH_SECRET || '';

    const parts = token.split('.');

    if (parts.length !== 3) {
      return null;
    }

    const [headerB64, payloadB64, signatureB64] = parts;

    // Verify signature
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(`${headerB64}.${payloadB64}`)
      .digest('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');

    if (signatureB64 !== expectedSignature) {
      return null;
    }

    // Decode payload
    const payload = JSON.parse(Buffer.from(payloadB64, 'base64').toString());

    // Check expiration
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    // Check issuer and audience
    if (payload.iss !== 'iamcoming.io') {
      return null;
    }

    return {
      userId: payload.userId,
      email: payload.email
    };
  } catch (error) {
    console.error('Error verifying session token:', error);
    return null;
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  setIacCorsHeaders(req, res);

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET', 'OPTIONS']);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Enable debugging
  const DEBUG = process.env.IAC_DEBUG === 'true' || process.env.NODE_ENV === 'development';

  try {
    let userId: string | null = null;
    let userEmail: string | null = null;
    let authMethod = 'none';

    // Check for cross-domain session cookie first (for external domains)
    let crossDomainToken = null;
    if (req.headers.cookie) {
      const cookies = parseCookies(req.headers.cookie);
      crossDomainToken = cookies['cross-domain-session'];
    }

    if (crossDomainToken) {
      authMethod = 'cross-domain-cookie';
      const tokenData = verifySessionToken(crossDomainToken, DEBUG);
      if (tokenData) {
        userId = tokenData.userId;
        userEmail = tokenData.email;
      } else {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid cross-domain session token',
          code: 'TOKEN_INVALID',
          debug: DEBUG ? { authMethod, tokenPresent: !!crossDomainToken } : undefined
        });
      }
    } else {
      authMethod = 'nextauth-session';
      // Fall back to regular NextAuth session (same-domain)
      const session = await getServerSession(req, res, authConfig);

      if (!session?.user?.id) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'No valid session found',
          code: 'SESSION_INVALID',
          debug: DEBUG ? {
            authMethod,
            sessionExists: !!session,
            userExists: !!session?.user,
            userIdExists: !!session?.user?.id,
            cookiesPresent: !!req.headers.cookie
          } : undefined
        });
      }
      userId = session.user.id;
      userEmail = session.user.email || null;
    }

    if (!userId) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'No valid session found',
        code: 'SESSION_INVALID',
        debug: DEBUG ? { authMethod, userIdMissing: true } : undefined
      });
    }

    const db = Database.getInstance();
    const profile = await db.getUserProfile(userId);

    if (!profile) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User profile not found',
        code: 'USER_NOT_FOUND',
        debug: DEBUG ? { authMethod, userId, profileMissing: true } : undefined
      });
    }

    // Fetch user organization RBAC data
    const userOrganizations = await getUserOrganizationsWithRBAC(userId);

    // Compute system-level admin permissions for this user
    const systemRolePermissions: AdminRolePermissions = profile.isAdmin
      ? (rbacConfig.admin.rolePermissions as AdminRolePermissions)
      : {};

    const apiToken = generateApiToken();

    const { passwordHash, resetPasswordToken, resetPasswordExpires, ...safeProfile } = profile as any;

    // Map to cleaned SessionOrganization structure (partner organizations only)
    const organizations: SessionOrganization[] = userOrganizations
      .filter(org => org.type === 'partner')
      .map(org => ({
        id: org.id,
        name: org.name,
        type: 'partner',
        rbacConfig: { rolePermissions: org.rbacConfig.rolePermissions },
      }));

    const sessionResponse: SessionAPIResponse = {
      user: {
        id: safeProfile.id,
        name: safeProfile.name || '',
        email: safeProfile.email,
        avatar: safeProfile.image || undefined,
        profile: safeProfile,
      },
      organizations,
      permissions: { rolePermissions: systemRolePermissions },
      token: apiToken,
    };

    return res.status(200).json(sessionResponse);
  } catch (error) {
    console.error('Session endpoint error:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve session data',
      code: 'SESSION_FETCH_ERROR',
    });
  }
}

