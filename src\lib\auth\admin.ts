// lib/auth/admin.ts
import { getServerSession } from "next-auth/next"
import { authConfig } from "@/auth"
import { GetServerSidePropsContext, GetServerSidePropsResult } from "next"
import { Session } from "next-auth"
import { Database } from "@/lib/database"

/**
 * Higher-order function that wraps getServerSideProps to check if the user is an admin
 * If the user is not an admin, they will be redirected to the access denied page
 */
export function withAdminAuth(getServerSidePropsFunc?: (context: GetServerSidePropsContext, session: Session) => Promise<any>) {
  return async (context: GetServerSidePropsContext): Promise<GetServerSidePropsResult<any>> => {
    try {
      const session = await getServerSession(
        context.req,
        context.res,
        authConfig
      );

      // If no session, redirect to login
      if (!session) {
        return {
          redirect: {
            destination: '/auth/signin',
            permanent: false,
          },
        };
      }

      // Check if user is an admin
      const db = Database.getInstance();
      const userProfile = await db.getUserByEmail(session.user.email);
      const isAdmin = !!userProfile?.isAdmin;

      // If not admin, redirect to access denied page
      if (!isAdmin) {
        return {
          redirect: {
            destination: '/access-denied',
            permanent: false,
          },
        };
      }

      // User is authenticated and is an admin, proceed
      const props = getServerSidePropsFunc
        ? await getServerSidePropsFunc(context, session)
        : {};

      return {
        props: {
          ...props,
          session,
        },
      };
    } catch (error) {
      console.error('Error in withAdminAuth:', error);
      return {
        redirect: {
          destination: '/auth/error',
          permanent: false,
        },
      };
    }
  };
}
