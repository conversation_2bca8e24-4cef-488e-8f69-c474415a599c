// =============================================================================
// PARTNER PORTAL RBAC DEFINITIONS
// =============================================================================

// Import shared permissions
import { permissions, type Permission } from './permissions';

// Partner Portal Resources
export const partnerResources = {
  // Root resource
  PARTNER: 'partner',

  // Core features
  PARTNER_DASHBOARD: 'partner:dashboard',
  PARTNER_VENUES: 'partner:venues',
  PARTNER_TEAM: 'partner:team',
  PARTNER_BILLING: 'partner:billing',
  PARTNER_CUSTOMERS: 'partner:customers',
  PARTNER_SETTINGS: 'partner:settings',

  // Venue-specific resources
  PARTNER_VENUE: 'partner:venue', // Base for dynamic venue IDs
  PARTNER_VENUE_EVENTS: 'partner:venue:events', // Base for dynamic venue IDs
  PARTNER_VENUE_SETTINGS: 'partner:venue:settings', // Base for dynamic venue IDs

  // Wildcard patterns
  PARTNER_VENUE_ALL: 'partner:venue:*',
} as const;

export type PartnerResource = typeof partnerResources[keyof typeof partnerResources];

// Partner Portal Roles
export const partnerRoles = {
  ADMIN: 'partner:admin',
  MANAGER: 'partner:manager',
  STAFF: 'partner:staff',
  VIEWER: 'partner:viewer',
} as const;

export type PartnerRole = typeof partnerRoles[keyof typeof partnerRoles];

// Permission configuration for each partner role
export const partnerRolePermissions: Record<PartnerRole, Record<string, Permission[]>> = {
  [partnerRoles.ADMIN]: {
    // Full access to all partner resources
    [partnerResources.PARTNER]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [partnerResources.PARTNER_DASHBOARD]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [partnerResources.PARTNER_VENUES]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [partnerResources.PARTNER_TEAM]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [partnerResources.PARTNER_BILLING]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [partnerResources.PARTNER_CUSTOMERS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [partnerResources.PARTNER_SETTINGS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
    [partnerResources.PARTNER_VENUE_ALL]: [permissions.VIEW, permissions.CREATE, permissions.EDIT, permissions.DELETE],
  },

  [partnerRoles.MANAGER]: {
    // Management access to dashboard, venues, team, and billing
    [partnerResources.PARTNER_DASHBOARD]: [permissions.VIEW],
    [partnerResources.PARTNER_VENUES]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
    [partnerResources.PARTNER_TEAM]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
    [partnerResources.PARTNER_BILLING]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
    [partnerResources.PARTNER_VENUE_ALL]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
  },

  [partnerRoles.STAFF]: {
    // Customer-focused access
    [partnerResources.PARTNER_DASHBOARD]: [permissions.VIEW],
    [partnerResources.PARTNER_VENUES]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
    [partnerResources.PARTNER_CUSTOMERS]: [permissions.VIEW, permissions.CREATE, permissions.EDIT],
  },

  [partnerRoles.VIEWER]: {
    // Read-only access
    [partnerResources.PARTNER_DASHBOARD]: [permissions.VIEW],
    [partnerResources.PARTNER_VENUES]: [permissions.VIEW],
    [partnerResources.PARTNER_CUSTOMERS]: [permissions.VIEW],
  },
};

/**
 * Helper function to check if a partner role has permission for a resource
 */
export function hasPartnerPermission(
  role: PartnerRole,
  resource: string,
  permission: Permission,
  venueId?: string
): boolean {
  const rolePermissions = partnerRolePermissions[role];

  // Handle venue-specific resources
  if (venueId && resource.includes('venue')) {
    const venueSpecificResource = resource.replace(':venue', `:venue:${venueId}`);
    if (rolePermissions[venueSpecificResource]?.includes(permission)) {
      return true;
    }
  }

  // Check wildcard permissions
  if (resource.includes('venue') && rolePermissions[partnerResources.PARTNER_VENUE_ALL]?.includes(permission)) {
    return true;
  }

  // Check direct resource permissions
  return rolePermissions[resource]?.includes(permission) || false;
}

/**
 * Get all permissions for a partner role
 */
export function getPartnerRolePermissions(role: PartnerRole): Record<string, Permission[]> {
  return partnerRolePermissions[role] || {};
}

// Backward compatibility aliases
export const hasPermission = hasPartnerPermission;
export const getRolePermissions = getPartnerRolePermissions;

