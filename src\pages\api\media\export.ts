import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { RenderInvite } from '@/lib/media/invite';
import { RenderLabel } from '@/lib/media/label';
import JSZ<PERSON> from 'jszip';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import sharp from 'sharp';
import { Database } from '@/lib/database';
import { Event, EventInvite } from '@/types';
import { getEventInviteImageUrl } from '@/lib/storage';
import fetch from 'node-fetch';
import { generateQrRsvpLink, mmToPixels } from '@/lib/utils';
import { GetLabelDimensions, MM_TO_INCH, DEFAULT_LOGO_PATH } from '@/constants/defaults';
import fs from 'fs';
import path from 'path';

/**
 * Fetches event background image directly from storage without caching
 * @param eventId - Event ID to fetch image for
 * @returns Image buffer
 */
async function getEventImage(eventId: string): Promise<Buffer> {
  console.log(`Fetching image for event ${eventId} directly from storage`);

  // Get the URL from storage
  const result = await getEventInviteImageUrl(eventId);
  const url = result?.url || null;

  if (!url) {
    console.warn(`No image URL found for event ${eventId}, using default image`);
    return createDefaultEventImage();
  }

  try {
    // Add a timestamp to the URL to prevent caching
    const urlWithTimestamp = url.includes('?')
      ? `${url}&t=${Date.now()}`
      : `${url}?t=${Date.now()}`;

    console.log(`Fetching image from URL: ${urlWithTimestamp}`);

    // Add a timeout of 5 seconds to avoid hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const res = await fetch(urlWithTimestamp, {
      signal: controller.signal,
      // Disable SSL verification for development purposes
      // In production, this should be properly handled with valid certificates
      agent: function (_parsedURL) {
        if (_parsedURL.protocol === 'https:') {
          const https = require('https');
          return new https.Agent({
            rejectUnauthorized: false
          });
        }
        return null;
      }
    });

    clearTimeout(timeoutId);

    if (!res.ok) throw new Error(`Failed to fetch event image: ${res.statusText}`);
    const buffer = await res.buffer();
    return buffer;
  } catch (error) {
    console.warn(`Failed to fetch event image for ${eventId}: ${error instanceof Error ? error.message : String(error)}`);
    // Return a default/fallback image instead
    return createDefaultEventImage();
  }
}

/**
 * Creates a default background image when the event image cannot be fetched
 * @returns Default image buffer
 */
async function createDefaultEventImage(): Promise<Buffer> {
  try {
    // Create a simple gradient background as fallback
    const width = 800;
    const height = 600;

    return await sharp({
      create: {
        width,
        height,
        channels: 4,
        background: { r: 245, g: 247, b: 250, alpha: 1 }
      }
    })
      .composite([
        {
          input: Buffer.from(
            `<svg width="${width}" height="${height}">
            <defs>
              <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#4776E6;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#8E54E9;stop-opacity:1" />
              </linearGradient>
            </defs>
            <rect width="${width}" height="${height}" fill="url(#grad)" />
            <text x="50%" y="50%" font-family="sans-serif" font-size="24" text-anchor="middle" fill="white">Event Background</text>
          </svg>`
          )
        }
      ])
      .png()
      .toBuffer();
  } catch (error) {
    console.error('Error creating default image:', error);
    // If creating a default image fails, create an even simpler fallback
    return await sharp({
      create: {
        width: 800,
        height: 600,
        channels: 3,
        background: { r: 100, g: 100, b: 200 }
      }
    })
      .png()
      .toBuffer();
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check authentication
    const session = await getServerSession(req, res, authConfig);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get query parameters
    const { type, invites: invitesParam } = req.query;
    const inviteIds = Array.isArray(invitesParam)
      ? invitesParam
      : typeof invitesParam === 'string'
        ? invitesParam.split(',')
        : [];

    if (inviteIds.length === 0) {
      return res.status(400).json({ error: 'No invites selected' });
    }

    // Get the database instance
    const db = Database.getInstance();

    // Get the first invite to determine eventId
    const firstInvite = await db.readData('invites', Database.normalizeId(inviteIds[0])) as EventInvite;
    if (!firstInvite) {
      return res.status(400).json({ error: 'Invalid invite ID' });
    }

    // Get event for print settings
    const event = await db.readData('events', Database.normalizeId(firstInvite.eventId)) as Event;
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Get all invites
    const invites = await Promise.all(
      inviteIds.map(id => db.readData('invites', Database.normalizeId(id)) as Promise<EventInvite>)
    );
    const validInvites = invites.filter(Boolean);

    if (validInvites.length === 0) {
      return res.status(404).json({ error: 'No valid invites found' });
    }

    // Determine export type and format
    const typeStr = type as string || '';

    // Handle formats like 'invitation-cards-pdf' or 'qr-labels-jpeg'
    let exportType: string;
    let exportFormat: string;

    if (typeStr.startsWith('invitation-cards-')) {
      exportType = 'invitation-cards';
      exportFormat = typeStr.replace('invitation-cards-', '');
    } else if (typeStr.startsWith('qr-labels-')) {
      exportType = 'qr-labels';
      exportFormat = typeStr.replace('qr-labels-', '');
    } else {
      // Fallback to legacy parsing or default values
      const parts = typeStr.split('-');
      exportType = parts[0] || 'qr-labels';
      exportFormat = parts[1] || 'pdf';
    }

    // Handle different export types
    if (exportType === 'qr-labels') {
      return handleQRLabelsExport(validInvites, exportFormat, event, res);
    } else if (exportType === 'invitation-cards') {
      return handleInvitationCardsExport(validInvites, exportFormat, event, res);
    } else {
      return res.status(400).json({ error: 'Invalid export type' });
    }

  } catch (error) {
    console.error('Export error:', error);
    return res.status(500).json({ error: 'Failed to process export' });
  }
}

/**
 * Creates a simple fallback label when there's an error generating the real one
 * @param guestName - Name of the guest for the label
 * @returns Object matching the structure of RenderLabel output
 */
async function createSimpleFallbackLabel(guestName: string): Promise<{
  fileContents: Buffer;
  mimeType: string;
  dimensions: { width: number; height: number };
}> {
  try {
    const width = 350;
    const height = 200;
    const escapedName = guestName
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');

    const svgImage = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${width}" height="${height}" fill="#FFFFFF" stroke="#000000" stroke-width="2" />
        <text x="50%" y="40%" font-family="sans-serif" font-size="18" text-anchor="middle" fill="#000000">QR Label</text>
        <text x="50%" y="60%" font-family="sans-serif" font-size="16" text-anchor="middle" fill="#000000">${escapedName}</text>
      </svg>`;

    const imageBuffer = await sharp(Buffer.from(svgImage))
      .png()
      .toBuffer();

    return {
      fileContents: imageBuffer,
      mimeType: "image/png",
      dimensions: { width, height }
    };
  } catch (error) {
    console.error('Error creating fallback label:', error);
    // Last resort fallback - create a simple colored rectangle
    const imageBuffer = await sharp({
      create: {
        width: 350,
        height: 200,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      }
    })
      .png()
      .toBuffer();

    return {
      fileContents: imageBuffer,
      mimeType: "image/png",
      dimensions: { width: 350, height: 200 }
    };
  }
}

/**
 * Handles QR label exports in PDF or JPEG format
 */
async function handleQRLabelsExport(invites: EventInvite[], format: string, event: Event, res: NextApiResponse) {
  // Generate QR labels using RenderLabel
  const labelPromises = invites.map(invite => {
    try {
      const qrContent = generateQrRsvpLink({ eventId: invite.eventId, ID: invite.ID });
      // Convert print settings to the expected format
      const labelOptions = event?.printSettings?.labelSettings ? {
        orientation: event.printSettings.labelSettings.orientation,
        theme: {
          name: 'default',
          qr: event.printSettings.labelSettings.qrColor || '#000000',
          background: event.printSettings.labelSettings.bgColor || '#FFFFFF',
        },
        showBranding: true
      } : undefined;

      // Sanitize the name to prevent XML parsing errors
      const sanitizedName = (invite.name || 'Guest').trim();

      return RenderLabel(sanitizedName, qrContent, labelOptions);
    } catch (error) {
      console.error(`Error generating QR label for ${invite.name}:`, error);
      // Create a simple fallback QR label
      return createSimpleFallbackLabel(invite.name);
    }
  });

  const labels = await Promise.all(labelPromises);

  if (format === 'pdf') {
    // Create a PDF with multiple labels per page in a grid layout
    const pdfDoc = await PDFDocument.create();

    // Load fonts for the header
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Load logo for header
    let logoImage;
    try {
      const logoPath = path.resolve(process.cwd(), 'public', '<EMAIL>');
      const logoData = fs.readFileSync(logoPath);
      logoImage = await pdfDoc.embedPng(logoData);
    } catch (error) {
      console.warn('Failed to load logo for PDF header:', error);
      // Continue without logo
    }

    // Create embeddings object for reuse in header drawing
    const embeddings = {
      logo: logoImage,
      regularFont: helveticaFont,
      boldFont: helveticaBoldFont
    };

    // A4 dimensions in points (1 point = 1/72 inch)
    const A4_WIDTH = 595.28;
    const A4_HEIGHT = 841.89;

    // Define margins (10mm = ~28.35 points)
    const MARGIN = 30;    // Header height in mm and points
    const HEADER_HEIGHT_MM = 15; // Allow 15mm for header (10mm for logo + some spacing)
    const HEADER_HEIGHT = HEADER_HEIGHT_MM * (72 / MM_TO_INCH); // Convert to points

    // Additional spacing after header (10mm extra space between header and first row)
    const HEADER_SPACING_MM = 10;
    const HEADER_SPACING = HEADER_SPACING_MM * (72 / MM_TO_INCH); // Convert to points

    // Get label dimensions from the constants, in mm
    const labelDimensions = GetLabelDimensions();

    // Convert mm to points (1 point = 1/72 inch, 1 inch = 25.4 mm)
    // So 1 mm = (1/25.4) * 72 points = 2.83464567 points
    const MM_TO_POINTS = 72 / MM_TO_INCH;

    // Get dimensions based on orientation from the event settings
    const orientation = event?.printSettings?.labelSettings?.orientation || 'portrait';
    const dimensionsInMM = orientation === 'landscape'
      ? labelDimensions.LANDSCAPE
      : labelDimensions.PORTRAIT;

    // Convert mm to points for PDF layout
    const labelWidthPoints = dimensionsInMM.width * MM_TO_POINTS;
    const labelHeightPoints = dimensionsInMM.height * MM_TO_POINTS;

    console.log(`Label dimensions in mm: ${dimensionsInMM.width}x${dimensionsInMM.height}mm`);
    console.log(`Label dimensions in points: ${labelWidthPoints.toFixed(2)}x${labelHeightPoints.toFixed(2)}pt`);

    // Check if rendered label dimensions are significantly different from expected dimensions
    // This would indicate that the label generation process is not respecting the dimensions
    const renderedWidth = labels[0].dimensions.width;
    const renderedHeight = labels[0].dimensions.height;
    const widthDiff = Math.abs(renderedWidth - labelWidthPoints);
    const heightDiff = Math.abs(renderedHeight - labelHeightPoints);

    if (widthDiff > 10 || heightDiff > 10) {
      console.warn(
        `Warning: Rendered label dimensions (${renderedWidth}x${renderedHeight}) differ significantly from ` +
        `expected dimensions (${labelWidthPoints.toFixed(2)}x${labelHeightPoints.toFixed(2)}). ` +
        `Labels may not be laid out as expected.`
      );
    }

    // Use a consistent spacing in points
    const SPACING_MM = 5; // 5mm spacing between labels
    const SPACING = SPACING_MM * MM_TO_POINTS; // Convert to points

    // For backward compatibility with existing code, define these variables
    const labelWidth = labelWidthPoints;
    const labelHeight = labelHeightPoints;

    // Ensure we have at least one label per row/column (defensive programming)
    const labelsPerRow = Math.max(1, Math.floor((A4_WIDTH - 2 * MARGIN) / (labelWidth + SPACING)));
    // Account for header space and additional spacing in column calculation
    const labelsPerCol = Math.max(1, Math.floor((A4_HEIGHT - 2 * MARGIN - HEADER_HEIGHT - HEADER_SPACING) / (labelHeight + SPACING)));
    const labelsPerPage = labelsPerRow * labelsPerCol;

    // Calculate actual spacing to ensure even distribution
    const effectiveWidth = A4_WIDTH - 2 * MARGIN;
    const effectiveHeight = A4_HEIGHT - 2 * MARGIN - HEADER_HEIGHT - HEADER_SPACING; // Account for header and spacing

    // Adjust spacing if we have multiple labels to ensure they're evenly distributed
    const horizontalSpacing = labelsPerRow > 1
      ? (effectiveWidth - (labelsPerRow * labelWidth)) / (labelsPerRow - 1)
      : SPACING;
    const verticalSpacing = labelsPerCol > 1
      ? (effectiveHeight - (labelsPerCol * labelHeight)) / (labelsPerCol - 1)
      : SPACING;

    // Calculate number of pages needed
    const totalPages = Math.ceil(labels.length / labelsPerPage);

    console.log(`Grid layout: ${labelsPerRow}x${labelsPerCol} = ${labelsPerPage} labels per page`);
    console.log(`Label dimensions: ${labelWidth}x${labelHeight} points`);
    console.log(`Spacing: horizontal=${horizontalSpacing.toFixed(1)}, vertical=${verticalSpacing.toFixed(1)} points`);
    console.log(`Total labels to print: ${labels.length} (${totalPages} page${totalPages !== 1 ? 's' : ''})`);

    // Convert all labels to JPEG format for PDF compatibility
    const jpegLabels = await Promise.all(
      labels.map(async (label) => {
        return await sharp(label.fileContents)
          .jpeg()
          .toBuffer();
      })
    );

    for (let pageNum = 0; pageNum < totalPages; pageNum++) {
      // Add a new A4 page
      const page = pdfDoc.addPage([A4_WIDTH, A4_HEIGHT]);

      // Draw page header
      await drawPageHeader(
        page,
        pageNum + 1,  // 1-based page numbering
        totalPages,
        event,
        embeddings,
        HEADER_HEIGHT,
        MARGIN,
        A4_WIDTH
      );

      // Calculate start index for labels on this page
      const startIdx = pageNum * labelsPerPage;
      const endIdx = Math.min(startIdx + labelsPerPage, labels.length);

      // Embed all images for this page
      const pageImages = await Promise.all(
        jpegLabels
          .slice(startIdx, endIdx)
          .map(async (jpegData) => await pdfDoc.embedJpg(jpegData))
      );

      // Place each label in the grid, accounting for header space
      for (let i = 0; i < pageImages.length; i++) {
        const row = Math.floor(i / labelsPerRow);
        const col = i % labelsPerRow;

        // Calculate position for this label with the adjusted spacing
        // Adjust y position to account for header height and additional spacing
        const x = MARGIN + col * (labelWidth + horizontalSpacing);
        const y = A4_HEIGHT - MARGIN - labelHeight - row * (labelHeight + verticalSpacing) - HEADER_HEIGHT - HEADER_SPACING;

        // Draw the label - always use the defined dimensions from GetLabelDimensions
        // This ensures that all labels have consistent size regardless of the image itself
        page.drawImage(pageImages[i], {
          x,
          y,
          width: labelWidth,
          height: labelHeight,
        });

        // Draw cut marks
        const lineWidth = 0.5;
        const cutMarkLength = 5;

        // Using PDF-Lib RGB color format with the rgb helper function
        const blackColor = rgb(0, 0, 0);

        try {
          // Helper function to ensure coordinates are within page bounds
          const ensureWithinBounds = (coord: number, max: number) => Math.max(0, Math.min(coord, max));

          // Draw cut marks around the label with bounds checking
          // Make sure to respect the header area at the top of the page
          const drawCutMark = (startX: number, startY: number, endX: number, endY: number) => {
            // Define forbidden area for header - no cut marks should intrude into header area
            const headerBottom = A4_HEIGHT - MARGIN - HEADER_HEIGHT - HEADER_SPACING;

            startX = ensureWithinBounds(startX, A4_WIDTH);
            startY = ensureWithinBounds(startY, A4_HEIGHT);
            // Don't draw into header area
            if (startY > headerBottom && startY < A4_HEIGHT - MARGIN) {
              startY = headerBottom;
            }

            endX = ensureWithinBounds(endX, A4_WIDTH);
            endY = ensureWithinBounds(endY, A4_HEIGHT);
            // Don't draw into header area
            if (endY > headerBottom && endY < A4_HEIGHT - MARGIN) {
              endY = headerBottom;
            }

            page.drawLine({
              start: { x: startX, y: startY },
              end: { x: endX, y: endY },
              thickness: lineWidth,
              color: blackColor,
            });
          };

          // Left top corner
          drawCutMark(x - cutMarkLength, y, x, y); // Horizontal
          drawCutMark(x, y - cutMarkLength, x, y); // Vertical

          // Right top corner
          drawCutMark(x + labelWidth, y - cutMarkLength, x + labelWidth, y); // Vertical
          drawCutMark(x + labelWidth, y, x + labelWidth + cutMarkLength, y); // Horizontal

          // Left bottom corner
          drawCutMark(x, y + labelHeight, x, y + labelHeight + cutMarkLength); // Vertical
          drawCutMark(x - cutMarkLength, y + labelHeight, x, y + labelHeight); // Horizontal

          // Right bottom corner
          drawCutMark(x + labelWidth, y + labelHeight, x + labelWidth, y + labelHeight + cutMarkLength); // Vertical
          drawCutMark(x + labelWidth, y + labelHeight, x + labelWidth + cutMarkLength, y + labelHeight); // Horizontal
        } catch (error) {
          console.warn(`Error drawing cut marks for label at (${x}, ${y}): ${error}. Continuing with next label.`);
        }
      }
    }

    // Save PDF with error handling
    let pdfBytes: Uint8Array;
    try {
      console.log('Finalizing PDF with cut marks...');
      pdfBytes = await pdfDoc.save();
      console.log(`Generated PDF: ${pdfBytes.length} bytes, ${totalPages} pages`);
    } catch (error) {
      console.error('Error saving PDF document:', error);
      return res.status(500).json({ error: 'Failed to generate PDF document' });
    }

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename="qr_labels.pdf"');
    return res.send(Buffer.from(pdfBytes));
  } else if (format === 'jpeg') {
    // If multiple labels, create a ZIP archive
    if (labels.length > 1) {
      const zip = new JSZip();

      labels.forEach((label: {
        fileContents: Buffer;
        mimeType: string;
        dimensions: { width: number; height: number };
      }, index: number) => {
        const invite = invites[index];
        const filename = `${invite.name.replace(/[^a-z0-9]/gi, '_')}_label.jpg`;
        zip.file(filename, label.fileContents);
      });

      const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

      res.setHeader('Content-Type', 'application/zip');
      res.setHeader('Content-Disposition', 'attachment; filename="qr_labels.zip"');
      return res.send(zipBuffer);
    } else {
      // Single label, return as JPEG
      res.setHeader('Content-Type', 'image/jpeg');
      res.setHeader('Content-Disposition', `attachment; filename="${invites[0].name.replace(/[^a-z0-9]/gi, '_')}_label.jpg"`);
      return res.send(labels[0].fileContents);
    }
  } else {
    return res.status(400).json({ error: 'Invalid export format' });
  }
}

/**
 * Creates a fallback invitation image when there's an error generating the real one
 * @param guestName - Name of the guest for the invitation
 * @returns ImageBuffer with fallback image
 */
async function createFallbackInviteImage(guestName: string): Promise<{
  fileContents: Buffer;
  mimeType: string;
  dimensions: { width: number; height: number };
}> {
  try {
    const width = 800;
    const height = 1200;
    const escapedName = guestName
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');

    const svgImage = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#4776E6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#8E54E9;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="${width}" height="${height}" fill="url(#grad)" />
        <text x="50%" y="45%" font-family="sans-serif" font-size="32" text-anchor="middle" fill="white">You're Invited!</text>
        <text x="50%" y="55%" font-family="sans-serif" font-size="24" text-anchor="middle" fill="white">${escapedName}</text>
      </svg>`;

    const imageBuffer = await sharp(Buffer.from(svgImage))
      .png()
      .toBuffer();

    return {
      fileContents: imageBuffer,
      mimeType: "image/png",
      dimensions: { width, height }
    };
  } catch (error) {
    console.error('Error creating fallback invitation:', error);
    // Last resort fallback - create a colored rectangle
    const imageBuffer = await sharp({
      create: {
        width: 800,
        height: 1200,
        channels: 4,
        background: { r: 100, g: 100, b: 200, alpha: 1 }
      }
    })
      .png()
      .toBuffer();

    return {
      fileContents: imageBuffer,
      mimeType: "image/png",
      dimensions: { width: 800, height: 1200 }
    };
  }
}

/**
 * Handles invitation card exports in PDF or JPEG format
 */
async function handleInvitationCardsExport(invites: EventInvite[], format: string, event: Event, res: NextApiResponse) {
  // For invitation cards, we need both the invitation template and the QR label

  // Get background image for invitations from storage
  let backgroundImage: Buffer;
  try {
    backgroundImage = await getEventImage(event.ID || '');
  } catch (error) {
    console.error('Error fetching event background image:', error);
    backgroundImage = await createDefaultEventImage();
  }

  // Get dimensions from the image
  const dimensions = await sharp(backgroundImage).metadata()
    .then(metadata => ({
      width: metadata.width || 1000,
      height: metadata.height || 1500
    }));

  const backgroundImageBuffer = {
    fileContents: backgroundImage,
    mimeType: 'image/png',
    dimensions
  };

  // Generate labels and then invitations
  const invitationPromises = invites.map(async (invite) => {
    try {
      const qrContent = generateQrRsvpLink({ eventId: invite.eventId, ID: invite.ID });
      // Convert print settings to the expected format for label
      const labelOptions = event?.printSettings?.labelSettings ? {
        orientation: event.printSettings.labelSettings.orientation,
        theme: {
          name: 'default',
          qr: event.printSettings.labelSettings.qrColor || '#000000',
          background: event.printSettings.labelSettings.bgColor || '#FFFFFF',
        },
        showBranding: true
      } : undefined;

      // Sanitize the name to prevent XML parsing errors
      const sanitizedName = (invite.name || 'Guest').trim();

      const label = await RenderLabel(sanitizedName, qrContent, labelOptions);

      // Render full invitation with background and label
      return RenderInvite(backgroundImageBuffer, label, event?.printSettings);
    } catch (error) {
      console.error(`Error generating invitation for ${invite.name}:`, error);
      // Return a fallback image instead of failing the whole export
      return createFallbackInviteImage(invite.name);
    }
  });

  const invitations = await Promise.all(invitationPromises);

  if (format === 'pdf') {
    // Create PDF with multiple invitations
    const pdfDoc = await PDFDocument.create();

    for (const invitation of invitations) {
      // Convert to JPEG for PDF compatibility
      const jpegData = await sharp(invitation.fileContents)
        .jpeg()
        .toBuffer();

      // Add page and embed image
      const page = pdfDoc.addPage([invitation.dimensions.width, invitation.dimensions.height]);
      const jpegImage = await pdfDoc.embedJpg(jpegData);

      page.drawImage(jpegImage, {
        x: 0,
        y: 0,
        width: invitation.dimensions.width,
        height: invitation.dimensions.height,
      });
    }

    const pdfBytes = await pdfDoc.save();

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename="invitations.pdf"');
    return res.send(Buffer.from(pdfBytes));
  } else if (format === 'jpeg') {
    // If multiple invitations, create a ZIP archive
    if (invitations.length > 1) {
      const zip = new JSZip();

      invitations.forEach((invitation: {
        fileContents: Buffer;
        mimeType: string;
        dimensions: { width: number; height: number };
      }, index: number) => {
        const invite = invites[index];
        const filename = `${invite.name.replace(/[^a-z0-9]/gi, '_')}_invitation.jpg`;
        zip.file(filename, invitation.fileContents);
      });

      const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

      res.setHeader('Content-Type', 'application/zip');
      res.setHeader('Content-Disposition', 'attachment; filename="invitations.zip"');
      return res.send(zipBuffer);
    } else {
      // Single invitation, return as JPEG
      res.setHeader('Content-Type', 'image/jpeg');
      res.setHeader('Content-Disposition', `attachment; filename="${invites[0].name.replace(/[^a-z0-9]/gi, '_')}_invitation.jpg"`);
      return res.send(invitations[0].fileContents);
    }
  } else {
    return res.status(400).json({ error: 'Invalid export format' });
  }
}

/**
 * Draws a header on a PDF page with event information, logo, and page numbers
 * @param page - The PDF page object to draw on
 * @param pageNumber - Current page number (1-based)
 * @param totalPages - Total number of pages
 * @param event - Event object containing event details
 * @param embeddings - Object containing embedded resources (logo, fonts)
 * @param headerHeight - Height of the header section in points
 * @param margin - Page margin in points
 * @param pageWidth - Width of the page in points
 */
async function drawPageHeader(
  page: any,
  pageNumber: number,
  totalPages: number,
  event: Event,
  embeddings: {
    logo?: any;
    regularFont?: any;
    boldFont?: any;
  },
  headerHeight: number,
  margin: number,
  pageWidth: number
) {
  // Get current date/time in ISO format with timezone
  const now = new Date();
  const printDate = now.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short'
  });

  // Set coordinates for header elements
  const headerY = page.getHeight() - margin; // Top of the header
  const logoHeight = headerHeight * 0.8; // Logo takes 80% of header height
  const textY = headerY - (headerHeight / 1.7) + logoHeight * 0.3; // Position text vertically centered with the logo

  // Draw logo if available
  let afterLogoX = margin;
  if (embeddings.logo) {
    const logoWidth = embeddings.logo.width * (logoHeight / embeddings.logo.height);
    page.drawImage(embeddings.logo, {
      x: margin,
      y: headerY - logoHeight,
      width: logoWidth,
      height: logoHeight
    });
    afterLogoX = margin + logoWidth + 10; // Add some spacing after the logo
  }

  // Set font sizes
  const eventNameFontSize = 14;
  const detailsFontSize = 9;
  const pageFontSize = 10;

  // Draw event name in bold
  if (embeddings.boldFont && event?.eventName) {
    page.drawText(event.eventName, {
      x: afterLogoX,
      y: textY,
      size: eventNameFontSize,
      font: embeddings.boldFont,
      color: rgb(0, 0, 0)
    });
  }

  // Draw event ID and print date underneath event name
  if (embeddings.regularFont) {
    const eventId = event?.ID || 'Unknown Event ID';
    const eventDetails = `ID: ${eventId} | Printed: ${printDate}`;

    page.drawText(eventDetails, {
      x: afterLogoX,
      y: textY - eventNameFontSize - 2,
      size: detailsFontSize,
      font: embeddings.regularFont,
      color: rgb(0.4, 0.4, 0.4)
    });
  }

  // Draw page numbers at the right side
  if (embeddings.regularFont) {
    const pageText = `Page ${pageNumber} of ${totalPages}`;
    const pageTextWidth = embeddings.regularFont.widthOfTextAtSize(pageText, pageFontSize);

    page.drawText(pageText, {
      x: pageWidth - margin - pageTextWidth,
      y: textY - 2,
      size: pageFontSize,
      font: embeddings.regularFont,
      color: rgb(0, 0, 0)
    });
  }

  // Draw a separator line under the header
  page.drawLine({
    start: { x: margin, y: headerY - headerHeight - 3 },
    end: { x: pageWidth - margin, y: headerY - headerHeight - 3 },
    thickness: 0.5,
    color: rgb(0.7, 0.7, 0.7)
  });
}
