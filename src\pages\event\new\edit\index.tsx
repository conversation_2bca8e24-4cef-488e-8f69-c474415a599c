import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Header } from '@/components/Header';
import { 
  Tabs, 
  TabsList, 
  TabsTrigger, 
  TabsContent 
} from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/DatePicker';
import { TimezoneCombobox } from '@/components/timezone-combobox';
import { useToast } from '@/hooks/use-toast';
import { getUserTimezone, FormatDate } from '@/lib/dayjs';
import { Sparkles, Calendar, MapPin, Clock, User, MessageSquare } from 'lucide-react';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
dayjs.extend(timezone);

// Helper function to calculate the default RSVP due date (11:59 PM the day before event date)
const calculateDefaultRSVPDueDate = (eventDate: Date, _timezone?: string): Date => {
  // Create a date object for the event date
  const date = new Date(eventDate);
  
  // Subtract one day to get the day before
  date.setDate(date.getDate() - 1);
  
  // Set the time to 11:59 PM (23:59:00)
  date.setHours(23, 59, 0, 0);
  
  return date;
};

// Get timezone offset display (e.g., "GMT+10")
const getTimezoneOffset = (timezone: string): string => {
  const offset = dayjs().tz(timezone).utcOffset();
  const hours = Math.floor(Math.abs(offset) / 60);
  const minutes = Math.abs(offset) % 60;
  const sign = offset >= 0 ? '+' : '-';
  
  if (minutes === 0) {
    return `GMT${sign}${hours}`;
  } else {
    return `GMT${sign}${hours}:${minutes.toString().padStart(2, '0')}`;
  }
};

const EventFormSchema = z.object({
  eventName: z.string().min(1, "Event name is required"),
  eventDate: z.date().refine(date => {
    // Ensure event date is not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date >= today;
  }, { message: "Event date cannot be in the past" }),
  start: z.string().min(1, "Start time is required"),
  end: z.string().optional(),
  location: z.string().min(1, "Location is required"),
  timezone: z.string().default('Australia/Melbourne'),
  rsvpDueDate: z.date().optional().refine(date => {
    if (!date) return true; // Optional, so null/undefined is fine
    
    // Ensure RSVP due date is not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date >= today;
  }, { message: "RSVP due date cannot be in the past" }),
  rsvpDueTime: z.string().optional(),
  message: z.string().optional(),
  host: z.string().min(1, "Host name is required").max(256),
}).refine(data => {
  // Skip validation if either field is missing
  if (!data.eventDate || !data.rsvpDueDate) return true;
  
  // Ensure RSVP due date is not after event date
  return data.rsvpDueDate <= data.eventDate;
}, {
  message: "RSVP due date cannot be after the event date",
  path: ["rsvpDueDate"]
});

// Available timezones with their offsets
const timezones = [
  { name: 'Australia/Melbourne', offsetString: 'GMT+11', offsetMinutes: 660 },
  { name: 'Australia/Sydney', offsetString: 'GMT+11', offsetMinutes: 660 },
  { name: 'Australia/Brisbane', offsetString: 'GMT+10', offsetMinutes: 600 },
  { name: 'Australia/Adelaide', offsetString: 'GMT+10:30', offsetMinutes: 630 },
  { name: 'Australia/Perth', offsetString: 'GMT+8', offsetMinutes: 480 },
  { name: 'Pacific/Auckland', offsetString: 'GMT+13', offsetMinutes: 780 },
  { name: 'America/New_York', offsetString: 'GMT-5', offsetMinutes: -300 },
  { name: 'America/Los_Angeles', offsetString: 'GMT-8', offsetMinutes: -480 },
  { name: 'Europe/London', offsetString: 'GMT+0', offsetMinutes: 0 },
  { name: 'Asia/Tokyo', offsetString: 'GMT+9', offsetMinutes: 540 },
];

export default function CreateEvent() {
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("event-details");
  const [isEnhancing, setIsEnhancing] = useState(false);
  
  const form = useForm<z.infer<typeof EventFormSchema>>({
    resolver: zodResolver(EventFormSchema),
    defaultValues: {
      eventName: "",
      eventDate: new Date(),
      start: "09:00",
      end: "17:00",
      location: "",
      timezone: getUserTimezone(),
      rsvpDueDate: calculateDefaultRSVPDueDate(new Date(), getUserTimezone()),
      rsvpDueTime: "23:59",
      message: "",
      host: session?.user?.name || "",
    },
  });

  // Update RSVP due date when event date or timezone changes
  useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      if (name === 'eventDate' || name === 'timezone') {
        const eventDate = form.getValues('eventDate');
        const timezone = form.getValues('timezone');
        
        if (eventDate && !form.formState.dirtyFields.rsvpDueDate) {
          form.setValue('rsvpDueDate', calculateDefaultRSVPDueDate(eventDate, timezone));
        }
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form]);

  // Update host name when session changes
  useEffect(() => {
    if (session?.user?.name && !form.formState.dirtyFields.host) {
      form.setValue('host', session.user.name);
    }
  }, [session, form]);

  const enhanceMessageWithAI = async () => {
    if (!session?.user?.hasAiAccess) {
      toast({
        title: "AI Access Required",
        description: "AI message enhancement is not available for your account.",
        variant: "destructive"
      });
      return;
    }

    setIsEnhancing(true);
    
    try {
      const formData = form.getValues();
      
      const response = await fetch('/api/ai/enhance-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventName: formData.eventName,
          eventDate: formData.eventDate,
          startTime: formData.start,
          endTime: formData.end,
          location: formData.location,
          timezone: formData.timezone,
          rsvpDueDate: formData.rsvpDueDate,
          rsvpDueTime: formData.rsvpDueTime,
          host: formData.host,
          currentMessage: formData.message,
          messageStyle: 'personalised',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to enhance message');
      }

      const result = await response.json();
      
      if (result.success && result.enhancedMessage) {
        form.setValue('message', result.enhancedMessage);
        toast({
          title: "Message Enhanced!",
          description: "Your event message has been enhanced with AI.",
        });
      }
    } catch (error) {
      console.error('Error enhancing message:', error);
      toast({
        title: "Enhancement Failed",
        description: "Failed to enhance message. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsEnhancing(false);
    }
  };

  const onSubmit = async (values: z.infer<typeof EventFormSchema>) => {
    try {
      // Here you would typically save the event
      console.log('Event data:', values);
      
      toast({
        title: "Event Created!",
        description: "Your event has been created successfully.",
      });
      
      // Redirect to event page or events list
      router.push('/events');
    } catch (error) {
      console.error('Error creating event:', error);
      toast({
        title: "Error",
        description: "Failed to create event. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Let's Create Your Event!</h1>
          <p className="text-muted-foreground">
            Use our AI to help generate event details in seconds
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="event-details" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Event Details
            </TabsTrigger>
            <TabsTrigger value="customise-rsvp" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Customise RSVP Page
            </TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <TabsContent value="event-details" className="space-y-0">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left Column - Event Details */}
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Calendar className="h-5 w-5" />
                          Event Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {/* Event Name */}
                        <FormField
                          control={form.control}
                          name="eventName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Event Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter event name" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Date and Time Row */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="eventDate"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Date</FormLabel>
                                <FormControl>
                                  <DatePicker
                                    value={field.value}
                                    onChange={field.onChange}
                                    fromDate={new Date()}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="start"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Start Time</FormLabel>
                                <FormControl>
                                  <Input type="time" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {/* End Time */}
                        <FormField
                          control={form.control}
                          name="end"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>End Time (Optional)</FormLabel>
                              <FormControl>
                                <Input type="time" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Location */}
                        <FormField
                          control={form.control}
                          name="location"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center gap-2">
                                <MapPin className="h-4 w-4" />
                                Event Location
                              </FormLabel>
                              <FormControl>
                                <Input placeholder="Enter event location" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Timezone */}
                        <FormField
                          control={form.control}
                          name="timezone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Timezone ({getTimezoneOffset(field.value)})
                              </FormLabel>
                              <FormControl>
                                <TimezoneCombobox
                                  timezones={timezones}
                                  value={field.value}
                                  onValueChange={field.onChange}
                                  placeholder="Select timezone"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>

                    {/* RSVP Details Card */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Clock className="h-5 w-5" />
                          RSVP Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="rsvpDueDate"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>RSVP Due Date</FormLabel>
                                <FormControl>
                                  <DatePicker
                                    value={field.value}
                                    onChange={field.onChange}
                                    fromDate={new Date()}
                                    toDate={form.getValues('eventDate')}
                                  />
                                </FormControl>
                                <FormDescription>
                                  {field.value && (
                                    <span className="text-sm text-muted-foreground">
                                      {FormatDate(field.value, form.getValues('timezone'))}, {form.getValues('rsvpDueTime') || '11:59 PM'}
                                    </span>
                                  )}
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="rsvpDueTime"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>RSVP Due Time</FormLabel>
                                <FormControl>
                                  <Input type="time" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Right Column - Message and Host */}
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <MessageSquare className="h-5 w-5" />
                          Title & Message
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label>Event Title</Label>
                            <span className="text-sm text-muted-foreground">
                              {form.watch('eventName') || 'Event Title'}
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label>Message for Guests (Optional)</Label>
                            {session?.user?.hasAiAccess && (
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={enhanceMessageWithAI}
                                disabled={isEnhancing}
                                className="flex items-center gap-2"
                              >
                                <Sparkles className="h-4 w-4" />
                                {isEnhancing ? 'Enhancing...' : 'Try AI Assistant'}
                              </Button>
                            )}
                          </div>

                          <FormField
                            control={form.control}
                            name="message"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Textarea
                                    placeholder="Write something fun for your guests to see when they receive their invite..."
                                    className="min-h-[120px] resize-none"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <User className="h-5 w-5" />
                          Event Host Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <FormField
                          control={form.control}
                          name="host"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Host Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter host name" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>

                    {/* Action Buttons */}
                    <div className="flex flex-col gap-3">
                      <Button
                        type="submit"
                        variant="default"
                        size="lg"
                        className="w-full bg-pink-500 hover:bg-pink-600 text-white"
                      >
                        Next
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="lg"
                        className="w-full"
                        onClick={() => router.push('/events')}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="customise-rsvp" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Customise RSVP Page</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      RSVP page customization options will be available here.
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </form>
          </Form>
        </Tabs>
      </div>
    </div>
  );
}
