import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Header } from '@/components/Header';
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent
} from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/DatePicker';
import { useToast } from '@/hooks/use-toast';
import { getUserTimezone, FormatDate } from '@/lib/dayjs';
import { Spark<PERSON> } from 'lucide-react';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
dayjs.extend(timezone);

// Helper function to calculate the default RSVP due date (11:59 PM the day before event date)
const calculateDefaultRSVPDueDate = (eventDate: Date, _timezone?: string): Date => {
  // Create a date object for the event date
  const date = new Date(eventDate);
  
  // Subtract one day to get the day before
  date.setDate(date.getDate() - 1);
  
  // Set the time to 11:59 PM (23:59:00)
  date.setHours(23, 59, 0, 0);
  
  return date;
};

// Get timezone offset display (e.g., "GMT+10")
const getTimezoneOffset = (timezone: string): string => {
  const offset = dayjs().tz(timezone).utcOffset();
  const hours = Math.floor(Math.abs(offset) / 60);
  const minutes = Math.abs(offset) % 60;
  const sign = offset >= 0 ? '+' : '-';

  if (minutes === 0) {
    return `GMT${sign}${hours}`;
  } else {
    return `GMT${sign}${hours}:${minutes.toString().padStart(2, '0')}`;
  }
};


const EventFormSchema = z.object({
  eventName: z.string().min(1, "Event name is required"),
  eventDate: z.date().refine(date => {
    // Ensure event date is not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date >= today;
  }, { message: "Event date cannot be in the past" }),
  start: z.string().min(1, "Start time is required"),
  end: z.string().optional(),
  location: z.string().min(1, "Location is required"),
  timezone: z.string().default('Australia/Melbourne'),
  rsvpDueDate: z.date().optional().refine(date => {
    if (!date) return true; // Optional, so null/undefined is fine
    
    // Ensure RSVP due date is not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date >= today;
  }, { message: "RSVP due date cannot be in the past" }),
  rsvpDueTime: z.string().optional(),
  message: z.string().optional(),
  host: z.string().min(1, "Host name is required").max(256),
}).refine(data => {
  // Skip validation if either field is missing
  if (!data.eventDate || !data.rsvpDueDate) return true;
  
  // Ensure RSVP due date is not after event date
  return data.rsvpDueDate <= data.eventDate;
}, {
  message: "RSVP due date cannot be after the event date",
  path: ["rsvpDueDate"]
});



export default function CreateEvent() {
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("event-details");
  const [isEnhancing, setIsEnhancing] = useState(false);

  const form = useForm<z.infer<typeof EventFormSchema>>({
    resolver: zodResolver(EventFormSchema),
    defaultValues: {
      eventName: "",
      eventDate: new Date(),
      start: "09:00",
      end: "17:00",
      location: "",
      timezone: getUserTimezone(),
      rsvpDueDate: calculateDefaultRSVPDueDate(new Date(), getUserTimezone()),
      rsvpDueTime: "23:59",
      message: "",
      host: session?.user?.name || "",
    },
  });

  // Update RSVP due date when event date or timezone changes
  useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      if (name === 'eventDate' || name === 'timezone') {
        const eventDate = form.getValues('eventDate');
        const timezone = form.getValues('timezone');

        if (eventDate && !form.formState.dirtyFields.rsvpDueDate) {
          form.setValue('rsvpDueDate', calculateDefaultRSVPDueDate(eventDate, timezone));
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Update host name when session changes
  useEffect(() => {
    if (session?.user?.name && !form.formState.dirtyFields.host) {
      form.setValue('host', session.user.name);
    }
  }, [session, form]);

  const enhanceMessageWithAI = async () => {
    if (!session?.user?.hasAiAccess) {
      toast({
        title: "AI Access Required",
        description: "AI message enhancement is not available for your account.",
        variant: "destructive"
      });
      return;
    }

    setIsEnhancing(true);
    
    try {
      const formData = form.getValues();
      
      const response = await fetch('/api/ai/enhance-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventName: formData.eventName,
          eventDate: formData.eventDate,
          startTime: formData.start,
          endTime: formData.end,
          location: formData.location,
          timezone: formData.timezone,
          rsvpDueDate: formData.rsvpDueDate,
          rsvpDueTime: formData.rsvpDueTime,
          host: formData.host,
          currentMessage: formData.message,
          messageStyle: 'personalised',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to enhance message');
      }

      const result = await response.json();
      
      if (result.success && result.enhancedMessage) {
        form.setValue('message', result.enhancedMessage);
        toast({
          title: "Message Enhanced!",
          description: "Your event message has been enhanced with AI.",
        });
      }
    } catch (error) {
      console.error('Error enhancing message:', error);
      toast({
        title: "Enhancement Failed",
        description: "Failed to enhance message. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsEnhancing(false);
    }
  };

  const onSubmit = async (values: z.infer<typeof EventFormSchema>) => {
    try {
      // Here you would typically save the event
      console.log('Event data:', values);
      
      toast({
        title: "Event Created!",
        description: "Your event has been created successfully.",
      });
      
      // Redirect to event page or events list
      router.push('/events');
    } catch (error) {
      console.error('Error creating event:', error);
      toast({
        title: "Error",
        description: "Failed to create event. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header
        title="Create Event"
        breadcrumbs={[
          { label: 'Events', href: '/events' }
        ]}
      />

      <div className="container mx-auto px-4 py-6 max-w-4xl">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Form */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2 mb-8 bg-gray-50 p-1 rounded-lg">
                      <TabsTrigger value="event-details" className="text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-md py-2">
                        <span className="flex items-center gap-2">
                          <span className="w-5 h-5 rounded-full bg-pink-500 text-white text-xs flex items-center justify-center font-semibold">1</span>
                          Event Details
                        </span>
                      </TabsTrigger>
                      <TabsTrigger value="customise-rsvp" className="text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-md py-2">
                        <span className="flex items-center gap-2">
                          <span className="w-5 h-5 rounded-full bg-gray-300 text-gray-600 text-xs flex items-center justify-center font-semibold">2</span>
                          Customize RSVP Page
                        </span>
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="event-details" className="space-y-8">
                      {/* When is your event happening? */}
                      <div className="space-y-4">
                        <h3 className="text-base font-medium text-gray-900">When is your event happening?</h3>
                        <div className="text-sm text-gray-500 flex items-center gap-2">
                          <span className="bg-gray-100 px-2 py-1 rounded text-xs">({getTimezoneOffset(form.watch('timezone'))})</span>
                        </div>

                        {/* Date and Time Row */}
                        <div className="grid grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name="eventDate"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <DatePicker
                                    value={field.value}
                                    onChange={field.onChange}
                                    fromDate={new Date()}
                                    className="w-full"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="start"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm text-gray-500">Start</FormLabel>
                                <FormControl>
                                  <Input type="time" {...field} className="w-full" />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="end"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm text-gray-500">End</FormLabel>
                                <FormControl>
                                  <Input type="time" {...field} className="w-full" />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {/* RSVP Closes */}
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-500">🗓️</span>
                            <span className="text-sm font-medium text-gray-700">RSVP Closes</span>
                          </div>
                          <p className="text-xs text-gray-500">Guests won't be able to RSVP after this deadline</p>
                          <div className="text-sm text-gray-700">
                            {form.watch('rsvpDueDate') && (
                              <span>
                                {FormatDate(form.watch('rsvpDueDate')!, form.watch('timezone'))}, {form.watch('rsvpDueTime') || '11:59 PM'}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Event Location Section */}
                      <div className="space-y-4">
                        <h3 className="text-base font-medium text-gray-900">Event location</h3>

                        <FormField
                          control={form.control}
                          name="location"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <div className="relative">
                                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">📍</span>
                                  <Input placeholder="Enter Location" {...field} className="w-full pl-10" />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Title & Message Section */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="text-base font-medium text-gray-900">Title & Message</h3>
                          {session?.user?.hasAiAccess && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={enhanceMessageWithAI}
                              disabled={isEnhancing}
                              className="flex items-center gap-2 text-xs text-pink-500 border-pink-200 hover:bg-pink-50"
                            >
                              <span className="text-pink-500">🌟</span>
                              {isEnhancing ? 'Enhancing...' : 'Help Me Write It'}
                            </Button>
                          )}
                        </div>

                        <div className="space-y-4">
                          <div>
                            <Label className="text-sm font-medium text-gray-700">Event Title</Label>
                            <div className="mt-1 p-3 bg-gray-50 rounded-md text-sm text-gray-900 min-h-[40px] flex items-center">
                              {form.watch('eventName') || 'Event Title'}
                            </div>
                          </div>

                          <FormField
                            control={form.control}
                            name="message"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm font-medium text-gray-700">Message for Guests (Optional)</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Write something like 'Party starts at 8, wear white!' — then refine it if needed."
                                    className="min-h-[120px] resize-none text-sm"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription className="text-xs text-gray-500">
                                  Let your guests know what to expect: share important details, dress code, surprises, or anything you'd like them to inform
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3 pt-6">
                        <Button
                          type="button"
                          variant="outline"
                          className="flex-1"
                          onClick={() => router.push('/events')}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          className="flex-1 bg-pink-500 hover:bg-pink-600 text-white"
                        >
                          Next →
                        </Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="customise-rsvp" className="space-y-6">
                      <div className="text-center py-8">
                        <p className="text-gray-500">
                          RSVP page customization options will be available here.
                        </p>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </div>

              {/* Right Column - Two Cards */}
              <div className="lg:col-span-1 space-y-4">
            {/* AI Assistant Card */}
            <div className="w-full p-4 rounded-xl border border-orange-200 bg-gradient-to-b from-orange-50 to-orange-100 flex flex-col items-start gap-2">
              {/* Pink Mascot Character */}
              <div className="w-12 h-12 bg-pink-200 rounded-full flex items-center justify-center mb-2">
                <span className="text-pink-600 text-lg">🎉</span>
              </div>

              <h3 className="text-base font-semibold text-gray-900">
                Let us Create Your Event!
              </h3>
              <p className="text-sm text-gray-600">
                Use our AI Cohost to generate event details in seconds
              </p>

              {session?.user?.hasAiAccess && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={enhanceMessageWithAI}
                  disabled={isEnhancing}
                  className="w-full mt-2 bg-pink-500 hover:bg-pink-600 text-white border-pink-500"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  {isEnhancing ? 'Enhancing...' : 'Try AI Assistant'}
                </Button>
              )}
            </div>

                {/* Event Host Details Card */}
                <div className="w-full p-4 rounded-xl border border-gray-200 bg-white flex flex-col items-start gap-2">
                  <h4 className="text-base font-semibold text-gray-900">Event Host Details</h4>

                  <div className="w-full space-y-3">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Your Name</Label>
                      <FormField
                        control={form.control}
                        name="host"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Enter your name" {...field} className="w-full mt-1" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
