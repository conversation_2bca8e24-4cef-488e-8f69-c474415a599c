# Migration Analysis: v1 Types to Universe/Packages/Types

This document analyzes the current state of the universe/packages/types system and provides recommendations for improving it to support both v1 legacy data structures and new modern patterns.

## Current Universe Types Package Analysis

### Current State
**Package**: `@iamcoming/types`
**Location**: `/universe/packages/types/`
**Size**: Minimal (4 interfaces, ~40 lines)

### Current Types (v0.0.0)

```typescript
interface IACAccount {
  id: string;
  name: string;
  type?: string; // 'partner', 'business', etc.
}

interface IACUser {
  id: string;
  name: string;
  email: string;
  roles: string[];
  avatar?: string;
  profile?: Record<string, unknown>;
  accountId?: string;
}

interface IACEvent {
  id: string;
  accountId: string;
  name: string;
  startDate: string;
  endDate: string;
  partnerId?: string; // For events created by partners
  ownerId?: string; // Host who owns the event after accepting
}

interface IACEventInvite {
  id: string;
  eventId: string;
  userId: string;
  status: 'pending' | 'accepted' | 'declined' | 'checked-in';
  checkinData?: {
    checkedInAt?: string; // ISO date string
    checkedInBy?: string; // User ID of the staff who checked in the guest
    adultsCount?: number; // Number of adults checked in
    childrenCount?: number; // Number of children checked in
    notes?: string; // Any additional notes from the check-in
  };
}
```

## V1 vs Universe Types Comparison

### Coverage Analysis

| Domain | V1 Types | Universe Types | Coverage Gap |
|--------|----------|----------------|--------------|
| **User Management** | 1 complex type | 1 basic type | 🔴 Significant |
| **Account/Organization** | 1 complex type | 1 basic type | 🔴 Significant |
| **Events** | 3 complex types | 1 basic type | 🔴 Major |
| **Invites/RSVP** | 6 types | 1 basic type | 🔴 Critical |
| **Authentication/RBAC** | 25+ types | 0 types | 🔴 Missing |
| **Partner/Venue** | 4 types | 0 types | 🔴 Missing |
| **API/Helper** | 15+ types | 0 types | 🔴 Missing |
| **Utilities** | 20+ types | 0 types | 🔴 Missing |

### Key Differences

#### 1. User Management
**V1 (UserProfile)**:
- Complex profile with Google OAuth integration
- Admin and AI access controls
- Organization membership
- Password and reset token support
- Profile completion status

**Universe (IACUser)**:
- Basic user fields only
- Generic roles array
- No authentication integration
- Missing profile management

#### 2. Organization/Account Structure
**V1 (Organization)**:
- Multi-tenant with member management
- Partner vs individual types
- Contact group settings
- AI usage overrides
- Comprehensive audit fields

**Universe (IACAccount)**:
- Basic name and type only
- No member management
- No organization features

#### 3. Event Management
**V1 (Event)**:
- Rich event lifecycle management
- Partner venue integration
- Payment status tracking
- AI usage monitoring
- Manager delegation
- Print settings
- Timezone support

**Universe (IACEvent)**:
- Basic event fields only
- Simple date strings
- No lifecycle management
- Missing partner integration

#### 4. RSVP/Invite System
**V1 (EventInvite)**:
- Complex invite management
- Adult/children tracking
- Message threads
- Activity history
- Response management
- Group organization

**Universe (IACEventInvite)**:
- Basic invite structure
- Simple status tracking
- Basic check-in data
- Missing communication features

## Recommendations for Universe Types Enhancement

### 1. Create Layered Type Architecture

```
@iamcoming/types/
├── core/           # Core domain entities
├── legacy/         # V1 compatibility types
├── api/           # API request/response types
├── auth/          # Authentication and RBAC
├── utils/         # Utility and helper types
└── index.ts       # Public exports
```

### 2. Implement V1 Compatibility Layer

**File**: `src/legacy/v1-types.ts`

```typescript
// Export all v1 types with V1 prefix for compatibility
export type V1UserProfile = import('iac-universe-v1/types').UserProfile;
export type V1Event = import('iac-universe-v1/types').Event;
export type V1EventInvite = import('iac-universe-v1/types').EventInvite;
export type V1Organization = import('iac-universe-v1/types').Organization;

// Migration helper types
export interface V1ToModernMapping {
  user: (v1: V1UserProfile) => IACUser;
  event: (v1: V1Event) => IACEvent;
  invite: (v1: V1EventInvite) => IACEventInvite;
  organization: (v1: V1Organization) => IACAccount;
}
```

### 3. Enhanced Core Types

**File**: `src/core/user.ts`

```typescript
interface IACUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  profile?: IACUserProfile;
  accountId?: string;
  
  // Enhanced from v1
  authentication?: {
    googleId?: string;
    hasGoogleLinked?: boolean;
    isProfileComplete: boolean;
    lastLoginAt?: string;
  };
  
  // Permission system
  permissions: {
    roles: string[];
    isAdmin?: boolean;
    hasAiAccess?: boolean;
  };
  
  // Audit fields
  createdAt: string;
  updatedAt: string;
}

interface IACUserProfile {
  // V1 compatibility
  image?: string;
  originalImage?: string;
  isWelcomeEmailSent?: boolean;
  
  // Enhanced profile
  firstName?: string;
  lastName?: string;
  phone?: string;
  timezone?: string;
  preferences?: Record<string, unknown>;
  
  // Department/organization context
  department?: string;
  title?: string;
  [key: string]: unknown; // Extensible profile
}
```

**File**: `src/core/organization.ts`

```typescript
interface IACAccount {
  id: string;
  name: string;
  type: 'individual' | 'partner' | 'business' | 'enterprise';
  
  // Enhanced from v1
  members?: IACAccountMember[];
  settings?: IACAccountSettings;
  subscription?: IACSubscription;
  
  // Audit fields
  createdAt: string;
  updatedAt: string;
  
  // V1 compatibility
  legacy?: {
    contactGroupSettings?: {
      hasBeenAsked: boolean;
    };
    overrides?: Record<string, any>;
  };
}

interface IACAccountMember {
  userId: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  permissions?: string[];
  joinedAt: string;
  invitedBy?: string;
}

interface IACAccountSettings {
  branding?: {
    logo?: string;
    colors?: Record<string, string>;
  };
  features?: {
    aiAccess: boolean;
    partnerPortal: boolean;
    advancedAnalytics: boolean;
  };
  limits?: {
    eventsPerMonth?: number;
    invitesPerEvent?: number;
    storageGB?: number;
  };
}
```

**File**: `src/core/event.ts`

```typescript
interface IACEvent {
  id: string;
  accountId: string;
  name: string;
  description?: string;
  
  // Enhanced timing
  timing: {
    startDate: string; // ISO string
    endDate: string;   // ISO string
    timezone: string;  // IANA timezone
    allDay?: boolean;
  };
  
  // Location information
  location: {
    name: string;
    address?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
    venueId?: string;    // Partner venue reference
    locationId?: string; // Specific area within venue
  };
  
  // Host and management
  host: {
    name: string;
    email?: string;
    userId?: string;
  };
  managers?: string[]; // User IDs
  
  // Event lifecycle
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  visibility: 'public' | 'private' | 'unlisted';
  
  // RSVP configuration
  rsvp: {
    enabled: boolean;
    dueDate?: string;
    allowGuests?: boolean;
    maxGuests?: number;
    requireApproval?: boolean;
  };
  
  // Partner-specific
  partnerId?: string;
  ownerId?: string;
  
  // Audit and metadata
  createdAt: string;
  updatedAt: string;
  
  // V1 compatibility
  legacy?: {
    plan?: 'free' | 'host_plus' | 'host_pro';
    paymentStatus?: 'pending' | 'paid' | 'failed';
    paymentId?: string;
    printSettings?: any;
    aiUsageCount?: number;
    overrides?: Record<string, any>;
    maxInvites?: number;
    pricePerAdditionalInvite?: number;
  };
}
```

**File**: `src/core/invite.ts`

```typescript
interface IACEventInvite {
  id: string;
  eventId: string;
  
  // Guest information
  guest: {
    name: string;
    email?: string;
    phone?: string;
    userId?: string; // If registered user
  };
  
  // Invitation details
  invitation: {
    sentAt?: string;
    sentBy: string; // User ID
    method: 'email' | 'sms' | 'manual' | 'import';
    group?: string;
    personalMessage?: string;
  };
  
  // RSVP response
  response?: {
    status: 'pending' | 'accepted' | 'declined' | 'tentative';
    respondedAt: string;
    guestCount: {
      adults: number;
      children: number;
    };
    dietaryRestrictions?: string;
    message?: string;
  };
  
  // Check-in data
  checkinData?: {
    checkedInAt: string;
    checkedInBy: string; // Staff user ID
    actualGuestCount: {
      adults: number;
      children: number;
    };
    notes?: string;
    method: 'qr_code' | 'manual' | 'search';
  };
  
  // Communication
  messages?: IACMessage[];
  
  // Activity tracking
  activity?: IACActivityRecord[];
  
  // Audit fields
  createdAt: string;
  updatedAt: string;
  
  // V1 compatibility (deprecated)
  status?: 'pending' | 'accepted' | 'declined' | 'checked-in';
  adults?: number;
  children?: number;
}

interface IACMessage {
  id: string;
  content: string;
  sender: {
    type: 'guest' | 'host' | 'system';
    userId?: string;
    name: string;
  };
  timestamp: string;
  readAt?: string;
}

interface IACActivityRecord {
  id: string;
  type: 'invite_sent' | 'link_opened' | 'rsvp_submitted' | 'checked_in' | 'message_sent';
  timestamp: string;
  metadata?: Record<string, any>;
  userId?: string;
}
```

### 4. Authentication and RBAC Types

**File**: `src/auth/index.ts`

```typescript
// Modern RBAC system built on v1 learnings
export interface IACPermission {
  resource: string;
  actions: ('view' | 'create' | 'edit' | 'delete')[];
  conditions?: Record<string, any>;
}

export interface IACRole {
  id: string;
  name: string;
  description: string;
  permissions: IACPermission[];
  context: 'system' | 'account' | 'venue';
}

export interface IACUserRole {
  userId: string;
  roleId: string;
  context?: {
    accountId?: string;
    venueId?: string;
    expiresAt?: string;
  };
  assignedBy: string;
  assignedAt: string;
  isActive: boolean;
}

export interface IACSession {
  user: IACUser;
  accounts: IACAccount[];
  activeAccountId?: string;
  permissions: IACPermission[];
  roles: IACRole[];
  token: string;
  expiresAt: string;
}
```

### 5. API and Helper Types

**File**: `src/api/index.ts`

```typescript
export interface IACAPIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    pagination?: IACPagination;
    timestamp: string;
    requestId: string;
  };
}

export interface IACPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface IACValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}
```

### 6. Utility Types

**File**: `src/utils/index.ts`

```typescript
// Date and time utilities
export type IACDateString = string; // ISO 8601 format
export type IACTimezone = string;   // IANA timezone identifier

// Status types
export type IACStatus = 'active' | 'inactive' | 'pending' | 'archived';

// Generic utilities
export interface IACTimestamps {
  createdAt: IACDateString;
  updatedAt: IACDateString;
}

export interface IACAuditFields extends IACTimestamps {
  createdBy: string;
  updatedBy: string;
}

// Configuration types
export interface IACFeatureFlags {
  [feature: string]: boolean;
}

export interface IACSettings {
  [key: string]: any;
}
```

## Implementation Strategy

### Phase 1: Foundation (Week 1-2)
1. Set up new package structure
2. Implement core types (User, Account, Event, Invite)
3. Create v1 compatibility layer
4. Add comprehensive tests

### Phase 2: Enhancement (Week 3-4)
1. Add authentication and RBAC types
2. Implement API response patterns
3. Create utility and helper types
4. Add migration utilities

### Phase 3: Integration (Week 5-6)
1. Update partner-web specs to use new types
2. Create type conversion utilities
3. Add validation schemas
4. Documentation and examples

### Phase 4: Migration Support (Week 7-8)
1. Build migration tools for v1 data
2. Create compatibility testing
3. Performance optimization
4. Production readiness

## Benefits of Enhanced Types System

### 1. **Backward Compatibility**
- Support for v1 database structures
- Gradual migration path
- No breaking changes for existing code

### 2. **Future-Proof Design**
- Extensible type system
- Modern TypeScript patterns
- Clean separation of concerns

### 3. **Developer Experience**
- Better type safety
- Comprehensive IntelliSense
- Clear documentation
- Consistent patterns

### 4. **Maintainability**
- Centralized type definitions
- Version control for types
- Breaking change detection
- Automated testing

### 5. **Cross-System Compatibility**
- Shared types across applications
- Consistent API contracts
- Reduced duplication
- Easier integration

## Breaking Changes and Migration Path

### Minimal Breaking Changes
```typescript
// Old (still supported)
interface IACUser {
  roles: string[];
}

// New (enhanced)
interface IACUser {
  roles?: string[]; // Legacy support
  permissions?: {   // New structure
    roles: string[];
    isAdmin?: boolean;
  };
}
```

### Migration Utilities
```typescript
// Type conversion utilities
export const migrateV1User = (v1User: V1UserProfile): IACUser => {
  return {
    id: v1User.id,
    name: v1User.name || '',
    email: v1User.email,
    avatar: v1User.image,
    permissions: {
      roles: [], // Map from v1 organization roles
      isAdmin: v1User.isAdmin,
      hasAiAccess: v1User.hasAiAccess,
    },
    authentication: {
      googleId: v1User.googleId,
      hasGoogleLinked: v1User.hasGoogleLinked,
      isProfileComplete: v1User.isProfileComplete,
    },
    createdAt: typeof v1User.createdOn === 'string' 
      ? v1User.createdOn 
      : v1User.createdOn?.toISOString() || new Date().toISOString(),
    updatedAt: typeof v1User.lastUpdatedOn === 'string'
      ? v1User.lastUpdatedOn
      : v1User.lastUpdatedOn?.toISOString() || new Date().toISOString(),
  };
};
```

## Recommended Package Structure

```
@iamcoming/types/
├── package.json
├── tsconfig.json
├── src/
│   ├── index.ts              # Main exports
│   ├── core/
│   │   ├── index.ts
│   │   ├── user.ts           # IACUser and related
│   │   ├── account.ts        # IACAccount and related
│   │   ├── event.ts          # IACEvent and related
│   │   └── invite.ts         # IACEventInvite and related
│   ├── auth/
│   │   ├── index.ts
│   │   ├── permission.ts     # RBAC types
│   │   ├── session.ts        # Session management
│   │   └── oauth.ts          # OAuth provider types
│   ├── api/
│   │   ├── index.ts
│   │   ├── response.ts       # API response patterns
│   │   ├── request.ts        # Request types
│   │   └── pagination.ts     # Pagination utilities
│   ├── utils/
│   │   ├── index.ts
│   │   ├── common.ts         # Common utility types
│   │   ├── dates.ts          # Date/time types
│   │   └── validation.ts     # Validation helpers
│   ├── legacy/
│   │   ├── index.ts
│   │   ├── v1-types.ts       # V1 compatibility
│   │   └── migration.ts      # Migration utilities
│   └── generated/            # Auto-generated types
│       └── database.ts       # Database schema types
└── dist/                     # Compiled output
```

This enhanced type system provides a solid foundation for both backward compatibility with v1 and future growth while maintaining clean, well-organized, and type-safe code across the entire IAC ecosystem.