declare module 'react-qr-scanner' {
  import { Component } from 'react';

  interface VideoConstraints {
    facingMode?: 'user' | 'environment';
    width?: number | { min?: number; ideal?: number; max?: number };
    height?: number | { min?: number; ideal?: number; max?: number };
    aspectRatio?: number | { min?: number; ideal?: number; max?: number };
    frameRate?: number | { min?: number; ideal?: number; max?: number };
    deviceId?: string;
    groupId?: string;
  }

  export interface QrScannerProps {
    delay?: number;
    style?: React.CSSProperties;
    className?: string;
    onError?: (error: Error) => void;
    onScan?: (data: { text: string } | null) => void;
    onLoad?: () => void;
    resolution?: number;
    facingMode?: 'user' | 'environment';
    chooseDeviceId?: () => string;
    constraints?: {
      video?: boolean | VideoConstraints;
      audio?: boolean | MediaTrackConstraints;
    };
    legacyMode?: boolean;
    maxImageSize?: number;
    showViewFinder?: boolean;
  }

  export default class QrScanner extends Component<QrScannerProps> {}
}
