import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { transporter } from '@/lib/mailer';
import { log } from '@/lib/logger';
import { rateLimit } from '@/lib/rateLimiter';
import { detectSpamContent, validateEmailDomain, checkHoneypot } from '@/lib/spamDetection';

type ContactFormData = {
  name: string;
  email: string;
  subject: string;
  message: string;
  recaptchaToken: string;
  website?: string; // Honeypot field
};

interface RecaptchaResponse {
  success: boolean;
  score: number;
  action: string;
  challenge_ts: string;
  hostname: string;
  'error-codes'?: string[];
}

const SUPPORT_EMAIL = '<EMAIL>';
const FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Apply rate limiting (5 requests per minute)
  if (!rateLimit(req, res, 5, 60 * 1000)) {
    return res.status(429).json({
      error: 'Too many requests, please try again later',
      retryAfter: res.getHeader('Retry-After')
    });
  }

  // Get form data
  const { name, email, subject, message, recaptchaToken, website } = req.body as ContactFormData;

  // Validate required fields
  if (!name || !email || !subject || !message) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    // Check honeypot field - if it's filled out, it's likely a bot
    if (!checkHoneypot(website || '')) {
      log('Spam protection: Honeypot triggered', {
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
        email,
        name,
        userAgent: req.headers['user-agent']
      });
      // Return success to avoid giving feedback to bots
      return res.status(200).json({
        success: true,
        message: 'Form submitted successfully'
      });
    }

    // Check for spam content in the message
    const spamCheck = detectSpamContent(message);
    if (spamCheck.isSpam) {
      log('Spam protection: Content detection triggered', {
        reason: spamCheck.reason,
        email,
        name,
        subject,
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
        userAgent: req.headers['user-agent']
      });
      return res.status(400).json({ error: 'Message contains inappropriate content' });
    }

    // Validate email domain
    if (!validateEmailDomain(email)) {
      log('Spam protection: Invalid email domain', {
        email,
        name,
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
        userAgent: req.headers['user-agent']
      });
      return res.status(400).json({ error: 'Please use a valid email address' });
    }

    // Verify reCAPTCHA token with a stricter threshold (0.6 instead of 0.5)
    const recaptchaVerified = await verifyRecaptchaToken(recaptchaToken);

    if (!recaptchaVerified.success || recaptchaVerified.score < 0.6) {
      log('reCAPTCHA verification failed', {
        score: recaptchaVerified.score,
        email,
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress
      });
      return res.status(400).json({
        error: 'Security verification failed',
        score: recaptchaVerified.score,
      });
    }

    // Get user session (optional, can be used to pre-fill the form)
    const session = await getServerSession(req, res, authConfig);

    // Send the contact form email
    await sendContactEmail({
      name,
      email,
      subject,
      message,
      userEmail: session?.user?.email || null,
    });

    // Log successful submission with reCAPTCHA score
    log('Contact form submitted successfully', {
      email,
      name,
      subject,
      recaptchaScore: recaptchaVerified.score,
      ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString()
    });

    return res.status(200).json({
      success: true,
      message: 'Form submitted successfully',
      user: session?.user?.email || null,
    });
  } catch (error) {
    console.error('Error processing contact form:', error);
    return res.status(500).json({ error: 'Failed to process contact form' });
  }
}

/**
 * Verify reCAPTCHA token with Google's verification API
 * @param token The reCAPTCHA token to verify
 * @returns The reCAPTCHA verification response
 */
async function verifyRecaptchaToken(token: string): Promise<RecaptchaResponse> {
  if (!token) {
    log('Empty reCAPTCHA token provided');
    return {
      success: false,
      score: 0,
      action: '',
      challenge_ts: '',
      hostname: '',
    };
  }

  try {
    const verifyUrl = `https://www.google.com/recaptcha/api/siteverify?secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${token}`;

    const response = await fetch(verifyUrl, { method: 'POST' });

    if (!response.ok) {
      const errorText = await response.text();
      log('reCAPTCHA API returned error', {
        status: response.status,
        statusText: response.statusText,
        errorText
      });
      throw new Error(`Failed to verify reCAPTCHA token: ${response.status} ${response.statusText}`);
    }

    const result = await response.json() as RecaptchaResponse;

    // Log the verification result
    log('reCAPTCHA verification result', {
      success: result.success,
      score: result.score,
      action: result.action,
      hostname: result.hostname,
      errorCodes: result['error-codes']
    });

    return result;
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    log('reCAPTCHA verification exception', { error: (error as Error).message });
    return {
      success: false,
      score: 0,
      action: '',
      challenge_ts: '',
      hostname: '',
      'error-codes': [(error as Error).message]
    };
  }
}

/**
 * Send contact form email using nodemailer transporter
 */
async function sendContactEmail({
  name,
  email,
  subject,
  message,
  userEmail,
}: {
  name: string;
  email: string;
  subject: string;
  message: string;
  userEmail: string | null;
}) {
  try {
    // Format the email content
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">New Contact Form Submission</h2>
        <div style="border-top: 1px solid #eee; border-bottom: 1px solid #eee; padding: 20px 0; margin: 20px 0;">
          <p><strong>Name:</strong> ${name}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Subject:</strong> ${subject}</p>
          <p><strong>Message:</strong></p>
          <div style="background-color: #f9f9f9; padding: 15px; border-left: 4px solid #f43f5e; margin: 10px 0;">
            ${message.replace(/\n/g, '<br>')}
          </div>
        </div>
        <div style="color: #666; font-size: 12px;">
          <p>This message was sent from the contact form on iamcoming.io</p>
        </div>
      </div>
    `;

    const textContent = `
      New Contact Form Submission

      Name: ${name}
      Email: ${email}
      Subject: ${subject}
      Message:
      ${message}
      ${userEmail ? `User Account: ${userEmail}` : ''}

      This message was sent from the contact form on iamcoming.io
    `;

    // Send email to support
    await transporter.sendMail({
      from: `Contact Form <${FROM_EMAIL}>`,
      to: SUPPORT_EMAIL,
      replyTo: email,
      subject: `Contact Form: ${subject}`,
      text: textContent,
      html: htmlContent,
    });

    log(`Contact form email sent from ${email} with subject "${subject}"`);
    return true;
  } catch (error) {
    console.error('Error sending contact form email:', error);
    throw new Error('Failed to send contact form email');
  }
}
