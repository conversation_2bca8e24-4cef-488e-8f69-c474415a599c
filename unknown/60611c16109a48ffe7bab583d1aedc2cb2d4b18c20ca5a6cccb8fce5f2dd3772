import { log } from './logger';

/**
 * Simple spam content detection
 * Checks for common spam patterns in message content
 */
export function detectSpamContent(content: string): { isSpam: boolean; reason?: string } {
  // Convert to lowercase for case-insensitive matching
  const lowerContent = content.toLowerCase();
  
  // Check for excessive URLs
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const urlMatches = lowerContent.match(urlRegex) || [];
  if (urlMatches.length > 5) {
    return { isSpam: true, reason: 'Too many URLs in content' };
  }
  
  // Check for common spam keywords
  const spamKeywords = [
    'viagra', 'cialis', 'casino', 'lottery', 'prize', 'winner', 
    'free money', 'make money fast', 'earn money online', 
    'bitcoin investment', 'crypto investment', 'forex trading',
    'weight loss', 'diet pills', 'enlargement', 'replica watches'
  ];
  
  for (const keyword of spamKeywords) {
    if (lowerContent.includes(keyword)) {
      return { isSpam: true, reason: `Contains spam keyword: ${keyword}` };
    }
  }
  
  // Check for excessive capitalization (shouting)
  const upperCaseChars = content.replace(/[^A-Z]/g, '').length;
  const totalChars = content.replace(/\s/g, '').length;
  if (totalChars > 20 && upperCaseChars / totalChars > 0.5) {
    return { isSpam: true, reason: 'Excessive capitalization' };
  }
  
  // Check for character repetition
  const repetitionRegex = /(.)\1{7,}/;
  if (repetitionRegex.test(content)) {
    return { isSpam: true, reason: 'Excessive character repetition' };
  }
  
  return { isSpam: false };
}

/**
 * Validate email domain by checking for MX records
 * This is a placeholder - in a real implementation, you would
 * use DNS lookups to verify the domain has valid MX records
 */
export function validateEmailDomain(email: string): boolean {
  // Extract domain from email
  const domain = email.split('@')[1];
  
  // List of known disposable email domains
  const disposableDomains = [
    'tempmail.com', 'throwawaymail.com', 'mailinator.com',
    'guerrillamail.com', 'yopmail.com', 'trashmail.com',
    'sharklasers.com', 'temp-mail.org', '10minutemail.com'
  ];
  
  if (disposableDomains.includes(domain)) {
    log('Disposable email domain detected', { email, domain });
    return false;
  }
  
  // In a real implementation, you would check for MX records here
  // For now, we'll just return true for all other domains
  return true;
}

/**
 * Check if a honeypot field was filled out
 * Bots often fill out all fields in a form, including hidden ones
 */
export function checkHoneypot(honeypotValue: string): boolean {
  return !honeypotValue || honeypotValue.trim() === '';
}
