import QRCodeBase from 'react-qr-code';
import { UTMParams, addUtmParams } from '@/lib/utm';

interface QRCodeProps {
  value: string;
  size?: number;
  className?: string;
  level?: 'L' | 'M' | 'Q' | 'H';
  utmParams?: UTMParams;
}

export function QRCode({
  value,
  size = 128,
  className = '',
  level = 'H',
  utmParams
}: QRCodeProps) {
  // If UTM parameters are provided, add them to the URL
  const finalValue = utmParams ? addUtmParams(value, utmParams) : value;

  return (
    <div className={`inline-block ${className}`}>
      <QRCodeBase
        value={finalValue}
        size={size}
        level={level}
        style={{ height: "auto", maxWidth: "100%", width: "100%" }}
      />
    </div>
  );
}