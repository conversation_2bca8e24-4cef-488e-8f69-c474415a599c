# Invite Image generation API

## Description

Develop a new API endpoint to dynamically generate a personalized invitation media (image or PDF) using an event background and a QR code tied to the invite ID. The generated content will be returned in the specified format and can be embedded or downloaded in digital or print workflows.

Endpoint

```
GET /api/media/invite?format={format}&inviteId={inviteId}&qrxp={qrxp}&qryp={qryp}&size={size}&orientation={orientation}
```

## Query Params

| Param | Type | Required | Description |
|-------|------|----------|-------------|
| inviteId | String | Yes | Unique ID of the invite; used to generate QR code with RSVP link and extract eventId |
| format | String | Optional | Output format: jpg, jpeg, png (default: jpg) |
| qrxp | Number | Optional | Horizontal QR position in % (default: 50%) |
| qryp | Number | Optional | Vertical QR position in % (default: 80%) |
| size | String | Optional | Image size; supports standard sizes (A4, A5, A6) or custom dimensions like "600x800" in pixels (default: A5) |
| orientation | Enum | Optional | portrait or landscape (default: portrait) |
| format | Enum | Optional | Output format: jpg, jpeg, png (default: jpg) |
| download | Boolean | No | Defaults to `false`. pass a truthy value to force download the image. |
| filename | String | No | filename for the downloaded file |

## Acceptance Criteria

 On request, download event image from https://media.iac.io/events/{eventId}.png

 If `download` is truthy, then force download the image.

 And if `filename` is also set, then the downloaded file will have a filename set to `{filename}.{format}`. Otherwise, the querystring's md5 hash string in lowercase will be used as filename.

 Cache the event image in-memory for 10 minutes to avoid redundant downloads

 Generate QR code with content: https://iamcoming.io/invite/{inviteId}

 Create a canvas of the specified size and orientation (A5, A4)

 Use event image as background, resized and fit to fill the canvas

 Overlay a white rounded rectangle behind both invite ID and QR code

 Add invite ID text (bold, black, centered) above the QR code

 Position rectangle, text, and QR code based on qrxp, qryp percentages

 Return response in format defined by format param:

jpg, jpeg, or png: return Content-Type: image/{format}

pdf: render content to a PDF and return Content-Type: application/pdf

 Validate format and return 400 on unsupported types

## Technical Notes
Use sharp for image processing (all image formats)

Use qrcode npm package to generate QR codes

Use pdfkit if PDF export is selected

Use node-fetch or axios for downloading images

Use lru-cache to cache event images (10-minute TTL)

Default to A5 portrait and jpg if optional params are not specified

Gracefully handle invalid/missing params (return meaningful 400)

Log and return 500 for unhandled errors

## Test Scenarios
✅ Generate media in each format (jpg, jpeg, png, pdf)

✅ Default to jpg when no format is provided

✅ Return 400 for unsupported formats (e.g., webp, bmp)

✅ White background and QR overlay work across all formats

✅ Invite ID text is rendered clearly and positioned above QR

✅ Valid positioning and image sizing across different qrxp, qryp, size, orientation inputs

✅ Image caching reduces repeated downloads

✅ Proper MIME type is returned with each format

## Example Code


```javascript
const express = require('express');
const sharp = require('sharp');
const QRCode = require('qrcode');
const fetch = require('node-fetch');
const LRU = require('lru-cache');

const app = express();
const PORT = 3000;

// Cache for event images
const imageCache = new LRU({
  max: 100,
  ttl: 10 * 60 * 1000
});

async function getEventImage(eventId) {
  const url = `https://media.iac.io/events/${eventId}.png`;

  if (imageCache.has(eventId)) {
    return imageCache.get(eventId);
  }

  const res = await fetch(url);
  if (!res.ok) throw new Error(`Failed to fetch event image: ${res.statusText}`);
  const buffer = await res.buffer();
  imageCache.set(eventId, buffer);
  return buffer;
}

app.get('/generate-image', async (req, res) => {
  const {
    eventId,
    inviteId,
    width = 600,
    height = 800,
    orientation = 'portrait',
    qrX = 50,
    qrY = 80
  } = req.query;

  if (!eventId || !inviteId) {
    return res.status(400).json({ error: 'eventId and inviteId are required' });
  }

  try {
    const eventImage = await getEventImage(eventId);

    const imageWidth = parseInt(width);
    const imageHeight = parseInt(height);

    const inviteLink = `https://iamcoming.io/invite/${inviteId}`;
    const qrSize = 200;

    const qrBuffer = await QRCode.toBuffer(inviteLink, { margin: 1, width: qrSize });

    const posX = Math.floor((qrX / 100) * imageWidth);
    const posY = Math.floor((qrY / 100) * imageHeight);

    // Create white background rectangle behind text + QR
    const rectWidth = qrSize + 40;
    const rectHeight = qrSize + 80;
    const rectSvg = `
      <svg width="${rectWidth}" height="${rectHeight}">
        <rect x="0" y="0" width="${rectWidth}" height="${rectHeight}" fill="white" rx="10" ry="10"/>
      </svg>
    `;
    const rectBuffer = Buffer.from(rectSvg);

    // Create SVG text for invite ID
    const textSvg = `
      <svg width="${qrSize}" height="40">
        <style>
          .title { fill: black; font-size: 24px; font-weight: bold; font-family: sans-serif; }
        </style>
        <text x="50%" y="60%" text-anchor="middle" class="title">${inviteId}</text>
      </svg>
    `;
    const textBuffer = Buffer.from(textSvg);

    // Build composite layers in order: background rectangle -> text -> QR code
    const finalImage = await sharp(eventImage)
      .resize(imageWidth, imageHeight, { fit: 'cover' })
      .composite([
        { input: rectBuffer, top: posY - 30, left: posX - 20 },
        { input: textBuffer, top: posY - 20, left: posX },
        { input: qrBuffer, top: posY + 20, left: posX }
      ])
      .png()
      .toBuffer();

    res.set('Content-Type', 'image/png');
    res.send(finalImage);

  } catch (err) {
    console.error('Error generating image:', err);
    res.status(500).json({ error: 'Failed to generate image' });
  }
});

app.listen(PORT, () => {
  console.log(`✅ Image generation API running on http://localhost:${PORT}`);
});
```