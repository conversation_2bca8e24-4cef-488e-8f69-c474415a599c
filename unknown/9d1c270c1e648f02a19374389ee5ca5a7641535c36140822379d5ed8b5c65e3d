import { NextApiRequest, NextApiResponse } from 'next';
import { log } from './logger';

// Simple in-memory store for rate limiting
// In production, you might want to use Redis or another persistent store
interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

const store: RateLimitStore = {};

// Clean up old entries periodically
setInterval(() => {
  const now = Date.now();
  Object.keys(store).forEach(key => {
    if (store[key].resetTime < now) {
      delete store[key];
    }
  });
}, 60 * 1000); // Clean up every minute

/**
 * Rate limiter middleware for API routes
 * @param req The Next.js API request
 * @param res The Next.js API response
 * @param maxRequests Maximum number of requests allowed in the time window
 * @param windowMs Time window in milliseconds
 * @returns Boolean indicating if the request should proceed
 */
export function rateLimit(
  req: NextApiRequest,
  res: NextApiResponse,
  maxRequests: number = 5,
  windowMs: number = 60 * 1000 // 1 minute by default
): boolean {
  // Get client IP
  const ip = 
    req.headers['x-forwarded-for'] || 
    req.socket.remoteAddress || 
    'unknown';
  
  const key = `${ip}:${req.url}`;
  const now = Date.now();
  
  // Initialize or get existing entry
  if (!store[key] || store[key].resetTime < now) {
    store[key] = {
      count: 1,
      resetTime: now + windowMs
    };
    return true;
  }
  
  // Increment count
  store[key].count++;
  
  // Check if over limit
  if (store[key].count > maxRequests) {
    log('Rate limit exceeded', { ip, url: req.url, count: store[key].count });
    
    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', maxRequests.toString());
    res.setHeader('X-RateLimit-Remaining', '0');
    res.setHeader('X-RateLimit-Reset', Math.ceil(store[key].resetTime / 1000).toString());
    res.setHeader('Retry-After', Math.ceil((store[key].resetTime - now) / 1000).toString());
    
    return false;
  }
  
  // Set rate limit headers
  res.setHeader('X-RateLimit-Limit', maxRequests.toString());
  res.setHeader('X-RateLimit-Remaining', (maxRequests - store[key].count).toString());
  res.setHeader('X-RateLimit-Reset', Math.ceil(store[key].resetTime / 1000).toString());
  
  return true;
}
