import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { sendInvitationEmail } from '@/lib/mailer';

/**
 * @api {POST} /event/:eventId/invites/:inviteId/send-email Send invitation email
 * @apiName SendInvitationEmail
 * @apiGroup Invites
 * @apiDescription Sends an invitation email to the guest
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId, inviteId } = req.query;

  if (!eventId || typeof eventId !== 'string' || !inviteId || typeof inviteId !== 'string') {
    return res.status(400).json({ error: 'Event ID and Invite ID are required' });
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Get user info from session
    const session = await getServerSession(req, res, authConfig);

    // Verify user has access to this event (optional)
    // This could be implemented based on your authorization requirements

    // Get event and invite data
    const event = await Database.getInstance().readData('events', eventId);
    const invite = await Database.getInstance().readData('invites', inviteId);

    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    if (!invite) {
      return res.status(404).json({ error: 'Invite not found' });
    }

    if (!invite.email) {
      return res.status(400).json({ error: 'Invite does not have an email address' });
    }

    // Send invitation email using the mailer function
    await sendInvitationEmail(event, invite, {
      name: invite.name,
      email: invite.email
    });

    // Log the email sending
    log(`Invitation email sent to ${invite.email} for event ${event.eventName}`);

    // Add to activity history but don't store in message array
    const activityItem = {
      timestamp: new Date().toISOString(),
      type: 'email_sent',
      details: {
        email: invite.email
      }
    };

    // We're not storing email sending activities in the message array anymore
    // Just log the activity without updating the invite
    log(`Email activity recorded for invite ${inviteId}: ${JSON.stringify(activityItem)}`);

    return res.status(200).json({
      success: true,
      message: `Invitation sent to ${invite.email}`
    });
  } catch (error) {
    console.error('Error sending invitation email:', error);
    return res.status(500).json({ error: 'Failed to send invitation email' });
  }
}
