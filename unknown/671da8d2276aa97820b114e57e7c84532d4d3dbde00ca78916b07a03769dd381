import React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter, CardTitle, CardDescription, CardContent } from './card';
import { Button } from './button';

const meta = {
  title: 'UI/Card',
  component: Card,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof Card>;

export const Default: Story = {
  render: () => (
    <Card className="w-[350px]">
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>Card Description</CardDescription>
      </CardHeader>
      <CardContent>
        <p>Card Content</p>
      </CardContent>
      <CardFooter>
        <p>Card Footer</p>
      </CardFooter>
    </Card>
  ),
};

export const WithAction: Story = {
  render: () => (
    <Card className="w-[350px]">
      <CardHeader>
        <CardTitle>Event Invitation</CardTitle>
        <CardDescription>You&apos;re invited to an exciting event!</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">Join us for an amazing evening of networking, food, and fun.</p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Decline</Button>
        <Button>Accept</Button>
      </CardFooter>
    </Card>
  ),
};

export const EventCard: Story = {
  render: () => (
    <Card className="w-[350px] overflow-hidden">
      <div className="h-[140px] bg-muted flex items-center justify-center">
        <span className="text-muted-foreground">Event Image</span>
      </div>
      <CardHeader>
        <CardTitle>Birthday Celebration</CardTitle>
        <CardDescription>Hosted by John Doe</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p className="text-sm">
            <span className="font-medium">Date:</span> May 15, 2025
          </p>
          <p className="text-sm">
            <span className="font-medium">Time:</span> 7:00 PM
          </p>
          <p className="text-sm">
            <span className="font-medium">Location:</span> Downtown Venue
          </p>
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full">RSVP Now</Button>
      </CardFooter>
    </Card>
  ),
};

export const ProfileCard: Story = {
  render: () => (
    <Card className="w-[300px]">
      <CardHeader className="items-center text-center pb-2">
        <div className="size-24 rounded-full bg-muted mb-2 flex items-center justify-center">
          <span className="text-muted-foreground">Photo</span>
        </div>
        <CardTitle>Alex Johnson</CardTitle>
        <CardDescription>Event Organizer</CardDescription>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-sm text-muted-foreground">
          Professional event organizer with 5+ years of experience creating memorable celebrations.
        </p>
      </CardContent>
      <CardFooter className="flex justify-center gap-2">
        <Button variant="outline" size="sm">Message</Button>
        <Button size="sm">Follow</Button>
      </CardFooter>
    </Card>
  ),
};