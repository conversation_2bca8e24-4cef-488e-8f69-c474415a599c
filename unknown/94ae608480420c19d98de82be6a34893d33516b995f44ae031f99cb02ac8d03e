/**
 * Utility functions for checking if event editing and invite management should be locked
 */

import { Event } from "@/types";

/**
 * Checks if the RSVP date for an event has passed
 * @param event The event to check
 * @returns boolean indicating if the RSVP date has passed
 */
export function isRsvpDatePassed(event: Event | null): boolean {
  if (!event || !event.rsvpDueDate) return false;
  
  const rsvpDueDate = new Date(event.rsvpDueDate);
  const now = new Date();
  
  return now > rsvpDueDate;
}

/**
 * Checks if the event date has passed
 * @param event The event to check
 * @returns boolean indicating if the event date has passed
 */
export function isEventDatePassed(event: Event | null): boolean {
  if (!event || !event.eventDate) return false;
  
  const eventDate = new Date(event.eventDate);
  // Add one day to the event date to consider the event as "passed" the day after
  eventDate.setDate(eventDate.getDate() + 1);
  const now = new Date();
  
  return now > eventDate;
}

/**
 * Checks if event editing should be locked
 * This happens when the RSVP date has passed
 * @param event The event to check
 * @returns boolean indicating if event editing should be locked
 */
export function isEventLocked(event: Event | null): boolean {
  return isRsvpDatePassed(event);
}

/**
 * Checks if invite management should be locked
 * This happens when the RSVP date has passed or the event date has passed
 * @param event The event to check
 * @returns boolean indicating if invite management should be locked
 */
export function isInviteManagementLocked(event: Event | null): boolean {
  return isRsvpDatePassed(event) || isEventDatePassed(event);
}

/**
 * Checks if reminders should be locked
 * This happens when the event date has passed
 * @param event The event to check
 * @returns boolean indicating if reminders should be locked
 */
export function isReminderLocked(event: Event | null): boolean {
  return isEventDatePassed(event);
}

/**
 * Returns a message explaining why the event is locked
 * @param event The event to check
 * @returns string message or null if event is not locked
 */
export function getEventLockMessage(event: Event | null): string | null {
  if (!isEventLocked(event)) return null;
  
  if (event?.rsvpDueDate) {
    const rsvpDueDate = new Date(event.rsvpDueDate);
    return `This event is locked for editing because the RSVP due date (${rsvpDueDate.toLocaleDateString()}) has passed.`;
  }
  
  return "This event is locked for editing because the RSVP due date has passed.";
}

/**
 * Returns a message explaining why reminders are locked
 * @param event The event to check
 * @returns string message or null if reminders are not locked
 */
export function getReminderLockMessage(event: Event | null): string | null {
  if (!isReminderLocked(event)) return null;
  
  if (event?.eventDate) {
    const eventDate = new Date(event.eventDate);
    return `Reminders are locked because the event date (${eventDate.toLocaleDateString()}) has passed.`;
  }
  
  return "Reminders are locked because the event has already passed.";
}

/**
 * Returns a message explaining why invite management is locked
 * @param event The event to check
 * @returns string message or null if invite management is not locked
 */
export function getInviteManagementLockMessage(event: Event | null): string | null {
  if (!isInviteManagementLocked(event)) return null;
  
  // If event date has passed, prioritize showing that message
  if (isEventDatePassed(event)) {
    if (event?.eventDate) {
      const eventDate = new Date(event.eventDate);
      return `Reminders are locked because the event date (${eventDate.toLocaleDateString()}) has passed.`;
    }
    return "Reminders are locked because the event has already passed.";
  }
  
  // Otherwise show RSVP date passed message
  if (event?.rsvpDueDate) {
    const rsvpDueDate = new Date(event.rsvpDueDate);
    return `This event is locked for editing because the RSVP due date (${rsvpDueDate.toLocaleDateString()}) has passed.`;
  }
  
  return "This event is locked for editing because the RSVP due date has passed.";
}