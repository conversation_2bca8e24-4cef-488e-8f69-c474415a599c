'use client'

import { <PERSON>actN<PERSON>, useState, useEffect } from "react"
import { useRouter } from "next/router"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { useSession } from "next-auth/react"
import { ProtectedLayout } from "./ProtectedLayout"
import { Header } from "@/components/Header"
import { Building2, Calendar, MapPin, Clock, CreditCard, Users, BarChart, Settings, QrCode } from "lucide-react"
import { PartnerAccessDenied } from "@/components/PartnerAccessDenied"

interface PartnerLayoutProps {
  children: ReactNode
}

export function PartnerLayout({ children }: PartnerLayoutProps) {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [isPartner, setIsPartner] = useState<boolean | null>(null)

  // Check if user is a partner
  useEffect(() => {
    if (status === 'authenticated') {
      setIsPartner(session?.user?.organization?.type === 'partner')
    }
  }, [session, status])

  const isActive = (path: string) => {
    return router.pathname.startsWith(path)
  }

  const isDashboardActive = () => {
    return router.pathname === "/partner" || router.pathname === "/partner/"
  }

  // Show loading state while checking partner status
  if (status === 'loading' || isPartner === null) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </ProtectedLayout>
    )
  }

  // Show access denied for non-partner users
  if (isPartner === false) {
    return (
      <ProtectedLayout>
        <PartnerAccessDenied />
      </ProtectedLayout>
    )
  }

  // Show partner panel for partner users
  return (
    <ProtectedLayout>
      <div className="min-h-screen bg-gray-50">
        <Header buttons={[]} />
        <div className="container mx-auto p-4">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Sidebar */}
            <aside className="w-full md:w-64 bg-white p-4 rounded-lg shadow-sm">
              <h2 className="text-xl font-bold mb-6">Partner Panel</h2>
              <nav className="space-y-2">
                <Link href="/partner" passHref>
                  <Button
                    variant={isDashboardActive() ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <BarChart className="mr-2 h-4 w-4" />
                    Dashboard
                  </Button>
                </Link>
                <Link href="/partner/venues" passHref>
                  <Button
                    variant={isActive("/partner/venues") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <Building2 className="mr-2 h-4 w-4" />
                    Venues
                  </Button>
                </Link>
                <Link href="/partner/events" passHref>
                  <Button
                    variant={isActive("/partner/events") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    Events
                  </Button>
                </Link>
                <Link href="/partner/time-slots" passHref>
                  <Button
                    variant={isActive("/partner/time-slots") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <Clock className="mr-2 h-4 w-4" />
                    Time Slots
                  </Button>
                </Link>
                <Link href="/partner/invoices" passHref>
                  <Button
                    variant={isActive("/partner/invoices") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <CreditCard className="mr-2 h-4 w-4" />
                    Invoices
                  </Button>
                </Link>
                <Link href="/partner/customers" passHref>
                  <Button
                    variant={isActive("/partner/customers") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Customers
                  </Button>
                </Link>
                <Link href="/partner/entry" passHref>
                  <Button
                    variant={isActive("/partner/entry") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <QrCode className="mr-2 h-4 w-4" />
                    Entry Control
                  </Button>
                </Link>
                <Link href="/partner/settings" passHref>
                  <Button
                    variant={isActive("/partner/settings") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Button>
                </Link>
              </nav>
            </aside>

            {/* Main Content */}
            <main className="flex-1 bg-white rounded-lg shadow-sm p-6">
              {children}
            </main>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
