'use client'

import { useState } from "react"
import { PartnerLayout } from "@/components/layouts/PartnerLayout"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/router"
import { Building2, CreditCard, Mail, Save, User } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useSession } from "next-auth/react"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export default function PartnerSettings() {
  const router = useRouter()
  const { data: session } = useSession()
  const [organizationName, setOrganizationName] = useState(session?.user?.organization?.name || "")
  const [contactEmail, setContactEmail] = useState(session?.user?.email || "")
  const [contactPhone, setContactPhone] = useState("+61 412 345 678")
  const [address, setAddress] = useState("123 Venue Street, Melbourne, VIC 3000")
  const [description, setDescription] = useState("Premier event venue and booking center")
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [smsNotifications, setSmsNotifications] = useState(false)
  const [defaultPaymentMethod, setDefaultPaymentMethod] = useState("bank_transfer")
  
  const handleSaveGeneralSettings = () => {
    // In a real app, this would send the data to the server
    console.log("Saving general settings:", {
      organizationName,
      contactEmail,
      contactPhone,
      address,
      description
    });
    
    // Show success message or notification
  };
  
  const handleSaveNotificationSettings = () => {
    // In a real app, this would send the data to the server
    console.log("Saving notification settings:", {
      emailNotifications,
      smsNotifications
    });
    
    // Show success message or notification
  };
  
  const handleSavePaymentSettings = () => {
    // In a real app, this would send the data to the server
    console.log("Saving payment settings:", {
      defaultPaymentMethod
    });
    
    // Show success message or notification
  };

  return (
    <PartnerLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Partner Settings</h1>
          <p className="text-muted-foreground">Manage your partner account settings</p>
        </div>
        
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="payment">Payment</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Manage your organization profile and contact information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="organization-name">Organization Name</Label>
                  <Input 
                    id="organization-name" 
                    placeholder="Enter organization name" 
                    value={organizationName}
                    onChange={(e) => setOrganizationName(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="contact-email">Contact Email</Label>
                  <Input 
                    id="contact-email" 
                    type="email"
                    placeholder="Enter contact email" 
                    value={contactEmail}
                    onChange={(e) => setContactEmail(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="contact-phone">Contact Phone</Label>
                  <Input 
                    id="contact-phone" 
                    placeholder="Enter contact phone" 
                    value={contactPhone}
                    onChange={(e) => setContactPhone(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Textarea 
                    id="address" 
                    placeholder="Enter organization address" 
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Organization Description</Label>
                  <Textarea 
                    id="description" 
                    placeholder="Enter a brief description of your organization" 
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleSaveGeneralSettings}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Manage how you receive notifications about events and bookings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-notifications">Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications about new bookings, changes, and updates via email
                    </p>
                  </div>
                  <Switch 
                    id="email-notifications" 
                    checked={emailNotifications}
                    onCheckedChange={setEmailNotifications}
                  />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms-notifications">SMS Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications about new bookings, changes, and updates via SMS
                    </p>
                  </div>
                  <Switch 
                    id="sms-notifications" 
                    checked={smsNotifications}
                    onCheckedChange={setSmsNotifications}
                  />
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <Label>Notification Types</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">New Bookings</p>
                          <p className="text-sm text-muted-foreground">When a new event is booked</p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Booking Changes</p>
                          <p className="text-sm text-muted-foreground">When an event is modified</p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Booking Cancellations</p>
                          <p className="text-sm text-muted-foreground">When an event is cancelled</p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Payment Notifications</p>
                          <p className="text-sm text-muted-foreground">When a payment is received</p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </Card>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleSaveNotificationSettings}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="payment">
            <Card>
              <CardHeader>
                <CardTitle>Payment Settings</CardTitle>
                <CardDescription>
                  Manage your payment preferences and billing information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="default-payment-method">Default Payment Method</Label>
                  <Select 
                    value={defaultPaymentMethod} 
                    onValueChange={setDefaultPaymentMethod}
                  >
                    <SelectTrigger id="default-payment-method">
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="card">Credit Card</SelectItem>
                      <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    This is your preferred method for paying invoices
                  </p>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <Label>Payment Methods</Label>
                  
                  <Card className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <CreditCard className="h-8 w-8 text-primary" />
                        <div>
                          <p className="font-medium">Credit Card</p>
                          <p className="text-sm text-muted-foreground">Pay invoices using a credit card</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Set Up
                      </Button>
                    </div>
                  </Card>
                  
                  <Card className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Building2 className="h-8 w-8 text-primary" />
                        <div>
                          <p className="font-medium">Bank Transfer</p>
                          <p className="text-sm text-muted-foreground">Pay invoices via bank transfer</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </Card>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <Label>Billing Information</Label>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="billing-name">Billing Name</Label>
                      <Input id="billing-name" defaultValue={organizationName} />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="billing-email">Billing Email</Label>
                      <Input id="billing-email" defaultValue={contactEmail} />
                    </div>
                    
                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="billing-address">Billing Address</Label>
                      <Textarea id="billing-address" defaultValue={address} />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleSavePaymentSettings}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PartnerLayout>
  )
}
