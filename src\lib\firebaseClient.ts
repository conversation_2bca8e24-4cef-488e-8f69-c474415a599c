// Import Firebase v9+ SDK
import { initializeApp, getApps, getApp } from 'firebase/app';
import { getRemoteConfig, fetchAndActivate, getValue, getBoolean } from 'firebase/remote-config';
import { debugLog } from './logger';
import RemoteConfig from './remoteConfig';

// Firebase configuration for client-side
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase client
export const initFirebase = async (): Promise<void> => {
  debugLog('Initializing Firebase client', { firebaseConfig });
  if (typeof window === 'undefined') {
    return; // Don't initialize on server side
  }

  try {
    // Only initialize if it hasn't been initialized yet
    const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();
    debugLog('Firebase client initialized');

    // Initialize Remote Config
    const remoteConfig = RemoteConfig.getInstance();
    await remoteConfig.initialize();
  } catch (error) {
    console.error('Error initializing Firebase client:', error);
  }
};

// Create a function to get the Firebase app instance
export const getFirebaseApp = () => {
  if (typeof window === 'undefined') {
    return null; // Return null on server side
  }
  
  return getApps().length === 0 ? null : getApp();
};

// Export the firebase remote config functions
export { getRemoteConfig, fetchAndActivate, getValue, getBoolean };