import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { Event } from '@/types';
import { SESClient, SendEmailCommand } from "@aws-sdk/client-ses";

// Initialize SES client
const FROM_EMAIL = process.env.FROM_EMAIL || "<EMAIL>";
const sesClient = new SESClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

/**
 * @api {POST} /api/event/:eventId/managers/invite Invite a manager to an event
 * @apiName InviteManager
 * @apiGroup Event
 * @apiDescription Sends an invitation email to a potential event manager
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { eventId } = req.query;

  if (!eventId || typeof eventId !== 'string') {
    return res.status(400).json({ error: 'Event ID is required' });
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Get user info from session
    const session = await getServerSession(req, res, authConfig);

    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get event data
    console.log(`Looking up event with ID: ${eventId}`);
    const event = await Database.getInstance().readData('events', eventId) as Event;

    if (!event) {
      console.error(`Event with ID ${eventId} not found`);
      return res.status(404).json({ error: 'Event not found' });
    }

    console.log(`Found event: ${event.eventName} (ID: ${eventId})`);

    // Verify user has access to this event (must be owner to invite managers)
    const isOwner = event.ownerAccountId === session.user.id || event.ownerEmail === session.user.email;
    if (!isOwner) {
      return res.status(403).json({ error: 'Only the event owner can invite managers' });
    }

    // Get the email from the request body
    const { email } = req.body;
    if (!email || typeof email !== 'string') {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Try to send invitation email to the manager, but continue even if it fails
    try {
      await sendManagerInvitationEmail(event, email, eventId);
      return res.status(200).json({
        success: true,
        message: `Invitation sent to ${email}`
      });
    } catch (emailError) {
      console.error('Email sending failed but continuing with manager addition:', emailError);
      // Return success even if email fails - the manager will still be added
      return res.status(200).json({
        success: true,
        emailSent: false,
        message: `Manager ${email} added, but invitation email could not be sent`
      });
    }
  } catch (error) {
    console.error('Error sending manager invitation:', error);
    return res.status(500).json({ error: 'Failed to send manager invitation' });
  }
}

/**
 * Sends an invitation email to a potential event manager
 * @param event The event to invite the manager to
 * @param managerEmail The email address of the potential manager
 * @param eventId The ID of the event (from the URL parameter)
 */
async function sendManagerInvitationEmail(event: Event, managerEmail: string, eventId: string) {
  try {
    // Create the email content
    const subject = `You've been invited to manage "${event.eventName}"`;

    // Get the base URL for the application
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://iamcoming.io';
    console.log(`Using base URL: ${baseUrl}`);

    // Check if NEXT_PUBLIC_APP_URL is set
    if (!process.env.NEXT_PUBLIC_APP_URL) {
      console.warn('NEXT_PUBLIC_APP_URL is not set, using default: https://iamcoming.io');
    }

    // Create the special link with the invitedEmail parameter
    // Use the eventId from the URL parameter instead of event.id which might not exist
    const inviteLink = `${baseUrl}/event/${eventId}?invitedEmail=${encodeURIComponent(managerEmail)}`;

    console.log(`Generated invitation link: ${inviteLink}`);

    // Create HTML body
    const htmlBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>You've been invited to manage an event</h2>
        <p>Hello,</p>
        <p>You have been invited to help manage the event "${event.eventName}" on I am Coming.</p>

        <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
          <h3 style="margin-top: 0;">${event.eventName}</h3>
          <p><strong>Date:</strong> ${new Date(event.eventDate).toLocaleDateString()}</p>
          <p><strong>Time:</strong> ${event.start} - ${event.end}</p>
          <p><strong>Location:</strong> ${event.location}</p>
          <p><strong>Hosted by:</strong> ${event.host}</p>
        </div>

        <p>To accept this invitation and start managing the event, please click the button below. If you don't have an account yet, you'll be guided through the sign-up process.</p>

        <div style="margin: 25px 0;">
          <a href="${inviteLink}" style="background-color: #F43F5E; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">Accept Invitation</a>
        </div>

        <p>Once you've signed in, you'll be able to help manage invites, track RSVPs, and more.</p>

        <p>Thank you,<br>The I am Coming Team</p>

        <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0 20px;">
        <p style="font-size: 12px; color: #718096; text-align: center;">
          I am Coming - The easiest way to manage your events<br>
          <a href="https://iamcoming.io/about" style="color: #718096;">About Us</a> |
          <a href="mailto:<EMAIL>" style="color: #718096;">Contact Support</a>
        </p>
      </div>
    `;

    // Create plain text body
    const textBody = `
You've been invited to manage an event

Hello,

You have been invited to help manage the event "${event.eventName}" on I am Coming.

EVENT DETAILS
--------------------
Event: ${event.eventName}
Date: ${new Date(event.eventDate).toLocaleDateString()}
Time: ${event.start} - ${event.end}
Location: ${event.location}
Hosted by: ${event.host}

To accept this invitation and start managing the event, please visit the link below:
${inviteLink}

If you don't have an account yet, you'll be guided through the sign-up process.

Once you've signed in, you'll be able to help manage invites, track RSVPs, and more.

Thank you,
The I am Coming Team

--------------------
I am Coming - The easiest way to manage your events
About Us: https://iamcoming.io/about
Contact Support: <EMAIL>
    `;

    // Send the email using AWS SES directly
    const params = {
      Source: `I am Coming<${FROM_EMAIL}>`,
      Destination: {
        ToAddresses: [managerEmail],
      },
      Message: {
        Subject: {
          Data: subject,
        },
        Body: {
          Text: {
            Data: textBody,
          },
          Html: {
            Data: htmlBody,
          },
        },
      },
    };

    const command = new SendEmailCommand(params);
    await sesClient.send(command);

    log(`Manager invitation email sent to ${managerEmail} for event ${event.eventName}`);
    return true;
  } catch (error) {
    console.error(`Error sending manager invitation email to ${managerEmail}:`, error);
    log(`Email sending failed for manager invitation to ${managerEmail}, but manager will still be added`);
    // Re-throw the error so it can be caught by the handler
    throw error;
  }
}
