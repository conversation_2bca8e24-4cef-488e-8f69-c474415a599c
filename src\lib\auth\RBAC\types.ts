import { rbacConfig } from './index';

/**
 * RBAC Types for Type-Safe Permission Management
 */

// Extract types from the configuration
export type RBACConfig = typeof rbacConfig;

// Permission context for venue-specific operations
export interface PermissionContext {
  venueId?: string;
  organizationId?: string;
  userId?: string;
}

// User role assignment
export interface UserRole {
  id: string;
  userId: string;
  role: string;
  portal: 'partner' | 'admin';
  context?: PermissionContext;
  assignedAt: Date;
  assignedBy: string;
  expiresAt?: Date;
  isActive: boolean;
}

// Permission check result
export interface PermissionCheckResult {
  granted: boolean;
  role: string;
  resource: string;
  permission: string;
  context?: PermissionContext;
  reason?: string;
}

// Audit log entry for permission changes
export interface PermissionAuditLog {
  id: string;
  action: 'grant' | 'revoke' | 'check';
  userId: string;
  targetUserId?: string;
  role: string;
  resource: string;
  permission: string;
  granted: boolean;
  context?: PermissionContext;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
}

// Role assignment request
export interface RoleAssignmentRequest {
  userId: string;
  role: string;
  portal: 'partner' | 'admin';
  context?: PermissionContext;
  expiresAt?: Date;
  assignedBy: string;
}

// Permission validation middleware context
export interface PermissionMiddlewareContext {
  user: {
    id: string;
    roles: UserRole[];
  };
  resource: string;
  permission: string;
  context?: PermissionContext;
}

// Resource action mapping for API endpoints
export interface ResourceActionMap {
  [endpoint: string]: {
    resource: string;
    permission: string;
    context?: (req: any) => PermissionContext;
  };
}

// Role hierarchy definition
export interface RoleHierarchy {
  role: string;
  inheritsFrom?: string[];
  portal: 'partner' | 'admin';
}

// Permission inheritance rules
export interface PermissionInheritance {
  parentResource: string;
  childResource: string;
  inheritedPermissions: string[];
}
