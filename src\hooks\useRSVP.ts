import { Error, Event, EventInvite } from "@/types";
import { useCallback, useEffect, useState } from "react";

export function useRSVP(eventId?: string, inviteId?: string) {
  const [event, setEvent] = useState<Event|null>(null);
  const [invite, setInvites] = useState<EventInvite|null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchInvite = useCallback(async () => {
    try {
      const event = await (await fetch('/api/event/' + eventId)).json();
      const invite = await (await fetch('/api/event/' + eventId + '/invites/' + inviteId)).json();

      setEvent(event);
      setInvites(invite);
      setError(null);

    } catch (error) {
      setError(error as Error);
      setInvites(null);
      setEvent(null);
    } finally {
      setLoading(false);
    }
  }, [eventId, inviteId]);

  useEffect(() => {
    if (!eventId) return;
    fetchInvite();
  }, [eventId, fetchInvite, inviteId]);

  const rsvpInvite = async (inviteRsvp: {
    status: 'accepted' | 'declined';
    adults: number;
    children: number;
    message: string;
    email: string;
    phone?: string;
  }) => {
    setLoading(true);
    setError(null);

    try {
      const res = await fetch('/api/event/' + eventId + '/invites/' + inviteId + '/rsvp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(inviteRsvp),
      });
      const data = await res.json(); 

      setInvites(data.invite);
      setLoading(false);

      return data;
    } catch (error) {
      setError(error as Error);
    }

    setLoading(false);
  }

  return { invite, event, loading, error, rsvpInvite, reloadInvite: fetchInvite };
}