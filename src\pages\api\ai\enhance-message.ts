import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import geminiService from '@/lib/gemini/service';
import { EventFormData } from '@/types';
import { generateTempEventId, createTempEventTracking, checkAIUsageLimit, incrementAIUsage } from '@/lib/ai-usage';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Force recompilation

  try {
    const {
      eventId,
      eventName,
      eventDate,
      startTime,
      endTime,
      location,
      timezone,
      host,
      currentMessage,
      messageStyle = 'personalised'
    } = req.body;

    // Validate required fields
    if (!eventName) {
      return res.status(400).json({ error: 'Event name is required' });
    }

    // Get session for user authentication
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Handle missing eventId by creating a temporary event for AI usage tracking
    let actualEventId = eventId;
    if (!eventId) {
      // Generate a temporary event ID for new events
      actualEventId = generateTempEventId(session.user.id);

      // Create temporary event tracking entry
      await createTempEventTracking(actualEventId, session.user.id, session.user.organization?.id);
    }

    // Check if user has AI access
    if (!session.user.hasAiAccess) {
      return res.status(403).json({ error: 'AI access not available for this user' });
    }

    // Check AI usage limits
    try {
      const limitCheck = await checkAIUsageLimit(actualEventId);

      if (!limitCheck.canUseAI) {
        return res.status(429).json({
          error: 'AI usage limit exceeded',
          details: limitCheck.message,
          currentUsage: limitCheck.currentUsage,
          limit: limitCheck.limit
        });
      }
    } catch (error) {
      console.error('Error checking AI usage limit:', error);
      return res.status(500).json({ error: 'Failed to check AI usage limits' });
    }

    // Validate messageStyle if provided
    const validStyles = ['personalised', 'casual_friendly', 'formal_professional', 'fun_energetic', 'business_professional', 'creative_unique'];
    if (messageStyle && !validStyles.includes(messageStyle)) {
      return res.status(400).json({ 
        error: `Invalid message style. Must be one of: ${validStyles.join(', ')}` 
      });
    }

    // Prepare event data for AI enhancement
    const eventData: EventFormData = {
      eventName,
      eventDate: eventDate ? new Date(eventDate) : new Date(),
      start: startTime,
      end: endTime,
      location: location,
      timezone: timezone,
      host: host,
      message: currentMessage,
      messageStyle: messageStyle
    };

    // Generate enhanced message using Gemini service
    const enhancedMessage = await geminiService.generateEventDescription(eventData);

    // Record AI usage after successful generation
    await incrementAIUsage(actualEventId);

    return res.status(200).json({
      success: true,
      enhancedMessage,
      originalMessage: currentMessage,
      timestamp: new Date().toISOString(),
      tempEventId: !eventId ? actualEventId : undefined // Return temp event ID if created
    });

  } catch (error) {
    console.error('Error enhancing message with AI:', error);
    return res.status(500).json({ 
      error: 'Failed to enhance message with AI',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
