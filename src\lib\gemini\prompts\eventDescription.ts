import { EventFormData } from '../../../types';

/**
 * Generate prompt for event description
 * Creates messages with different styles based on messageStyle parameter
 */
export function generateEventDescriptionPrompt(eventData: EventFormData, formattedDate: string): string {
  // Define style-specific guidelines
  const styleGuides = {
    personalised: {
      tone: 'warm, personal, and professionally crafted',
      approach: 'tailored and personal touch',
      instructions: 'Write in first person as if the host is speaking directly to guests. Use a warm, genuine, and sophisticated tone - professional yet personal.'
    },
    casual_friendly: {
      tone: 'relaxed, friendly, and approachable',
      approach: 'conversational and laid-back',
      instructions: 'Write in a casual, friendly tone like you\'re talking to close friends. Use relaxed language and be approachable.'
    },
    formal_professional: {
      tone: 'polished, professional, and elegant',
      approach: 'sophisticated and refined',
      instructions: 'Write in a formal, professional tone with elegant language. Maintain sophistication while being clear and respectful.'
    },
    fun_energetic: {
      tone: 'playful, exciting, and energetic',
      approach: 'vibrant and enthusiastic',
      instructions: 'Write with enthusiasm and energy! Use playful language and convey excitement about the event.'
    },
    business_professional: {
      tone: 'corporate, formal, and businesslike',
      approach: 'professional and corporate',
      instructions: 'Write in a corporate, business-professional tone. Be formal, clear, and maintain professional standards.'
    },
    creative_unique: {
      tone: 'original, innovative, and creative',
      approach: 'unique and artistic',
      instructions: 'Write in a creative, unique style that stands out. Be original and innovative while maintaining clarity.'
    }
  };

  const style = styleGuides[eventData.messageStyle || 'personalised'];

  return `
    Create a ${style.tone} event description for guests with a ${style.approach}. This should sound like it's coming directly from the host.

    Event Information:
    - Event Name: ${eventData.eventName}
    - Date: ${formattedDate}
    - Start Time: ${eventData.start}
    - End Time: ${eventData.end}
    - Location: ${eventData.location}
    - Host: ${eventData.host}
    - Current Message: ${eventData.message || 'Not provided'}

    Style Guidelines:
    1. ${style.instructions}
    2. Add 1-2 tasteful emojis that match the event type and style (wedding 💍, birthday 🎉, etc.)
    3. Structure the message with proper flow: opening excitement → event details → what to expect → closing invitation
    4. Include the key details naturally and elegantly in the conversation
    5. Don't start with generic phrases like "Hey everyone!" - be more creative and specific to the event
    6. Write 500-1000 characters (about 4-6 well-crafted sentences)
    7. Include what makes this event special and what guests can look forward to
    8. End with a heartfelt invitation that matches the chosen style
    9. Use proper grammar, punctuation, and sentence structure
    10. Make it feel exclusive and special, not generic

    Write ONLY the polished description content - no prefixes, no markdown formatting. Create a message that reflects the ${style.approach} style perfectly.
  `;
}
