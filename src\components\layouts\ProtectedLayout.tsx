"use client"

import { useSession } from "next-auth/react"
import { ProfileCompletionForm } from "@/components/ProfileCompletionForm"
import { useRouter } from "next/router"
import { ComponentProps, useEffect } from "react"
import { Footer } from "@/components/Footer"
import { Header } from "../Header"


interface ProtectedLayoutProps {
  children: React.ReactNode
  isAdmin?: boolean
  headerProps?: ComponentProps<typeof Header>
  hideFooter?: boolean
}

export function ProtectedLayout({ children, isAdmin, headerProps, hideFooter }: ProtectedLayoutProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      // Redirect to the sign-in page if the user is not authenticated
      const isSignInPage = router.asPath.startsWith("/auth/signin")
      if (!isSignInPage) {
        const redirectUrl = router.asPath
        sessionStorage.setItem("redirectUrl", redirectUrl)
      }
      router.push("/auth/signin")
    } else if (status === "authenticated") {
      // Handle redirect URL if it exists
      const redirectUrl = sessionStorage.getItem("redirectUrl")
      if (redirectUrl) {
        sessionStorage.removeItem("redirectUrl")
        router.push(redirectUrl || "/events")
      }
    }
  }, [status, router])

  if (status === "authenticated" && isAdmin && !session.user.isAdmin) {
    router.push("/404")
    return null
  }

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!session?.user?.isProfileComplete) {
    return (
      <div className="container flex items-center justify-center min-h-screen py-12">
        <ProfileCompletionForm onComplete={() => { }} />
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      {headerProps && <Header {...headerProps} />}
      <div className="content-area flex-1 flex flex-col">
        {children}
      </div>
      <Footer type="app" className={hideFooter ? "hidden lg:block" : ""} />
    </div>
  )
}